#!/bin/bash

# 解析命令行参数（支持 --start 和 --end 参数）
while [[ "$#" -gt 0 ]]; do
    case $1 in
        --start) START_DATE="$2"; shift 2 ;;
        --end) END_DATE="$2"; shift 2 ;;
        *) echo "未知参数: $1"; exit 1 ;;
    esac
done

# 如果未提供参数，提示用户输入
if [ -z "$START_DATE" ] || [ -z "$END_DATE" ]; then
    read -p "请输入开始日期（格式：YYYY-MM-DD）: " START_DATE
    read -p "请输入结束日期（格式：YYYY-MM-DD）: " END_DATE
fi

# 定义文件类型
FILE_TYPES="\.vue$|\.js$|\.ts$|\.json$|\.css$|\.scss$|\.java$|\.py$|\.php$|\.rb$|\.go$|\.cs$|\.sql$|\.xml$|\.yml$|\.yaml$|\.md$"

# 获取提交哈希
commits=$(git log --since="$START_DATE" --until="$END_DATE" --pretty=format:"%H")

# 初始化统计
total_lines=0

# 定义文件类型映射
file_types=("vue" "js" "ts" "json" "css" "scss" "java" "py" "php" "rb" "go" "cs" "sql" "xml" "yml" "yaml" "md")
for type in "${file_types[@]}"; do
    declare "count_$type"=0
done

# 遍历提交
for commit in $commits; do
    # 检查是否为首次提交（无父提交）
    if [ -z "$commit^" ]; then
        continue
    fi

    # 获取提交的差异（使用 --numstat）
    diff_output=$(git diff $commit^ $commit --numstat)

    # 解析差异中的行数变化
    while IFS=$'\t' read -r added deleted file; do
        # 过滤文件类型
        if [[ $file =~ $FILE_TYPES ]]; then
            # 计算净增行数
            net_lines=$((added - deleted))
            total_lines=$((total_lines + net_lines))

            # 根据文件扩展名统计类型
            if [[ $file == *.vue ]]; then
                count_vue=$((count_vue + net_lines))
            elif [[ $file == *.js ]]; then
                count_js=$((count_js + net_lines))
            elif [[ $file == *.ts ]]; then
                count_ts=$((count_ts + net_lines))
            elif [[ $file == *.json ]]; then
                count_json=$((count_json + net_lines))
            elif [[ $file == *.css ]]; then
                count_css=$((count_css + net_lines))
            elif [[ $file == *.scss ]]; then
                count_scss=$((count_scss + net_lines))
            elif [[ $file == *.java ]]; then
                count_java=$((count_java + net_lines))
            elif [[ $file == *.py ]]; then
                count_py=$((count_py + net_lines))
            elif [[ $file == *.php ]]; then
                count_php=$((count_php + net_lines))
            elif [[ $file == *.rb ]]; then
                count_rb=$((count_rb + net_lines))
            elif [[ $file == *.go ]]; then
                count_go=$((count_go + net_lines))
            elif [[ $file == *.cs ]]; then
                count_cs=$((count_cs + net_lines))
            elif [[ $file == *.sql ]]; then
                count_sql=$((count_sql + net_lines))
            elif [[ $file == *.xml ]]; then
                count_xml=$((count_xml + net_lines))
            elif [[ $file == *.yml ]]; then
                count_yml=$((count_yml + net_lines))
            elif [[ $file == *.yaml ]]; then
                count_yaml=$((count_yaml + net_lines))
            elif [[ $file == *.md ]]; then
                count_md=$((count_md + net_lines))
            fi
        fi
    done <<< "$diff_output"
done

# 输出结果
echo "📅 时间范围：$START_DATE 至 $END_DATE"
echo "📊 总新增代码行数：$total_lines"
echo "🔍 按文件类型统计："
for type in "${file_types[@]}"; do
    count_var="count_$type"
    count=${!count_var}
    echo "  $type: $count"
done