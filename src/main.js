/*
 * @Description:
 * @Author: xiu<PERSON>
 * @Date: 2024-03-25 09:56:59
 * @LastEditTime: 2025-07-22 11:13:30
 * @LastEditors: xiuji <EMAIL>
 */

// 版本检测和自动刷新逻辑 - 页面活跃时检查
(function checkVersion() {
  let checkTimer = null;

  const checkForUpdate = () => {
    fetch('/version.json?t=' + Date.now(), { 
      method: 'GET',
      cache: 'no-cache'
    }).then(response => {
      if (response.ok) {
        return response.json();
      }
      throw new Error('版本检查失败');
    }).then(data => {
      const storedVersion = localStorage.getItem('app_version');
      if (storedVersion && storedVersion !== data.version) {
        // 先更新本地版本号，防止死循环
        localStorage.setItem('app_version', data.version);
        window.location.reload();
      } else if (!storedVersion) {
        localStorage.setItem('app_version', data.version);
      }
    }).catch(() => {
      // 版本文件无法访问时的处理
    });
  };

  const startChecking = () => {
    if (checkTimer) return; // 避免重复启动
    
    checkForUpdate(); // 立即检查一次
    checkTimer = setInterval(checkForUpdate, 60 * 1000);
  };

  const stopChecking = () => {
    if (checkTimer) {
      clearInterval(checkTimer);
      checkTimer = null;
    }
  };

  // 页面可见性变化时控制检查
  document.addEventListener('visibilitychange', () => {
    if (!document.hidden) {
      startChecking(); // 页面可见时开始检查
    } else {
      stopChecking(); // 页面隐藏时停止检查
    }
  });

  // 页面加载时开始检查
  startChecking();
})();

import {
    createApp
} from 'vue'
import App from './App.vue'
import router from './router/index'
import store from './store/index'
import Antd from 'ant-design-vue'
import ElementPlus from 'element-plus'
import VueLazyload from 'vue-lazyload';
import MarkdownIt from 'markdown-it'
import 'element-plus/dist/index.css'
// import '@unocss/reset/tailwind.css'
import "ant-design-vue/dist/antd.css";
import 'animate.css';
import "@/assets/iconfont/iconfont.css"
import "@/assets/iconfont/iconfont.js"
import '@/assets/css/_font.scss'
import '@/assets/css/_layout.scss'
import VMdPreview from '@kangc/v-md-editor/lib/preview';
import '@kangc/v-md-editor/lib/style/preview.css';
import githubTheme from '@kangc/v-md-editor/lib/theme/github.js';
import '@kangc/v-md-editor/lib/theme/style/github.css';
// 引入打印插件
import print from "vue3-print-nb";
// highlightjs
import hljs from 'highlight.js';

VMdPreview.use(githubTheme, {
  Hljs: hljs,
});

import('@/assets/css/index.scss').then(() => {
    createApp(App).use(router).use(store).use(Antd).use(print).use(VMdPreview).use(MarkdownIt).use(ElementPlus).use(VueLazyload, {
        // loading: 'https://smart.jsisi.cn:8099/portal/resource/2025/01/06/200b0fc7-33bd-4133-955e-ecc1fc91dd89/loading.gif', // 加载中时的图片
    }).mount('#app')
})

// Vue.use(VueLazyload, {
//     preLoad: 1.3, // 预加载高度比例，默认为1.3
//     error: 'path/to/error.png', // 加载失败时的图片
//     loading: 'path/to/loading.gif', // 加载中时的图片
//     attempt: 1 // 尝试加载图片的次数，默认为1
//   });
