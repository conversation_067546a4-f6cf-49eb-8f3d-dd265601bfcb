import {
    getRequest,
    postRequestBody3,
    getRequestByValue
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    dataCount: baseURL + 'view/schemeModule/data/overview',
    projectList: baseURL + 'view/schemeModule/combine/page',
    projectSceneList: baseURL + 'view/solution/module/page',
    tradeList: baseURL + 'view/solution/category/list',
    providerList: baseURL + 'backend/system/org/list?level=2',
    labelList: baseURL + 'backend/system/label/list',
    // 场景能力合并接口
    guideList: baseURL + 'view/combine/draft/guide/page',
    selectLabel: baseURL + 'backend/ability/type/tree',
    
    //新场景方案图谱接口
    proNewSceneList: baseURL + 'view/sceneInfo/page',
    //新案例图谱接口
    proNewCaseList: baseURL + 'view/caseInfo/page',
}

// 获取方案总览
export const getCount = (data) => getRequest(url.dataCount, data)

// 获取方案列表
export const getProjectList = (data) => getRequest(url.projectList, data)
export const getSchemeList = (data) => getRequest(url.projectList, data)
export const getSceneSchemeList = (data) => getRequest(url.projectSceneList, data)
export const guideList = (data) => getRequest(url.guideList, data)

// 获取类目列表
export const getTradeList = (data) => getRequest(url.tradeList, data)
// 获取类目列表
export const getProviderList = (data) => getRequest(url.providerList, data)
// 获取类目列表
export const getLabelList = (data) => getRequest(url.labelList, data)
// 获取筛选标签数据
export const selectLabel = (data) => getRequest(url.selectLabel, data)

//新场景方案图谱接口
export const getNewSceneSchemeList = (data) => getRequest(url.proNewSceneList, data)
//新案例图谱接口
export const getNewCaseList = (data) => getRequest(url.proNewCaseList, data)