import {
    getRequest,
    postRequestBody3,
    getRequestByValue,
    putRequest,
    deleteRequest
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
// let baseURL1 = 'http://192.168.100.23:8098/portal/';
const url = {
    dataDetail: `${baseURL}view/solution/module/`,
    dataDetailNew: `${baseURL}view/schemeModule/`,
    emsCode: `${baseURL}/xhly/download/sendCode`,
    subCode: `${baseURL}/xhly/download/verifyCode`,
    dowFile: `${baseURL}/xhly/download/getDownloadFileInfo`,
    loadCount: `${baseURL}view/schemeModule/downloadCount/`,
    soluLoadCount: `${baseURL}view/solution/module/downloadCount/`,
    introduce: `${baseURL}view/schemeModule/introduce`,
    collect: `${baseURL}view/schemeModule/collect/`,
    cancelCollect: `${baseURL}view/schemeModule/cancelCollect/`,
    review: `${baseURL}view/ability/review/create`,
    reviewList: `${baseURL}view/ability/review/page`,
    reviewDelete: `${baseURL}view/ability/review/`,
    solveDetail: `${baseURL}view/solution/`,
    cityDetail: `${baseURL}view/city/solution/`,
    
    //获取场景新详情
    sceneInfoDetail: `${baseURL}view/sceneInfo/`,
    //获取案例新详情
    caseInfoDetail: `${baseURL}view/caseInfo/`,
}

// 获取详情
export const getDetail = (data) => getRequestByValue(url.dataDetail, data)
export const dataDetailNew = (data) => getRequestByValue(url.dataDetailNew, data)

// 验证码
export const getCode = (data) => postRequestBody3(url.emsCode, data)
// 引入申请
export const getIntroduce = (data) => postRequestBody3(url.introduce, data)

// 校验验证码
export const subCode = (data) => postRequestBody3(url.subCode, data)

// 下载附件
export const downFile = (id) => getRequest(url.dowFile, id)
// 下载次数
export const getDownCount = (data) => putRequest(url.loadCount, data)

//收藏
export const collect = (data) => putRequest(url.collect, data)
// 取消收藏
export const cancelCollect = (data) => putRequest(url.cancelCollect, data)

// 评价
export const getReview = (data) => postRequestBody3(url.review, data)
// 评价列表
export const getReviewList = (data) => getRequest(url.reviewList, data)
// 删除评价
export const getReviewDelete = (data) => deleteRequest(url.reviewDelete, data)

//场景下载次数
export const getSoluDownCount = (data) => putRequest(url.soluLoadCount, data)
export const getSolveDetail = (data) => getRequestByValue(url.solveDetail, data)


//新场景方案详情
export const getSceneInfoDetail = (data) => getRequestByValue(url.sceneInfoDetail, data)
//新案例详情
export const getCaseInfoDetail = (data) => getRequestByValue(url.caseInfoDetail, data)
//市级方案详情
export const getCityDetail = (data) => getRequestByValue(url.cityDetail, data)