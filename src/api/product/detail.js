import { getRequest, postRequestBody3, getRequestByValue, putRequest } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
// let baseURL1 = 'http://192.168.100.23:8098/portal/';
const url = {
    dataDetail: `${baseURL}view/product/`,
    emsCode: `${baseURL}/xhly/download/sendCode`,
    subCode: `${baseURL}/xhly/download/verifyCode`,
    dowFile: `${baseURL}/xhly/download/getDownloadFileInfo`,
    loadCount: `${baseURL}view/product/downloadCount/`,
    introduce: `${baseURL}view/product/introduce`,
    collect: `${baseURL}view/product/collect/`,
    cancelCollect: `${baseURL}view/product/cancelCollect/`,
}

// 获取详情
export const getDetail = (data) => getRequestByValue(url.dataDetail, data)

// 验证码
export const getCode = (data) => postRequestBody3(url.emsCode, data)
// 引入申请
export const getIntroduce = (data) => postRequestBody3(url.introduce, data)

// 校验验证码
export const subCode = (data) => postRequestBody3(url.subCode, data)

// 下载附件
export const downFile = (id) => getRequest(url.dowFile, id)
// 下载次数
export const getDownCount = (data) => putRequest(url.loadCount, data)

//收藏
export const collect = (data) => putRequest(url.collect, data)
// 取消收藏
export const cancelCollect = (data) => putRequest(url.cancelCollect, data)