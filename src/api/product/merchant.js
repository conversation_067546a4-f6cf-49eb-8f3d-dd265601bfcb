import { getRequest, putRequestBody, postRequestBody, deleteRequest, deleteRequestBody, getRequestByValue } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();

const url = {
    // getMerchantProList: `${baseURL}backend/product/label/list`,
    labelList: `${baseURL}backend/product/merchant/label/page`,
    labelDetail: `${baseURL}backend/product/merchant/label/`,
    labelTreeSelect: `${baseURL}backend/product/merchant/label/tree`,
    getMerchantProList: `${baseURL}backend/product/page`,
    getCloudLabel: `${baseURL}backend/moveCloud/label/tree`
}
// 获取列表
export const getMerchantProList = (data) => getRequest(url.getMerchantProList, data)

export const getAllList = (data) => getRequest(url.allList, data)
// 标签树
export const merchantGetLabelList = (data) => getRequest(url.labelList, data)

// 标签详情
export const labelDetail = (data) => getRequestByValue(url.labelDetail, data)

// 标签下拉
export const labelTreeSelect = (data) => getRequest(url.labelTreeSelect, data)
// 云标签下拉
export const getCloudLabel = (data) => getRequest(url.getCloudLabel, data)