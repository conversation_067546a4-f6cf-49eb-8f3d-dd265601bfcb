import { getRequest, postBlobRequest } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
const baseURL = getBaseUrl();

const url = {
    labelList: `${baseURL}backend/product/merchant/label/page`,
    importExcel: `${baseURL}backend/product/specs/importExcel`,
}
export const importExcel = (data) => postBlobRequest(url.importExcel, data)
export const merchantGetLabelList = (data) => getRequest(url.labelList, data)
