import { getRequest, postRequestBody3, putRequestBody, getRequestByValue } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
const baseURL = getBaseUrl();
const url = {
    create: `${baseURL}backend/product/create`,
    update: `${baseURL}backend/product/update`,
    edit: `${baseURL}backend/product/update`,
    detail: `${baseURL}backend/product/`,
    getProviderList: `${baseURL}backend/system/org/list?`,
    check: `${baseURL}backend/product/add/check`,
    updateReview: `${baseURL}backend/product/update/review`

}


//新增
export const create = (data) => postRequestBody3(url.create, data)
//产品名称编码校验
export const check = (data) => postRequestBody3(url.check, data)
//草稿保存
export const update = (data) => putRequestBody(url.update, data)

// 更新
export const updateReview = (data) => putRequestBody(url.updateReview, data)

//修改
export const edit = (data) => putRequestBody(url.edit, data)

//详情
export const detail = (data) => getRequestByValue(url.detail, data)

//详情
export const getProviderList = (data) => getRequest(url.getProviderList, data)


