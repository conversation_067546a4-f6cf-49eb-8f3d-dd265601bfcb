
import { getRequest, postRequestBody3,deleteRequest,putRequestBody,getRequestByValue } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
const baseURL = getBaseUrl();
const url = {
    teamTree: `${baseURL}backend/system/org/list`,
    createTeam: `${baseURL}backend/system/org`,
    detail:`${baseURL}backend/system/org/`,
    selectTree: `${baseURL}backend/ecopartner/listTwo`,
    selectTreeNew: `${baseURL}backend/ecopartner/listTwo?name=`,
    newTree: `${baseURL}view/ecopartner/list`
}

// 获取组织树
export const getTree = (data) => getRequest(url.teamTree, data)

// 创建组织
export const createTeam = (data) => postRequestBody3(url.createTeam, data)

//详情
export const detail = (data) => getRequestByValue(url.detail,data)

//修改
export const update = (data) => putRequestBody(url.createTeam,data)

//删除
export const deleteRecord = (data) => deleteRequest(url.detail, data)

export const getNewTree = (data) => getRequest(url.newTree, data)
export const selectTree = (data) => getRequest(url.selectTree, data)
export const selectTreeNew = (data) => getRequestByValue(url.selectTreeNew, data)