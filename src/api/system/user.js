import {
    getRequest,
    postRequestBody,
    getRequestByValue,
    putRequestBody,
    deleteRequest,
    putRequestBody2
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
const baseURL = getBaseUrl();
// const baseURL1 = 'http://192.168.100.23:8098/portal/';
const url = {
    userList: `${baseURL}backend/system/user/page`,
    createRole: `${baseURL}backend/system/user`,
    roleDetail: `${baseURL}backend/system/user/`,
    userAlert: `${baseURL}backend/system/user/allocateRole`,
    resetPwd: `${baseURL}backend/system/user/resetPwd`,
    createPen: `${baseURL}backend/system/account/create`,
    getHelp: `${baseURL}backend/helpDocument/download`,
    userAllList: `${baseURL}backend/system/user/list`,
}

// 获取用户列表
export const getUserList = (data) => getRequest(url.userList, data)

// 获取所有用户列表
export const getAllUserList = (data) => getRequest(url.userAllList, data)

// 创建用户
export const createRole = (data) => postRequestBody(url.createRole, data)

// 用户详情
export const roleDetail = (data) => getRequestByValue(url.roleDetail, data)
// 用户详情
export const getHelp = (data) => getRequest(url.getHelp, data)

// 修改用户
export const revampRole = (data) => putRequestBody(url.createRole, data)

// 删除用户
export const deleteRole = (data) => deleteRequest(url.roleDetail, data)

// 批量修改用户角色
export const revampMoreRole = (data) => putRequestBody2(url.userAlert, data)

// 重置密码
export const resetPwd = (data) => putRequestBody2(url.resetPwd, data)

//创建账号开通申请
export const createPen = (data) => postRequestBody(url.createPen, data)