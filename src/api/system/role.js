import { getRequest, putRequestBody, postRequestBody, deleteRequest, getRequestByValue } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
const baseURL = getBaseUrl();

const url = {
    roleList: `${baseURL}backend/system/role/page`,
    revampRole: `${baseURL}backend/system/role`,
    roleDelete: `${baseURL}backend/system/role/`,
    authMenus: `${baseURL}backend/system/role/authMenus`
}

// 获取角色列表
export const getRoleList = (data) => getRequest(url.roleList, data)

// 修改角色
export const revampRole = (data) => putRequestBody(url.revampRole, data)

// 创建角色
export const createRole = (data) => postRequestBody(url.revampRole, data)

// 删除角色
export const roleDelete = (data) => deleteRequest(url.roleDelete, data)

// 角色详情
export const roleDetail = (data) => getRequestByValue(url.roleDelete, data)

// 角色权限修改
export const authMenu = (data) => putRequestBody(url.authMenus, data)