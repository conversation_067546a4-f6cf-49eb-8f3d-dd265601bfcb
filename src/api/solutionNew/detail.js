import { getRequest, postRequestBody3, getRequestByValue, putRequest, postRequestBody } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    dataDetail: `${baseURL}view/solution/`,
    emsCode: `${baseURL}/xhly/download/sendCode`,
    subCode: `${baseURL}/xhly/download/verifyCode`,
    dowFile: `${baseURL}/xhly/download/getDownloadFileInfo`,
    downCount: `${baseURL}view/solution/downloadCount/`,
    introduce: `${baseURL}view/solution/introduce`,
    collect: `${baseURL}view/solution/collect/`,
    cancelCollect: `${baseURL}view/solution/cancelCollect/`,
    newDownCount: `${baseURL}backend/file/addDownloadCount`,
    applyDownload: `${baseURL}view/workOrder/create`,
}

// 获取详情
export const getDetail = (data) => getRequestByValue(url.dataDetail, data)
// 引入申请
export const getIntroduce = (data) => postRequestBody3(url.introduce, data)
// 验证码
export const getCode = (data) => postRequestBody3(url.emsCode, data)

// 校验验证码
export const subCode = (data) => postRequestBody3(url.subCode, data)

// 下载附件
export const downFile = (id) => getRequest(url.dowFile, id)
// 下载次数
export const getDownCount = (data) => putRequest(url.downCount, data)

//收藏
export const collect = (data) => putRequest(url.collect, data)
// 取消收藏
export const cancelCollect = (data) => putRequest(url.cancelCollect, data)

// 下载附件
export const getNewDownCount = (data) => getRequest(url.newDownCount, data)

// 申请下载审核
export const applyDownload = (data) => postRequestBody(url.applyDownload, data)
