import {
    getRequest,
    postRequestBody3
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    dataCount: baseURL + 'view/solution/data/overview',
    projectList: baseURL + 'view/solution/page',
    tradeList: baseURL + 'view/solution/category/list',
    typeList: `${baseURL}backend/scheme/type/page`,
    labelTreeList: `${baseURL}backend/system/label/tree`,
    industryList: `${baseURL}backend/system/label/industry/tree`,
    hotTableList:`${baseURL}backend/solution/page`,
    teamTree: `${baseURL}view/solution/provider/list`,
    teamSchemeTree: `${baseURL}view/schemeModule/provider/list`,
    searchList:`${baseURL}backend/ecopartner/list`,
}

// 获取生态合作方
export const getSearchList = (data) => getRequest(url.searchList, data)

// 获取热门方案
export const getHotList = (data) => getRequest(url.hotTableList, data)

// 获取方案总览
export const getCount = (data) => getRequest(url.dataCount, data)

// 获取方案列表
export const getsolutionList = (data) => getRequest(url.projectList, data)
export const getProgrammeList = (data) => getRequest(url.projectList, data)

// 获取类目列表
export const getTradeList = (data) => getRequest(url.tradeList, data)

export const getTypeList = (data) => getRequest(url.typeList, data)

// 标签树
export const getLabelTreeList = (data) => getRequest(url.labelTreeList, data)
// 行业树
export const industryList = (data) => getRequest(url.industryList, data)

//方案提供方
export const getTeamTree = (data) => getRequest(url.teamTree, data)
//能力提供方
export const getTeamSchemeTree = (data) => getRequest(url.teamSchemeTree, data)