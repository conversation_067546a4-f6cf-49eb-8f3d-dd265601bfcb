import {
    getRequest,
    postRequestBody3
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    dataCount: baseURL + 'view/scene/data/overview',
    projectList: baseURL + 'view/scene/page',
    labelTreeList: `${baseURL}view/scene/label/list`,
    labelList: `${baseURL}backend/scene/label/page`,
}

// 获取方案总览
export const getSceneCount = (data) => getRequest(url.dataCount, data)

// 获取方案列表
export const getSceneList = (data) => getRequest(url.projectList, data)

// 标签树
export const getSceneTradeList = (data) => getRequest(url.labelTreeList, data)

//获取整体分类
export const getLabelList = (data) => getRequest(url.labelList, data)