import { getRequest, postRequestBody3, getRequestByValue, putRequest } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    dataDetail: `${baseURL}view/scene/`,
    emsCode: `${baseURL}/xhly/download/sendCode`,
    subCode: `${baseURL}/xhly/download/verifyCode`,
    dowFile: `${baseURL}/xhly/download/getDownloadFileInfo`,
    downCount: `${baseURL}backend/solution/downloadCount/`,
    introduce: `${baseURL}view/solution/introduce`,
    collect: `${baseURL}view/solution/collect/`,
    cancelCollect: `${baseURL}view/solution/cancelCollect/`,
    detail: `${baseURL}backend/demandScheme/`,
}

//详情
export const detail = (data) => getRequestByValue(url.detail, data)
// 获取详情
export const getSceneDetail = (data) => getRequestByValue(url.dataDetail, data)
// 引入申请
export const getIntroduce = (data) => postRequestBody3(url.introduce, data)
// 验证码
export const getCode = (data) => postRequestBody3(url.emsCode, data)

// 校验验证码
export const subCode = (data) => postRequestBody3(url.subCode, data)

// 下载附件
export const downFile = (id) => getRequest(url.dowFile, id)
// 获取类目列表
export const getDownCount = (data) => putRequest(url.downCount, data)

//收藏
export const collect = (data) => putRequest(url.collect, data)
// 取消收藏
export const cancelCollect = (data) => putRequest(url.cancelCollect, data)