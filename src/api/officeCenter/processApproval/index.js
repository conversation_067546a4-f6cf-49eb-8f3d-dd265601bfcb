import request from "@/utils/request.js";
import {isDev} from "@/setting.js";

const UrlPrefix=isDev ? "/Qilin" : "";

// 获取流程信息接口
export function getProcessInfo(params){
    return request({
        url:UrlPrefix+"/workflow/process/detail?procInsId="+params,
        method:"get"
    });
};
// 撤回已办流程接口
export function withdrawProcess(params){
    return request({
        url:UrlPrefix+"/workflow/task/revokeProcess",
        method:"post",
        data:params
    });
};
// 提交撤回流程接口--亦是审批流程接口
export function submitWithdrawProcess(params){
    return request({
        url:UrlPrefix+"/workflow/task/complete",
        method:"post",
        data:params
    });
};
// 审批不同意接口--即驳回确认接口
export function approvalReject(params){
    return request({
        url:UrlPrefix+"/workflow/task/return",
        method:"post",
        data:params
    });
};
// 获取驳回id接口
export function getApprovalRejectId(params){
    return request({
      url:UrlPrefix+"/workflow/task/returnList",
      method:"post",
      data:params
    });
};
// 删除流程接口
export function deleteProcess(params){
    return request({
        url:UrlPrefix+"/workflow/instance/delete",
        method:"delete",
        params
    });
};
// 提交销假申请接口
export function submitCancelLeaveData(processId,params){
    return request({
        url:UrlPrefix+"/workflow/process/start/"+processId,
        method:"post",
        data:params
    });
};
// 补交附件接口
export function suppleFilesData(params){
    return request({
        url:UrlPrefix+"/workflow/process/repairAttachment",
        method:"post",
        data:params
    });
};
