import request from "@/utils/request.js";
import {isDev} from "@/setting.js";

const UrlPrefix=isDev ? "/Qilin" : "";

// 获取待办流程表格数据接口
export function getBacklogData(params){
    return request({
        url:UrlPrefix+"/workflow/process/todoList",
        method:"get",
        params
    });
};
// 批量审批接口
export function approvalMoreBacklogData(params){
    return request({
        url:UrlPrefix+"/workflow/task/batchComplete",
        method:"post",
        data:params
    });
};
