import request from "@/utils/processRequest.js";
import { isDev } from "@/setting.js";

const UrlPrefix = isDev ? "/portal" : "";

// 提交流程申请接口
// export function submitProcessData(processId, params) {
//   return request({
//     url: UrlPrefix + "/workflow/process/start/" + processId,
//     method: "post",
//     data: params
//   });
// };

// 获取流程信息接口
export function getProcessInfo(params) {
  return request({
    url: UrlPrefix + "/workflow/process/detail?procInsId=" + params,
    method: "get"
  });
};
export function getProcessInfoNew(params) {
  return request({
    url: UrlPrefix + "/workflow/dispatchTicket/detail?processInstanceId=" + params,
    method: "get"
  });
};
export function isReselect(params) {
  return request({
    url: UrlPrefix + "/workflow/process/rejectInfo?procInsId=" + params,
    method: "get"
  });
};

// 提交多模块售前申请
export function submitOrder(data) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/submitOrder",
    method: "post",
    data
  });
};
// 查询order详情下待办的任务
export function querytOrderById(orderId) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/getTodoTask/" + orderId,
    method: "get",
  });
};
// 查询流程详情
export function selectOrderById(orderId) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/selectOrderById/" + orderId,
    method: "get",
  });
};
// 查询流程待办详情
export function operateOrderById(orderId) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/operateOrderById/" + orderId,
    method: "get",
  });
};
// 查询工单备忘录
export function selectOrderComments(orderId) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/selectOrderComments/" + orderId,
    method: "get",
  });
};
// 新增工单备忘录
export function addOrderComment(data) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/addComment",
    method: "post",
    data
  });
};
// 修改工单备忘录
export function updateOrderComment(data) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/updateComment",
    method: "post",
    data
  });
};
// 删除工单备忘录
export function deleteOrderComment(orderId) {
  return request({
    url: UrlPrefix + "/workflow/dispatch/delComment/"+orderId,
    method: "delete",
  });
};