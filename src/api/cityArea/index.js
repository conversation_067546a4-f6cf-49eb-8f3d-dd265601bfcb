import {
  getRequest,
  postRequestBody,
  getRequestByValue,
  putRequest
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
  cityParamList: baseURL + 'view/city/solution/page',
  cityParamDetail: baseURL + 'view/city/solution/',
  // caseInfoList:baseURL + 'backend/caseInfo/page',
  caseInfoList:baseURL + 'view/caseInfo/page',
  caseInfoDetail: baseURL + 'backend/caseInfo/',
  getDownCount: baseURL + 'city/solution/downloadCount/',
  caseDownCount: baseURL + 'backend/caseInfo/downloadCount/',
  caseSearchParam: baseURL + 'backend/caseInfo/solution/',
  overview: baseURL + 'view/city/solution/data/overview',
}
// 地市方案获取浏览数
export const cityOverview = (data) => getRequest(url.overview, data)
// 地市方案查询
export const cityParamList = (data) => getRequest(url.cityParamList, data)
// 地市方案详情
export const cityParamDetail = (data) => getRequestByValue(url.cityParamDetail, data)
// 案例列表查询
export const caseInfoList = (data) => getRequest(url.caseInfoList, data)
// 案例详情
export const caseInfoDetail = (data) => getRequestByValue(url.caseInfoDetail, data)
// 案例查方案详情
export const caseSearchParam = (data) => getRequestByValue(url.caseSearchParam, data)
// 下载次数
export const getDownCount = (data) => putRequest(url.downCount, data)
// 地市案例下载次数
export const caseDownCount = (data) => putRequest(url.caseDownCount, data)
export const postList = (data) => postRequestBody(url.postList, data)