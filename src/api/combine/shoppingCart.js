import { getRequest, putRequestBody, postRequestBody3 } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    myShoppingCartList: `${baseURL}view/shopping/cart/query`,
    addShoppingCart: `${baseURL}view/shopping/cart/add`,
    clearShopping: `${baseURL}view/shopping/cart/delete`,
    toCombine:`${baseURL}view/combine/draft/go`,
    toGuide:`${baseURL}view/combine/draft/guide/submit`,
}

export const myShoppingCartList = (data) => getRequest(url.myShoppingCartList, data)

export const clearShopping = (data) => postRequestBody3(url.clearShopping, data)

export const addShoppingCart = (data) => postRequestBody3(url.addShoppingCart, data)

export const toCombinePage = (data) => postRequestBody3(url.toCombine, data)
//引导提交
export const toGuidePage = (data) => postRequestBody3(url.toGuide, data)
