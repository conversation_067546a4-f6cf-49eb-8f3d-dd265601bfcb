import { getRequest, postRequestBody, postRequestBody3, deleteRequest, getBlobRequest } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    userList: `${baseURL}backend/system/user/page`,
    create: `${baseURL}view/scheme/writing/create`,
    mergeFile: `${baseURL}view/solution/combine/mergeFile`,
    mergeFile2: `${baseURL}view/solution/combine/mergeFile2`,
    myCombineList: `${baseURL}view/combine/draft/query`,
    addCombine: `${baseURL}view/combine/draft/add`,
    combineSave: `${baseURL}backend/solution/combine/create`,
    deleteCombine: `${baseURL}view/combine/draft/`,
    showGuide:`${baseURL}view/solution/combine/guide`,
    updateTitle:`${baseURL}view/combine/draft/update/title`,
    
    aiCoreFunctionQuery:`${baseURL}view/ai/aiCoreFunctionQuery`,
    aiCoreFunctionDownload:`${baseURL}view/ai/aiCoreFunctionDownload`,
    replace:`${baseURL}view/combine/draft/replace`
}

// 获取用户列表
export const userList = (data) => getRequest(url.userList, data)
//新增
export const create = (data) => postRequestBody3(url.create, data)

export const mergeFile = (data) => postRequestBody3(url.mergeFile, data)
//获取ppt文本流
export const getMergeFile = (data) => postRequestBody3(url.mergeFile2, data)

export const myCombineList = (data) => getRequest(url.myCombineList, data)

export const addCombine = (data) => postRequestBody3(url.addCombine, data)

export const combineSave = (data) => postRequestBody3(url.combineSave, data)

export const deleteCombine = (data) => deleteRequest(url.deleteCombine, data)

export const updateTitle = (data) => postRequestBody3(url.updateTitle, data)

//判断是否引导
export const isShowGuide = (data) => getRequest(url.showGuide, data)

//ai推荐
export const showAICore = (data) => getRequest(url.aiCoreFunctionQuery, data)

export const uploadAICore = (data) => getBlobRequest(url.aiCoreFunctionDownload, data)

// 替换
export const replace = (data) => postRequestBody(url.replace, data)