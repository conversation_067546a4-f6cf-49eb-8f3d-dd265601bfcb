import { getRequest, putRequestBody } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    makeList: `${baseURL}view/layout/page`,
    getDetail: `${baseURL}view/layout/queryById`,
    overView: `${baseURL}view/layout/data/overview/`,
    countUpdate: `${baseURL}view/layout/data/overview/update`,

}

// 获取编排列表
export const getMakeList = (data) => getRequest(url.makeList, data)

// 获取编排详情
export const getDetail = (id) => getRequest(url.getDetail, id)

//详情增加浏览量
export const addView = (data) => getRequest(url.overView, data)


// 数量修改
export const countUpdate = (data) => putRequestBody(url.countUpdate, data)
