import { getRequest, getRequestByValue } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
const baseURL = getBaseUrl();

const url = {
    visionList: `${baseURL}view/policy/insights/page`,
    allVisionList: `${baseURL}view/policy/insights/list`,
    policyDetail: `${baseURL}view/policy/insights/`,
}

// 获取最新政策列表
export const visionList = (data) => getRequest(url.visionList, data)
// 查看通用全部列表
export const allVisionList = (data) => getRequest(url.allVisionList, data)
// 查看详情
export const policyDetail = (data) => getRequestByValue(url.policyDetail, data)