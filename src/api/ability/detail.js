import { getRequest, postRequestBody3,getRequestByValue } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
const baseURL = getBaseUrl();
// let baseURL = 'http://192.168.100.98:8098/portal/'
const url = {
    dataDetail: baseURL+'backend/Ability/',
    emsCode:baseURL+"/xhly/download/sendCode",
    subCode:baseURL+"/xhly/download/verifyCode",
    importProject:baseURL+"backend/abilityIntroduce/create",
    dowFile:baseURL+'/xhly/download/getDownloadFileInfo'
}

// 获取详情
export const getDetail = (id) => getRequestByValue(url.dataDetail, id)

// 验证码
export const getCode = (data) => postRequestBody3(url.emsCode,data)

// 校验验证码
export const subCode = (data) => postRequestBody3(url.subCode,data)

// 校验验证码
export const importProject = (data) => postRequestBody3(url.importProject,data)

// 下载附件
export const downFile = (id) => getRequest(url.dowFile, id)