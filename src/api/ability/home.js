import { getRequest, postRequestBody3,postBlobRequest,putRequestBody, deleteRequestBody,postRequestBody,postRequestBody2,getRequestByValue} from '@/utils/request'

import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    dataCount:baseURL+'view/Ability/calculate',
    projectList: baseURL+'view/Ability/page',
    treeCount: baseURL+'view/Ability/count',
    countUpdate:baseURL+'view/AbilityNumber/update',
    company:baseURL+'backend/abilityIntroduce/company'
}

// 获取方案总览
export const getCount = (data) => getRequest(url.dataCount, data)

// 获取方案列表
export const getProjectList = (data) => getRequest(url.projectList, data)

// 获能力分类总数
export const treeCount = (data) => getRequest(url.treeCount, data)


// 数量修改
export const countUpdate = (data) => putRequestBody(url.countUpdate, data)

// 首页热门数据获取
export const getHotAbility = (data) => getRequest(url.projectList, data)

// 公司列表
export const getCompanyList = (data) => getRequest(url.company, data)