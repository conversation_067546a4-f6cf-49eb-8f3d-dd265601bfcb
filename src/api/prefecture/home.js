import { getRequest, getRequestByValue } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    list: `${baseURL}view/zone/page`,
    detailList: `${baseURL}view/zone/`,
    policyList: `${baseURL}view/policy/page`,
    policyDetail: `${baseURL}view/policy/`,
    zoneView: `${baseURL}view/zone/overview`,
}

// 获取分页
export const getList = (data) => getRequest(url.list, data)
// 获取详情
export const getDetailList = (data) => getRequestByValue(url.detailList, data)
// 政策
export const policyList = (data) => getRequest(url.policyList, data)
// 获取政策详情
export const policyDetail = (data) => getRequestByValue(url.policyDetail, data)
// 获取概览数据
export const zoneView = (data) => getRequest(url.zoneView, data)


