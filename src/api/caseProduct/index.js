import {
    getRequest,
    getRequestByValue,
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    caseProductList: baseURL + 'view/productCaseInfo/page',
    caseProductDetail: baseURL + 'view/productCaseInfo/',
}
// 产品案例列表查询
export const caseProductList = (data) => getRequest(url.caseProductList, data)
// 产品案例详情
export const caseProductDetail = (data) => getRequestByValue(url.caseProductDetail, data)