import { getRequest, postRequestBody3, postRequestBody } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    identifyCode: `${baseURL}captcha`,
    login: `${baseURL}login`,
    logOut: `${baseURL}logout`,
    userInfo: `${baseURL}userInfo`,
    code: `${baseURL}sendCode`,
    resetPass: `${baseURL}resetPassword`,
    operate: `${baseURL}view/system/user/operate`,
    freeLogin: `${baseURL}freeLogin`,
    // ecologicalDetails: `http://36.137.192.176:8087/companyBasicInfoApi/details/list`,
    ecologicalDetails: `https://smart.jsisi.cn:8099/companyBasicInfoApi/details/list`,
    
    //用户获取待办工单
    combinedTasklist: `${baseURL}workflow/process/CombinedTasklist`,
}
export const getIdentifyCode = (data) => getRequest(url.identifyCode, data)

// 退出登录
export const getLogOut = (data) => postRequestBody3(url.logOut, data)
// 获取用户信息
export const getUserInfo = (data) => getRequest(url.userInfo, data)

export const login = (data,headers) => postRequestBody3(url.login, data,headers)

//获取手机验证码
export const getCode = (data) => postRequestBody(url.code, data)

//重置密码
export const resetPass = (data) => postRequestBody(url.resetPass, data)

//每进行一次路由跳转
export const operate = (data) => getRequest(url.operate, data)

//免登录
export const freeLogin = (data) => postRequestBody(url.freeLogin, data)

export const getEcologicalDetails = (data) => postRequestBody(url.ecologicalDetails, data)

//用户获取待办工单
export const getCombinedTasklist = (data) => getRequest(url.combinedTasklist, data)