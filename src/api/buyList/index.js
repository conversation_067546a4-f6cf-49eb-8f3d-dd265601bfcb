import {
    getRequest,
    postRequestBody3,
    postRequestBody,
    deleteRequest,
    getRequestByValue
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    addShop: baseURL + 'view/product/shopping/cart/add',
    shopList: baseURL + 'view/product/shopping/cart/query',
    deleteShop: baseURL + 'view/product/shopping/cart/delete',
    toShopList: baseURL + 'view/product/package/add',
    // shopTable: baseURL + 'view/product/package/query',
    shopTable: baseURL + 'view/product/package/query/',
    deleteManyShop: baseURL + 'view/product/package/',
    holdShop: baseURL + 'view/product/package/save',
    addShopPro: baseURL + 'view/product/package/add/product',
    viewShop: baseURL + 'view/product/package/queryByPackageId',
    postList: baseURL + 'view/product/package/add/product',
    go: baseURL + 'view/product/package/go',
    queryProduct: baseURL + 'view/product/package/query/product',
    
}

export const queryProduct = (data) => getRequest(url.queryProduct, data)
export const go = (data) => postRequestBody3(url.go, data)
export const addShop = (data) => postRequestBody(url.addShop, data)
export const addShopPro = (data) => postRequestBody(url.addShopPro, data)
export const shopList = (data) => getRequest(url.shopList, data)
export const deleteShop = (data) => postRequestBody3(url.deleteShop, data)
export const toShopList = (data) => postRequestBody3(url.toShopList, data)
// getRequest 
export const shopTable = (data) => getRequestByValue(url.shopTable, data)
export const viewShop = (data) => getRequest(url.viewShop, data)
export const deleteManyShop = (data) => deleteRequest(url.deleteManyShop, data)
export const holdShop = (data) => postRequestBody(url.holdShop, data)
export const postList = (data) => postRequestBody(url.postList, data)