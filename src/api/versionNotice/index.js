import { getRequest, postRequestBody } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
const baseURL = getBaseUrl();

const url = {
    visionList: `${baseURL}backend/notice/latest`,
    readNotice: `${baseURL}backend/notice/read/hasUnreadNotifications`,
    havedNotice: `${baseURL}backend/notice/read/markAsRead`,
}

// 获取最新版本公告
export const getVisionList = (data) => getRequest(url.visionList, data)
// 读取公告
export const readNotice = (data) => getRequest(url.readNotice, data)
// 标记为已读
export const havedNotice = (data) => postRequestBody(url.havedNotice, data)



