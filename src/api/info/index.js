import { getRequest, putRequestBody } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    resetPwd: `${baseURL}backend/system/user/updatePwd`,
    projectList: baseURL+'view/index/myProject',
    buyList:`${baseURL}/backend/abilityIntroduce/page`,
    abilityList:`${baseURL}view/index/myAbility`
}

// 修改密码
export const resetPwds = (data) => putRequestBody(url.resetPwd, data)
// 获取方案列表
export const getProjectList = (data) => getRequest(url.projectList, data)
// 获取订购列表
export const getBuyList = (data) => getRequest(url.buyList, data)
// 获取能力列表
export const getAbilityList = (data) => getRequest(url.abilityList, data)