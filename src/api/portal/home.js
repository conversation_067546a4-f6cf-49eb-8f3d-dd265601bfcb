import {
    getRequest,postRequestBody,postRequestBody3
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
// const baseURL1 = 'http://**************:8098/portal/';
const url = {
    swiperInfo: `${baseURL}view/index/calculate`,

    mySolution:`${baseURL}view/index/mySolution`,
    noticeList:`${baseURL}view/notice/page`,
    workList:`${baseURL}backend/workOrder/page`,
    menuList: `${baseURL}view/system/navi/list`,
    roleMenuList: `${baseURL}view/system/navi/getRoleNavis`,
    proList: `${baseURL}view/project/queryMyProject`,
}

// 获取轮播数据
export const getSwiperInfo = (data) => getRequest(url.swiperInfo, data)

// 获取我的方案
export const getMySolution = (data) => getRequest(url.mySolution, data)
// 获取工单列表
export const getWorkList = (data) => getRequest(url.workList, data)
// 获取导航菜单列表
export const getGuideList = (data) => getRequest(url.menuList, data)
export const getGuideRoleList = (data) => getRequest(url.roleMenuList, data)
// 获取项目列表
export const getProList = (data) => getRequest(url.proList, data)
