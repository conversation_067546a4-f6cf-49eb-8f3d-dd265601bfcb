/*
 * @Author: x<PERSON><PERSON> <EMAIL>
 * @Date: 2025-04-30 14:06:48
 * @LastEditors: xiuji <EMAIL>
 * @LastEditTime: 2025-04-30 20:03:13
 * @FilePath: /wisdom_manage/src/api/ticket/ticket.js
 * @Description: 这是默认设置,请设置`customMade`, 打开koroFileHeader查看配置 进行设置: https://github.com/OBKoro1/koro1FileHeader/wiki/%E9%85%8D%E7%BD%AE
 */
import {
  getRequest,
  getRequestByValue,
  postRequestBody
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
  ticketList: `${baseURL}backend/workOrder/page`,
  totalList: `${baseURL}workflow/workStation/mid/totalList`,
  detail: `${baseURL}backend/workOrder/`,
  audit: `${baseURL}backend/workOrder/audit`,
  exportDown: `${baseURL}backend/workOrder/export`,
  solutionDetail: `${baseURL}view/solution/`,
  sceneDetail: `${baseURL}view/sceneInfo/`,
  abilityDetail: `${baseURL}view/schemeModule/`,
  queryMultiDetail: `${baseURL}view/oneClickScheduling/query`,
  person: `${baseURL}backend/system/user/list?roleKey=`,
  beginSend: `${baseURL}workflow/process/list`,
  ecoToken: `${baseURL}generateToken`,
  ticketDetail: `${baseURL}backend/workOrder/`,

  deliverUser: `${baseURL}workflow/dispatchTicket/deliveryUser?name=`,
  createCoordinationSupport: `${baseURL}backend/mid/sales/support/create`,
  getCoordinationSupport: `${baseURL}backend/mid/sales/support/`,
  getCoordinationSupportDetail: `${baseURL}backend/workOrder/`,
  createScore: `${baseURL}backend/mid/sales/support/score`,
  productDetail: `${baseURL}backend/product/`,
  exportTotal: `${baseURL}workflow/workStation/mid/totalList/export`,

}


// 获取动态列表
export const getList = (data) => getRequest(url.ticketList, data)
export const getTotalList = (data) => getRequest(url.totalList, data)

//详情
export const detail = (data) => getRequestByValue(url.detail, data)
export const ticketDetail = (data) => getRequestByValue(url.ticketDetail, data)

// 导出列表
export const getWorkOrderDown = (data) => getRequest(url.exportDown, data)
// 获取方案详情
export const solutionDetail = (data) => getRequestByValue(url.solutionDetail, data)
// 获取场景详情
export const sceneDetail = (data) => getRequestByValue(url.sceneDetail, data)
// 获取能力详情
export const abilityDetail = (data) => getRequestByValue(url.abilityDetail, data)
// 获取一键调度的所有模块详情
export const queryMultiDetail = (data) => postRequestBody(url.queryMultiDetail, data)

// 获取人员列表
export const getPerson = (data) => getRequestByValue(url.person, data)
// 获取交付人员列表
export const getDeliverUser = (data) => getRequestByValue(url.deliverUser, data)
// 发起流程
export const beginSend = (data) => getRequest(url.beginSend, data)


// 获取生态工单的token
export const getEcoToken = (data) => getRequest(url.ecoToken, data)

// 创建售中调度
export const createCoordinationSupport = (data) => postRequestBody(url.createCoordinationSupport, data)
// 获取售中调度详情
export const getCoordinationSupport = (data) => getRequestByValue(url.getCoordinationSupport, data)
// 获取售中调度详情
export const getCoordinationSupportDetail = (data) => getRequestByValue(url.getCoordinationSupportDetail, data)
// 创建评分
export const createScore = (data) => postRequestBody(url.createScore, data)

//详情
export const productDetail = (data) => getRequestByValue(url.productDetail, data)

//  导出全部列表
export const exportTotal = (data) => getRequest(url.exportTotal, data)