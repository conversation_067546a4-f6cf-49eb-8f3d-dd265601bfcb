import { getRequest,postRequestBody3 } from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    outside: `${baseURL}view/operate/statistics1`,
    inside: `${baseURL}view/operate/statistics2`,
    rightData: `${baseURL}view/operate/intoList`
}


// 省外柱状图数据
export const getOutside = (data) => getRequest(url.outside, data)

// 省内柱状图数据
export const getInside = (data) => getRequest(url.inside, data)

// 右侧数据
export const getRight = (data) => getRequest(url.rightData, data)
