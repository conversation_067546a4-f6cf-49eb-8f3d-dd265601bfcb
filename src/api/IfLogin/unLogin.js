import {
    getRequest,
    getRequestByValue
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";
// let baseURL = "http://192.168.10.56:8098"
const baseURL = getBaseUrl();
const url = {
    noticeList: `${baseURL}view/dynamic/page`,
    detail: `${baseURL}view/dynamic/`
}

// 获取详情
export const getList = (data) => getRequest(url.noticeList, data)
//详情
export const detail = (data) => getRequestByValue(url.detail, data)