import { getRequest, postRequestBody3, deleteRequest, postBlobRequest, putRequestBody, getRequestByValue } from '@/utils/request'
import { getUrl } from "@/utils/getUrl";
const baseURL = getUrl();

const url = {
    uploadFile: `${baseURL}/backend/file/upload`,
    pptTopdf: `${baseURL}/backend/file/toPdf`,
}

// 上传
export const uploadFileList = (data) => postBlobRequest(url.uploadFile, data)

// ppt转pdf
export const pptTopdf = (data) => getRequest(url.pptTopdf, data)
