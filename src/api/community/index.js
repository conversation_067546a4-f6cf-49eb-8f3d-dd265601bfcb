import {
    getRequest,
    postRequestBody,
    getRequestByValue,
    putRequest,
    deleteRequest
} from '@/utils/request'
import getBaseUrl from "@/utils/baseUrl";

const baseURL = getBaseUrl();
const url = {
    setMsg: `${baseURL}view/forum/post/create`,
    msgList: `${baseURL}view/forum/post/page`,
    allData: `${baseURL}view/forum/post/overview`,
    abouteMe: `${baseURL}view/forum/post/my/data`,
    detail: `${baseURL}view/forum/post/`,
    allocation: `${baseURL}view/forum/post/distribute`,
    toTop: `${baseURL}backend/forum/post/setTop/`,
    deleteData: `${baseURL}backend/forum/post/`,
    refusetop: `${baseURL}backend/forum/post/cancelTop/`,
    confirm: `${baseURL}view/forum/post/confirm`,
    getUser: `${baseURL}backend/forum/handleConfig/userList`,
    orgList: `${baseURL}backend/forum/handleConfig/list`,
    feedback: `${baseURL}view/forum/post/feedback`,
    comment: `${baseURL}view/forum/post/comment`,
    resubmit: `${baseURL}view/forum/post/resubmit`,
    deleteWord: `${baseURL}view/forum/post/comment/`,
}

// 社区发帖
export const setMessage = (data) => postRequestBody(url.setMsg, data)
// 驳回后重新发帖
export const rsetMessage = (data) => postRequestBody(url.resubmit, data)
// 审核管理员驳回
export const setAllocation = (data) => postRequestBody(url.allocation, data)
// 评论
export const setComment = (data) => postRequestBody(url.comment, data)
// 主办/协办反馈
export const setFeedback = (data) => postRequestBody(url.feedback, data)
// 提交人确认
export const setConfirm = (data) => postRequestBody(url.confirm, data)
// 获取社区列表
export const getMsgList = (data) => getRequest(url.msgList, data)
// 获取数据展示
export const getAllData = (data) => getRequest(url.allData, data)
// 获取关于我的
export const getAbouteMe = (data) => getRequest(url.abouteMe, data)
// 帖子详情
export const getDetail = (data) => getRequestByValue(url.detail, data)
// 取消置顶
export const refuseTop = (data) => putRequest(url.refusetop, data)
// 置顶
export const setTop = (data) => putRequest(url.toTop, data)
// 删除
export const setDelete = (data) => deleteRequest(url.deleteData, data)
// 删除留言
export const setDeleteWord = (data) => deleteRequest(url.deleteWord, data)
// 获取用户列表
export const getUserList = (data) => getRequest(url.getUser, data)
// 获取组织列表
export const getOrgList = (data) => getRequest(url.orgList, data)