import { defineStore } from 'pinia'

export const currentTab = defineStore('common',
    {
        state: () => {
            return {
                curTab: "",
                linkUrl: "",
                type: "",
                layoutActive: ""
            }
        },
        persist: {
            key: 'linkStore',
            storage: sessionStorage, // 存储方式
            paths: ['linkUrl', 'type', 'layoutActive']
        }
    })