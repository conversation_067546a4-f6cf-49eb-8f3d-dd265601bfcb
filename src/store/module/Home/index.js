/*
 * @Description: 
 * @Author: xiuji
 * @Date: 2023-08-09 10:27:00
 * @LastEditTime: 2024-03-27 10:23:26
 * @LastEditors: Do not edit
 */
import {
    defineStore
} from 'pinia'
import { select } from 'ranui';

export const useHomeStore = defineStore('Home', {
    state: () => {
        return {
            Authorization: "",
            userInfo: {
                name: "",
                avatar: "",
                role: [],
                phone: "",
            },
            contralShop:true,
            allRoles: {},
            loginData: {},
            startTimeLogin: null,
            smsTime: 60,
            smsBtnDisabled: true,
            resetTimeStart: null,
            resetTime: 60,
            resetBtnDisabled: true,
            deviceType: 'android',
            username: '0',
            loginName:'',
            buyListStroe:{},
            aiBuyListStroe:{},
            coverName:'',
            showTop:false,
            productList:[],
            parentId:'',
            selectNum:undefined
        }
    },
    // computed
    getters: {

    },
    // methods
    actions: {

        storeLoginInfo(params) {
            this.Authorization = `Bearer ${params.data.access_token}`;
        },
        storeUserInfo(params, roles) {
            this.userInfo = {
                name: params.user.realName,
                avatar: params.user.realName.substring(params.user.realName.length - 2),
                role: params.roles.toString(),
                phone: params.user.phonenumber,
            };
            this.allRoles = roles;
            this.checkDeviceType();
        },
        resetLoginInfo() {
            this.Authorization = "";
            this.userInfo = {};
            this.startTimeLogin = null;
            this.smsTime = 60;
            this.smsBtnDisabled = true;
            this.resetTimeStart = null;
            this.resetTime = 60;
            this.resetBtnDisabled = true;
        },
        rememberMe(params) {
            const {
                username,
                password
            } = params;
            this.loginData = {
                username: username,
                password: password,
            }
        },
        setTimer(params, username) {
            this.smsBtnDisabled = true;
            let startTime = this.startTimeLogin;
            let nowTime = new Date().getTime();
            if (startTime) {
                let surplus = 60 - parseInt((nowTime - startTime) / 1000, 10);
                this.smsTime = surplus <= 0 ? 0 : surplus;
            } else {
                this.smsTime = 60;
                this.startTimeLogin = nowTime;
            }

            params.value = setInterval(() => {
                this.smsTime -= 1;
                if (this.smsTime <= 0) {
                    this.startTimeLogin = null;
                    clearInterval(params.value);
                    this.smsTime = 60;
                    if (username.trim().length === 0) {
                        this.smsBtnDisabled = true;
                    } else {
                        this.smsBtnDisabled = false;
                    }
                }
            }, 1000);
        },
        setResetTimer(params, username) {
            this.resetBtnDisabled = true;
            let startTime = this.resetTimeStart;
            let nowTime = new Date().getTime();
            if (startTime) {
                let surplus = 60 - parseInt((nowTime - startTime) / 1000, 10);
                this.resetTime = surplus <= 0 ? 0 : surplus;
            } else {
                this.resetTime = 60;
                this.resetTimeStart = nowTime;
            }

            params.value = setInterval(() => {
                this.resetTime -= 1;
                if (this.resetTime <= 0) {
                    this.resetTimeStart = null;
                    clearInterval(params.value);
                    this.resetTime = 60;
                    if (username.trim().length === 0) {
                        this.resetBtnDisabled = true;
                    } else {
                        this.resetBtnDisabled = false;
                    }
                }
            }, 1000);
        },
        checkDeviceType() {
            const isAndroid = navigator.userAgent.toLowerCase().indexOf("android") > -1; //&& ua.indexOf("mobile");
            const isIos = /iPad|iPhone|iPod/.test(navigator.userAgent) && !window.MSStream;

            if (isAndroid) {
                this.deviceType = 'android';
            } else if (isIos) {
                this.deviceType = 'ios';
            }
        }
    },
    persist: true
})