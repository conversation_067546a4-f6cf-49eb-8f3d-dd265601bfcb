import { defineStore } from "pinia";

export const home = defineStore({
    id: "home",
    state: () => {
        return {
          homeData:[]
        }
    },
    getters: {
        // 获取流程数据
        getHomeData(){
            return this.homeData;
        },
    },
    actions: {
        // 设置流程数据
        setHomeData(data){
            this.homeData=data;
        },
        // 清除数据
        deleteHomeData(data){
            this.homeData=[];
        },
    },
    persist: {
        enabled: true,
        strategies: [
            {
                key: "departList",
                storage: localStorage
            }
        ]
    }
});
