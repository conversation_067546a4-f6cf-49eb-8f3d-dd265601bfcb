<template>
  <a-form :rules="rules" :model="videoInfo" ref="videoForm">
    <a-row>
      <a-col :span="24">
        <a-form-item :label="videoInfo.label" name="videoUrl">
          <a-upload
          	:disabled="fileLength == videoInfo.acceptLength"
            v-model:file-list="mainVideoList"
            :accept="videoInfo.accept || 'video/mp4,video/flv,video/m4v,video/mov'"
            :customRequest="uploadMainVideo"
            name="avatar"
            :multiple="false"
            class="avatar-uploader"
            :before-upload="beforeUpload"
            :show-upload-list="false"
          >
          	
          	<a-button :loading="uploading" :class="['fileBtn',{'disabled-btn':  fileLength == videoInfo.acceptLength}]">
          		上传
            </a-button>
            <span class="font_A2ABB5 font_12"> {{ videoInfo.videoHint || '支持mp4/m4v/mov格式的视频上传，文件限制为300M'}}</span>
          </a-upload>
          <div class="file-list">
          	<p v-for="(file, index) in videoInfo.fileList" :key="index">
              <span @click.stop="showVideo(file)">
                <i class="iconfont icon-annex"></i>
                <a> &nbsp;{{ file.name }}</a>
              </span>

              <svg
                @click="deleteVideo(file)"
                class="close iconSize"
                aria-hidden="true"
              >
                <use xlink:href="#icon-close1"></use>
              </svg>
            </p>
          </div>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
  <a-modal
    :visible="previewVisible"
    centered
    :footer="null"
    @cancel="handleCancel"
  >
    <video controls style="width: 100%;height: 100%;margin-top: 20px;" :src="videoUrl" />
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { message } from "ant-design-vue";

import { PlusOutlined, LoadingOutlined } from "@ant-design/icons-vue";
import { uploadFileList } from "@/api/notice/notice.js";
import eventBus from "@/utils/eventBus";
import { filter, cloneDeep } from "lodash-es";

export default defineComponent({
  name: "addForm",
  components: { PlusOutlined, LoadingOutlined },
  emits: ["update-url", "validate-result"],
  props: {
    videoInfo: {
      type: Object,
      default: {},
    },
  },
  setup(props, { emit }) {
    const videoForm = ref();

    const validateMainVideo = async (rule, value) => {
      if (data.videoInfo.fileList && data.videoInfo.fileList.length != 0) {
        return Promise.resolve();
      }
      return Promise.reject("请上传视频");
    };

    const data = reactive({
    	fileLength:0,
      mainVideoList: [],
      uploading: false,
      flag: false,
      videoInfo: props.videoInfo,
      previewVisible: false,
      videoUrl: "",
      rules:
        props.videoInfo.required == false
          ? { videoUrl: [] }
          : {
              videoUrl: {
                required: true,
                validator: validateMainVideo,
                trigger: "change",
              },
            },
    });
    

    // 删除
    const deleteVideo = (file) => {
      let fileList = cloneDeep(data.videoInfo.fileList);
      data.videoInfo.fileList = filter(
        fileList,
        (item) => item.path !== file.path
      );
      data.fileLength = data.videoInfo.fileList.length;
      console.log(data.videoInfo);
      emit("update-url", data.videoInfo);
    };

    const uploadMainVideo = (info) => {
      uploadFile(info, "video");
    };
    const uploadFile = (info) => {
      data.uploading = true;
      let formData = new FormData();
      formData.append("file", info.file);
      uploadFileList(formData).then(result => {
      	if (result.code == 200) {
		      data.videoInfo.fileList = [];
		      data.videoInfo.fileList.push({
	          name: result.data.fileName,
	          url: result.data.fileUrl,
	          path: result.data.filePath,
	          category: data.videoInfo.category || "",
	          type: data.videoInfo.category || "",
	        });
	        emit("update-url", data.videoInfo);
	        message.success("上传成功");
	        data.uploading = false;
	        info.onSuccess();
	        data.uploading = false;
	        return false
	      } else {
	      	data.uploading = false;
	      	message.success("上传失败");
	      }
      }).catch(err => {
      	data.uploading = false;
      });
    };

    const beforeUpload = (file, fileList) => {
      if (data.uploading) {
        message.warning("正在上传中，请稍后再试");
        data.uploading = false;
        return Promise.reject();
      }
      return new Promise((resolve, reject) => {
        if (file.size > 10485760*30) {
          message.warning("单个文件总大小不能超过10M");
          data.uploading = false;
          reject();
        }
        resolve();
        return;
      });
    };

    const validateVideo = async () => {
      return await videoForm.value.validate();
    };

    watch(
      () => props.videoInfo,
      (newVal) => {
        data.videoInfo = newVal;
        data.fileLength =
          newVal.fileList && newVal.fileList.length
            ? newVal.fileList.length
            : 0;
      },
      {
        immediate: true,
        deep: true,
      }
    );
    watch(
      () => data.videoInfo,
      (newVal) => {
        data.videoInfo = newVal;
        data.fileLength =
          newVal.fileList && newVal.fileList.length
            ? newVal.fileList.length
            : 0;
      },
      {
        immediate: true,
        deep: true,
      }
    );
    const showVideo = (file) => {
    	const href = file.url;
    	let windowOrigin = window.location.origin;
    	let token = localStorage.getItem("token");
    	let newHref = href;
    	if(href.includes(windowOrigin)){
    		newHref = "/portal" + href.split(windowOrigin)[1]
    	}
      data.videoUrl = windowOrigin + newHref + "?token=" + token;
      data.previewVisible = true;
    };
    const handleCancel = () => {
    	data.videoUrl = "";
      data.previewVisible = false;
    };
    return {
      ...toRefs(data),
      videoForm,
      handleCancel,
      showVideo,
      validateVideo,
      deleteVideo,
      beforeUpload,
      uploadMainVideo,
      validateMainVideo
    };
  },
});
</script>

<style lang="scss" scoped>
:deep(.ant-form-item-label) {
  width: 100px;

  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}

:deep(.ant-upload) {
  border-radius: 4px;
  border: none !important;
}

.fileBtn {
  font-weight: 500;
  margin-right: 12px;
  font-size: 14px;
  color: #ffffff;
  background: #0c70eb;
  border-radius: 4px;
}

.disabled-btn {
  pointer-events: none;
  border: none;
  color: #00000040;
  background: #f5f5f5;
}

.file-list {
  width: 40%;
  margin-top: 16px;
  cursor: pointer;

  p {
    position: relative;
    padding: 2px 24px 2px 24px;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 8px;
    background: rgba(12, 112, 235, 0.08);
    border-radius: 4px;
  }

  .iconSize {
    width: 15px;
    height: 15px;
  }

  .close {
    position: absolute;
    right: 8px;
    top: 7px;
  }

  .icon-annex {
    width: 13px;
    height: 13px;
    color: #0c70eb;
    position: absolute;
    top: 2px;
    left: 8px;
  }
}
</style>