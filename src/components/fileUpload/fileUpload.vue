<template>
  <a-form :rules="rules" :model="fileInfo" ref="fileForm">
    <a-row>
      <a-col :span="24">
        <a-form-item :label="fileInfo.label" name="fileList">
          <a-upload :disabled="fileLength == fileInfo.acceptLength" :show-upload-list="false"
            v-model:file-list="fileList" :customRequest="uploadFile" :multiple="fileInfo.multiple"
            :before-upload="beforeUpload" :accept="fileInfo.accept">
            <div style="display: flex;justify-content: space-between;align-items: center;">
              <a-button :loading="uploading" :class="[
                'fileBtn', customClassName,
                {
                  'disabled-btn': fileLength == fileInfo.acceptLength,
                },
              ]">
                {{ btnName }}
              </a-button>
              <div class="downloadTemplate" v-if="fileInfo.downloadTemplate" @click.stop="downloadTtemplate">下载模版</div>
              <span class="font_A2ABB5 font_12" style="margin-left: 12px;"> {{ fileInfo.mark }}</span>
            </div>
          </a-upload>
          <a class="font_0C70EB font_12" v-if="fileInfo.label == '能力介绍'" @click="downloadScheme">
            下载模板</a>
          <div class="file-list">
            <p v-for="(file, index) in fileInfo.fileList" :key="index">
              <span @click="view(file.path, file.name, file.url)">
                <i class="iconfont icon-annex"></i>
                <a-tooltip overlayClassName="tooltip_class">
                  <template #title>{{ file.name }}</template>
                  <a> &nbsp;{{ file.name }}</a>
                </a-tooltip>
              </span>

              <svg @click="deleteFile(file)" class="close iconSize" aria-hidden="true">
                <use xlink:href="#icon-close1"></use>
              </svg>
            </p>
          </div>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, watch, computed } from "vue";
import { message } from "ant-design-vue";
import { useRouter } from "vue-router";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { PlusOutlined, LoadingOutlined } from "@ant-design/icons-vue";
import { uploadFileList } from "@/api/notice/notice.js";
import { filter, cloneDeep } from "lodash-es";
export default defineComponent({
  name: "addForm",
  components: { PlusOutlined, LoadingOutlined },
  emits: ["update-file", "validate-result", 'update-load', 'downloadTemplate'],
  props: {
    fileInfo: {
      type: Object,
      default: {},
    },
    btnName: {
      type: String,
      default: "上传"
    },
    customClassName: {
      type: String,
      default: ""
    }
  },
  setup(props, { emit }) {
    const fileForm = ref();
    const validateFileRequired = async (rule, value) => {
      if (data.fileInfo.fileList.length != 0) {
        return Promise.resolve();
      }
      return Promise.reject("请上传附件");
    };

    const data = reactive({
      fileList: [],
      viewLoading: false,
      uploading: false,
      fileInfo: props.fileInfo,
      fileLength: 0,
      rules:
        props.fileInfo.required == false
          ? { fileList: [] }
          : {
            fileList: {
              required: true,
              validator: validateFileRequired,
              trigger: "change",
            },
          },
    });

    const beforeUpload = (file, fileList) => {
      return new Promise((resolve, reject) => {
        const fileType = '.' + file.name.split(".").pop();
        if (!data.fileInfo.accept.split(',').includes(fileType)) {
          message.warning(`请上传正确格式的文件`);
          reject();
        }
        if (file.size > data.fileInfo.size * 1024 * 1024) {
          message.warning(`单个文件总大小不能超${data.fileInfo.size}M`);
          reject();
        }
        resolve();
        return;
      });
    };

    const uploadFile = async (info) => {
      data.uploading = true;
      let formData = new FormData();
      formData.append("file", info.file);
      formData.append("name", info.file.name);
      const result = await uploadFileList(formData);
      if (result.code == 200) {
        data.uploading = false;
        data.fileInfo.fileList.push({
          name: result.data.fileName,
          url: result.data.fileUrl,
          path: result.data.filePath,
          category: data.fileInfo.category || "",
        });

        emit("update-file", data.fileInfo);
        message.success("上传成功");
        info.onSuccess();
        return result;
      }
      data.uploading = false;
      message.success("上传失败");
    };

    // 删除

    const deleteFile = (file) => {
      let fileList = cloneDeep(data.fileInfo.fileList);
      data.fileInfo.fileList = filter(
        fileList,
        (item) => item.path !== file.path
      );
      data.fileLength = data.fileInfo.fileList.length;
      emit("update-file", data.fileInfo);
    };

    const validateFile = async () => {
      return await fileForm.value.validate();
    };

    const downloadTtemplate = () => {
      emit('downloadTemplate')
    }

    watch(
      () => props.fileInfo,
      (newVal) => {
        data.fileInfo = newVal;
        data.fileLength =
          newVal.fileList && newVal.fileList.length
            ? newVal.fileList.length
            : 0;
      },
      {
        immediate: true,
        deep: true,
      }
    );
    watch(
      () => data.fileInfo,
      (newVal) => {
        data.fileInfo = newVal;
        data.fileLength =
          newVal.fileList && newVal.fileList.length
            ? newVal.fileList.length
            : 0;
      },
      {
        immediate: true,
        deep: true,
      }
    );
    const Router = useRouter();
    const view = (path, name, url) => {
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let iamgesList = ["png", "jpg", "jpeg", "bmp", "gif", "webp", "svg"];
      if (iamgesList.includes(name.split(".")[1])) {
        let a = document.createElement("a");
        a.setAttribute("href", url);
        a.setAttribute("target", "_blank");
        a.setAttribute("download", name);
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        return;
      }
      if (name.split(".")[1] == "pdf") {
        let newHref = url;
        if (url.includes(windowOrigin)) {
          newHref = "/portal" + url.split(windowOrigin)[1]
        }
        let previewUrl = Router.resolve({
          name: "lookPdf",
          query: {
            urlMsg: encodeURIComponent(windowOrigin + newHref + "?token=" + token),
            urlName: name,
          },
        });
        window.open(previewUrl.href, "_blank");
        return;
      }
      data.viewLoading = true;
      emit("update-load", data.viewLoading);
      pptTopdf({
        filePath: path,
      }).then((res) => {
        if (res.code == 200) {
          data.viewLoading = false;
          emit("update-load", data.viewLoading);
          let newHref = res.data;
          if (res.data.includes(windowOrigin)) {
            newHref = "/portal" + res.data.split(windowOrigin)[1]
          }
          const newpage = Router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(windowOrigin + newHref + "?token=" + token),
              urlName: name,
            },
          });
          window.open(newpage.href, "_blank");
        }
      });
      return false;
    };

    const downloadScheme = () => {
      let windowOrigin = window.location.origin;
      let url =
        windowOrigin + "/portal/template/%E8%83%BD%E5%8A%9B%E6%A8%A1%E6%9D%BF.pptx";
      const downloadElement = document.createElement("a");
      downloadElement.href = url + "?token=" + localStorage.getItem("token");
      downloadElement.download = "能力介绍.pptx";
      document.body.appendChild(downloadElement);
      downloadElement.click(); //点击下载
      document.body.removeChild(downloadElement); //下载完成移除元素
    }

    return {
      ...toRefs(data),
      fileForm,
      downloadTtemplate,
      uploadFile,
      deleteFile,
      beforeUpload,
      validateFile,
      view,
      downloadScheme,
      Router
    };
  },
});
</script>

<style lang="scss" scoped>
:deep(.ant-form-item-label) {
  width: 100px;

  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}

:deep(.ant-upload) {
  border-radius: 4px;
  border: none !important;
}

.downloadTemplate {
  width: 80px;
  min-width: 80px;
  max-width: 80px;
  height: 32px;
  margin-left: 0;
  display: flex;
  justify-content: center;
  align-items: center;
  border: 1px solid #5d5c5c;
  border-radius: 4px;
  cursor: pointer;
  user-select: none;
}

.fileBtn {
  font-weight: 500;
  margin-right: 12px;
  font-size: 14px;
  color: #ffffff;
  background: #0c70eb;
  border-radius: 4px;
}

.disabled-btn {
  pointer-events: none;
  border: none;
  color: #00000040;
  background: #f5f5f5;
}

.file-list {
  width: 40%;
  margin-top: 16px;
  cursor: pointer;

  p {
    position: relative;
    padding: 2px 24px 2px 24px;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 8px;
    background: rgba(12, 112, 235, 0.08);
    border-radius: 4px;
  }

  .iconSize {
    width: 15px;
    height: 15px;
  }

  .close {
    position: absolute;
    right: 8px;
    top: 7px;
  }

  .icon-annex {
    width: 13px;
    height: 13px;
    color: #0c70eb;
    position: absolute;
    top: 2px;
    left: 8px;
  }
}

.custom_btn {
  color: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  border: none;
}

.active_btn {
  background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
}

.custom_btn:hover,
.custom_btn:focus {
  opacity: 0.6;
}
</style>