<template>
  <div class="flex just-center">
    <img
      src="@/assets/images/make/noData.png"
      alt=""
      width="240"
      height="244"
    />
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";

export default defineComponent({
  setup() {
    const data = reactive({});

    return {
      ...toRefs(data),
    };
  },
});
</script>

<style lang="scss" scoped></style>