<template>
  <div id="page">
    <div id="phrase_box">
      <!-- <svg width="100%" height="100%">
        <defs>
          <mask
            id="mask"
            maskUnits="userSpaceOnUse"
            maskContentUnits="userSpaceOnUse"
          >
            <linearGradient
              id="linearGradient"
              gradientUnits="objectBoundingBox"
              x2="0"
              y2="1"
            >
              <stop stop-color="white" stop-opacity="0" offset="0%" />
              <stop stop-color="white" stop-opacity="1" offset="30%" />
              <stop stop-color="white" stop-opacity="1" offset="70%" />
              <stop stop-color="white" stop-opacity="0" offset="100%" />
            </linearGradient>
            <rect width="100%" height="100%" fill="url(#linearGradient)" />
          </mask>
        </defs>
        <g width="100%" height="100%" style="mask: url(#mask)">
          <g id="phrases" ref="phrases"></g>
        </g>
      </svg> -->

      <div>
        <div class="box">
          <span class="item">正在进行方案数据加载</span>
          <a-spin v-if="firstShow" :indicator="indicator" />
          <img v-else src="../../assets/dui.svg" alt="">
        </div>
        <div class="box">
          <span class="item">正在进行方案智能生成</span>
          <a-spin v-if="secondShow" :indicator="indicator" />
          <img v-else src="../../assets/dui.svg" alt="">
        </div>
        <div class="box">
          <span class="item">正在进行方案完整性校验</span>
          <a-spin v-if="thirdShow" :indicator="indicator" />
          <img v-else src="../../assets/dui.svg" alt="">
        </div>
      </div>
    </div>
    <div id="footer">
      <div id="logo"></div>
    </div>
  </div>
</template>

<script>
export default {
  props:["endFunction"],
  data(props) {
    return {
      verticalSpacing: 50,
      indicator: true,
      phrases: [
        "正在进行方案数据加载",
        "正在进行方案智能生成",
        "正在进行方案完整性校验",
      ],
      firstShow: true,
      secondShow: true,
      thirdShow: true,
    };
  },
  mounted() {
    setTimeout(() => {
      this.firstShow = false;
    }, 4000);
    setTimeout(() => {
      this.secondShow = false;
    }, 8000);
  },
  methods: {},
};
</script>

<style>
#page {
  align-items: center;
  /* background: #ffffff; */
  bottom: 0;
  display: flex;
  justify-content: center;
  left: 0;
  position: absolute;
  right: 0;
  top: 0;
  transition: opacity 1s;
}

#phrase_box {
  display: flex;
  flex-flow: column;
  height: 150px;
  overflow: hidden;
  width: 460px;
  align-items: center;
}

#phrases {
  -webkit-animation: slide-phrases-upward 20s;
  animation: slide-phrases-upward 20s;
}

#footer {
  bottom: 30px;
  color: white;
  display: flex;
  justify-content: center;
  left: 0;
  position: fixed;
  right: 0;
}
#loadingCheckCircleSVG-0 {
  -webkit-animation: fill-to-white 5000ms;
  animation: fill-to-white 5000ms;
  -webkit-animation-delay: -1.5s;
  animation-delay: -1.5s;
  fill: white;
  opacity: 0;
}

#loadingCheckCircleSVG-1 {
  -webkit-animation: fill-to-white 5000ms;
  animation: fill-to-white 5000ms;
  -webkit-animation-delay: -0.5s;
  animation-delay: -0.5s;
  fill: white;
  opacity: 0;
}

#loadingCheckCircleSVG-2 {
  -webkit-animation: fill-to-white 5000ms;
  animation: fill-to-white 5000ms;
  -webkit-animation-delay: 0.5s;
  animation-delay: 0.5s;
  fill: white;
  opacity: 0;
}
.item {
  font-size: 20px;
  margin-right: 14px;
  line-height: 20px;
  margin-right: 10px;
  color: #595959;
}
.box{
    align-items: center;
    margin-bottom: 16px;
}
.box img{
    margin-bottom: 4px;
}
</style>
