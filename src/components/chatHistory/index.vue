<template>
    <div class="chatHistory">
        <div class="navigation" :class="[isNavigationOpen ? 'navigation-open' : 'navigation-close']">
            <div v-if="isNavigationOpen">
                <div class="maxWidth maxHeight padding_t_40">
                    <div class="maxWidth flex align-center just-sb">
                        <div :class="[showNewChat ? 'width102' : 'width0']" style="line-height: 40px;"
                            class="newChat margin_l_24 pointer text-center" @click="startNewChat()">
                            开启新对话
                        </div>
                        <div class="pointer" v-if="historyDataList.length !== 0" @click="deleteAllHistory()">
                            <a-tooltip>
                                <template #title>
                                    删除所有历史记录
                                </template>
                                <img style="width: 20px;height: 20px; margin-left: 15px;"
                                    src="../../assets/images/AI/deleteCard.png" alt="">
                            </a-tooltip>
                        </div>
                        <div style="width: 24px; height: 24px;" class="margin_r_16 pointer"
                            @click="contorlNavigation('close')">
                            <img class="maxWidth maxHeight" src="../../assets/images/AI/closeHistory.png" alt="">
                        </div>
                    </div>
                    <div v-if="historyDataList.length !== 0" class="allHistoryList">
                        <div class="margin_b_40 margin_t_32" v-for="(item, index) in historyList" :key="index">
                            <div v-if="item.chatList.length !== 0">
                                <div :class="[showNewChat ? '' : 'width0']" style="overflow: hidden;"
                                    class="title margin_b_16">
                                    {{ item.time }}
                                </div>
                                <div class="list">
                                    <div style="padding: 10px;border-radius: 6px;"
                                        :class="[showNewChat ? 'width220' : 'width0', selectedIndex?.groupIndex === index && selectedIndex?.chatIndex === listIndex ? 'selected-item' : '']"
                                        class="eachChat flex align-center just-sb"
                                        v-for="(listItem, listIndex) in item.chatList" :key="listIndex"
                                        @mouseenter="mouseEnter(index, listIndex)"
                                        @mouseleave="mouseLeave(index, listIndex)">
                                        <div style="width: 170px;" class="textEllipsis pointer"
                                            @click="searchHistory(listItem.sessionId, { groupIndex: index, chatIndex: listIndex })">
                                            <a-tooltip>
                                                <template #title>
                                                    {{ listItem.question }}
                                                </template>
                                                {{ listItem.question }}
                                            </a-tooltip>
                                        </div>
                                        <a-dropdown :trigger="['hover']">
                                            <img v-if="showItemImg(index, listIndex)" class="menu-icon pointer"
                                                src="../../assets/images/AI/pointpoint.png" alt="">
                                            <template #overlay>
                                                <a-menu>
                                                    <a-menu-item key="delete" @click="deleteChatHis(listItem)">
                                                        <img style="width: 16px;height: 16px; margin-right: 8px;"
                                                            src="../../assets/images/AI/deleteCard.png" alt="">
                                                        <span>删除</span>
                                                    </a-menu-item>
                                                </a-menu>
                                            </template>
                                        </a-dropdown>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div v-else class="noHistory flex just-center align-center">
                        暂无历史对话
                    </div>
                </div>
                <div v-if="showAIDownloadBtn()" @click="AIDownload()"
                    class="maxWidth text-center margin_b_10 margin_t_10 pointer"
                    style="font-size: 14px; color: #2D65F7;">
                    导出缺失要素
                </div>
            </div>
            <div class="flex just-center padding_t_40" v-else>
                <div style="width: 24px; height: 24px;" class="pointer" @click="contorlNavigation('open')">
                    <img class="maxWidth maxHeight" src="../../assets/images/AI/openHistory.png" alt="">
                </div>
            </div>
        </div>
        <a-modal v-model:visible="delAllVisible" title="提示" @ok="delAllHistoryHandleOk">
            <p>您是否确认删除所有聊天记录?</p>
        </a-modal>
    </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { getAISearchHistoryList, deleteAISearchHistory, getChatContent, clearAllChat, getAILackDownload } from "@/api/AI/ai.js";
import { useRoute, useRouter } from "vue-router";
import eventBus from "@/utils/eventBus";
export default defineComponent({
    setup() {
        const router = useRouter();
        const route = useRoute();
        const data = reactive({
            showDelete: false,
            isNavigationOpen: false,
            showNewChat: true,
            historyList: [
                {
                    time: '今天',
                    chatList: []
                },
                {
                    time: '昨天',
                    chatList: []
                },
                {
                    time: '7天内',
                    chatList: []
                },
                {
                    time: '更早',
                    chatList: []
                },
            ],
            selectedIndex: null,
            historyDataList: [],
            delAllVisible: false,
        });
        // 导航栏开关
        const contorlNavigation = (type) => {
            if (type == 'open') {
                data.isNavigationOpen = true
                setTimeout(() => {
                    data.showNewChat = true
                    data.showDelete = true
                }, 200);
            } else if (type == 'close') {
                data.showNewChat = false
                data.isNavigationOpen = false
                data.showDelete = false
            }
        }
        // 开始新对话
        const startNewChat = () => {
            router.push({
                path: "/home",
            });
        }
        // 计算距离当前日子多少天
        const countDays = (date) => {
            const parts = date.split('-');
            const year = parseInt(parts[0], 10);
            const month = parseInt(parts[1], 10) - 1; // 月份从0开始
            const day = parseInt(parts[2], 10);

            // 创建输入日期的Date对象（本地时间0点）
            const userDate = new Date(year, month, day);
            userDate.setHours(0, 0, 0, 0);

            // 创建今天的Date对象（本地时间0点）
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // 计算时间差（毫秒）
            const diffTime = userDate.getTime() - today.getTime();

            // 转换为天数并返回
            return Math.floor(diffTime / (1000 * 60 * 60 * 24));
        }
        // 获取多轮对话历史记录
        const getHistoryChat = () => {
            data.historyList[0].chatList = []
            data.historyList[1].chatList = []
            data.historyList[2].chatList = []
            data.historyList[3].chatList = []
            getAISearchHistoryList().then((res) => {
                console.log('历史记录', res)
                if (res.data.length !== 0) {
                    res.data.sort((a, b) => new Date(b.createTime) - new Date(a.createTime));
                }
                data.historyDataList = res.data
                getHistoryChatData()
                // 在历史记录加载完成后，设置选中状态
                setSelectedIndex()
            })
        }
        const getHistoryChatData = () => {
            if (data.historyDataList.length !== 0) {
                for (let i of data.historyDataList) {
                    console.log('11111111111111', countDays(i.createTime))
                    if (countDays(i.createTime) == 0) {
                        // 说明是今天
                        data.historyList[0].chatList.push({
                            question: i.question,
                            sessionId: i.sessionId,
                        })
                    } else if (countDays(i.createTime) == -1) {
                        //说明是昨天
                        data.historyList[1].chatList.push({
                            question: i.question,
                            sessionId: i.sessionId,
                        })
                    } else if (countDays(i.createTime) < -1 && countDays(i.createTime) > -7) {
                        //说明是七天内
                        data.historyList[2].chatList.push({
                            question: i.question,
                            sessionId: i.sessionId,

                        })
                    } else if (countDays(i.createTime) < -7) {
                        // 说明过去了七天以上
                        data.historyList[3].chatList.push({
                            question: i.question,
                            sessionId: i.sessionId,
                        })
                    }
                }
            }
        }
        const searchHistory = (sessionId, index) => {
            data.selectedIndex = index;
            console.log('index', index)
            // 触发openChatLoading事件
            eventBus.emit('openChatLoading');
            eventBus.emit('writingOver');
            eventBus.emit('fromHistoryDialogType');
            getChatContent({
                sessionId: sessionId
            }).then((res) => {
                // 触发closeChatLoading事件
                console.log('resxxxxxxxxxx', res)
                eventBus.emit('closeChatLoading');
                localStorage.setItem("homeHisData", JSON.stringify(res.data))
                localStorage.setItem("isFromHistory", '是')
                localStorage.setItem("isFromHome", '否')
                router.push({
                    path: "AISearch",
                    query: {
                        isFromHomeHis: true,
                        sessionId: sessionId,
                        selectedGroupIndex: index.groupIndex,
                        selectedChatIndex: index.chatIndex
                    }
                });
            })
        }
        // 设置临时历史记录
        const setTemporaryHistory = (question, sessionId) => {
            // console.log('question', question)
            data.historyList[0].chatList.unshift({
                question: question ? question : '新对话',
                sessionId: sessionId ? sessionId : ''
            })
            data.historyDataList.unshift({
                question: question ? question : '新对话',
                sessionId: sessionId ? sessionId : ''
            })
            console.log('data.historyList', data.historyList)
            data.selectedIndex = {
                groupIndex: 0,
                chatIndex: 0
            }
        }
        eventBus.on('setTemporaryHistory', () => {
            setTemporaryHistory()
        })
        const setTemporaryHistoryInfo = (info) => {
            console.log('JSON.parse(info)', JSON.parse(info))
            if (JSON.parse(info).question) {
                data.historyList[0].chatList[0].question = JSON.parse(info).question
            }
            if (JSON.parse(info).sessionId) {
                data.historyList[0].chatList[0].sessionId = JSON.parse(info).sessionId
            }
        }
        eventBus.on('setTemporaryHistoryInfo', (info) => {
            setTemporaryHistoryInfo(info); // 将参数传递给函数
        });
        // 删除历史记录
        const deleteChatHis = (item) => {
            console.log('item', item)
            console.log('route.sessionId', route.query.sessionId)
            deleteAISearchHistory({
                sessionId: item.sessionId
            }).then((res) => {
                console.log('删除历史记录', res)
                data.historyList.forEach(group => {
                    group.chatList = group.chatList.filter(chat => chat.sessionId !== item.sessionId)
                })
                if (route.query.sessionId == item.sessionId) {
                    startNewChat()
                }
            })
        }
        const mouseEnter = (index, listIndex) => {
            data.nowHoverIndex = index
            data.nowHoverListIndex = listIndex
        }
        const mouseLeave = (index, listIndex) => {
            data.nowHoverIndex = null
            data.nowHoverListIndex = null
        }
        const showItemImg = (index, listIndex) => {
            return data.nowHoverIndex === index && data.nowHoverListIndex === listIndex
        }
        const delAllHistoryHandleOk = () => {
            console.log('删除所有历史记录')
            clearAllChat().then((res) => {
                data.delAllVisible = false
                console.log('删除所有历史记录', res)
                data.historyDataList = []
                data.historyList = [
                    {
                        time: '今天',
                        chatList: []
                    },
                    {
                        time: '昨天',
                        chatList: []
                    },
                    {
                        time: '7天内',
                        chatList: []
                    },
                    {
                        time: '更早',
                        chatList: []
                    },
                ]
                startNewChat()
                getHistoryChat()
            })
        }
        // 删除所有历史记录
        const deleteAllHistory = () => {
            console.log('删除所有历史记录')
            data.delAllVisible = true
        }
        // 设置选中状态的方法
        const setSelectedIndex = () => {
            const sessionId = route.query.sessionId
            if (sessionId) {
                for (let i = 0; i < data.historyList.length; i++) {
                    for (let j = 0; j < data.historyList[i].chatList.length; j++) {
                        if (data.historyList[i].chatList[j].sessionId === sessionId) {
                            data.selectedIndex = {
                                groupIndex: i,
                                chatIndex: j
                            }
                            // 保存到localStorage
                            localStorage.setItem("selectedHistoryIndex", JSON.stringify({
                                groupIndex: i,
                                chatIndex: j
                            }))
                            break
                        }
                    }
                    if (data.selectedIndex) break // 如果找到了匹配项，就跳出外层循环
                }
            }
        }
        // 下载AI缺失的东西
        const AIDownload = () => {
            getAILackDownload().then((res) => {
                if (res.code == 200) {
                	const href = res.msg;
					        let windowOrigin = window.location.origin;
					        let token = localStorage.getItem("token");
					        let newHref = href;
					        if(href.includes(windowOrigin)){
					          newHref = "/portal" + href.split(windowOrigin)[1]
					        }
					        window.open(windowOrigin + newHref + "?token=" + token);
					        return false;
                    console.log('msg', res.msg)
                    const link = document.createElement("a");
                    link.href = res.msg;
                    link.download = "缺失能力列表";
                    link.click();
                    URL.revokeObjectURL(link.href);
                }
            })
        }
        // 下载按钮的权限
        const showAIDownloadBtn = () => {
            const userInfo = JSON.parse(localStorage.getItem("userInfo"));
            console.log('userInfo.roleKeyList', userInfo.roleKeyList)
            if (userInfo && userInfo.roleKeyList) {
                return userInfo.roleKeyList.includes('sysAdmin') || userInfo.roleKeyList.includes('auditManager');
            }
            return false;
        }
        // 监听路由变化
        watch(() => route.name, (newName) => {
            if (newName === 'home') {
                data.selectedIndex = -1; // 取消高亮
                localStorage.removeItem('selectedHistoryIndex'); // 清除本地存储的选中状态
            }
        }, { immediate: true });
        onMounted(() => {
            if (route.name == 'AISearch') {
                data.isNavigationOpen = true
                data.showNewChat = true
                data.showDelete = true
            }
            // 获取历史记录
            getHistoryChat()
            if (localStorage.getItem('isFromHome') == '是' && localStorage.getItem('fromHomeSessionId')) {
                data.selectedIndex = {
                    groupIndex: 0,
                    chatIndex: 0
                }
            }
        })
        return {
            ...toRefs(data),
            router,
            contorlNavigation,
            startNewChat,
            getHistoryChat,
            getHistoryChatData,
            searchHistory,
            deleteChatHis,
            mouseEnter,
            mouseLeave,
            showItemImg,
            deleteAllHistory,
            delAllHistoryHandleOk,
            AIDownload,
            showAIDownloadBtn,
        };
    },
});
</script>

<style lang="scss" scoped>
.chatHistory {
    height: calc(100vh - 60px);
}

.navigation {
    height: 100%;
    background-color: #F2F3FE;
    box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.24);
    // overflow: hidden;
    transition: 0.5s;

    .newChat {
        // width: 102px;
        height: 40px;
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        border-radius: 4px 4px 4px 4px;
        font-weight: 500;
        font-size: 14px;
        color: #fff;
        transition: 0.5s;
        overflow: hidden;
    }

    .width0 {
        width: 0px;
    }

    .width102 {
        width: 102px;
    }

    .allHistoryList {
        height: calc(100vh - 60px - 40px - 32px - 50px);
        padding: 0px 24px;
        overflow-y: auto;
        
        /* 隐藏滚动条但保持滚动功能 */
        scrollbar-width: none; /* Firefox */
        -ms-overflow-style: none; /* IE and Edge */
        
        &::-webkit-scrollbar {
            display: none; /* Chrome, Safari and Opera */
        }

        .title {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.45);
            font-size: 14px;
        }

        .list {
            .eachChat {
                font-weight: 400;
                color: rgba(0, 0, 0, 0.85);
                font-size: 14px;
                font-family: Source Han Sans CN, Source Han Sans CN;
                transition: all 0.3s ease;
                width: 0;
                position: relative;
                border-radius: 4px;
                padding: 4px 8px;

                &:hover {
                    background-color: rgba(24, 144, 255, 0.1);

                    .delete-icon {
                        opacity: 1;
                    }
                }

                .delete-icon {
                    opacity: 0;
                    transition: opacity 0.3s ease;
                    cursor: pointer;
                    margin-right: 8px;
                }
            }

            .width0 {
                width: 0px;
            }

            .width220 {
                width: 220px;
            }
        }
    }
}

.navigation-close {
    width: 64px;
}

.navigation-open {
    width: 250px;
}

.menu-icon {
    color: #999;
    font-size: 20px;
    padding: 0 8px;
    transition: all 0.3s ease;

    &:hover {
        color: #333;
    }
}

:deep(.ant-dropdown-menu) {
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;

    &:hover {
        background-color: #f5f5f5;
    }
}

.selected-item {
    background-color: #e6f7ff;
    cursor: pointer;
    transition: background-color 0.3s ease;
}

.noHistory {
    width: 100%;
    height: calc(100vh - 60px - 40px - 32px - 40px);
    font-size: 18px;
    color: rgba(0, 0, 0, 0.45);
}
</style>