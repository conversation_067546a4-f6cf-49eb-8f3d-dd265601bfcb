<template>
    <div>
        <div class="header">
            <img src="@/assets/images/layout/info.png" alt=""/>
            <div class="title">提示</div>
        </div>
        <div class="text">
            <div class="msg" v-if="applyInfo.applyTimes != 3">{{ applyInfo.msg }},请申请工单获取下载次数。</div>
            <div class="msg" v-if="applyInfo.applyTimes == 3">{{ applyInfo.msg }}</div>
            <!-- <div class="tips">请工单申请下载次数</div> -->
        </div>
        <div class="btns flex just-end margin_t_12">
            <a-button class="margin_r_20" @click="cancel()">
                取消
            </a-button>
            <a-button v-if="applyInfo.applyTimes != 3" type="primary" style="background-color: #0C70EB;" @click="confirm()">
                申请
            </a-button>
        </div>
    </div>

</template>

<script>
import { defineComponent,defineProps, reactive, toRefs, ref, computed } from "vue";
export default defineComponent({
    name: "promptBox",
    emits: ["downloadModalCancel", "downloadModalConfirm"],
    components: {
    },
    props:{
        msgObj:{
            type: Object,
            default: {
                applyTimes:'',
                msg:'您今日下载附件量已超过上限，如果继续下载需提交工单审核。'
            },
        }
    },
    setup(props, { emit }) {
        const data = reactive({
            applyInfo:props.msgObj
        });
        // 取消
        const cancel = () => {
            console.log('取消')
            emit("downloadModalCancel")
        }
        // 确认
        const confirm = () => {
            console.log('确定')
            // window.location.origin
            // 'http://*************:5174'
            window.location.replace(
                window.location.origin + `/backend/#/downloadReview/downloadReviewForm?applyTimes=${data.applyInfo.applyTimes}&action=add&routeName=${data.applyInfo.fullPath}`
            );
            // emit("downloadModalConfirm")
        }
        return {
            ...toRefs(data),
            cancel,
            confirm,
        };
    },
});
</script>

<style lang="scss" scoped>
.header{
    display: flex;
    align-items: center;
    font-size: 16px;
    font-weight: 600px;
    .title{
        margin-left: 12px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 500;
        font-size: 16px;
        color: #00060E;
    }
}
.text {
    margin-top: 12px;
    padding: 12px 0;
    font-size: 16px;
    display: flex;
    flex-flow: column nowrap;
    justify-content: center;
    align-items: center;
    .msg{
        width: 330px;
        font-weight: 400;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-size: 14px;
        color: #00060E;
    }
    .tips{
        margin-top: 10px;
        font-size: 14px;
        color: rgb(183, 174, 174);
    }
}
</style>