<template>
  <div class="number-scroll">
    {{ displayNumber }}
  </div>
</template>

<script>
import { ref, onMounted, watch } from 'vue'

export default {
  name: 'NumberScroll',
  props: {
    value: {
      type: Number,
      required: true
    },
    duration: {
      type: Number,
      default: 1000
    }
  },
  setup(props) {
    const displayNumber = ref(0)
    
    const animateNumber = (target) => {
      const startNumber = displayNumber.value
      const startTime = Date.now()
      
      const updateNumber = () => {
        const currentTime = Date.now()
        const progress = Math.min((currentTime - startTime) / props.duration, 1)
        
        displayNumber.value = Math.floor(startNumber + (target - startNumber) * progress)
        
        if (progress < 1) {
          requestAnimationFrame(updateNumber)
        } else {
          displayNumber.value = target
        }
      }
      
      requestAnimationFrame(updateNumber)
    }
    
    watch(() => props.value, (newValue) => {
      animateNumber(newValue)
    })
    
    onMounted(() => {
      animateNumber(props.value)
    })
    
    return {
      displayNumber
    }
  }
}
</script>

<style scoped>
.number-scroll {
  font-size: 28px;
  font-weight: bold;
  color: #0C70EB;;
}
</style> 