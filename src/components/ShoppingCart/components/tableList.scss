.to-combine {
    width: 90px;
    height: 30px;
    background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
    border-radius: 24px 24px 24px 24px;
    color: #fff;
    text-align: center;
    line-height: 30px;
}

.box .right {
    cursor: pointer;
}

.clear {
    line-height: 30px;
    color: #0C70EB;
}

.line {
    padding: 12px 0;
    margin-bottom: 12px;
    border-bottom: 1px solid rgba(0, 6, 14, 0.08);
}

.title {
    font-weight: 500;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);

    .sub-count {
        display: inline-block;
        margin-left: 12px;
        font-weight: 400;
        font-size: 14px;
        color: #00060E;
    }
}

.emptyPhoto {
    text-align: center;
    font-size: 16px;


    .tip {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
    }

    .select {
        color: #1E63FF;
        cursor: pointer;
    }
}

.tabContent {
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    position: relative;

    .loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .cardContent {
        width: 100%;
        position: relative;
    }

    .cardContent::-webkit-scrollbar {
        display: none;
    }

    .tab_text {
        font-size: 18px;
        color: #262626;
        cursor: pointer;
        height: 40px;
    }

    .activeTab {
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
        padding-top: 32px;
    }

    .topTab {
        margin-top: 0;
        background-image: url("@/assets/images/home/<USER>");
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
    }

    :deep(.ant-tag) {
        font-weight: 500;
        font-size: 12px;
        color: #2E7FFF;
        height: 20px;
        display: flex;
        align-items: center;
    }


  

    .card_content {
        height: 163px;
        margin-bottom: 24px;
        border-radius: 10px;
        position: relative;
        background: #F5F7FC;
    }

    .cart-button {
        position: absolute;
        right: 20px;
        bottom: 6px;
    }

    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }


    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 117px;
        margin-left: 12px;
        width: 80%
    }

    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card_tag {
            width: 75%;
            display: flex;
            align-items: center;
        }

        .card_title {
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        :deep(.cityStyle) {
            cursor: pointer;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;

            .add-icon {
                width: 16px;
            }

            .add {
                font-weight: 500;
                font-size: 12px;
                color: #0C70EB;
            }
        }
    }

    .card_des {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
    }

    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }
}

:deep(.ant-image-img) {
    height: 117px !important;
}