<template>
  <div>
    <transition name="slide-fade">
      <div v-if="show" class="box">
        <div class="flex just-sb">
          <div class="combine" style="cursor: pointer" @click="changeTab(1)">
            <img
              width="14px"
              v-if="selectNum == '1'"
              height="8px"
              src="@/assets/images/home/<USER>"
              style="margin-right: 10px"
            />
            <img
              width="78px"
              v-if="selectNum == '1'"
              height="19px"
              src="@/assets/images/combine/tip.png"
            />
            <img
              width="78px"
              v-else
              height="19px"
              src="@/assets/images/buyList/unselectCombine.png"
            />
          </div>
          <div class="buyList" style="cursor: pointer" @click="changeTab(2)">
            <img
              width="14px"
              v-if="selectNum == '2'"
              height="8px"
              src="@/assets/images/home/<USER>"
              style="margin-right: 10px"
            />
            <img
              width="78px"
              v-if="selectNum == '2'"
              height="19px"
              src="@/assets/images/buyList/buyList.png"
            />
            <img
              width="78px"
              v-else
              height="19px"
              src="@/assets/images/buyList/unselectBuy.png"
            />
          </div>
          <div class="flex right">
            <div class="clear" @click="clear">清空</div>
            <div
              class="to-combine margin_l_24"
              v-if="selectNum == '1'"
              @click="toCombine"
            >
              去组合>
            </div>
            <div class="to-combine margin_l_24" v-else @click="toCombine">
              去封装>
            </div>
            <img
              @click="close"
              width="78px"
              class="margin_t_6"
              height="19px"
              src="@/assets/images/combine/close.svg"
            />
          </div>
        </div>
        <div>
          <div class="flex just-sb line">
            <div class="title" v-if="selectNum == '1'">
              方案<span class="sub-count">({{ solutionCount }})</span>
            </div>
            <div class="title" v-else>
              场景<span class="sub-count">({{ solutionCount }})</span>
            </div>
          </div>

          <div
            class="tabContent"
            v-if="solutionList && solutionList.length > 0"
          >
            <div class="cardContent">
              <div class="card_total flex-1">
                <div
                  class="card_content"
                  v-for="(item, index) in solutionList"
                  :key="index"
                >
                  <div style="display: flex; padding: 24px">
                    <a-radio-group :value="valueId" v-if="selectNum == '1'">
                      <a-radio :value="item.id" @change="getId"> </a-radio>
                    </a-radio-group>
                    <a-checkbox-group :value="movalue" v-if="selectNum == '2'">
                      <a-checkbox :value="item.id" @change="getboxId">
                      </a-checkbox>
                    </a-checkbox-group>
                    <div>
                      <a-image
                        :width="168"
                        :height="105"
                        :preview="false"
                        v-if="item.logo"
                        :src="`${item.logo}`"
                        style="width: 168px; height: 117px"
                      />
                      <img
                        src="@/assets/images/home/<USER>"
                        style="width: 168px; height: 117px"
                        v-else
                      />
                    </div>
                    <div class="card_center">
                      <div class="card_text">
                        <div class="card_tag">
                          <a-tag color="#D7E6FF">{{ item.industryName }}</a-tag>
                          <div class="card_title">
                            {{ item.name }}
                          </div>
                        </div>
                        <div
                          class="cityStyle"
                          v-if="selectNum == '1'"
                          @click="move(item, '1')"
                        >
                          <img
                            class="add-icon"
                            src=" @/assets/images/AI/cancelAdd.png"
                          /><span class="add"> &nbsp;移出预选</span>
                        </div>
                        <div class="cityStyle" v-else @click="move(item, '1')">
                          <img
                            class="add-icon"
                            src=" @/assets/images/AI/cancelAdd.png"
                          /><span class="add"> &nbsp;移出订购</span>
                        </div>
                      </div>
                      <div class="card_des">
                        {{ item.description }}
                      </div>

                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                        "
                      >
                        <div style="display: flex" v-if="selectNum == '1'">
                          <a-tag color="#D7E6FF" style="display: block">{{
                            item.label[0]
                          }}</a-tag>
                          <a-tag
                            color="#D7E6FF"
                            v-if="item.label[1]"
                            style="display: block"
                            >{{ item.label[1] }}</a-tag
                          >
                        </div>
                        <div style="display: flex">
                          <div class="margin_r_12">
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.viewCount"
                              >{{ item.viewCount }}</span
                            >
                            <span v-else>-</span>
                          </div>
                          <div>
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.downloadCount"
                              >{{ item.downloadCount }}</span
                            >
                            <span v-else>-</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          <div v-else class="emptyPhoto">
            <div>
              <img src="@/assets/images/combine/noData.png" />
            </div>
            <span class="tip" v-if="selectNum == '1'"
              >未选择方案<span
                class="select"
                @click="toList('topContentNew', 'topContentNew')"
                >去选择></span
              ></span
            >
            <span class="tip" v-else
              >未选择场景<span class="select" @click="toList('scenarioPlan')"
                >去选择></span
              ></span
            >
          </div>
        </div>
        <div v-if="selectNum == '1'">
          <div class="flex just-sb line">
            <div class="title">
              方案场景<span class="sub-count">({{ solutionModuleCount }})</span>
            </div>
          </div>
          <div
            class="tabContent"
            v-if="solutionModuleList && solutionModuleList.length > 0"
          >
            <div class="cardContent">
              <div class="card_total flex-1">
                <template
                  v-for="(item, index) in solutionModuleList"
                  :key="index"
                >
                  <div :class="['card_content']">
                    <div style="display: flex; padding: 24px">
                      <a-checkbox-group :value="scevalue">
                        <a-checkbox :value="item.id" @change="getSceneId">
                        </a-checkbox>
                      </a-checkbox-group>
                      <div>
                        <a-image
                          :width="168"
                          :height="1"
                          :preview="false"
                          v-if="item.image"
                          :src="`${item.image}`"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 117px"
                          v-else
                        />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <a-tag color="#D7E6FF" v-if="item.categoryName">{{
                              item.categoryName
                            }}</a-tag>
                            <div class="card_title">
                              {{ item.name }}
                            </div>
                          </div>
                          <div class="flex">
                            <div
                              class="cityStyle"
                              v-if="selectNum == '1'"
                              @click="move(item, '3')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出预选</span>
                            </div>
                            <div
                              class="cityStyle"
                              v-else
                              @click="move(item, '2')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出订购</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ item.summary }}
                        </div>

                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        >
                          <div
                            style="display: flex"
                            v-if="selectNum == '1'"
                          ></div>
                          <div style="display: flex; align-items: center">
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.viewCount"
                              >{{ item.viewCount }}</span
                            >
                            <span v-else>-</span>
                            <img
                              src="@/assets/images/home/<USER>"
                              style="
                                width: 16px;
                                height: 16px;
                                margin-left: 18px;
                              "
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.downloadCount"
                              >{{ item.downloadCount }}</span
                            >
                            <span v-else>-</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div v-else class="emptyPhoto">
            <div v-if="selectNum == '1'">
              <img src="@/assets/images/combine/noData.png" />
            </div>
            <span class="tip" v-if="selectNum == '1'"
              >未选择方案场景<span
                class="select"
                @click="toList('topContentNew', 'scene')"
                >去选择></span
              ></span
            >
          </div>
        </div>
        <div>
          <div class="flex just-sb line">
            <div class="title" v-if="selectNum == '1'">
              能力<span class="sub-count">({{ abilityCount }})</span>
            </div>
            <div class="title" v-else>
              产品<span class="sub-count">({{ moduleCount }})</span>
            </div>
          </div>
          <div class="tabContent" v-if="moduleList && moduleList.length > 0">
            <div class="cardContent">
              <div class="card_total flex-1">
                <template v-for="(item, index) in moduleList" :key="index">
                  <div :class="['card_content']">
                    <div style="display: flex; padding: 24px">
                      <a-checkbox-group :value="movalue">
                        <a-checkbox :value="item.id" @change="getModuId">
                        </a-checkbox>
                      </a-checkbox-group>
                      <div>
                        <a-image
                          :width="168"
                          :height="1"
                          :preview="false"
                          v-if="item.abilityPicture"
                          :src="`${item.abilityPicture}`"
                        />
                        <img
                          src="@/assets/images/home/<USER>"
                          style="width: 168px; height: 117px"
                          v-else
                        />
                      </div>
                      <div class="card_center">
                        <div class="card_text">
                          <div class="card_tag">
                            <a-tag color="#D7E6FF" v-if="item.abilityType">{{
                              item.abilityType
                            }}</a-tag>
                            <div class="card_title">
                              {{ item.name }}
                            </div>
                          </div>
                          <div class="flex">
                            <div
                              class="cityStyle"
                              v-if="selectNum == '1'"
                              @click="move(item, '2')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出预选</span>
                            </div>
                            <div
                              class="cityStyle"
                              v-else
                              @click="move(item, '2')"
                            >
                              <img
                                class="add-icon"
                                src=" @/assets/images/AI/cancelAdd.png"
                              /><span class="add"> &nbsp;移出订购</span>
                            </div>
                          </div>
                        </div>
                        <div class="card_des">
                          {{ item.abilityIntro }}
                        </div>

                        <div
                          style="
                            display: flex;
                            align-items: center;
                            justify-content: space-between;
                          "
                        >
                          <div
                            style="display: flex; align-items: center"
                            v-if="selectNum == '1'"
                          >
                            <a-tag
                              v-if="item.label"
                              color="#D7E6FF"
                              style="display: block"
                              >{{ item.label }}</a-tag
                            >
                          </div>
                          <div style="display: flex; align-items: center">
                            <img
                              src="@/assets/images/home/<USER>"
                              style="width: 16px; height: 16px"
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.viewCount"
                              >{{ item.viewCount }}</span
                            >
                            <span v-else>-</span>
                            <img
                              src="@/assets/images/home/<USER>"
                              style="
                                width: 16px;
                                height: 16px;
                                margin-left: 18px;
                              "
                            />
                            <span
                              style="
                                font-size: 12px;
                                color: rgba(0, 0, 0, 0.45);
                              "
                              v-if="item.downloadCount"
                              >{{ item.downloadCount }}</span
                            >
                            <span v-else>-</span>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </template>
              </div>
            </div>
          </div>
          <div v-else class="emptyPhoto">
            <div>
              <img src="@/assets/images/combine/noData.png" />
            </div>
            <span class="tip" v-if="selectNum == '1'"
              >未选择能力<span class="select" @click="toList('moduleList')"
                >去选择></span
              ></span
            >
            <span class="tip" v-else
              >未选择产品<span class="select" @click="toList('productList')"
                >去选择></span
              ></span
            >
          </div>
        </div>
      </div>
    </transition>
    <div class="img" @click="showAnimation">
      <img
        width="36px"
        height="36px"
        src="@/assets/images/combine/cartIcon.png"
      />
      <p class="prepare">{{ label }}</p>
    </div>
    <a-modal
      v-if="selectNum == '1'"
      v-model:visible="visible"
      title="提示"
      @ok="handleOk"
    >
      <p>已存在定制组合，请确认是否替换已有组合?</p>
    </a-modal>
    <a-modal
      v-if="selectNum == '2'"
      v-model:visible="visible"
      title="提示"
      @ok="handleOk"
    >
      <p>已存在封装产品，请确认是否替换已有封装?</p>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { useRouter } from "vue-router";
import {
  myShoppingCartList,
  clearShopping,
  toCombinePage,
} from "@/api/combine/shoppingCart.js";
import { useHomeStore } from "@/store";
import { message } from "ant-design-vue";
import { myCombineList } from "@/api/combine/combine.js";
import eventBus from "@/utils/eventBus";
import {
  shopList,
  deleteShop,
  shopTable,
  toShopList,
  go
} from "@/api/buyList/index";
export default defineComponent({
  props: {
    type: {
      type: String,
      default: "预选组合",
    },
  },
  setup(props, { emit }) {
    const router = useRouter();
    const counterStore = useHomeStore();
    const data = reactive({
      show: false,
      visible: false,
      solutionList: [],
      moduleList: [],
      abilityList: [],
      productList: [],
      solutionModuleList: [],
      solutionCount: 0,
      moduleCount: 0,
      abilityCount: 0,
      productCount: 0,
      solutionModuleCount: 0,
      selectList: [],
      valueId: null,
      solveId: {},
      sceneId: {},
      movalue: [],
      scevalue: [],
      selectNum: "1",
      showShop: counterStore.contralShop,
      label: "预选组合",
    });

    const showAnimation = (val) => {
      data.show = !data.show;
    };
    watch(
      () => counterStore.contralShop,
      (nval) => {
        data.showShop = nval;
        if (data.showShop == true) {
          data.label = "预选组合";
          data.selectNum = "1";
        } else {
          data.label = "订购产品";
          data.selectNum = "2";
        }
      },
      { deep: true }
    );
    const getList = () => {
      if (data.selectNum == "1") {
        myShoppingCartList()
          .then((res) => {
            data.solutionCount = 0;
            data.moduleCount = 0;
            data.abilityCount = 0;
            data.solutionModuleCount = 0;
            data.productCount = 0;
            data.solutionList = [];
            data.abilityList = [];
            data.solutionModuleList = [];
            data.moduleList = [];
            data.productList = [];
            if (res.data.solutionList) {
              data.solutionList = res.data.solutionList;
              data.solutionList.forEach((item) => {
                item.label = item.labelName.split(",");
              });
              data.solutionCount = res.data.solutionCount;
            }
            if (res.data.abilityList) {
              data.moduleList = res.data.abilityList;
              data.abilityCount = res.data.abilityCount;
            }
            if (res.data.solutionModuleList) {
              data.solutionModuleList = res.data.solutionModuleList;
              data.solutionModuleCount = res.data.solutionModuleCount;
              data.solutionModuleCount.forEach((item) => {
                item.industryName = item.categoryName;
              });
            }
            if (res.data.productList) {
              data.productList = res.data.productList;
              data.productCount = res.data.productCount;
            }
          })
          .catch((error) => {
            console.error("获取购物车列表失败:", error);
          });
      } else {
        shopList()
          .then((res) => {
            data.solutionCount = 0;
            data.moduleCount = 0;
            data.solutionList = [];
            data.moduleList = [];
            if (res.data.productList) {
              data.moduleList = res.data.productList;
              data.moduleList.forEach((item) => {
                item.abilityIntro = item.introduction;
                item.industryName = item.labelName;
                item.abilityPicture = item.image;
                item.abilityType = item.labelName;
              });
              data.moduleCount = res.data.productList.length;
            }
            if (res.data.sceneList) {
              data.solutionList = res.data.sceneList;
              data.solutionList.forEach((item) => {
                item.logo = item.mainImg;
                item.industryName = item.classifyName;
                item.description = item.introduce;
                item.label = item.labelName;
              });
              data.solutionCount = res.data.sceneList.length;
            }
          })
          .catch((error) => {
            console.error("获取购物车列表失败:", error);
          });
      }
    };
    getList();
    eventBus.on("cartRefresh", getList);

    // 清空
    const clear = () => {
      let solution = [];
      let modules = [];
      let abiModules = [];
      let product = [];
      data.scevalue = [];
      data.movalue = [];
      data.valueId = null;
      if (data.selectNum == "1") {
        if (data.solutionList.length > 0) {
          solution = data.solutionList.map((item) => {
            return { schemeId: item.id, type: "1" };
          });
        }
        if (data.moduleList.length > 0) {
          abiModules = data.moduleList.map((item) => {
            return { schemeId: item.id, type: "2" };
          });
        }
        if (data.solutionModuleList.length > 0) {
          modules = data.solutionModuleList.map((item) => {
            return { schemeId: item.id, type: "3" };
          });
        }
        if (data.productList.length > 0) {
          product = data.productList.map((item) => {
            return { schemeId: item.id, type: "4" };
          });
        }
        clearShopping([...modules, ...solution, ...abiModules, ...product])
          .then((res) => {
            data.solutionCount = 0;
            data.moduleCount = 0;
            data.solutionList = [];
            data.moduleList = [];
            data.solutionModuleList = [];
            data.solutionModuleCount = 0;
            data.abilityCount = 0;
            data.productCount = 0;
            data.productList = [];
            eventBus.emit("moduleAllRefresh");
            eventBus.emit("solutionAllRefresh");
            eventBus.emit("solutionDetailRefresh");
            eventBus.emit("moduleDetailRefresh");
            eventBus.emit("applyDetailRefresh");
            eventBus.emit("productRefresh");
            eventBus.emit("productDetailRefresh");
          })
          .catch((error) => {});
      } else {
        if (data.solutionList.length > 0) {
          solution = data.solutionList.map((item) => {
            return { productId: item.id, type: "1" };
          });
        }
        if (data.moduleList.length > 0) {
          modules = data.moduleList.map((item) => {
            return { productId: item.id, type: "2" };
          });
        }
        deleteShop([...modules, ...solution])
          .then((res) => {
            data.solutionCount = 0;
            data.moduleCount = 0;
            data.solutionList = [];
            data.moduleList = [];
            eventBus.emit("productRefresh");
            eventBus.emit("scenarioRefresh");
            eventBus.emit("scnarioDetailRefresh");
            // eventBus.emit("productDetailRefresh");
          })
          .catch((error) => {});
      }
    };

    const toCombine = () => {
      if (data.selectNum == "1") {
        if (Object.keys(data.solveId).length != 0) {
          myCombineList()
            .then((res) => {
              // 提示
              if (
                res.data.list.some((item) => item.list && item.list.length > 0)
              ) {
                data.visible = true;
                data.show = false;
              } else {
                handleOk();
              }
            })
            .catch((error) => {});
        } else {
          message.warning("请选择数据进行组合,方案必选一个");
          return;
        }
      } else if (data.selectNum == "2") {
        if (Object.keys(data.selectList).length > 0) {
          shopTable(1).then((res) => {
            if (res.data.productPackageLists.length > 0) {
              data.visible = true;
              data.show = false;
            } else {
              handleOk();
            }
          });
        } else {
          message.warning("请至少选择一个场景或者产品");
          return;
        }
      }
    };
    const handleOk = () => {
      if (data.selectNum == "1") {
        data.selectList.push(data.solveId);
        console.log(data.selectList);
        return false;
        clearShopping(data.selectList)
          .then((res) => {
            getList();
            eventBus.emit("moduleAllRefresh");
            eventBus.emit("solutionAllRefresh");
            eventBus.emit("productRefresh");
          })
          .catch((error) => {});
        toCombinePage({ list: data.selectList, source: "1" }).then((res) => {
          getList();
          data.solutionCount = 0;
          data.moduleCount = 0;
          data.solutionList = [];
          data.moduleList = [];
          data.solutionModuleList = [];
          data.abilityCount = 0;
          data.productCount = 0;
          data.productList = [];
          data.solveId = {};
          data.selectList = [];
          data.valueId = null;
          data.movalue = [];
          data.visible = false;
          eventBus.emit("customRefresh");
          eventBus.emit("cartRefresh");
          router.push({
            name: "customizedList",
          });
        });
      } else {
        // go
        toShopList({ productShoppingCarts: data.selectList, source: "1" })
          .then((res) => {})
          .catch((error) => {});
        getList();
        data.solveId = {};
        data.selectList = [];
        data.valueId = null;
        data.movalue = [];
        data.visible = false;
        counterStore.buyListStroe = {};
        eventBus.emit("buyListRefresh");
        eventBus.emit("cartRefresh");
        router.push({
          name: "buyListPage",
        });
      }
    };

    const toList = (name, type) => {
      if (type === "scene") {
        router.push({
          name: name,
          query: {
            activeNum: "2",
          },
        });
      } else if (type === "topContentNew") {
        router.push({
          name: name,
          query: {
            activeNum: "1",
          },
        });
      } else {
        router.push({
          name: name,
        });
      }
    };

    const move = (item, type) => {
      if (data.selectNum == "1") {
        clearShopping([{ schemeId: item.id, type: type }])
          .then((res) => {
            if (type == "3") {
              eventBus.emit("applyDetailRefresh");
            }
            if (type == "1") {
              eventBus.emit("solutionDetailRefresh");
              eventBus.emit("getSchemeList");
            } else {
              eventBus.emit("moduleAllRefresh");
              eventBus.emit("solutionAllRefresh");
              eventBus.emit("moduleDetailRefresh");
              eventBus.emit("productRefresh");
              eventBus.emit("productDetailRefresh");
            }
            getList();
          })
          .catch((error) => {});
      } else {
        deleteShop([{ productId: item.id, type: type }]).then((res) => {
          eventBus.emit("productRefresh");
          eventBus.emit("scenarioRefresh");
          eventBus.emit("scnarioDetailRefresh");
          eventBus.emit("productDetailRefresh");
          getList();
        });
      }
    };

    const close = () => {
      data.show = false;
    };
    const getId = (e) => {
      if (e.target.checked == true) {
        data.solveId = { type: 1, schemeId: e.target.value };
        data.valueId = e.target.value;
      } else {
        data.valueId = "";
        data.solveId = {};
      }
    };
    const getModuId = (e) => {
      if (data.selectNum == "1") {
        data.movalue.push(e.target.value);
        if (e.target.checked == true) {
          data.selectList.push({ type: 2, schemeId: e.target.value });
        } else {
          data.selectList = data.selectList.filter(
            (item) => item.schemeId != e.target.value
          );
          data.movalue = data.movalue.filter((item) => item != e.target.value);
        }
      } else if (data.selectNum == "2") {
        if (e.target.checked == true) {
          data.movalue.push(e.target.value);
          data.selectList.push({ type: 2, productId: e.target.value });
        } else {
          data.selectList = data.selectList.filter(
            (item) => item.productId != e.target.value
          );
          data.movalue = data.movalue.filter((item) => item != e.target.value);
        }
      }
    };
    const getSceneId = (e) => {
      data.scevalue.push(e.target.value);
      if (e.target.checked == true) {
        data.selectList.push({ type: 3, schemeId: e.target.value });
      } else {
        data.selectList = data.selectList.filter(
          (item) => item.schemeId != e.target.value
        );
        data.scevalue = data.scevalue.filter((item) => item != e.target.value);
      }
    };
    const getboxId = (e) => {
      if (e.target.checked == true) {
        data.movalue.push(e.target.value);
        data.selectList.push({ type: 1, productId: e.target.value });
      } else {
        data.selectList = data.selectList.filter(
          (item) => item.productId != e.target.value
        );
        data.movalue = data.movalue.filter((item) => item != e.target.value);
      }
    };
    
    const changeTab = (e) => {
      data.movalue = [];
      data.selectList = [];
      data.selectNum = e;
      getList();
    };

    return {
      ...toRefs(data),
      router,
      changeTab,
      getboxId,
      getId,
      getModuId,
      showAnimation,
      toCombine,
      toList,
      clear,
      close,
      move,
      handleOk,
      counterStore,
      getSceneId
    };
  },
});
</script>

<style lang="scss" scoped>
.slide-fade-enter-active {
  transition: all 0.3s ease-out;
}

.slide-fade-leave-active {
  transition: all 0.3s cubic-bezier(1, 0.5, 0.8, 1);
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  transform: translateY(20px);
  opacity: 0;
}

.img {
  cursor: pointer;
  width: 72px;
  height: 72px;
  text-align: center;
  background: #ffffff;
  box-shadow: 0px 4px 8px 0px rgba(116, 157, 219, 0.3);
  border-radius: 24px 24px 24px 24px;

  img {
    margin-top: 10px;
  }
}

:deep(.ant-checkbox-group) {
  display: flex !important;
  justify-content: center !important;
  margin-right: 12px !important;
  align-items: center !important;
}

:deep(.ant-radio-group) {
  display: flex !important;
  justify-content: center !important;
  // margin-right: 12px !important;
  align-items: center !important;
}

.box {
  position: absolute;
  right: -30px;
  bottom: 85px;
  padding: 24px 24px 0 24px;
  background-color: rgba(255, 255, 255, 1);
  border-radius: 8px;
  color: rgba(0, 0, 0, 0.85);
  width: 600px;
  max-height: 560px;
  overflow-y: auto;
  z-index: 9999;
  box-shadow: 0px 4px 8px 0px rgba(116, 157, 219, 0.3);
}

.prepare {
  font-weight: 400;
  font-size: 12px;
  color: rgba(0, 0, 0, 0.65);
  text-align: center;
}
</style>

<style lang="scss" scoped src="./components/tableList.scss"></style>