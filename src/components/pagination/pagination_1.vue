<template>
  <div class="margin_t_16 textRight flex align-center just-end">
    <a-pagination
      class="mypage"
      v-model:current="currentPage"
      v-model:pageSize="pageSize"
      :total="total"
      show-less-items
      show-quick-jumper
      show-size-changer
      @showSizeChange="onShowSizeChange"
      @change="pageChange"
      :show-total="(total) => `共 ${total} 条`"
      :disabled="total == 0"
    />
  </div>
</template>

<script>
import { defineComponent, ref, reactive, toRefs, watch } from "vue";

export default defineComponent({
  name: "pagination_1",
  emits: ["size-change", "page-change"],
  props: {
    current: {
      type: Number,
      default() {
        return 1;
      },
    },
    total: {
      type: Number,
    },
  },
  setup(props, context) {
    const data = reactive({
      currentPage: 0,
      pageSize: 10,
    });

    const pageChange = (page) => {
      context.emit("page-change", page);
    };

    watch(
      () => props.current,
      (newV) => {
        data.currentPage = +newV;
      }
    );

    const onShowSizeChange = (current, size) => {
      data.pageSize = size;
      context.emit("size-change", data.pageSize);
    };
    data.currentPage = +props.current;

    return {
      ...toRefs(data),
      pageChange,
      onShowSizeChange,
    };
  },
});
</script>

<style lang="scss" >
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #0C70EB;
    border:none;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;
    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>
