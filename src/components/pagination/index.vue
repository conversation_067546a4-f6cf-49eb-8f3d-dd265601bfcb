<template>
  <div class="text-center">
    <a-pagination
      v-model:pageSize="pageItemSize"
      v-model:current="currentPage"
      :pageSizeOptions="pageSizeOptions"
      show-quick-jumper
      show-size-changer
      :total="totalItemCount"
      @change="pageChange"
      @showSizeChange="sizeChange"
      class="mypage"
    />
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";

export default defineComponent({
  emits: ["size-change", "page-change"],
  props: {
    currentPage: {
      type: Number,
      default() {
        return 1;
      },
    },
    pageItemSize: {
      type: Number,
      default() {
        return 10;
      },
    },
    totalItemCount: {
      type: Number,
      default() {
        return 0;
      },
    },
  },
  setup(props, context) {
    const data = reactive({
      pageSizeOptions: ["10", "20", "30", "50"],
      currentPage: props.currentPage,
      pageItemSize: props.pageItemSize,
    });
    watch(
      () => props.currentPage,
      (val) => {
        data.currentPage = +val;
      }
    );
    const pageChange = (page) => {
      context.emit("page-change", page);
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      context.emit("size-change", data.pageItemSize);
    };
    data.currentPage = +props.currentPage;
    return {
      ...toRefs(data),
      pageChange,
      sizeChange,
    };
  },
});
</script>

<style lang="scss" scoped></style>

<style  lang="scss">
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }
  .ant-pagination-item {
    background: #f8fafc;
  }
  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff !important;
  }
  .ant-pagination-item-disabled:focus,
  .ant-pagination-item-disabled:hover {
    background: #f8fafc !important;
  }
  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;
    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>