<template>
    <div>
        <div class="text">
            <a-form ref="addAbility" :model="formData" labelAlign="right" :rules="rules">
                <a-row>
                    <a-col :span="24">
                        <a-form-item label="工单名称" name="title">
                            <a-input v-model:value="formData.title" placeholder="请输入工单名称"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="24">
                        <a-form-item label="工单类型" name="type">
                            <a-input v-model:value="formData.type" disabled placeholder="请输入工单类型"></a-input>
                        </a-form-item>
                    </a-col>
                </a-row>
                <a-row>
                    <a-col :span="24">
                        <a-form-item label="工单描述" name="description" :rules="[
                            { required: true, message: '请输入工单描述' },
                            {
                                validator: valueValid,
                                trigger: 'blur',
                            },

                            { validator: descriptionDesc, trigger: 'blur' },
                        ]">
                            <a-textarea :rows="7" :showCount="true" :maxlength="200" placeholder="请输入工单描述，限制200个字符"
                                v-model:value="formData.description"></a-textarea>
                        </a-form-item>
                    </a-col>
                </a-row>
            </a-form>
        </div>
        <div class="btns flex just-center margin_t_10">
            <a-button class="margin_r_20" @click="cancel()">
                取消
            </a-button>
            <a-button type="primary" @click="confirm()">
                确定
            </a-button>
        </div>
    </div>

</template>

<script>
import { defineComponent, reactive, toRefs, ref, computed } from "vue";
import { valueValid } from "@/utils/index.js";
import { applyDownload } from "../../api/solutionNew/detail";
export default defineComponent({
    name: "reviewForm",
    emits: ["downloadFormCancel", "downloadFormConfirm"],
    components: {

    },
    setup(props, { emit }) {
        const addAbility = ref()
        const data = reactive({
            formData: {
                title: null,
                type: '下载申请',
                description: "",
                businessType: 0
            },
            rules: {
                title: [
                    { required: true, message: "请输入工单名称", trigger: "blur" }
                ],
                type: [
                    { required: true, message: "请输入工单类型", trigger: "blur" }
                ]
            }
        });
        // 取消
        const cancel = () => {
            console.log('取消')
            emit('downloadFormCancel')
        }
        // 确认
        const confirm = () => {
            addAbility.value.validate().then(() => {
                console.log('确定')
                applyDownload({
                    title: data.formData.title,
                    type: 4,
                    description: data.formData.description,
                    businessType: data.formData.businessType,
                }).then((res) => {
                    emit('downloadFormConfirm')
                })
            })

        }
        return {
            ...toRefs(data),
            valueValid,
            addAbility,
            cancel,
            confirm,
        };
    },
});
</script>

<style lang="scss" scoped></style>