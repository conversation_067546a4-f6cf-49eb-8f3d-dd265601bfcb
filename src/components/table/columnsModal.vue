<template>
  <div>
    <div>
      <a-checkbox
        v-model:checked="checkAll"
        :indeterminate="indeterminate"
        @change="onCheckAllChange"
      >
        列名
      </a-checkbox>
    </div>
    <a-divider />
    <a-checkbox-group style="height:400px" v-model:value="checkedList" :options="plainOptions" />
    <div class="flex just-center">
      <a-button class="margin_r_8" @click="cancel"> 取消 </a-button>
      <a-button type="primary" @click="submit"> 确定 </a-button>
    </div>
  </div>
</template>
<script>
import { defineComponent, toRefs, reactive, computed, watch } from "vue";
import searchJson from "@/json/search/monitors/hostSearch";
import tableJson from "@/json/table/monitors/hostTable";
export default defineComponent({
  name: "columnModal",
  emits: ["submit-ok", "submit-cancel"],
  props: {
    plainOptions: {
      type: Array,
      default() {
        return [];
      },
    },
    defaultSelected: {
      type: Array,
      default() {
        return [];
      },
    },
    disabledList: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  setup(props, { emit }) {
    const plainOptions = props.plainOptions;
    const data = reactive({
      searchJson,
      indeterminate: false,
      checkAll:true,
      checkedList: props.defaultSelected || [],
    });
    const onCheckAllChange = (e) => {
      let newArr = plainOptions.map((item) => {
        return item.value;
      });
      Object.assign(data, {
        checkedList: e.target.checked ? newArr : props.disabledList,
        indeterminate: false,
      });
    };
    const submit = () => {
      emit("submit-ok", data.checkedList);
    };
    const cancel = () => {
      emit("submit-cancel");
    };

    watch(
      () => data.checkedList,
      (val) => {
        data.indeterminate = !!val.length && val.length < plainOptions.length;
        data.checkAll = val.length === plainOptions.length;
      },
      { deep: true },
      { immediate: true }
    );
    return {
      ...toRefs(data),
      plainOptions,
      onCheckAllChange,
      submit,
      cancel,
    };
  },
});
</script>
<style lang="scss" scoped>
.ant-divider-horizontal {
  margin: 4px 0;
}
.ant-checkbox-wrapper {
  display: flex;
  width: 100%;
  padding-right: 16px;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
}
.ant-checkbox-group {
  width: 100%;
  overflow-y: scroll;
  padding-right: 10px;
}
::v-deep .ant-checkbox-group-item {
  display: flex;
  margin-right: 0;
  height: 36px;
  flex-direction: row-reverse;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid rgba(0, 0, 0, 0.06);
}
</style>