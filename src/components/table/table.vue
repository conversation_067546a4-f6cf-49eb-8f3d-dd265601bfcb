<template>
  <a-config-provider>
    <template #renderEmpty>
      <img alt="" class="maxwidth maxHeight" src="@/assets/images/nodata.png" />
      <p style="color: #a2abb5">{{ errMsg }}</p>
    </template>
    <a-table
      :columns="columns"
      :custom-row="rowClick"
      :data-source="tableValue"
      :loading="loading"
      :locale="locale"
      :pagination="false"
      :row-key="(record) => JSON.stringify(record)"
      :row-selection="rowSelection"
      :scroll="{ x: 1000 }"
      :size="size"
      :sticky="sticky"
      @change="tableChange"
      @resizeColumn="handleResizeColumn"
    >
      <template #headerCell="{ column }">
        {{ column.title }}
      </template>
      <template #bodyCell="{ column, index, record }">
        <template v-if="column.slot">
          <slot :name="column.dataIndex" :row="record"></slot>
        </template>
        <template v-else-if="column.key === 'index'">
          {{ index + 1 }}
        </template>
        <template v-else>
          {{ record[column.dataIndex] }}
        </template>
      </template>
    </a-table>
  </a-config-provider>
</template>

<script>
import { computed, defineComponent, reactive, toRefs, watch } from "vue";

export default defineComponent({
  name: "table_2",
  props: {
    errorMsg: {
      type: String,
      default() {
        return "暂无数据";
      },
    },
    tableKey: {
      type: Array,
      default() {
        return [];
      },
    },
    tableJson: {
      type: Object,
      default() {
        return null;
      },
    },
    tableValue: {
      type: Array,
      default() {
        return [];
      },
    },
    size: {
      type: String,
      default() {
        return "small";
      },
    },
    loading: {
      type: Boolean,
      default() {
        return false;
      },
    },
    rowSelection: {
      type: Object,
      default() {
        return null;
      },
    },
    sticky: {
      type: Boolean,
      default() {
        return false;
      },
    },
  },
  emits: ["check-file", "row-clicked", "table-change"],
  setup(props, context) {
    const data = reactive({
      selectedRowKeys: [],
      // Check here to configure the default column
      loading: props.loading,
      locale: {
        cancelSort: "点击取消排序",
        triggerAsc: "点击升序",
        triggerDesc: "点击降序",
      },
      errMsg: props.errorMsg,
    });
    const hasSelected = computed(() => data.selectedRowKeys.length > 0);
    const onSelectChange = (selectedRowKeys) => {
      data.selectedRowKeys = selectedRowKeys;
    };

    const columns = computed(() => {
      let arr = [];
      props.tableKey.forEach((key) => {
        arr.push(props.tableJson[key]);
      });
      return arr;
    });

    const checkFile = (record) => {
      context.emit("check-file", record);
    };

    const rowClick = (record, index) => {
      return {
        onclick: () => {
          context.emit("row-clicked", record, index);
        },
      };
    };

    const tableChange = (pag, filters, sorter) => {
      context.emit("table-change", sorter);
    };
    watch(
      () => props.loading,
      (newVal) => {
        data.loading = props.loading;
      }
    );
    watch(
      () => props.errorMsg,
      (newVal) => {
        data.errMsg = newVal;
      }
    );
    return {
      ...toRefs(data),
      columns,
      // dataJson,
      checkFile,
      hasSelected,
      rowClick,
      onSelectChange,
      tableChange,
      handleResizeColumn: (w, col) => {
        col.width = w;
      },
    };
  },
});
</script>

<style scoped>
.yellow {
  color: rgb(241, 191, 25);
}

.green {
  color: rgb(0, 165, 0);
}

.red {
  color: rgb(255, 104, 104);
}

.gray {
  color: #999;
}

.blue {
  color: #2491ff;
}
:deep(thead) {
  border-radius: 6px;
}

:deep(thead th) {
  background: #f5f6fa;
  font-weight: 500;
  font-size: 14px;
  color: #a2abb5;
}

:deep(tr, td) {
  height: 54px;
  padding: 8px 16px !important;
}


:deep(tbody td) {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 6, 14, 0.8);
  padding: 0 16px !important;
}
:deep(.ant-table-thead > tr > th) {
  border-bottom: none;
    padding: 8px 16px !important;
}
:deep(
    .ant-table-thead
      > tr
      > th:not(:last-child):not(.ant-table-selection-column):not(
        .ant-table-row-expand-icon-cell
      ):not([colspan])::before
  ) {
  display: none;
}
</style>
