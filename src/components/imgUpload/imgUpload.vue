<template>
  <a-form :rules="rules" :model="imgInfo" ref="imgForm">
    <a-row>
      <a-col :span="24">
        <a-form-item :label="imgInfo.label" name="imgUrl">
          <a-upload
            v-model:file-list="mainImgList"
            :accept="
              imgInfo.accept || 'image/png,image/jpeg,image/jpg,image/gif'
            "
            :customRequest="uploadMainImg"
            name="avatar"
            :multiple="false"
            list-type="picture-card"
            :show-upload-list="false"
            class="avatar-uploader"
            :before-upload="beforeUpload"
          >
            <img
              v-if="imgInfo.imgUrl"
              :src="`${imgInfo.imgUrl}`"
              class="m-img"
              alt=""
              @click.stop="showImg(imgInfo)"
            />
            <div v-else>
              <loading-outlined v-if="uploading"></loading-outlined>
              <plus-outlined class="font_A2ABB5" v-else></plus-outlined>
              <div class="ant-upload-text font_A2ABB5 font_14">上传</div>
            </div>
          </a-upload>
          <div
            class="deleteImg font_0C70EB"
            v-if="imgInfo.imgUrl && !imgInfo.config"
            @click="deleteImg"
          >
            删除
          </div>
          <span class="font_A2ABB5 font_12">{{
            imgInfo.imgHint ||
            "支持jpg、jpeg、png,gif格式的图片上传，文件限制为10M"
          }}</span>
        </a-form-item>
      </a-col>
    </a-row>
  </a-form>
  <a-modal
    :visible="previewVisible"
    centered
    :footer="null"
    @cancel="handleCancel"
  >
    <img alt="example" style="width: 100%" :src="imgUrl" />
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { message } from "ant-design-vue";

import { PlusOutlined, LoadingOutlined } from "@ant-design/icons-vue";
import { uploadFileList } from "@/api/notice/notice.js";

export default defineComponent({
  name: "addForm",
  components: { PlusOutlined, LoadingOutlined },
  emits: ["update-url", "validate-result"],
  props: {
    imgInfo: {
      type: Object,
      default: "",
    },
  },
  setup(props, { emit }) {
    const imgForm = ref();

    const validateMainImg = async (rule, value) => {
      if (data.imgInfo.imgUrl) {
        return Promise.resolve();
      }
      return Promise.reject("请上传图片");
    };

    const data = reactive({
      mainImgList: [],
      uploading: false,
      flag: false,
      imgInfo: props.imgInfo,
      previewVisible: false,
      imgUrl: "",
      rules:
        props.imgInfo.required == false
          ? { imgUrl: [] }
          : {
              imgUrl: {
                required: true,
                validator: validateMainImg,
                trigger: "change",
              },
            },
    });

    // 删除
    const deleteImg = () => {
      data.imgInfo.imgUrl = "";
      emit("update-url", data.imgInfo);
    };

    const uploadMainImg = (info) => {
      uploadFile(info, "img");
    };
    const uploadFile = (info) => {
      data.uploading = true;
      let formData = new FormData();
      formData.append("file", info.file);
      if (data.imgInfo.compress) {
        formData.append("compress", true);
      }
      uploadFileList(formData).then(result => {
	      if (result.code == 200) {
	        if (data.imgInfo.compress) {
	          data.imgInfo.imgUrl = result.data.compressFileUrl;
	        } else {
	          data.imgInfo.imgUrl = result.data.fileUrl;
	        }
	        emit("update-url", data.imgInfo);
	        message.success("上传成功");
	        data.uploading = false;
	        info.onSuccess();
	        data.uploading = false;
	        return false;
	      } else {
		      data.uploading = false;
		      message.success("上传失败");
	      }
      }).catch(err => {
      	data.uploading = false;
      });
    };

    const beforeUpload = (file, fileList) => {
      if (data.uploading) {
        message.warning("正在上传中，请稍后再试");
        data.uploading = false;
        return Promise.reject();
      }
      return new Promise((resolve, reject) => {
        if (file.size > 10485760) {
          message.warning("单个文件总大小不能超过10M");
          data.uploading = false;
          reject();
        }
        resolve();
        return;
      });
    };

    const validateImg = async () => {
      return await imgForm.value.validate();
    };

    watch(
      () => props.imgInfo,
      (newVal) => {
        data.imgInfo = newVal;
      },
      {
        immediate: true,
        deep: true,
      }
    );
    const showImg = (file) => {
      data.imgUrl = file.imgUrl;
      data.previewVisible = true;
    };
    const handleCancel = () => {
      data.previewVisible = false;
    };
    return {
      ...toRefs(data),
      imgForm,
      handleCancel,
      showImg,
      validateImg,
      deleteImg,
      beforeUpload,
      uploadMainImg,
    };
  },
});
</script>

<style lang="scss" scoped>
:deep(.ant-form-item-label) {
  width: 100px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}
:deep(.ant-upload) {
  border-radius: 4px;
  border: none !important;
}

.deleteImg {
  position: absolute;
  cursor: pointer;
  left: 110px;
  top: 80px;
  width: max-content;
}
.m-img {
  max-width: 78px;
  max-height: 78px;
}
.fileBtn {
  font-weight: 500;
  font-size: 14px;
  color: #ffffff;
  background: #0c70eb;
  border-radius: 4px;
}
</style>