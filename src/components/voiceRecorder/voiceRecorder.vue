<template>
    <div class="voiceRecorder flex align-center ">
        <a-button class="pointer" :disabled="canBtnUse" @click="useMicrophone()">
            <!-- <button @click="toggleRecording">{{ isRecording ? '停止录音' : '录音' }}</button> -->
            <img class="pic" v-if="!openMicrophone && !isTranslating" src="../../assets/images/AI/microphone.png"
                alt="">
            <img class="pic" v-if="openMicrophone && !isTranslating" src="../../assets/images/AI/speaking.png" alt="">
            <div class="loading-spinner" v-if="isTranslating"></div>
        </a-button>
    </div>

</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
let recorder; // 全局定义录音器实例
let audioChunks = [];
export default defineComponent({
    emits: ['audioReady'],
    props: {
        isTranslating: {
            type: Boolean,
            default: false,
        },
        canBtnUse: {
            type: Boolean,
            default: false,
        }
    },
    setup(props, { emit }) {
        const data = reactive({
            openMicrophone: false,//是否打开麦克风
            isTranslating: props.isTranslating,//正在编译
            canBtnUse: props.canBtnUse,
        });
        // 使用麦克风
        const useMicrophone = () => {
            console.log('点击麦克风按钮')
            // 麦克风未使用时，打开麦克风进行语音输入
            if (!data.openMicrophone) {
                // 开始录音
                console.log('打开了麦克风')
                navigator.mediaDevices.getUserMedia({ audio: true })
                    .then((stream) => {

                        console.log('调用了麦克风录音')
                        // 初始化录音器
                        recorder = new MediaRecorder(stream);
                        audioChunks = []; // 每次录音前清空
                        // 当有录音数据时，将其添加到音频块数组
                        recorder.ondataavailable = (event) => {
                            audioChunks.push(event.data);
                        };
                        // 开始录音
                        recorder.start();
                        data.openMicrophone = true
                        console.log('录音开始');
                    })
                    .catch((error) => {
                        console.error('无法获取音频流', error);
                    });
            } else {
                console.log('点击按钮关闭麦克风')
                // 麦克风使用完毕，录音结束以后，调接口翻译录音内容
                recorder.stop(); // 停止录音
                data.openMicrophone = false
                console.log('录音停止');

                // 当录音停止时，处理录音数据
                recorder.onstop = () => {
                    // 将录音数据块合并成一个 Blob (wav 格式)
                    const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                    // 清空音频数据块
                    audioChunks = [];

                    // 发出事件，传递音频数据
                    emit('audioReady', audioBlob);
                };
            }
        }
        watch(
            () => props.isTranslating,
            (newV) => {
                data.isTranslating = newV
            }
        )
        watch(
            () => props.canBtnUse,
            (newV) => {
                data.canBtnUse = newV
            }
        )
        return {
            ...toRefs(data),
            useMicrophone
        };
    },
});
</script>

<style lang="scss" scoped>
.voiceRecorder {
    height: 56px;
    background-color: #fff;

    .pic {
        width: 24px;
        height: 24px;
    }

    /* 加载动画 */
    .loading-spinner {
        border: 4px solid rgba(0, 0, 0, 0.1);
        border-left-color: #007bff;
        border-radius: 50%;
        width: 20px;
        height: 20px;
        animation: spin 1s linear infinite;
    }

    @keyframes spin {
        0% {
            transform: rotate(0deg);
        }

        100% {
            transform: rotate(360deg);
        }
    }

    :deep(.ant-btn) {
        border: none;
    }

    :deep(.ant-btn[disabled]) {
        border: none;
        background-color: #fff;
    }
}
</style>