<template>
    <!-- 新版畅sir说有点意思的语音 -->
    <div class="voiceRecorder flex align-center ">
        <button class="flex just-center align-center"
            style="background: linear-gradient( 270deg, #0142FD 0%, #2475F9 100%); width: 104px;height: 48px;"
            @click="handleRecording" :disabled="recordStatus === 'CONNECTING'">
            <div style="font-size: 18px;font-weight: 500;color: #fff;" v-if="recordStatus === 'CLOSED'">开始</div>
            <div style="font-size: 18px;font-weight: 500;color: #fff;" v-if="recordStatus === 'OPEN'">结束</div>
            <div class="loading-spinner" v-if="recordStatus === 'CONNECTING' || recordStatus === 'CLOSING'"></div>
        </button>
    </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { ref } from 'vue'
import CryptoJS from 'crypto-js'
import RecorderManager from './record/index.esm.js'
import { message } from "ant-design-vue";
export default defineComponent({
    props: {
    },
    setup(props, { emit }) {
        // 状态定义s
        const recordLabel = ref('开始录音')
        const recordStatus = ref('CLOSED')
        const appId = '020fa0c7'
        const apiKey = 'dce447720fd1de676443e5af1beba0d0'
        const websocket = ref(null)
        const recorder = ref(null)
        const resultText = ref('') // 一整句句话，负责完整展示
        const resultTextTemp = ref('') // 组成一句话的词
        const timer = ref(null)
        const data = reactive({
        })

        // 方法定义
        const changeBtnStatus = (status) => {
            recordStatus.value = status
            if (status === "CONNECTING") {
                recordLabel.value = "建立连接中"
                resultText.value = ""
                resultTextTemp.value = ""
            } else if (status === "OPEN") {
                recordLabel.value = "录音中"
            } else if (status === "CLOSING") {
                recordLabel.value = "关闭连接中"
            } else if (status === "CLOSED") {
                recordLabel.value = "开始录音"
            }
        }

        const handleRecording = () => {
            if (recordStatus.value === "UNDEFINED" || recordStatus.value === "CLOSED") {
                // emit('clearMicWords')
                startRecording()
                emit('startSpeaking')
            } else if (recordStatus.value === "CONNECTING" || recordStatus.value === "OPEN") {
                console.log('停止录音')
                recorder.value.stop()
                // clearTimeout(timer.value)
                // timer.value = null
                emit('getVoiceWord')
            }
        }

        const initRecorder = async () => {
            return new Promise((resolve, reject) => {
                try {
                    const recorderInstance = new RecorderManager("/js/processor")
                    recorder.value = recorderInstance

                    recorderInstance.onStart = () => {
                        console.log('录音开始')
                        changeBtnStatus("OPEN")
                    }

                    recorderInstance.onFrameRecorded = ({ isLastFrame, frameBuffer }) => {
                        if (websocket.value && websocket.value.readyState === websocket.value.OPEN) {
                            websocket.value.send(new Int8Array(frameBuffer))
                            if (isLastFrame) {
                                websocket.value.send('{"end": true}')
                                changeBtnStatus("CLOSING")
                            }
                        }
                    }

                    recorderInstance.onStop = () => {
                        console.log('录音停止')
                    }

                    resolve()
                } catch (error) {
                    console.error('RecorderManager 创建失败:', error)
                    reject(error)
                }
            })
        }

        const startRecording = async () => {
            await initRecorder()
            console.log('录音初始化成功')
            connectWebSocket()
        }

        const getWebSocketUrl = () => {
            const url = 'wss://rtasr.xfyun.cn/v1/ws'
            const ts = Math.floor(new Date().getTime() / 1000)
            const baseString = appId + ts
            const md5Str = CryptoJS.MD5(baseString).toString()
            const signa = CryptoJS.enc.Base64.stringify(CryptoJS.HmacSHA1(md5Str, apiKey))
            return `${url}?appid=${appId}&ts=${ts}&signa=${encodeURIComponent(signa)}`
        }

        const handleResponse = (response) => {
            let jsonData = JSON.parse(response)
            if (jsonData.action == "started") {
                console.log("握手成功")
            } else if (jsonData.action === 'result') {
                try {
                    // 返回的结果
                    const resultData = JSON.parse(jsonData.data)
                    // 一句话中每个词
                    resultTextTemp.value = ''
                    resultData.cn.st.rt.forEach((j) => {
                        j.ws.forEach((k) => {
                            k.cw.forEach((l) => {
                                resultTextTemp.value += l.w
                            })
                        })
                    })
                    // 遍历完成，拿到一句话
                    // 判断语句是否结束,0-最终结果
                    if (resultData.cn.st.type !== "0") {
                        emit('audioReady', resultText.value + resultTextTemp.value);
                    }
                    if (resultData.cn.st.type === "0") {
                        resultText.value += resultTextTemp.value
                        emit('audioReady', resultText.value);
                    }
                } catch (error) {
                    console.error('解析识别结果失败:', error)
                }
            } else if (jsonData.action == "error") {
                console.log("出错了:", jsonData)
                recorder.value.stop()
            }
        }

        const connectWebSocket = () => {
            const url = getWebSocketUrl()
            if (!("WebSocket" in window)) {
                alert("浏览器不支持WebSocket")
                return
            }

            websocket.value = new WebSocket(url)
            changeBtnStatus("CONNECTING")

            websocket.value.onopen = () => {
                // 提示用户可以开始说话，在一分钟内完成描述
                message.success('您可以用一分钟语音描述一下需求')
                recorder.value.start({
                    sampleRate: 16000,
                    frameSize: 1280
                })
            }

            // 设置1分钟后自动关闭WebSocket连接
            timer.value = setTimeout(() => {
                // 提示用户语音识别即将结束
                message.warning('语音识别即将结束')
                if (websocket.value && websocket.value.readyState === websocket.value.OPEN) {
                    websocket.value.send('{"end": true}')
                    changeBtnStatus("CLOSING")
                }
            }, 60000)

            websocket.value.onmessage = (e) => {
                handleResponse(e.data)
            }

            websocket.value.onerror = () => {
                console.error('WebSocket错误')
                recorder.value.stop()
                changeBtnStatus("CLOSED")
            }

            websocket.value.onclose = () => {
                console.log('WebSocket连接已关闭')
                recorder.value.stop()
                changeBtnStatus("CLOSED")
                clearTimeout(timer.value)
                timer.value = null
            }
        }

        const closeConnection = () => {
            if (websocket.value && recordStatus.value !== 'CLOSED') {
                websocket.value.send('{"end": true}')
                changeBtnStatus("CLOSING")
            }
        }
        return {
            handleRecording,
            recordLabel,
            recordStatus,
            resultText,
            closeConnection,
            timer
        };
    }
})


</script>

<style scoped>
.hello {
    padding: 20px;
}

button {
    font-size: 16px;
    cursor: pointer;
    background-color: #fff;
    color: white;
    border: none;
    border-radius: 4px;
}

button:disabled {
    background-color: #ccc;
    cursor: not-allowed;
}

#result {
    margin-top: 20px;
    padding: 10px;
    min-height: 100px;
    border: 1px solid #ddd;
    border-radius: 4px;
    white-space: pre-wrap;
}

.pic {
    width: 24px;
    height: 24px;
}

.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #fff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

.loading-spinner_newYear {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #FF5900FF;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}
</style>