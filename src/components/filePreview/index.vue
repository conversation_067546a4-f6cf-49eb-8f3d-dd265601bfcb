<template>
  <div class="dialog_content">
    <vue-office-docx
      :src="typeFile"
      style="width: 100%; height: 100vh"
      v-if="typeFile.split('.').pop() == 'docx'"
    />
    <vue-office-excel
      :src="typeFile"
      style="width: 100%; height: 100vh"
      v-if="typeFile.split('.').pop() == 'xlsx'"
    />
    <iframe
      :src="typeFile"
      style="width: 100%; height: 100vh"
      frameborder="0"
      v-if="typeFile.split('.').pop() == 'pdf'"
      title=""
    />
    <div
      class="txt"
      v-if="typeFile.split('.').pop() == 'txt'"
      style="width: 100%; height: 100vh"
      :src="typeFile"
    >
      {{ textContent }}
    </div>

    <iframe
      :src="typeFile"
      title=""
      v-if="typeFile.split('.').pop() == 'jpg' || 'png' || 'jpeg'"
      style="z-index: 1000; height: 650px; width: 100%; margin: 0 auto"
      sandbox="allow-scripts allow-top-navigation allow-same-origin allow-popups"
    />
    <div
      v-if="excelData && excelData.length && typeFile.split('.').pop() == 'xls'"
      style="width: 100%; height: 100vh"
    >
      <table>
        <thead>
          <tr>
            <th v-for="(header, index) in excelData[0]" :key="index">
              {{ header }}
            </th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="(row, rowIndex) in excelData.slice(1)" :key="rowIndex">
            <td v-for="(cell, cellIndex) in row" :key="cellIndex">
              {{ cell }}
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import axios from "axios";
import VueOfficeDocx from "@vue-office/docx";
import VueOfficeExcel from "@vue-office/excel";
import * as XLSX from "xlsx";

import "@vue-office/excel/lib/index.css";
import "@vue-office/docx/lib/index.css";
import { useRoute } from "vue-router";

export default defineComponent({
  components: {
    VueOfficeDocx,
    VueOfficeExcel,
  },
  props: {
    typeFile: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const data = reactive({
      typeFile: props.typeFile,
      excelData: [],
    });
    // 使用 useRoute 获取参数
    const route = useRoute();
    const fileUri = route.query.docxFile;

    const loadExcelFile = async () => {
      if (props.typeFile.split(".").pop() == "xls") {
        const response = await fetch(props.typeFile);
        const blob = await response.blob();
        const reader = new FileReader();
        reader.onload = (e) => {
          const fileData = new Uint8Array(e.target.result);
          const workbook = XLSX.read(fileData, { type: "array" });
          const sheetName = workbook.SheetNames[0];
          const worksheet = workbook.Sheets[sheetName];
          const jsonData = XLSX.utils.sheet_to_json(worksheet, { header: 1 });
          data.excelData = jsonData;
        };
        reader.readAsArrayBuffer(blob);
      }
    };
    const textContent = ref("");
    const transformData = (data) => {
      return new Promise((resolve) => {
        let reader = new FileReader();
        reader.readAsText(data, ""); //这里如果出现乱码可改成reader.readAsText(data, "")试试
        // http://192.168.10.56:8829//xhly/resource/DocumentsAndFees/13749cf1-0d4d-42af-b6f9-81e80f2e3d90.txt
        reader.onload = () => {
          resolve(reader.result);
        };
      });
    };
    axios
      .get(fileUri, {
        responseType: "blob",
        transformResponse: [
          async function (data) {
            return await transformData(data);
          },
        ],
      })
      .then((res) => {
        res.data.then((data) => {
          textContent.value = data; // 编译好的txt重新赋值给textContent
        });
      });

    loadExcelFile();

    return {
      ...toRefs(data),
      textContent,
      transformData,
    };
  },
});
</script>

<style lang="scss" scoped>
.dialog_content {
  max-height: 100vh;
  overflow: auto;
}
.dialog_content::-webkit-scrollbar {
  display: block;
}
table {
  width: 100%;
  border-collapse: collapse;
}

th,
td {
  border: 1px solid #ddd;
  padding: 8px;
  text-align: left;
  max-width: 150px;
  overflow: hidden;
  white-space: nowrap;
  text-overflow: ellipsis;
}
</style>