<template>
    <div id="phrase_box1">
        <div>
            <div class="boxLoading">
                <span class="circle"></span>
                <span class="item">{{ writingFirstText }}</span>
                <a-spin v-if="firstShow" :indicator="indicator" size="small" />
                <img v-else src="../../assets/dui.svg" alt="" />
            </div>
            <div class="boxLoading" v-if="showSecond">
                <span class="circle"></span>
                <span class="item" :class="{ 'fade-out': isFading }">{{ writingSecondText }}</span>
                <a-spin v-if="secondShow" :indicator="indicator" size="small" />
                <img v-else src="../../assets/dui.svg" alt="" />
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, onMounted, reactive, toRefs, watch } from "vue";
import { getSemantics } from "@/api/AI/ai.js";
export default defineComponent({
    emits: [],
    props: {
        searchQuestion: {
            type: String,
            default: ""
        }
    },
    setup(props, { emit }) {
        const data = reactive({
            firstShow: true,
            secondShow: true,
            showSecond: false,
            searchQuestion: props.searchQuestion,
            firstText: "正在进行语义分析",
            writingFirstText: "",
            writingSecondText: "",
            index1: 0, // 用于第一行打字机效果
            index2: 0, // 用于第二行打字机效果
            currentTextIndex: 0, // 当前正在打印的文字段索引（1-5）
            showWordsList: [],
            isFading: false, // 控制淡出动画
        });

        // 第一行打字机效果
        const typeWriterFirst = () => {
            if (data.index1 < data.firstText.length) {
                data.writingFirstText += data.firstText.charAt(data.index1);
                data.index1++;
                setTimeout(typeWriterFirst, 50); // 每隔 50ms 打印一个字
            }
        };

        // 第二行打字机效果
        const typeWriterSecond = () => {
            let currentText = "";
            if (data.currentTextIndex < data.showWordsList.length) {
                currentText = data.showWordsList[data.currentTextIndex];
                if (data.index2 < currentText.length) {
                    data.writingSecondText += currentText.charAt(data.index2);
                    data.index2++;
                    setTimeout(typeWriterSecond, 50); // 每隔 50ms 打印一个字
                } else {
                    // 添加淡出动画类
                    if (data.currentTextIndex < data.showWordsList.length - 1) {
                        setTimeout(() => {
                            data.secondShow = false; // loading结束显示√
                        }, 500);
                        data.isFading = true;
                    }
                    setTimeout(() => {
                        // 如果后续还有需要打印的文字，则继续
                        data.index2 = 0; // 重置索引
                        if (data.currentTextIndex < data.showWordsList.length - 1) {
                            data.isFading = false
                            data.writingSecondText = ""; // 清空当前显示的文字
                        }
                        data.secondShow = true; // 重新显示 loading 状态
                        data.currentTextIndex++; // 切换到下一段文字
                        typeWriterSecond(); // 启动下一段文字的打字机效果
                    }, 1000); // 延迟 1 秒
                }
            }
        };

        const getSemanticsInfo = () => {
            data.showWordsList = [];
            getSemantics({
                question: data.searchQuestion
            }).then((res) => {
                console.log('res', res);
                if (res.data.solutionList) {
                    for (let i of res.data.solutionList) {
                        if (i.key !== '') {
                            data.showWordsList.push('正在为您匹配' + i.key + '的方案');
                        } else {
                            data.showWordsList.push('正在为您匹配' + i.description + '的方案');
                        }
                    }
                }
                if (res.data.abilityList) {
                    for (let i of res.data.abilityList) {
                        if (i.key !== '') {
                            data.showWordsList.push('正在为您检索' + i.key + '的能力');
                        } else {
                            data.showWordsList.push('正在为您检索' + i.description + '的能力');
                        }
                    }
                }
                if (res.data.sceneList) {
                    for (let i of res.data.sceneList) {
                        if (i.key !== '') {
                            data.showWordsList.push('正在为您搜索' + i.key + '的方案场景');
                        } else {
                            data.showWordsList.push('正在为您搜索' + i.description + '的方案场景');
                        }
                    }
                }
                if (res.data.productList) {
                    for (let i of res.data.productList) {
                        if (i.key !== '') {
                            data.showWordsList.push('正在为您查找' + i.key + '的产品');
                        } else {
                            data.showWordsList.push('正在为您查找' + i.description + '的产品');
                        }
                    }
                }
                if (res.data.productPackageList) {
                    for (let i of res.data.productPackageList) {
                        if (i.key !== '') {
                            data.showWordsList.push('正在为您查询' + i.key + '的商客场景');
                        } else {
                            data.showWordsList.push('正在为您查询' + i.description + '的商客场景');
                        }
                    }
                }
                console.log('data.showWordsList', data.showWordsList);
                data.firstShow = false; // 第一行完成后，取消 loading 状态
                data.showSecond = true; // 显示第二行
                typeWriterSecond(); // 启动第二行打字机效果
            });
        };

        onMounted(() => {
            getSemanticsInfo();
            typeWriterFirst(); // 启动第一行打字机效果
        });

        watch(
            () => props.searchQuestion,
            (newVal) => {
                data.searchQuestion = newVal;
            }
        );

        return {
            ...toRefs(data),
        };
    },
});
</script>

<style lang="scss" scoped>
.item {
    font-size: 14px !important;
    margin-right: 14px;
    line-height: 20px;
    margin-right: 10px;
    color: #595959;
    text-indent: 2em;
    margin-left: 8px;
    transition: color 1s ease; // 添加颜色过渡效果
}

.fade-out {
    color: #fff; // 淡出到白色
}

.boxLoading {
    align-items: center;
    margin-bottom: 8px !important;
    height: 27px;
}

.boxLoading img {
    margin-bottom: 4px;
    width: 12px;
    height: 12px;
}

#phrase_box1 {
    padding-top: 15px;
    display: flex;
    flex-flow: column;
    overflow: hidden;
    width: 1200px;
    background: #ffffff;
    margin-left: 8px;
    border-radius: 12px;
    padding-left: 24px;
    margin: 0 auto;
}

.close_load {
    animation: close 6s;
}

.circle {
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background: #0C70EB;
    display: inline-block;
    margin-bottom: 4px;
}

@keyframes close {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}
</style>