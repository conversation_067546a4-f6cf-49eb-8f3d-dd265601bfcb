<template>
    <div id="phrase_box1">
        <div>
            <div class="boxLoading">
                <span class="circle"></span>
                <span class="item">{{ writingFirstText }}</span>
                <a-spin v-if="firstShow" :indicator="indicator" size="small" />
                <img v-else src="../../assets/dui.svg" alt="" />
            </div>
            <div class="boxLoading" v-if="showSecond && showWordsList.length !== 0">
                <span class="circle"></span>
                <span class="item">{{ writingSecondText }}</span>
                <a-spin v-if="secondShow" :indicator="indicator" size="small" />
                <img v-else src="../../assets/dui.svg" alt="" />
            </div>
            <div class="boxLoading" v-if="showThird && showWordsSecondList.length !== 0">
                <span class="circle"></span>
                <span class="item">{{ writingThirdText }}</span>
                <a-spin v-if="thirdShow" :indicator="indicator" size="small" />
                <img v-else src="../../assets/dui.svg" alt="" />
            </div>
        </div>
    </div>
</template>

<script>
import { defineComponent, onMounted, reactive, toRefs, watch } from "vue";
import { getSemantics } from "@/api/AI/ai.js";
export default defineComponent({
    emits: ['getCustomizeType'],
    props: {
        searchQuestion: {
            type: String,
            default: ""
        }
    },
    setup(props, { emit }) {
        const data = reactive({
            firstShow: true,
            secondShow: true,
            showSecond: false,
            showThird: false,
            thirdShow: true,
            searchQuestion: props.searchQuestion,
            firstText: "正在进行语义分析",
            fourthText: '网络较慢，正在努力为您匹配合适的内容，请耐心等待',
            writingFirstText: "",
            writingSecondText: "",
            writingThirdText: "",
            index1: 0, // 用于第一行打字机效果
            index2: 0, // 用于第二行打字机效果
            index3: 0, // 用于第三行打字机效果
            index4: 0, // 用于提示词打字机效果
            currentTextIndex: 0, // 第二行正在打印的文字段索引
            currentTextSecondIndex: 0,// 第三行正在打印的文字段索引
            showWordsList: [],//第二行需要打印的文字数组
            showWordsSecondList: [],//第三行需要打印的文字数组
        });

        // 第一行打字机效果
        const typeWriterFirst = () => {
            if (data.index1 < data.firstText.length) {
                data.writingFirstText += data.firstText.charAt(data.index1);
                data.index1++;
                setTimeout(typeWriterFirst, 50); // 控制打字机频率，每50ms打印一个字
            }
        };
        // 第二行打字机效果
        const typeWriterSecond = () => {
            let currentText = "";
            if (data.currentTextIndex < data.showWordsList.length) {
                // 第二行的数据列表没有完全打印结束
                currentText = data.showWordsList[data.currentTextIndex]
                if (data.index2 < currentText.length) {
                    data.writingSecondText += currentText.charAt(data.index2); //.charAt方法获取字符串每个字符的索引
                    data.index2++;
                    setTimeout(typeWriterSecond, 50); // 控制打字机频率，每50ms打印一个字
                } else {
                    if (!(data.currentTextIndex == data.showWordsList.length - 1 && data.showWordsSecondList.length == 0)) {
                        // 如果当前是第二行的数组的最后一项，且第三行数组没有数据时，应当保持loading，当前取反，只要不是以上条件，则在loading0.5s以后变成√
                        setTimeout(() => {
                            data.secondShow = false; // loading结束显示√
                        }, 1000);
                    }
                    setTimeout(() => {
                        // 如果后续还有需要打印的文字，则继续
                        data.index2 = 0; // 重置索引
                        if (data.currentTextIndex < data.showWordsList.length - 1) {
                            data.writingSecondText = ""; // 如果当前不是数组里的最后一项则清空当前显示的文字（是最后一项则保留文字）
                        }
                        data.secondShow = true; // 重新显示 loading 状态
                        data.currentTextIndex++; // 切换到下一段文字
                        typeWriterSecond(); // 启动下一段文字的打字机效果
                    }, 1500); // 延迟 1 秒
                }
            } else {
                // 第二行的数据列表已完全打印结束
                if (data.showWordsSecondList.length !== 0) {
                    // 如果第三行数据的数组里有内容，则启动第三行打字机
                    data.showThird = true
                    data.secondShow = false
                    typeWriterThird()
                } else {
                    // 如果第三行数据的数组里没有内容，则6秒后启动提示词打字机，覆盖第二行的内容
                    setTimeout(() => {
                        data.writingSecondText = ""
                        typeWriterFourth()
                    }, 6000);
                }

            }
        };

        // 第三行打字机效果
        const typeWriterThird = () => {
            let currentText = "";
            if (data.currentTextSecondIndex < data.showWordsSecondList.length) {
                // 第三行的数据列表没有完全打印结束
                currentText = data.showWordsSecondList[data.currentTextSecondIndex]
                if (data.index3 < currentText.length) {
                    data.writingThirdText += currentText.charAt(data.index3);
                    data.index3++;
                    setTimeout(typeWriterThird, 50); // 控制打字机频率，每50ms打印一个字
                } else {
                    // 当前项打印完成
                    if (data.currentTextSecondIndex < data.showWordsSecondList.length - 1) {
                        setTimeout(() => {
                            data.thirdShow = false; // loading结束显示√
                        }, 1000);
                    }
                    setTimeout(() => {
                        // 因为没有完全打印完成，1秒后打印下一项
                        data.index3 = 0; // 重置索引
                        if (data.currentTextSecondIndex < data.showWordsSecondList.length - 1) {
                            data.writingThirdText = ""; // 如果当前不是数组里的最后一项则清空当前显示的文字（是最后一项则保留文字）
                        }
                        data.thirdShow = true; // 重新显示 loading 状态
                        data.currentTextSecondIndex++; // 切换到下一段文字
                        typeWriterThird(); // 启动下一段文字的打字机效果
                    }, 1500); // 延迟 1 秒
                }
            } else {
                // 数组内容已经全部打印完毕，但是因为接口太慢，6秒后给出提示：网络较慢，正在努力为您匹配合适的内容，请耐心等待
                setTimeout(() => {
                    data.writingThirdText = ""
                    typeWriterFourth()
                }, 6000);
            }
        };

        // 提示语打字机效果
        const typeWriterFourth = () => {
            if (data.showWordsSecondList.length == 0) {
                // 如果第三行的数据数组里没有内容，则提示词覆盖的是第二行
                if (data.index4 < data.fourthText.length) {
                    data.writingSecondText += data.fourthText.charAt(data.index4);
                    data.index4++;
                    setTimeout(typeWriterFourth, 50);// 控制打字机频率，每50ms打印一个字
                }
            } else {
                // 如果第三行的数据数组里有内容，则提示词覆盖的是第三行
                if (data.index4 < data.fourthText.length) {
                    data.writingThirdText += data.fourthText.charAt(data.index4);
                    data.index4++;
                    setTimeout(typeWriterFourth, 50);// 控制打字机频率，每50ms打印一个字
                }
            }

        };
        // 调接口拿语义数据并处理
        const getSemanticsInfo = () => {
            data.showWordsList = []
            data.showWordsSecondList = []
            getSemantics({
                question: data.searchQuestion
            }).then((res) => {
                console.log('res', res);
                // 把方案和产品的内容放在第二行加载
                // 把能力、场景、产品包放在第三行加载（范xx的点子）
                if (res.data.solutionList) {
                    for (let i of res.data.solutionList) {
                        if (i.key !== '' || i.description !== '') {
                            if (i.key !== '') {
                                data.showWordsList.push('正在为您匹配' + i.key + '的DICT解决方案')
                            } else {
                                data.showWordsList.push('正在为您匹配' + i.description + '的DICT解决方案')
                            }
                        }
                    }
                }
                if (res.data.productList) {
                    for (let i of res.data.productList) {
                        if (i.key !== '' || i.description !== '') {
                            if (i.key !== '') {
                                data.showWordsList.push('正在为您查找' + i.key + '的产品')
                            } else {
                                data.showWordsList.push('正在为您查找' + i.description + '的产品')
                            }
                        }
                    }
                }
                // if (res.data.abilityList) {
                //     for (let i of res.data.abilityList) {
                //         if (i.key !== '' || i.description !== '') {
                //             if (i.key !== '') {
                //                 data.showWordsSecondList.push('正在为您检索' + i.key + '的功能')
                //             } else {
                //                 data.showWordsSecondList.push('正在为您检索' + i.description + '的功能')
                //             }
                //         }
                //     }
                // }
                // if (res.data.sceneList) {
                //     for (let i of res.data.sceneList) {
                //         if (i.key !== '' || i.description !== '') {
                //             if (i.key !== '') {
                //                 data.showWordsSecondList.push('正在为您搜索' + i.key + '的功能')
                //             } else {
                //                 data.showWordsSecondList.push('正在为您搜索' + i.description + '的功能')
                //             }
                //         }
                //     }
                // }
                // if (res.data.productPackageList) {
                //     for (let i of res.data.productPackageList) {
                //         if (i.key !== '' || i.description !== '') {
                //             if (i.key !== '') {
                //                 data.showWordsSecondList.push('正在为您查询' + i.key + '的商客方案')
                //             } else {
                //                 data.showWordsSecondList.push('正在为您查询' + i.description + '的商客方案')
                //             }
                //         }
                //     }
                // }
                // 处理 abilityList、sceneList 和 productPackageList
                const abilityList = res.data.abilityList || [];
                const sceneList = res.data.sceneList || [];
                const productPackageList = res.data.productPackageList || [];

                // 获取三个数组的最大长度
                const maxLength = Math.max(abilityList.length, sceneList.length, productPackageList.length);

                // 按照索引顺序提取数据
                for (let i = 0; i < maxLength; i++) {
                    if (abilityList[i] && (abilityList[i].key !== '' || abilityList[i].description !== '')) {
                        if (abilityList[i].key !== '') {
                            data.showWordsSecondList.push('正在为您搜索' + abilityList[i].key + '的功能');
                        } else {
                            data.showWordsSecondList.push('正在为您搜索' + abilityList[i].description + '的功能');
                        }
                    }
                    if (sceneList[i] && (sceneList[i].key !== '' || sceneList[i].description !== '')) {
                        if (sceneList[i].key !== '') {
                            data.showWordsSecondList.push('正在为您搜索' + sceneList[i].key + '的功能');
                        } else {
                            data.showWordsSecondList.push('正在为您搜索' + sceneList[i].description + '的功能');
                        }
                    }
                    if (productPackageList[i] && (productPackageList[i].key !== '' || productPackageList[i].description !== '')) {
                        if (productPackageList[i].key !== '') {
                            data.showWordsSecondList.push('正在为您查询' + productPackageList[i].key + '的商客方案');
                        } else {
                            data.showWordsSecondList.push('正在为您查询' + productPackageList[i].description + '的商客方案');
                        }
                    }
                }
                data.showWordsList = [...new Set(data.showWordsList)]
                data.showWordsSecondList = [...new Set(data.showWordsSecondList)]
                console.log('data.showWordsList', data.showWordsList)
                console.log('data.showWordsSecondList', data.showWordsSecondList)
                data.firstShow = false; // 第一行完成后，取消 loading 状态
                data.showSecond = true; // 显示第二行
                typeWriterSecond(); // 启动第二行打字机效果

                // 用于判断是否让用户选择还是直接默认定制
                if ((res.data.solutionList || res.data.abilityList || res.data.sceneList) && (res.data.productList || res.data.productPackageList)) {
                    // 2：让客户选择定制方案还是产品包
                    emit('getCustomizeType', 2)
                } else if ((res.data.solutionList || res.data.abilityList || res.data.sceneList) && !(res.data.productList || res.data.productPackageList)) {
                    // 1：默认定制方案
                    emit('getCustomizeType', 1)
                } else if (!(res.data.solutionList || res.data.abilityList || res.data.sceneList) && (res.data.productList || res.data.productPackageList)) {
                    // 3：默认定制产品包
                    emit('getCustomizeType', 3)
                }
            });
        };

        onMounted(() => {
            getSemanticsInfo();
            typeWriterFirst(); // 启动第一行打字机效果
        });

        watch(
            () => props.searchQuestion,
            (newVal) => {
                data.searchQuestion = newVal;
            }
        );

        return {
            ...toRefs(data),
        };
    },
});
</script>

<style lang="scss" scoped>
.item {
    font-size: 14px !important;
    margin-right: 14px;
    line-height: 20px;
    margin-right: 10px;
    color: #595959;
    text-indent: 2em;
    margin-left: 8px;
}

.boxLoading {
    align-items: center;
    margin-bottom: 8px !important;
    height: 27px;
}

.boxLoading img {
    margin-bottom: 4px;
    width: 12px;
    height: 12px;
}

#phrase_box1 {
    padding-top: 15px;
    display: flex;
    flex-flow: column;
    /* height: 144px; */
    overflow: hidden;
    width: 1200px;
    background: #ffffff;
    margin-left: 8px;
    border-radius: 12px;
    padding-left: 24px;
    margin: 0 auto;
}

.close_load {
    animation: close 6s;
}

/* #phrases1 {
  -webkit-animation: slide-phrases-upward 60s;
  animation: close 3s;
} */
.circle {
    width: 3px;
    height: 3px;
    border-radius: 50%;
    background: #0C70EB;
    display: inline-block;
    margin-bottom: 4px;
}

@keyframes close {
    0% {
        opacity: 1;
    }

    50% {
        opacity: 1;
    }

    100% {
        opacity: 0;
    }
}
</style>