const isDev = process.env.NODE_ENV === "development";
// const isPro=process.env.NODE_ENV === "production";

// 本地开发代理&远程服务器代理
const baseUrl = isDev ? location.protocol + "//" + location.host : location.protocol + "//" + location.host + "/portal";

// 服务器静态资地址代理
// const serverBaseUrl=isDev ? "http://localhost:5000" : location.protocol+"//"+location.host+"/portal";
// const uploadImageUrl=serverBaseUrl+"/uploads/";
// const uploadXlsxUrl=serverBaseUrl+"/uploads/files/";
// const esignUrl= isPro ? location.protocol+"//"+location.host : 'http://*************:80';
const imageIp = isDev ? "http://*************:80" : location.protocol + "//" + location.host + "/portal";
// 图片查看配置项
const imageOptions = {
    serverOriginalName: "originalFilename", //原始文件名称
    serverName: "newFileName", //回显文件的名称
    serverUrl: "fileName", //回显文件的url
};

export {
    isDev,
    baseUrl,
    // uploadImageUrl,
    // uploadXlsxUrl,
    // esignUrl,
    imageIp,
    imageOptions
}
