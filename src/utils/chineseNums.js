// 1-100的数字到汉字映射
const chineseNums = {
    '1': '一',
    '2': '二',
    '3': '三',
    '4': '四',
    '5': '五',
    '6': '六',
    '7': '七',
    '8': '八',
    '9': '九',
    '10': '十',
    '11': '十一',
    '12': '十二',
    '13': '十三',
    '14': '十四',
    '15': '十五',
    '16': '十六',
    '17': '十七',
    '18': '十八',
    '19': '十九',
    '20': '二十',
    '21': '二十一',
    '22': '二十二',
    '23': '二十三',
    '24': '二十四',
    '25': '二十五',
    '26': '二十六',
    '27': '二十七',
    '28': '二十八',
    '29': '二十九',
    '30': '三十',
    '31': '三十一',
    '32': '三十二',
    '33': '三十三',
    '34': '三十四',
    '35': '三十五',
    '36': '三十六',
    '37': '三十七',
    '38': '三十八',
    '39': '三十九',
    '40': '四十',
    '41': '四十一',
    '42': '四十二',
    '43': '四十三',
    '44': '四十四',
    '45': '四十五',
    '46': '四十六',
    '47': '四十七',
    '48': '四十八',
    '49': '四十九',
    '50': '五十',
    '51': '五十一',
    '52': '五十二',
    '53': '五十三',
    '54': '五十四',
    '55': '五十五',
    '56': '五十六',
    '57': '五十七',
    '58': '五十八',
    '59': '五十九',
    '60': '六十',
    '61': '六十一',
    '62': '六十二',
    '63': '六十三',
    '64': '六十四',
    '65': '六十五',
    '66': '六十六',
    '67': '六十七',
    '68': '六十八',
    '69': '六十九',
    '70': '七十',
    '71': '七十一',
    '72': '七十二',
    '73': '七十三',
    '74': '七十四',
    '75': '七十五',
    '76': '七十六',
    '77': '七十七',
    '78': '七十八',
    '79': '七十九',
    '80': '八十',
    '81': '八十一',
    '82': '八十二',
    '83': '八十三',
    '84': '八十四',
    '85': '八十五',
    '86': '八十六',
    '87': '八十七',
    '88': '八十八',
    '89': '八十九',
    '90': '九十',
    '91': '九十一',
    '92': '九十二',
    '93': '九十三',
    '94': '九十四',
    '95': '九十五',
    '96': '九十六',
    '97': '九十七',
    '98': '九十八',
    '99': '九十九',
    '100': '一百'
};

/**
 * 将数字转换为汉字
 * @param {number} num - 要转换的数字（1-100）
 * @returns {string} 转换后的汉字
 */
export const numberToChinese = (num) => {
    if (num < 1 || num > 100) {
        console.warn('数字超出范围（1-100）');
        return String(num);
    }
    return chineseNums[String(num)] || String(num);
};

export default numberToChinese; 