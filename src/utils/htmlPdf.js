import { async } from '@kangc/v-md-editor';
import { Item } from 'ant-design-vue/lib/menu';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';

function getSplitPositions(element) {
  const pages = [];
  const A4_HEIGHT = 1122;
  let currentY = 0;
  // 深度遍历DOM节点
  element.querySelectorAll('.con').forEach(node => {
    // console.log('node', node.getBoundingClientRect());

    const rect = node.getBoundingClientRect();
    const nodeTop = rect.top + window.scrollY;
    const nodeHeight = rect.height;
    pages.push(rect.top - 47);
    // 判断是否跨页
    // if (currentY + nodeHeight >= A4_HEIGHT) {
    //   pages.push(currentY);
    //   currentY = nodeHeight; // 重置当前页高度
    // } else {
    //   currentY += nodeHeight;
    // }
  });
  return pages;
}
export const generatePDF = async (elementId, fileName) => {
  const element = document.getElementById(elementId);
  window.scrollTo(0,0)
  const canvas = await html2canvas(element, {
    scale: 2,          // 提升清晰度
    useCORS: true,      // 解决跨域图片问题
    backgroundColor: '#FFFFFF',  // 强制白底
    width: 794,
    height: element.scrollHeight,
    scrollY: element.scrollHeight,
    windowHeight: element.scrollHeight,
    windowWidth: 794,
    allowTaint: false
  });
  // PDF尺寸设置
  const pdf = new jsPDF({
    orientation: 'p',
    unit: 'px',
    format: 'a4',
    margin: [0, 0, 0, 0],
    hotfixes: ["px_scaling"],
    compress: false,        // 暂时关闭压缩
    putOnlyUsedFonts: false
  });

  // console.log('canvas',canvas);
  // 转换为图片数据
  const imgUrl = canvas.toDataURL('image/png');
  const pages = getSplitPositions(element)
  // console.log('pages', pages);
  const pageImgList = []

  pages.forEach((pageY, index) => {
    if (pages.length == 1) {
      pdf.addImage(imgUrl, 'PNG', 0, 0, 798, 1122);
    } else {
      // 创建一个新的 Canvas 对象，用于存储裁剪后的图片
      const croppedCanvas = document.createElement('canvas');
      const ctx = croppedCanvas.getContext('2d');
      // 设置新 Canvas 的尺寸（根据需要裁剪的区域大小）
      croppedCanvas.width = 798; // 裁剪后的宽度
      croppedCanvas.height = 1122; // 裁剪后的高度

      ctx.drawImage(canvas, 0, pageY*2, 1596, 2244, 0, 0, 798, 1122);
      const croppedImg = croppedCanvas.toDataURL('image/png')
      // console.log('croppedImg', croppedImg);
      pageImgList.push(croppedImg)

    }
  });
  pageImgList.forEach((item, index) => {
    if (index > 0) {
      pdf.addPage();
      pdf.addImage(item, 'PNG', 0, 0, 798, 1122);
    } else {
      // 添加分页内容
      pdf.addImage(item, 'PNG', 0, 0, 798, 1122);
    }
  })
  const a4width = 190;  // A4有效打印宽度
  const a4height = 280;  // A4有效打印宽度
  const imgHeight = (a4height * canvas.width) / a4width;

  // pdf.addImage(canvas, 'JPEG', 0, 0, 794, element.scrollHeight);
  const blobData = pdf.output('blob')
  return {
    blobData,
    imgUrl
  }
  return pdf.output('blob');  // 返回Blob对象用于上传 
};