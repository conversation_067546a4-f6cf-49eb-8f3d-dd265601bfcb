import CryptoJS from "crypto-js";
import md5 from 'crypto-js/md5';


const encryptlx = (key, src) => {
    key = CryptoJS.enc.Utf8.parse(key);
    let bytes = CryptoJS.AES.encrypt(src, key, {
        mode: CryptoJS.mode.ECB,
        padding: CryptoJS.pad.Pkcs7,
    });
    return bytes.toString();
}

const encryptSolve = (key, iv, src) => {
    key = CryptoJS.enc.Utf8.parse(key);
    let bytes = CryptoJS.AES.encrypt(src, key, {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
        iv: CryptoJS.enc.Utf8.parse(iv),
    });


    return bytes.toString();
}
Date.prototype.format = function (fmt) {
    var o = {
        'M+': this.getMonth() + 1, // 月份
        'd+': this.getDate(), // 日
        'h+': this.getHours(), // 小时
        'm+': this.getMinutes(), // 分
        's+': this.getSeconds(), // 秒
        'q+': Math.floor((this.getMonth() + 3) / 3), // 季度
        'S': this.getMilliseconds() // 毫秒
    }
    if (/(y+)/.test(fmt)) {
        fmt = fmt.replace(RegExp.$1, (this.getFullYear() + '').substr(4 - RegExp.$1.length))
    }
    for (var k in o) {
        if (new RegExp('(' + k + ')').test(fmt)) {
            fmt = fmt.replace(RegExp.$1, (RegExp.$1.length == 1) ? (o[k]) : (('00' + o[k]).substr(('' + o[k]).length)))
        }
    }
    return fmt
}



export const AESlx = () => {
    const platform = "lingxi"
    const username = JSON.parse(localStorage.getItem("userInfo")).username;
    const secret = "yd@10086qwerty!@"
    let hours = (new Date().getHours() + "").padStart(2, "0")
    let minutes = (new Date().getMinutes() + "").padStart(2, "0")
    let second = (new Date().getSeconds() + "").padStart(2, "0")
    const timeTamp = (new Date()).format("yyyyMMdd") + hours + minutes + second;
    let md5Field = md5(`${platform}${username}${secret}${timeTamp}`).toString(CryptoJS.enc.Hex).toLowerCase();
    let src = `platform=${platform}&user=${username}&sign=${md5Field}&timestamp=${timeTamp}`
    const key = `w+LUuRbKIWnLojjAmm0/JA==`;
    return encryptlx(key, src)
}

/**
 * 生成AES加密的签名
 *
 * @returns 返回加密后的签名
 */
export const AESsolve = () => {
    const platform = "Solution"
    const username = JSON.parse(localStorage.getItem("userInfo")).phone;
    // const username = "15952049878"
    const secret = "yd@10086qwerty!@"
    const timeTamp = (new Date()).valueOf();
    let md5Field = md5(`${platform}${username}${secret}${timeTamp}`).toString(CryptoJS.enc.Hex).toLowerCase();

    let src = `platform=${platform}&user=${username}&sign=${md5Field}&timestamp=${timeTamp}`
    return encryptSolve(secret, secret, src)
}

let AES = {};
AES.encrypt = (key, iv, word) => {
  if (typeof (word) !== 'string') {
    word = JSON.stringify(word)
  }

  key = CryptoJS.enc.Utf8.parse(key);
  let bytes = CryptoJS.AES.encrypt(word, key, {
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
    iv: CryptoJS.enc.Utf8.parse(iv),
  });
  return bytes.toString();
}
// 解密
AES.decrypt = (key, iv, word) => {
  key = CryptoJS.enc.Utf8.parse(key);
  let decrypted = CryptoJS.AES.decrypt(word, key, {
    iv: CryptoJS.enc.Utf8.parse(iv),
    mode: CryptoJS.mode.CBC,
    padding: CryptoJS.pad.Pkcs7,
  });
  return decrypted.toString(CryptoJS.enc.Utf8);
}
export default AES