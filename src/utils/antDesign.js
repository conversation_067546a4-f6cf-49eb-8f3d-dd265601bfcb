/*
 * @Description: 
 * @Author: xiuji
 * @Date: 2024-04-07 09:23:11
 * @LastEditTime: 2024-04-17 09:10:00
 * @LastEditors: Do not edit
 */
import empty from "@/components/empty/empty.vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { Modal } from "ant-design-vue";
import { createVNode } from "vue";

export const AntdDelModal = (title, callback) => {
  Modal.info({
    title: '提示',
    class: 'timeoutModal flex',
    icon: createVNode(ExclamationCircleOutlined),
    content: title,
    okText: "确定",
    closable: false,
    onOk() {
      return new Promise((resolve, reject) => {
        callback(resolve, reject)
      })
    },
  });
}

export const AntdEmpty = (text = '暂无数据') => {
  return {
    emptyText: createVNode(empty, {
      text
    }),
  }
}
