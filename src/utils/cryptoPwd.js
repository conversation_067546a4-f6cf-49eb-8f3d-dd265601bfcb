import CryptoJS from "crypto-js";

let AES = {};
AES.encrypt = (key, iv, word) => {
    if (typeof (word) !== 'string') {
        word = JSON.stringify(word)
    }

    key = CryptoJS.enc.Utf8.parse(key);
    let bytes = CryptoJS.AES.encrypt(word, key, {
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
        iv: CryptoJS.enc.Utf8.parse(iv),
    });
    return bytes.toString();
}
// 解密
AES.decrypt = (key, iv, word) => {
    key = CryptoJS.enc.Utf8.parse(key);
    let decrypted = CryptoJS.AES.decrypt(word, key, {
        iv: CryptoJS.enc.Utf8.parse(iv),
        mode: CryptoJS.mode.CBC,
        padding: CryptoJS.pad.Pkcs7,
    });
    return decrypted.toString(CryptoJS.enc.Utf8);
}
export default AES