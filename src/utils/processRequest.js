import axios from "axios";
import { baseUrl } from "@/setting.js";
// import {useRouter} from "vue-router";
// const router=useRouter();
import { popMsg } from "./index"

import Store from "@/store";
import { useHomeStore } from "@/store";
import AES from "@/utils/aes.js";
// import router from "@/router/index.js";
// import { loading } from "@/global/utils/loading.js";
import JSONBIG from 'json-bigint'

axios.defaults.transformResponse = [
  function (data) {
    const json = JSONBIG({
      storeAsString: true
    })
    let resData = data;
    // 返回为JSON字符串明文
    if (resData.startsWith('{') || resData.startsWith('[')) {
      // 直接 parse 返回
      return json.parse(resData);
    }
    // 返回为加密字符串密文
    let token = localStorage.getItem("token");
    const keyStr = token.substring(token.length - 8) + 'QILIN666';
    const ivStr = token.substring(token.length - 16, token.length - 8) + 'QILIN666';
    let result = AES.decrypt(keyStr, ivStr, resData);
    return json.parse(result);
  }
]
// 全局加载样式
// let loading;
// function startLoading(){
//     loading=ElLoading.service({
//         lock:true,
//         text:"请求加载中...",
//         background:"rgba(0,0,0,0.7)"
//     });
// };
// function endLoading(){
//     loading.close();
// };
const tokenWhiteList = ["login", "captcha", "logout"];

const request = axios.create({
  baseURL: baseUrl,
  timeout: 180 * 1000,
  // headers: {
  //   "Content-Type": "application/json",
  // }
});
// 接口尾部名称
const getApiPureName = (config) => {
  const urlArr = config.url.split("/");
  if (urlArr[urlArr.length - 1] != "") {
    const url = urlArr[urlArr.length - 1];
    return url;
  } else {
    const url = urlArr[urlArr.length - 2];
    return url;
  }
};
request.interceptors.request.use(function (config) {
  let token = localStorage.getItem("token")
  const apiName = getApiPureName(config);
  if (Store.state.flag) {
    config.cancelToken = new Axios.CancelToken((cancelMethod) => {
      Store.commit('pushToken', cancelMethod);
      Store.commit('cancelRequest');
    })
    return
  }
  // 去除登录相关接口的判断
  if (!token && !tokenWhiteList.includes(apiName)) {
    window.location.replace(
      window.location.origin
    );
    return Promise.reject({
      msg: "会话已失效，请重新登录！",
    });
  }
  // 请求头添加token
  config.headers.Authorization = token || "";

  return config;
}, function (error) {
  return Promise.reject(error);
});

request.interceptors.response.use((res) => {
  if (res.data.success === false) {
    popMsg(res.data.msg || "网络繁忙，请稍后再试", 'error')
    return Promise.reject(res.data);
  } else {
    if (res.data.code === 500) {
      popMsg(res.data.msg || "系统异常，请稍后再试", 'error')
      return Promise.reject(res.data);
    }

    return Promise.resolve(res.data);
  }
}, (err) => {

  if (err.response && err.response.status === 401) {
    const counterStore = useHomeStore();
    counterStore.username = '0';
    popMsg("用户操作权限异常，请联系管理员")
    window.location.replace(
      window.location.origin
    );
  }
  return Promise.reject(err);
});

export default request;
