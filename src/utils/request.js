import Axios from 'axios';
import { popMsg } from "./index"
import Store from "@/store";
import { useHomeStore } from "@/store";
import AES from "@/utils/aes.js";
import JSONBIG from 'json-bigint'
Axios.defaults.transformResponse = [
  function (data) {
    const json = JSONBIG({
      storeAsString: true
    })
    let resData = data;
    // 返回为JSON字符串明文
    if (resData.startsWith('{') || resData.startsWith('[')) {
      // 直接 parse 返回
      return json.parse(resData);
    }
    // 返回为加密字符串密文
    let token = localStorage.getItem("token");
    const keyStr = token.substring(token.length - 8) + 'QILIN666';
    const ivStr = token.substring(token.length - 16, token.length - 8) + 'QILIN666';
    let result = AES.decrypt(keyStr, ivStr, resData);
    return json.parse(result);
  }
]

const request = Axios.create({
  timeout: 180000,
  headers: {
    "Content-Type": "application/json",
  }
});


const tokenWhiteList = ["login", "captcha", "system", "sendCode", "resetPassword", "freeLogin"];
// 接口尾部名称
const getApiPureName = (config) => {
  const urlArr = config.url.split("/");
  if (urlArr.includes("system") && urlArr.includes("backend")) {
    let index = urlArr.indexOf("system");
    return urlArr[index]
  }
  if (urlArr[urlArr.length - 1] != "") {
    const url = urlArr[urlArr.length - 1];
    return url;
  } else {
    const url = urlArr[urlArr.length - 2];
    return url;
  }
};
// 返回登录
const forceLogin = () => {
  // 清缓存
  window.localStorage.setItem("token", "");
  window.localStorage.setItem("userInfo", "");
  window.location.replace(window.location.origin + "/#/login")
};

request.interceptors.request.use(function (config) {
  let token = localStorage.getItem("token")
  const apiName = getApiPureName(config);
  if (Store.state.flag) {
    config.cancelToken = new Axios.CancelToken((cancelMethod) => {
      Store.commit('pushToken', cancelMethod);
      Store.commit('cancelRequest');
    })
    return
  }
  // 去除登录相关接口的判断
  if (!token && !tokenWhiteList.includes(apiName)) {
    forceLogin();
    return Promise.reject({
      msg: "会话已失效，请重新登录！",
    });
  }

  //请求地址判断
  if (config.url.includes('companyBasicInfoApi')) {
    token = "_S_:e81721a040ecd755456a87804bc3270e1f124b1a510c699f8284f0204257481eb457dbb4fd23ad9487cd81c8ff5e0d3532311364758c2a380fddd2e73854a4fdd56e5749efac6fb1948083071883c12bdce6ef2186aae844ae928fd7bd6189693cc075ac6d00680eccc6bd6385ccf1387298823bde3eedf652e09691942363eabdcbce682bfc66978d6f40a68d9ffeb2"
  }
  // 请求头添加token
  config.headers.Authorization = token || "";

  return config;
}, function (error) {
  return Promise.reject(error);
});

request.interceptors.response.use((res) => {
  let result = res.data;
  if (result.success === false) {
    popMsg(result.msg || "网络繁忙，请稍后再试", 'error')
    return Promise.reject(result);
  } else {
    if (result.code === 500) {
      popMsg(result.msg || "系统异常，请稍后再试", 'error')
      return Promise.resolve(result);
    }
    return Promise.resolve(result);
  }
}, (err) => {
  console.log(err, "err");
  if (err.response && err.response.status === 401) {
    const counterStore = useHomeStore();
    counterStore.username = '0';
    forceLogin();
    popMsg("登录信息过期，请重新登录", 'error')
  }
  return Promise.reject(err);
});


export const postRequestBody = (url, params) => {
  return request({
    method: 'post',
    url: `${url}`,
    data: params
  })
}
export const postRequestBody2 = (url, params, value) => {
  return request({
    method: 'post',
    url: `${url}${value}`,
    data: params
  })
}
export const postRequestBody3 = (url, params,headers = {}) => {
  return request({
    method: 'post',
    url: `${url}`,
    data: params,
    dataType: "json",
    headers:{
      "Content-Type": "application/json",
      ...headers
    }
  })
}
export const putRequest = (url, params) => {
  return request({
    method: 'put',
    url: `${url}${params}`,
  })
}
export const putRequestBody = (url, params) => {
  return request({
    method: 'put',
    url: `${url}`,
    data: params
  })
}
export const putRequestBody2 = (url, params) => {
  return request({
    method: 'put',
    url: `${url}`,
    params: params
  })
}

export const deleteRequest = (url, data) => {
  return request({
    method: 'delete',
    url: `${url}${data}`,
  })
}
export const deleteRequestBody = (url, params) => {
  return request({
    method: 'delete',
    url: `${url}`,
    params: params
  })
}

export const getBlobRequest = (url, params) => {
  return request({
    method: 'get',
    url: `${url}`,
    params: params,
    responseType: 'blob'
  })
}

export const getRequest = (url, params) => {
  return request({
    method: 'get',
    url: `${url}`,
    params: params,
  })
}

export const getRequestByValue = (url, data) => {
  return request({
    method: 'get',
    url: `${url}${data}`,
  })
}

export const postBlobRequest = (url, params) => {
  return request({
    method: 'post',
    url: `${url}`,
    data: params,
    // responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
}

export const AIBlobRequest = (url, params) => {
  return request({
    method: 'post',
    url: `${url}`,
    data: params,
    // responseType: 'blob',
    headers: {
      'Content-Type': 'multipart/form-data',
    }
  })
}