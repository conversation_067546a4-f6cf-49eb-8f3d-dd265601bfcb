@font-face {
  font-family: "iconfont"; /* Project id 4537123 */
  src: url('iconfont.woff2?t=1733363538819') format('woff2'),
       url('iconfont.woff?t=1733363538819') format('woff'),
       url('iconfont.ttf?t=1733363538819') format('truetype'),
       url('iconfont.svg?t=1733363538819#iconfont') format('svg');
}

.iconfont {
  font-family: "iconfont" !important;
  font-size: 16px;
  font-style: normal;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

.icon-yuyin:before {
  content: "\e802";
}

.icon-fasong:before {
  content: "\e803";
}

.icon-zone3:before {
  content: "\e7ff";
}

.icon-zone2:before {
  content: "\e800";
}

.icon-zone1:before {
  content: "\e801";
}

.icon-customized2:before {
  content: "\e7f9";
}

.icon-customized1:before {
  content: "\e7fd";
}

.icon-customized3:before {
  content: "\e7fe";
}

.icon-comment2:before {
  content: "\e7fa";
}

.icon-comment1:before {
  content: "\e7fb";
}

.icon-comment3:before {
  content: "\e7fc";
}

.icon-scene3:before {
  content: "\e691";
}

.icon-scene1:before {
  content: "\e692";
}

.icon-scene2:before {
  content: "\e693";
}

.icon-product3:before {
  content: "\e68e";
}

.icon-product1:before {
  content: "\e68f";
}

.icon-product2:before {
  content: "\e690";
}

.icon-yunyingfankui1:before {
  content: "\e7f5";
}

.icon-yunyingfankui3:before {
  content: "\e7f6";
}

.icon-yunyingfankui2:before {
  content: "\e7f7";
}

.icon-jiejuefangan2:before {
  content: "\e7f3";
}

.icon-jiejuefangan1:before {
  content: "\e7f4";
}

.icon-datareporting2:before {
  content: "\e68a";
}

.icon-datareporting1:before {
  content: "\e68b";
}

.icon-datareporting3:before {
  content: "\e68d";
}

.icon-rightArrow:before {
  content: "\e68c";
}

.icon-annex:before {
  content: "\e689";
}

.icon-preview:before {
  content: "\e687";
}

.icon-download:before {
  content: "\e688";
}

.icon-ability2:before {
  content: "\e685";
}

.icon-ability3:before {
  content: "\e686";
}

.icon-ability1:before {
  content: "\e684";
}

.icon-more:before {
  content: "\e613";
}

.icon-display:before {
  content: "\e682";
}

.icon-hide:before {
  content: "\e683";
}

.icon-dynamic2:before {
  content: "\e679";
}

.icon-dynamic1:before {
  content: "\e67a";
}

.icon-dynamic3:before {
  content: "\e67b";
}

.icon-announcement1:before {
  content: "\e67c";
}

.icon-addphoto:before {
  content: "\e67d";
}

.icon-folding:before {
  content: "\e67e";
}

.icon-close1:before {
  content: "\e67f";
}

.icon-close2:before {
  content: "\e680";
}

.icon-dropdown:before {
  content: "\e681";
}

.icon-system2:before {
  content: "\e66f";
}

.icon-announcement2:before {
  content: "\e671";
}

.icon-announcement3:before {
  content: "\e670";
}

.icon-workorder3:before {
  content: "\e672";
}

.icon-workorder1:before {
  content: "\e673";
}

.icon-workorder2:before {
  content: "\e674";
}

.icon-system3:before {
  content: "\e675";
}

.icon-system1:before {
  content: "\e676";
}

.icon-folding2:before {
  content: "\e677";
}

.icon-arrow1:before {
  content: "\e678";
}

