<svg width="24" height="24" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="&#230;&#160;&#135;&#233;&#162;&#152;&#232;&#163;&#133;&#233;&#165;&#176;">
<g id="Ellipse 150" opacity="0.8" filter="url(#filter0_f_241_3489)">
<ellipse cx="11.4444" cy="11.9993" rx="4.44444" ry="3.33333" fill="url(#paint0_linear_241_3489)"/>
</g>
<g id="Ellipse 151" opacity="0.8" filter="url(#filter1_f_241_3489)">
<ellipse cx="17.556" cy="12.0007" rx="2.22222" ry="1.66667" fill="url(#paint1_linear_241_3489)"/>
</g>
<circle id="Ellipse 149" cx="7" cy="12" r="5" fill="url(#paint2_linear_241_3489)"/>
</g>
<defs>
<filter id="filter0_f_241_3489" x="6" y="7.66602" width="10.8889" height="8.66699" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_241_3489"/>
</filter>
<filter id="filter1_f_241_3489" x="14.3337" y="9.33398" width="6.44446" height="5.33301" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_241_3489"/>
</filter>
<linearGradient id="paint0_linear_241_3489" x1="16.9145" y1="11.9993" x2="7" y2="11.9993" gradientUnits="userSpaceOnUse">
<stop stop-color="#5790FF"/>
<stop offset="1" stop-color="#1A66FB"/>
</linearGradient>
<linearGradient id="paint1_linear_241_3489" x1="20.291" y1="12.0007" x2="15.3337" y2="12.0007" gradientUnits="userSpaceOnUse">
<stop stop-color="#5790FF"/>
<stop offset="1" stop-color="#1A66FB"/>
</linearGradient>
<linearGradient id="paint2_linear_241_3489" x1="13.1538" y1="12" x2="2" y2="12" gradientUnits="userSpaceOnUse">
<stop stop-color="#5790FF"/>
<stop offset="1" stop-color="#1A66FB"/>
</linearGradient>
</defs>
</svg>
