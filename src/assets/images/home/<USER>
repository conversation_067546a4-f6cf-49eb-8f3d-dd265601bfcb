<svg width="16" height="8" viewBox="0 0 16 8" fill="none" xmlns="http://www.w3.org/2000/svg">
<g opacity="0.8" filter="url(#filter0_f_2146_1666)">
<ellipse cx="7.55556" cy="4.0001" rx="3.55556" ry="2.66667" fill="url(#paint0_linear_2146_1666)"/>
</g>
<g opacity="0.8" filter="url(#filter1_f_2146_1666)">
<ellipse cx="12.4451" cy="3.9999" rx="1.77778" ry="1.33333" fill="url(#paint1_linear_2146_1666)"/>
</g>
<circle cx="4" cy="4" r="4" fill="url(#paint2_linear_2146_1666)"/>
<defs>
<filter id="filter0_f_2146_1666" x="3" y="0.333435" width="9.11108" height="7.33331" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_2146_1666"/>
</filter>
<filter id="filter1_f_2146_1666" x="9.66736" y="1.66656" width="5.55554" height="4.66669" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="0.5" result="effect1_foregroundBlur_2146_1666"/>
</filter>
<linearGradient id="paint0_linear_2146_1666" x1="11.9316" y1="4.0001" x2="4" y2="4.0001" gradientUnits="userSpaceOnUse">
<stop stop-color="#5790FF"/>
<stop offset="1" stop-color="#1A66FB"/>
</linearGradient>
<linearGradient id="paint1_linear_2146_1666" x1="14.6332" y1="3.9999" x2="10.6674" y2="3.9999" gradientUnits="userSpaceOnUse">
<stop stop-color="#5790FF"/>
<stop offset="1" stop-color="#1A66FB"/>
</linearGradient>
<linearGradient id="paint2_linear_2146_1666" x1="8.92308" y1="4" x2="-7.77176e-08" y2="4" gradientUnits="userSpaceOnUse">
<stop stop-color="#5790FF"/>
<stop offset="1" stop-color="#1A66FB"/>
</linearGradient>
</defs>
</svg>
