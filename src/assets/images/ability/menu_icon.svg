<svg width="18" height="18" viewBox="0 0 18 18" fill="none" xmlns="http://www.w3.org/2000/svg">
<g id="Group 16">
<g id="Rectangle 50" opacity="0.8" filter="url(#filter0_f_2096_3298)">
<rect x="4" y="4" width="10" height="10" rx="2" fill="url(#paint0_linear_2096_3298)"/>
</g>
<rect id="Rectangle 49" x="1" y="2" width="10" height="10" rx="2" fill="url(#paint1_linear_2096_3298)"/>
</g>
<defs>
<filter id="filter0_f_2096_3298" x="0" y="0" width="18" height="18" filterUnits="userSpaceOnUse" color-interpolation-filters="sRGB">
<feFlood flood-opacity="0" result="BackgroundImageFix"/>
<feBlend mode="normal" in="SourceGraphic" in2="BackgroundImageFix" result="shape"/>
<feGaussianBlur stdDeviation="2" result="effect1_foregroundBlur_2096_3298"/>
</filter>
<linearGradient id="paint0_linear_2096_3298" x1="9" y1="4" x2="9" y2="14" gradientUnits="userSpaceOnUse">
<stop stop-color="#236CFF"/>
<stop offset="1" stop-color="#0053FA"/>
</linearGradient>
<linearGradient id="paint1_linear_2096_3298" x1="6" y1="2" x2="6" y2="12" gradientUnits="userSpaceOnUse">
<stop stop-color="#236CFF"/>
<stop offset="1" stop-color="#0053FA"/>
</linearGradient>
</defs>
</svg>
