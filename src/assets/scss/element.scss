
:root{
    --el-color-primary:#{$primary-text-color};
    --el-menu-item-height:46px;
    // --el-color-primary-light-9:#{$primary-text-color};
}
/*
    统一修改el-table样式
*/
.el-table{
    th.el-table__cell{
        background-color:#F5F7FA !important;
        color:$text-color-85;
        padding:15px 0;
        &:hover{
            cursor:default;
        }
    }
    td>.cell{
        color:#606266;
        &:hover{
            cursor:default;
        }
    }
    .el-table__body tr.current-row>td.el-table__cell{
        background-color:$primary-text-color;
        >.cell{
            color:#fff;
        }
    }
    .el-table__body tr.current-row:hover>td.el-table__cell{
        background-color:$primary-text-color;
    }
}
/*
    统一修改el-pagination样式
*/
.el-pagination{
    float:right;
    padding:20px 0;
}
/*
    统一修改el-steps样式
*/
.el-steps{
    .el-step__icon{
        width: 8px;
        height: 8px;
        background-color: #BFBFBF;
        overflow: hidden;
        margin-top: 8px;
        margin-right: 16px;
        color:#BFBFBF;
        border-width:0 !important;
    }
    .el-step__line{
        width:92%;
        border-width:0;
        // height:3px !important;
    }
    .el-step__head{
        height:23px;
    }
    .el-step__head.is-success{
        .el-step__icon{
            background-color:$primary-text-color;
            color:$primary-text-color;
        }
        .el-step__line{
            border-color:$primary-text-color;
            // background-color:$primary-text-color;
        }
    }
    .el-step__title{
        color:$text-color-65 !important;
        &.is-process{
            color:$text-color-85 !important;
            font-weight:400;
        }
    }
    .el-step__description{
        color:$text-color-45 !important;
        font-size:$default-text-size;
    }
}

/*
    统一修改el-menu样式
*/
.el-menu{
    border-right-width:0;
    background-color:transparent !important;
}

/*
    统一修改el-dialog样式
*/
.el-dialog{
    >.el-dialog__header{
        border-bottom:1px solid $border-color;
        margin-right:0;
        padding-bottom:15px;
        >span{
            color:$text-color-85;
            font-size:$medium-text-size;
        }
    }
    >.el-dialog__body{
        padding:20px;
        max-height:550px;
        overflow:auto;
        -ms-overflow-style:none;// ie下隐藏滚动条
        scrollbar-width:none;
        &::-webkit-scrollbar{ //谷歌下隐藏滚动条
            display:none;
        }
    }
}

/*
    统一修改el-drawer样式
*/
.el-drawer{
    >.el-drawer__header{
        border-bottom:1px solid $border-color;
        padding-bottom:20px;
        margin-bottom:0;
        >span{
            color:$text-color-85;
            font-size:$medium-text-size;
        }
    }
    >.el-drawer__footer{
        text-align:center;
    }
}

/*
    统一修改el-upload样式
*/
.el-upload{
    &.el-upload--text{
        .el-upload-dragger{
            padding:20px 10px;
        }
        .el-upload__text{
            >span{
                color:$text-color-85;
            }
            >div{
                color:$text-color-45;
            }
        }
        .el-upload__tip{
            color:$color-text-support;
            font-size:$small-text-size;
        }
    }
    &.el-upload--picture-card{
        width:120px;
        height:120px;
        .el-upload-dragger{
            height:100%;
            line-height:30px;
            display:flex;
            flex-flow:column nowrap;
            align-items:center;
            &:hover{
                i{
                    color:$primary-text-color;
                }
            }
            >.image-tip-text{
                font-size:$small-text-size;
            }
        }
        .el-upload-list__item{

        }
        >div:first-child{
            display:flex;
            flex-flow:column nowrap;
            align-items:center;
            >.el-upload__tip{
                line-height:18px;
                color:$color-text-invalid;
                font-size:$small-text-size;
                padding:0 20px;
            }
        }
    }
}
.el-upload-list--picture-card{
    .el-upload-list__item{
        width:120px;
        height:120px;
        .el-icon--close-tip{
            display:none !important;
        }
    }
}

/*
    统一修改确认弹窗的样式
*/
.el-message-box{
    .el-message-box__header{
        border-bottom:1px solid $border-color;
    }
    .el-message-box__title{
        color:$text-color-85;
        font-size:$medium-text-size;
    }
}

/*
    统一修改el-tabs样式
*/
.el-tabs{
    .el-tabs__nav-wrap{
        padding: 0 20px;
    }
    .el-tabs__item{
        &.is-active{
            color:$primary-text-color;
        }
        &:hover{
            color:$primary-text-color;
        }
    }
    .el-tabs__active-bar{
        background-color:$primary-text-color;
    }
}

/*
    统一修改el-input样式
*/
.el-input{
    // 隐藏数字输入框上下箭头
    .el-input__inner::-webkit-outer-spin-button,.el-input__inner::-webkit-inner-spin-button{
        -webkit-appearance: none;
    }
    .el-input__inner[type="number"]{
        -moz-appearance: textfield;
    }
}

/*
    统一修改el-form样式
*/
.el-form{
    >.el-form-item{
        margin-bottom:24px;
    }
}
