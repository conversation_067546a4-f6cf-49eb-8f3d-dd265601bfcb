@import "./_base.scss";
@import "./element.scss";


a{
    text-decoration: none;
}
ul{
    list-style: none;
}
body *{
    scrollbar-width:thin;
    scrollbar-color:$border-color #fff;
    // 兼容IE浏览器
    scrollbar-darkshadow-color:#fff;
    scrollbar-base-color:$border-color;
    &::-webkit-scrollbar{
        width:5px !important;
        height:5px !important;
    }
    &::-webkit-scrollbar-thumb{
        background-color:$border-color;
        border-radius:5px;
    }
    &::-webkit-scrollbar-track{
        background-color:#fff;
    }
}

// 隐藏滚动条样式
.hide-scrollbar{
    overflow:auto;
    -ms-overflow-style:none;// ie下隐藏滚动条
    scrollbar-width:none;
    &::-webkit-scrollbar{ //谷歌下隐藏滚动条
        display:none;
    }
}

// 美化滚动条样式--纵向滚动条
.beauty-scrollbar-y{
    overflow-y:auto;
    // 兼容火狐浏览器
    scrollbar-width:thin;
    scrollbar-color:$border-color #fff;
    // 兼容IE浏览器
    scrollbar-darkshadow-color:#fff;
    scrollbar-base-color:$border-color;
    &::-webkit-scrollbar{
        width:5px !important;
    }
    &::-webkit-scrollbar-thumb{
        background-color:$border-color;
        border-radius:5px;
    }
    &::-webkit-scrollbar-track{
        background-color:#fff;
    }
}
// 美化滚动条样式--横向滚动条
.beauty-scrollbar-x{
    overflow-x:auto;
    // 兼容火狐浏览器
    scrollbar-width:thin;
    scrollbar-color:$border-color #fff;
    // 兼容IE浏览器
    scrollbar-darkshadow-color:#fff;
    scrollbar-base-color:$border-color;
    &::-webkit-scrollbar{
        height:5px !important;
    }
    &::-webkit-scrollbar-thumb{
        background-color:$border-color;
        border-radius:5px;
    }
    &::-webkit-scrollbar-track{
        background-color:#fff;
    }
}
// 超出单行省略号展示
.single-ellipsis{
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
}
// 超出n行省略号展示
@mixin multipleEllipsis($n) {
    display:-webkit-box;
    overflow:hidden;
    -webkit-box-orient:vertical;
    -webkit-line-clamp:$n; //超出n行加省略号
}
// flex布局-row
.flex-row{
    display:flex;
    flex-flow:row nowrap;
}
// flex布局-column
.flex-column{
    display:flex;
    flex-flow:column nowrap;
}
// 隐藏样式
.is-hide{
    display:none !important;
}
// 表格展开行样式
.qilin-table-tooltip{
    white-space:nowrap;
    overflow:hidden;
    text-overflow:ellipsis;
    display:inline;
    cursor:pointer;
    &:hover{
        color:$primary-text-color;
    }
}
// v-viewer图片预览组件弹出层样式
.viewer-container{
    z-index:9999 !important;
}
