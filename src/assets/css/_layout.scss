$MARGIN_SIZE: 0 8 16 24 32;
$PADDING_SIZE: 0 5 8 10 12 14 16 20 24 32 40 100;

$MARGIN_TOP_SIZE: -32 -20 3 4 5 6 8 10 12 15 16 19 20 24 32 34 39 40 72 80 350;
$MARGIN_BOTTOM_SIZE: 5 6 8 10 12 14 16 18 19 20 24 28 32 48 40;
$MARGIN_LEFT_SIZE: -24 -15 1 2 4 6 8 10 12 16 24 28 32 36 40 42 48 54 90 100 120 240;
$MARGIN_RIGHT_SIZE: -24 1 2 4 6 8 9 10 11 12 16 20 22 24 32 40 48 60 70 74 80 130 140 150;

$PADDING_TOP_SIZE: 3 4 8 10 12 16 17 20 24 32 40;
$PADDING_BOTTOM_SIZE: 6 8 10 12 16 24 32;
$PADDING_LEFT_SIZE: 5 6 8 10 12 16 20 24 30 32 90;
$PADDING_RIGHT_SIZE: 6 8 10 12 16 20 24 32;

$POSITION: fixed absolute relative;
$FLOAT: left right;

$JUSTIFYNICKNAME: start end center sb sa;
$JUSTIFYVALUE: flex-start flex-end center space-between space-around;
$ALIGNNICKNAME: start end center;
$ALIGNVALUE: flex-start flex-end center;

@mixin LAYOUT($SIZES, $CLASSNAME, $CLASSKEY) {
  @each $SIZE in $SIZES {
    .#{$CLASSNAME}_#{$SIZE} {
      #{$CLASSKEY}: $SIZE + px;
    }
  }
}

@mixin LAYOUT_($VALUE, $CLASSKEY) {
  @each $VAL in $VALUE {
    .#{$VAL} {
      #{$CLASSKEY}: $VAL;
    }
  }
}

@include LAYOUT($MARGIN_TOP_SIZE, margin_t, margin-top);
@include LAYOUT($MARGIN_BOTTOM_SIZE, margin_b, margin-bottom);
@include LAYOUT($MARGIN_LEFT_SIZE, margin_l, margin-left);
@include LAYOUT($MARGIN_RIGHT_SIZE, margin_r, margin-right);
@include LAYOUT($MARGIN_SIZE, margin, margin);

@include LAYOUT($PADDING_TOP_SIZE, padding_t, padding-top);
@include LAYOUT($PADDING_BOTTOM_SIZE, padding_b, padding-bottom);
@include LAYOUT($PADDING_LEFT_SIZE, padding_l, padding-left);
@include LAYOUT($PADDING_RIGHT_SIZE, padding_r, padding-right);
@include LAYOUT($PADDING_SIZE, padding, padding);

@include LAYOUT_($POSITION, position);
@include LAYOUT_($FLOAT, float);

.dailyflex {
  display: flex;
  justify-content: space-around;
  flex-direction: column;
  align-items: center;
}

.pending_b_12vh {
  padding-bottom: 1.11vh;
}

.margin_b_1vh {
  margin-bottom: 1vh;
}