$FONT_SIZE :10 12 14 15 16 17 18 19 20 22 24 28 30 32 34 36 64;
$FONT_ALIGN: left center right justify;
$FONT_STYLE: underline;

@each $FONT in $FONT_SIZE {
  .font_#{$FONT} {
    font-size: $FONT + px;
  }
}

@each $ALIGN in $FONT_ALIGN {
  $LEN: str-length($ALIGN);
  $CLASS_NAME: to-upper-case(str-slice($ALIGN, 1, 1))+to-lower-case(str-slice($ALIGN, 2, $LEN));

  .text#{$CLASS_NAME} {
    text-align: $ALIGN;
  }
}

.fontDel {
  text-decoration: line-through;
}

.fontUnderline {
  text-decoration: underline;
}

.fontItalic {
  font-style: italic;
}

.fontNormal {
  font-style: normal;
}

.fontBold {
  font-weight: 600;
}

.weight600 {
  font-weight: 600;
}
.weight500 {
  font-weight: 500;
}

.textNowrap {
  white-space: nowrap;
}

.textWrap {
  word-wrap: break-word;
}

.textBreak {
  word-break: break-all;
}

.textEllipsis {
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  word-wrap: break-word;
  word-break: break-all;
}

.textEllipsis2 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
}

.textEllipsis3 {
  display: -webkit-box;
  overflow: hidden;
  text-overflow: ellipsis;
  word-wrap: break-word;
  white-space: normal;
  -webkit-line-clamp: 3;
  -webkit-box-orient: vertical;
  word-break: break-all;
}

/* 针对数字 */
.numFontSize {
  font-family: Helvetica;
}

.font_22vw {
  font-size: 1.145vw;
}

.font_24vw {
  font-size: 1.25vw;
}

.font_20vw {
  font-size: 1.04vw;
}

.font_18vw {
  font-size: 0.94vw;
}

.font_16vw {
  font-size: 0.83vw;
}

.font_14vw {
  font-size: 0.73vw;
  // font-size: 1.5vh;
}

.font_12vw {
  font-size: 0.625vw;
}

.font_24vw {
  font-size: 1.25vw;
}

.font_32vw {
  font-size: 1.66vw;
}

.font_36vw {
  font-size: 2.5vw;
}