<!--
 * @Description:
 * @Author: xiuji
 * @Date: 2024-03-25 09:56:59
 * @LastEditTime: 2024-03-27 15:55:16
 * @LastEditors: Do not edit
-->
<template>
  <div>

    <a-config-provider :locale="zhCN" :getPopupContainer="getPopupContainer">
      <!-- <keep-alive :include="['AI']">
        <router-view />
      </keep-alive> -->
      
      <router-view v-slot="{ Component, route }">
        <keep-alive :include="['AI']">
          <component :is="Component" />
        </keep-alive>
      </router-view>

    </a-config-provider>
  </div>
</template>

<script>
import { defineComponent, onErrorCaptured } from "vue";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { RouterView } from "vue-router";
export default defineComponent({
  name: "App",
  components: {
    RouterView,
  },
  setup() {
    onErrorCaptured((err, instance, info) => {
      console.error('App.vue 捕获到错误:', err)
      // true阻止错误继续向上传播,false不阻止错误继续向上传播
      return false
    })
    //解决 antdesign a-select组件下拉列表在局部滚动时不跟随 问题
    const getPopupContainer = (el, dialogContext) => {
      if (dialogContext) {
        return dialogContext.getDialogWrap();
      } else {
        return document.body;
      }
    };

    return {
      getPopupContainer,
      zhCN,
    };
  },
});
</script>

<style lang="scss" scoped></style>
