<template>
  <div v-if="textList && textList.length > 0">
    <div id="#appList">
      <div class="app_content">
        <img
          src="@/assets/images/solution/detail/leftIcon.png"
          style="width: 33px; height: 22px"
          alt=""
        />
        <div class="tit">应用行业</div>
        <img
          src="@/assets/images/solution/detail/rightIcon.png"
          style="width: 33px; height: 22px"
          alt=""
        />
      </div>
      <div class="applyContent">
        <div
          class="flex align-center just-start"
          style="gap: 48px 60px; flex-wrap: wrap; margin-left: 70px"
        >
          <template v-for="(item, index) in textList" :key="index">
            <div class="matter">
              <div
                class="bac"
                :style="{ backgroundImage: `url('${item.bac}')` }"
              >
                <img :src="item.activeImg" width="40px" height="40px" alt=""/>
                <div class="text">{{ item.name }}</div>
              </div>
            </div>
          </template>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import precinct from "@/assets/images/product/precinct/party.png";
import partyActive from "@/assets/images/product/precinct/partyActive.png";
import policy from "@/assets/images/product/precinct/policy.png";
import policyActive from "@/assets/images/product/precinct/policyActive.png";
import industry from "@/assets/images/product/precinct/industry.png";
import industryActive from "@/assets/images/product/precinct/industryActive.png";
import traffic from "@/assets/images/product/precinct/traffic.png";
import trafficActive from "@/assets/images/product/precinct/trafficActive.png";
import medical from "@/assets/images/product/precinct/medical.png";
import medicalActive from "@/assets/images/product/precinct/medicalActive.png";
import teach from "@/assets/images/product/precinct/teach.png";
import teachActive from "@/assets/images/product/precinct/teachActive.png";
import newCard from "@/assets/images/product/precinct/new.png";
import newActive from "@/assets/images/product/precinct/newActive.png";
import finace from "@/assets/images/product/precinct/finace.png";
import finaceActive from "@/assets/images/product/precinct/finaceActive.png";
import farmer from "@/assets/images/product/precinct/farmer.png";
import farmerActive from "@/assets/images/product/precinct/farmerActive.png";
import internet from "@/assets/images/product/precinct/internet.png";
import internetActive from "@/assets/images/product/precinct/internetActive.png";
import ves from "@/assets/images/product/precinct/ves.png";
import vesActive from "@/assets/images/product/precinct/vesActive.png";
import level from "@/assets/images/product/precinct/level.png";
import levelActive from "@/assets/images/product/precinct/levelActive.png";
import rest from "@/assets/images/product/precinct/rest.png";
import restActive from "@/assets/images/product/precinct/restActive.png";

import { pptTopdf } from "@/api/fileUpload/uploadFile.js";

export default defineComponent({
  props: {
    applyIndustryName: {
      type: String,
      default() {
        return "";
      },
    },
  },
  setup(props) {
    const data = reactive({
      activeKey: "1",
      loadShow: false,
      fileList: [],
      isActive: 0,
      applyIndustryName: props.applyIndustryName,
      appList: [
        {
          name: "党政",
          bac: precinct,
          activeImg: partyActive,
        },
        {
          name: "政法公安",
          bac: policy,
          activeImg: policyActive,
        },
        {
          name: "工业",
          bac: industry,
          activeImg: industryActive,
        },
        {
          name: "交通",
          bac: traffic,
          activeImg: trafficActive,
        },
        {
          name: "医疗",
          bac: medical,
          activeImg: medicalActive,
        },
        {
          name: "教育",
          bac: teach,
          activeImg: teachActive,
        },
        {
          name: "创新融合",
          bac: newCard,
          activeImg: newActive,
        },
        {
          name: "金融",
          bac: finace,
          activeImg: finaceActive,
        },
        {
          name: "农商",
          bac: farmer,
          activeImg: farmerActive,
        },
        {
          name: "互联网",
          bac: internet,
          activeImg: internetActive,
        },
        {
          name: "信创",
          bac: ves,
          activeImg: vesActive,
        },
        {
          name: "低空经济",
          bac: level,
          activeImg: levelActive,
        },
        {
          name: "其他",
          bac: rest,
          activeImg: restActive,
        },
      ],
      currentAnchor: "#appList",
      anchorList: [
        // {
        //   key: "appList",
        //   href: "#appList",
        //   title: "应用行业",
        // },
        // {
        //   key: "pptList",
        //   href: "#pptList",
        //   title: "应用行业PPT",
        // },
      ],
      textList: [],
    });

    watch(
      () => props.applyIndustryName,
      (val) => {
        let text = val.split(",");
        data.appList.filter((item) => {
          text.forEach((val) => {
            if (item.name == val) {
              return data.textList.push(item);
            }
          });
        });
      },
      { immediate: true }
    );

    const fileShow = (val) => {
      data.loadShow = true;
      pptTopdf({
        filePath: val.path,
        fileUrl: val.url,
      }).then((res) => {
        data.loadShow = false;
        if (res.code == 200) {
          let windowOrigin = window.location.origin;
		      let token = localStorage.getItem("token");
		      let newHref = res.data;
					if(res.data.includes(windowOrigin)){
						newHref = "/portal" + res.data.split(windowOrigin)[1]
					}
		      const newpage = Router.resolve({
		        name: "lookPdf",
		        query: {
		          urlMsg: encodeURIComponent(
		          	windowOrigin + newHref + "?token=" + token
		          ),
		          urlName: val.name,
		        },
		      });
          window.open(newpage.href, "_blank");
        }
      });
      return false;
    };

    const handleClick = (e, link) => {
      const href = link.href.replace("#", "");
      e.preventDefault();
      data.currentAnchor = "#" + href;
      let srcolls = document.getElementById(link.href);
      srcolls &&
        srcolls.scrollIntoView({
          block: "center",
          behavior: "smooth",
        });
    };

    const change = (v) => {
      data.isActive = v;
    };

    return {
      ...toRefs(data),
      fileShow,
      handleClick,
      change,
    };
  },
});
</script>

<style lang="scss" scoped>
@import "./index.scss";
.markInfo {
  font-weight: bold;
  font-size: 28px;
  color: #24456a;
  line-height: 33px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 20px;

  .left {
    width: 48px;
    height: 2px;
    background: linear-gradient(90deg, rgba(36, 69, 106, 0) 0%, #24456a 100%);
  }
  .right {
    width: 48px;
    height: 2px;
    background: linear-gradient(-90deg, rgba(36, 69, 106, 0) 0%, #24456a 100%);
  }
}
.list {
  padding-inline-start: 0;
  list-style-type: none;
  margin: 24px auto;
  display: flex;
  justify-content: space-between;
  flex-wrap: wrap;

  li {
    width: 48%;
    display: inline-block;
    background: linear-gradient(180deg, #edf0f9 0%, #fefeff 100%);
    box-shadow: 0px 4px 24px 0px #eaedf3;
    border-radius: 10px 10px 10px 10px;
    border: 2px solid #ffffff;
    padding: 4px 24px 4px 24px;
    margin-bottom: 24px;

    img {
      display: inline-block;
      width: 72px;
      height: 72px;
    }

    p {
      display: inline-block;
      font-weight: 500;
      font-size: 16px;
      color: #2e3852;
      line-height: 28px;
    }

    .left_box {
      display: flex;
      padding-top: 19px;
    }
  }

  .li_box {
    display: flex;
    justify-content: space-between;
    align-items: center;
  }

  li:nth-of-type(odd) {
    margin-right: 24px;
  }

  .fileText {
    overflow: hidden;
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
  }
}

.app_content {
  text-align: center;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  margin-top: 48px;

  .tit {
    font-weight: bold;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 28px;
    display: inline-block;
    margin-left: 6px;
    margin-right: 6px;
  }

  img {
    width: 33px;
    height: 22px;
  }

  .left {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-size: contain;
    position: relative;
    top: 3px;
  }

  .right {
    display: inline-block;
    width: 24px;
    height: 24px;
    background-size: contain;
    position: relative;
    top: 3px;
  }
}
.applyContent {
  background-color: #fff;
  padding-bottom: 60px;
  margin-top: 42px;
  margin-bottom: 42px;

  .matter {
    display: flex;
    align-items: center;
  }
  .bac {
    width: 100px;
    height: 100px;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    position: relative;
    margin-top: 36px;
    img {
      position: absolute;
      bottom: 10px;
      right: 0;
    }
    .text {
      font-weight: 400;
      font-size: 18px;
      color: #24456a;
      position: absolute;
      bottom: -30px;
      right: 0;
    }
  }
}
</style>
