<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow"></a-spin>
    </div>
    <div class="con">
      <div class="top">
        <div class="searchInfo AIBg">
          <div class="vocationPull" style="flex: 1">
            <div class="switch">
              <div class="AIlogo"></div>
              <!--<a-switch checked-children="on" un-checked-children="off" v-model:checked="switchOnOff" />-->
            </div>
            <a-config-provider
              :locale="zhCN"
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
            >
              <!-- <a-input v-model:value="data.name" class="inputClass" allow-clear height="56px" @focus="handleFocus(true)"
          @keyup.enter="seekContent1" placeholder="您好！我是麒麟AI助手，请问有什么可以帮您的吗？" /> -->
              <a-textarea
                v-model:value="name"
                class="inputClass"
                style="min-height: 55px; font-size: 14px"
                allow-clear
                auto-size
                @focus="handleFocus(true)"
                @keydown.enter.prevent="seekContent1"
                placeholder="您好！我是麒麟AI助手，请问有什么可以帮您的吗？"
              />
              <!--<voiceRecorder style="margin-right: 60px; height: 44px; background-color: #ffffff"
          :isTranslating="data.isTranslating" :canBtnUse="data.canBtnUse" @audioReady="handleAudio" />-->
              <new-voiceRecorder
                ref="recorderCom"
                style="
                  height: 44px;
                  background-color: #ffffff;
                  position: absolute;
                  top: 30px;
                  right: 100px;
                "
                :isTranslating="isTranslating"
                :canBtnUse="canBtnUse"
                @audioReady="handleAudio"
              ></new-voiceRecorder>
              <i
                class="sendbtn iconfont icon-fasong pointer"
                style="color: #0c70eb"
                @click="seekContent1()"
              ></i>
            </a-config-provider>
          </div>
        </div>
        <!-- <div class="img_box flex">
          <div>
            <img
              class="left_img"
              src="@/assets/images/buyList/left.png"
              alt=""
            />
            <img
              class="right_img"
              src="@/assets/images/buyList/box.png"
              alt=""
            />
          </div>
          <span class="guid" @click="showGuid('1')">新产品订购</span>
        </div> -->
        <div class="top_form">
          <span class="top_tit">基础信息</span>
          <div class="line"></div>
          <a-form ref="formRefTop" :model="formState" :rules="rules">
            <a-row>
              <a-col :span="11" style="margin-right: 3%"
                ><a-form-item
                  ref="enterpriseName"
                  label="企业名称"
                  name="enterpriseName"
                >
                  <a-input
                    v-model:value="formState.enterpriseName"
                    placeholder="请输入企业名称"
                    maxlength="20"
                  /> </a-form-item
              ></a-col>
              <a-col :span="12"
                ><a-form-item
                  ref="enterpriseScale"
                  label="企业规模"
                  name="enterpriseScale"
                >
                  <a-input
                    v-model:value="formState.enterpriseScale"
                    placeholder="请输入企业规模"
                    maxlength="10"
                  /> </a-form-item
              ></a-col>
            </a-row>
            <a-row>
              <a-col :span="11" style="margin-right: 3%">
                <a-form-item
                  ref="customerName"
                  label="联系人"
                  name="customerName"
                >
                  <a-input
                    v-model:value="formState.customerName"
                    placeholder="请输入联系人"
                    maxlength="10"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  ref="contactNumber"
                  label="联系电话"
                  name="contactNumber"
                >
                  <a-input
                    v-model:value="formState.contactNumber"
                    placeholder="请输入客户电话"
                    maxlength="11"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="11" style="margin-right: 3%">
                <a-form-item ref="address" label="联系地址" name="address">
                  <a-input
                    v-model:value="formState.address"
                    placeholder="请输入联系地址"
                    maxlength="20"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item label="所属行业" name="industry">
                  <a-select
                    placeholder="请选择所属行业"
                    v-model:value="formState.industry"
                    style="width: 480px"
                  >
                    <template v-for="(opt, index) in typeList" :key="index">
                      <a-select-option :value="opt.name">
                        {{ opt.name }}
                      </a-select-option>
                    </template>
                  </a-select>
                </a-form-item>
              </a-col>
            </a-row>

            <!-- <a-form-item :wrapper-col="{ span: 14, offset: 4 }">
              <a-button type="primary" @click="onSubmit">Create</a-button>
              <a-button style="margin-left: 10px" @click="resetForm"
                >Reset</a-button
              >
            </a-form-item> -->
          </a-form>
        </div>
      </div>
      <div class="content">
        <div class="content_box">
          <span class="con_tit tit">产品列表</span>
        </div>
        <a-table
          :columns="tableJson"
          :data-source="tableData"
          :pagination="pagination"
          :rowSelection="{
            selectedRowKeys: selectedRowKeys,
            onChange: onSelectionChange,
            selectAll: selectAll,
          }"
          :rowKey="
            (record) => {
              return record.id;
            }
          "
        >
          <template #productDesc="{ record, text }">
            <a-tooltip placement="topLeft">
              <template v-if="isShowToolTip(text, 10)" #title>{{
                text
              }}</template>
              {{ text }}
            </a-tooltip>
          </template>
          <template #productTariffList="{ record, text }">
            <span>
              <a-select
                v-model:value="record.productType"
                @change="(value) => handleSpecChange(value, record)"
              >
                <template v-for="(opt, key) in text" :key="opt.id">
                  <a-select-option :value="opt.id">
                    <a-tooltip placement="topLeft">
                      <template
                        v-if="isShowToolTip(opt.specification, 10)"
                        #title
                        >{{ opt.specification }}</template
                      >
                      {{ opt.specification }}
                    </a-tooltip>
                  </a-select-option>
                </template>
              </a-select>
            </span>
          </template>
          <template #price="{ record, text }">
            <div
              style="
                font-weight: 400;
                font-size: 12px;
                color: rgba(0, 0, 0, 0.65);
                line-height: 14px;
              "
            >
              <span>原价：</span>
              <span
                style="
                  font-weight: 500;
                  font-size: 14px;
                  color: rgba(0, 0, 0, 0.85);
                  line-height: 16px;
                "
                >{{ text }}</span
              >
              <span>{{ unit }}</span>
            </div>
          </template>
          <template #productQuantity="{ record, text }">
            <a-input-number
              v-model:value="record.productQuantity"
              :formatter="(value) => `${value}`"
              min="1"
              :precision="0"
              :parser="(value) => value.replace('.', '')"
            />
          </template>
          <template #action="{ record, text }">
            <a-popconfirm
              title="确定删除当前产品?"
              ok-text="确定"
              cancel-text="取消"
              @confirm="deletePro(record.id, '1')"
            >
              <span
                class="font_F51D0F margin_r_12"
                style="cursor: pointer; color: #0c70eb"
                >删除</span
              >
            </a-popconfirm></template
          >
        </a-table>
        <div class="addPro" id="addPro">
          <span @click="showGuid('2')">点击添加产品</span>
        </div>
      </div>
      <div class="content">
        <div class="top_form">
          <span class="top_tit">客户经理信息</span>
          <div class="line"></div>
          <a-form ref="formRef" :model="formState" :rules="rules">
            <a-row>
              <a-col :span="11" style="margin-right: 3%">
                <a-form-item
                  ref="customerManager"
                  label="客户经理"
                  name="customerManager"
                >
                  <a-input
                    v-model:value="formState.customerManager"
                    placeholder="请输入客户经理名称"
                    maxlength="10"
                  />
                </a-form-item>
              </a-col>
              <a-col :span="12">
                <a-form-item
                  ref="managerNumber"
                  label="联系电话"
                  name="managerNumber"
                >
                  <a-input
                    v-model:value="formState.managerNumber"
                    placeholder="请输入客户经理电话"
                    maxlength="11"
                  />
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="11" style="margin-right: 3%">
                <a-form-item
                  ref="contractDate"
                  label="生成日期"
                  name="contractDate"
                >
                  <a-date-picker
                    v-model:value="formState.contractDate"
                    placeholder="请选择生成日期"
                    :value-format="'YYYY-MM-DD'"
                  />
                </a-form-item>
              </a-col>
            </a-row>
          </a-form>
        </div>
      </div>
    </div>
    <div class="foot">
      <div class="left_btn">
        <a-checkbox v-model:checked="checkedStatus" @click="checkedAll"
          >全选</a-checkbox
        >
        <span class="delete" @click="deletePro">删除已选产品</span>
        <!-- <span class="btn">分享</span> -->
        <a-button
          class="btn"
          :loading="loading"
          @click="holdData"
          style="cursor: pointer"
          >保存</a-button
        >
      </div>
      <div class="right_con">
        <div class="right_btn" @click="born">
          <span>生成产品单</span>
        </div>
      </div>
    </div>
    <a-modal
      :visible="previewVisible"
      @cancel="close"
      :title="title"
      :width="modalWidth"
      :maskClosable="false"
      :footer="null"
    >
      <templete #title>
        <img
          src="../../assets/images/combine/title.png"
          alt=""
          style="width: 14px; height: 8px"
        />
      </templete>
      <div v-if="modelType == '1'">
        <guide-table @close="close"></guide-table>
      </div>
      <div v-if="modelType == '3'">
        <a-form :model="formData" labelAlign="right" ref="groupForm">
          <a-row>
            <a-col :span="24">
              <a-form-item
                label="定制名称"
                name="name"
                :rules="[{ required: true, message: '请输入定制名称' }]"
              >
                <a-input
                  v-model:value="formData.name"
                  placeholder="请输入定制名称"
                >
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="flex just-center">
          <a-button style="margin-right: 20px" @click="close">取消</a-button>
          <a-button type="primary" @click="submit()">
            <span>提交</span>
          </a-button>
        </div>
      </div>
      <div v-if="modelType == '2'">
        <chose-table
          :parentId="parentId"
          @close="close"
          @onSubmit="onSubmit"
          :proList="proList"
        ></chose-table>
      </div>
    </a-modal>
    <a-modal
      :visible="showTips"
      @cancel="manualOrAiVisible = false"
      title="选择场景定制方法"
      width="634px"
      :destroyOnClose="true"
      :maskClosable="false"
      :footer="null"
      centered
      :closable="false"
    >
      <templete #header>
        <img
          src="@/assets/images/home/<USER>"
          alt=""
          style="width: 14px; height: 8px; margin-right: 10px"
        />
      </templete>
      <div class="flex just-sb" style="padding: 0 13px 13px">
        <div class="manual" @click="toSelf">
          <img src="@/assets/images/newProject/manual.png" alt="" />
          <div>手动定制</div>
          <div>通过场景定制流程添加产品</div>
        </div>
        <div class="ai" @click="toAI">
          <img src="@/assets/images/newProject/aiLogo.png" alt="" />
          <div>AI定制</div>
          <div>通过麒麟AI助手智能分析定制场景</div>
        </div>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import BasicTable from "@/components/table/table.vue";
import GuideTable from "./compontents/guideTable.vue";
import ChoseTable from "./compontents/choseTable.vue";
import { getTradeList } from "@/api/solutionNew/home";
import { useHomeStore } from "@/store";
import { message } from "ant-design-vue";
import { useRouter, useRoute } from "vue-router";
import {
  deleteShop,
  holdShop,
  shopTable,
  deleteManyShop,
  viewShop,
  postList,
} from "@/api/buyList/index";
import { isShowToolTip, getDateTime } from "@/utils/index.js";
import eventBus from "@/utils/eventBus";
export default defineComponent({
  name: "buyList",
  components: {
    BasicTable,
    ChoseTable,
    GuideTable,
  },
  setup() {
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    const router = useRouter();
    const route = useRoute();
    const data = reactive({
      formState: {
        customerName: "",
        contactNumber: "",
        enterpriseScale: "",
        industry: undefined,
        loadShow: false,
        address: "",
        enterpriseName: "",
        customerManager: userInfo.realName,
        managerNumber: userInfo.phone,
        contractDate: getDateTime("date"),
      },
      typeList: [],
      proList: [],
      formData: {
        name: "",
      },
      tabData: [],
      checkedStatus: false,
      selectIds: [],
      selectList: [],
      modelType: "1",
      loading: false,
      tableJson: [
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
          align: "left",
          slots: { customRender: "productName" },
        },
        {
          title: "产品介绍",
          dataIndex: "productDesc",
          key: "productDesc",
          align: "left",
          ellipsis: true,
          slots: { customRender: "productDesc" },
        },
        {
          title: "产品规格",
          dataIndex: "productTariffList",
          key: "productTariffList",
          align: "left",
          ellipsis: true,
          slots: { customRender: "productTariffList" },
          width: 200,
        },
        {
          title: "标准价格",
          dataIndex: "price",
          key: "price",
          align: "left",
          ellipsis: true,
          slots: { customRender: "price" },
          width: 200,
        },
        {
          title: "产品数量",
          dataIndex: "productQuantity",
          key: "productQuantity",
          align: "left",
          ellipsis: true,
          slots: { customRender: "productQuantity" },
        },
        {
          title: "操作",
          dataIndex: "action",
          align: "left",
          width: 180,
          slots: { customRender: "action" },
        },
      ],
      previewVisible: false,
      title: "",
      type: "",
      id: undefined,
      modalWidth: "1200px",
      choseType: null,
      parentId: null,
      tableData: [],
      showTips: false,
      name: "",
    });

    const rules = {
      customerName: [
        { required: true, message: "请输入联系人", trigger: "blur" },
      ],
      contactNumber: [
        { required: true, message: "请输入联系电话", trigger: "blur" },
        { pattern: /^1(3|4|5|6|7|8|9)\d{9}$/, message: "请正确输入手机号码" },
      ],
      enterpriseName: [
        { required: true, message: "请输入企业名称", trigger: "blur" },
      ],
    };
    const counterStore = useHomeStore();
    const showGuid = (e) => {
      data.modelType = e;
      data.previewVisible = true;
      data.modalWidth = "1200px";
      if (e == "1") {
        data.title = "引导";
      } else if (e == "2") {
        data.title = "添加产品";
        counterStore.buyListStroe = {
          ...data.formState,
        };
        let productPackageLists = [];
        data.tableData.forEach((ele) => {
          let foundMatch = false;
          let tariffId = "";
          ele.productTariffList.forEach((val) => {
            if (ele.productType === val.id) {
              tariffId = val.id;
              foundMatch = true;
            }
          });
          if (ele.productType == "") {
            foundMatch = true;
            tariffId = "";
          }

          if (foundMatch) {
            productPackageLists.push({
              productQuantity: ele.productQuantity,
              productId: ele.productId,
              tariffId: tariffId,
            });
          }
        });
        let params = {
          productPackageLists: productPackageLists,
        };
        data.proList = params.productPackageLists;
      }
    };
    const close = () => {
      data.previewVisible = false;
      if (data.modelType == "3") {
        groupForm.value.resetFields();
      }
      if (data.modelType == "1") {
        setTimeout(() => {
          getList();
        }, 1000);
      }
    };
    const onSubmit = () => {
      data.previewVisible = false;
      if (data.modelType == "3") {
        groupForm.value.resetFields();
      }
      setTimeout(() => {
        getList();
      }, 1000);
    };
    const getDate = () => {
      const now = new Date();
      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0");
      const day = String(now.getDate()).padStart(2, "0");
      data.formState.contractDate = `${year}-${month}-${day}`;
    };
    const getTypeList = () => {
      getTradeList()
        .then((res) => {
          data.typeList = res.data;
        })
        .catch((error) => {
          console.error("获取行业列表失败：", error);
        });
    };
    getTypeList();
    const formRef = ref();
    const formRefTop = ref();
    const holdData = () => {
      formRefTop.value.validate().then(() => {
        if (data.selectIds.length === 0) {
          message.warning("请选择产品");
          return;
        } else {
          data.previewVisible = true;
          data.modalWidth = "900px";
          data.modelType = "3";
          data.title = "定制名称";
        }
      });
    };
    const getList = () => {
      data.loadShow = true;
      delete counterStore.buyListStroe.productPackageLists;
      shopTable(1).then((res) => {
        data.loadShow = false;
        data.tableData = res.data.productPackageLists || [];
        data.id = res.data.id;
        if (res.data.productPackageLists.length == 0) {
          data.showTips = true;
        } else {
          router.push({
            query: {
              type: 10,
            },
            name: "newAllProject",
          });
        }
        data.tableData.forEach((element) => {
          if (element.productTariffList.length > 0) {
            const selectedTariff = element.tariffId
              ? element.productTariffList.find((t) => t.id === element.tariffId)
              : element.productTariffList[0];
            if (selectedTariff) {
              element.productType = selectedTariff.id;
              element.price = `${selectedTariff.tariff}${selectedTariff.unit}`;
              if (selectedTariff.tariff >= 8888) {
                element.price = "以具体业务定价为准";
              }
            } else {
              element.productType = "";
              element.price = "-";
            }
          } else {
            element.productType = "";
            element.price = "-";
          }
          if (
            element.tariffId == null &&
            element.productTariffList.length > 0
          ) {
            element.tariffId = element.productTariffList[0].id;
          }
        });
        data.formState = {
          ...res.data,
          customerManager: res.data.customerManager || userInfo.realName,
          managerNumber: res.data.managerNumber || userInfo.phone,
          contractDate: res.data.contractDate || getDateTime("date"),
        };
        data.parentId = res.data.id;
        if (Object.keys(counterStore.buyListStroe).length > 0) {
          data.formState = {
            ...data.formState,
            ...counterStore.buyListStroe,
          };
        }
        data.checkedStatus =
          data.tableData.length > 0 &&
          data.tableData.length === data.selectIds.length;
        checkedAll();
        const ids = data.tableData.map((item) => item.id);
        selectedRowKeys.value = ids;
        data.selectIds = ids;
        data.selectList = data.tableData;
      });
    };
    const conShowData = () => {
      setTimeout(() => {
        getList();
      }, 1000);
    };
    eventBus.on("buyListRefresh", conShowData);

    const handleSpecChange = (id, record) => {
      const matchedItem = record.productTariffList.find(
        (item) => item.id === id
      );
      if (matchedItem) {
        if (matchedItem.tariff >= 8888) {
          record.price = "以具体业务定价为准";
        } else {
          record.price = `${matchedItem.tariff}${matchedItem.unit}`;
        }
        record.productType = matchedItem.id;
        record.tariffId = matchedItem.id;
      }
    };
    const comfirmDelete = (e) => {
      deleteShop([{ productId: e, type: 2 }]).then((res) => {
        getList();
      });
    };
    getList();
    const selectedRowKeys = ref([]);
    const onSelectionChange = (selectedKeys, selectedRows) => {
      selectedRowKeys.value = selectedKeys;
      data.selectIds = selectedKeys;
      data.selectList = selectedRows;
      data.tabData = selectedRows;
      selectedRows.forEach((item) => {
        item.productTariffList.some((value) => {
          if (item.productType === value.id) {
            item.tariffId = value.id;
            return true;
          }
          return false;
        });
      });

      data.checkedStatus = selectedRows.length === data.tableData.length;
    };

    const deletePro = async (value, type) => {
      counterStore.buyListStroe = {
        ...data.formState,
      };
      try {
        if (type === "1") {
          await deleteManyShop(value);
          data.selectIds = data.selectIds.filter((item) => item != value);
          data.selectList = data.selectList.filter((item) => item.id != value);
          data.tabData = data.tabData.filter((item) => item.id != value);
        } else if (data.selectIds.length > 0) {
          const params = data.selectIds.join(",");
          await deleteManyShop(params);
          data.selectIds = [];
        } else {
          message.warning("请选择产品");
          return false;
        }
        getList();
      } catch (error) {
        console.error("Error deleting products:", error);
      }
    };
    const deletePro1 = async (value, type) => {
      try {
        if (type === "1") {
          await deleteManyShop(value);
          data.selectIds = data.selectIds.filter((item) => item != value);
          data.tabData = data.tabData.filter((item) => item.id != value);
        } else if (data.selectIds.length > 0) {
          const params = data.selectIds.join(",");
          await deleteManyShop(params);
          data.selectIds = [];
        } else {
          return false;
        }
        getList();
      } catch (error) {
        console.error("Error deleting products:", error);
      }
    };

    const checkedAll = () => {
      if (data.checkedStatus == false) {
        data.tabData = data.tableData;
        const ids = data.tableData.map((item) => item.id);
        selectedRowKeys.value = ids;
        data.selectIds = ids;
        data.selectList = data.tableData;
        data.checkedStatus = true;
      } else {
        selectedRowKeys.value = [];
        data.selectIds = [];
      }
    };
    const born = () => {
      const tabData = data.tabData;
      let show = true;
      let seen = {};
      tabData.forEach((item) => {
        const key = `${item.tariffId}-${item.productId}`;
        if (seen[key]) {
          show = false;
        } else {
          seen[key] = true;
        }
      });

      data.tabData.forEach((item) => {
        if (item.productTariffList && item.productTariffList.length > 0) {
          item.productType_specification = item.productTariffList.filter(
            (i) => item.productType == i.id
          )[0].specification;
        }
      });
      if (show) {
        counterStore.buyListStroe = {
          ...data.formState,
          productPackageLists: data.tabData,
        };
        formRefTop.value.validate().then(() => {
          formRef.value
            .validate()
            .then(() => {
              if (data.selectIds.length === 0) {
                message.warning("请选择产品");
                return;
              } else {
                window.open(window.location.origin + "/#/buyDetail", "_blank");
              }
            })
            .catch((e) => {});
        });
      } else {
        message.warning("存在重复规格的产品，请重新选择");
      }
    };
    const pagination = {
      defaultPageSize: 100, // 默认每页显示条数
    };
    eventBus.on("checkAll", checkedAll);
    eventBus.on("deletePro", deletePro1);
    window.addEventListener("beforeunload", (event) => {
      // 设置returnValue属性可以显示一个提示信息，询问用户是否确实要离开页面
      counterStore.buyListStroe = {
        ...data.formState,
        productPackageLists: data.tabData,
      };
    });

    document.addEventListener("DOMContentLoaded", (event) => {
      // 确保DOM完全加载后执行此操作
      var referenceNode = document.getElementById("addPro");
      if (referenceNode) {
        var newNode = document.createElement("div");
        // 确认参考节点存在后再插入
        referenceNode.parentNode.insertBefore(newNode, referenceNode);
      } else {
        console.error("参考节点不存在，无法插入新节点。");
      }
    });
    const groupForm = ref();
    const submit = () => {
      groupForm.value.validate().then(() => {
        data.loading = true;
        let productPackageLists = data.selectList.map((item) => {
          return {
            productId: item.productId,
            productQuantity: item.productQuantity,
            tariffId: item.tariffId,
          };
        });
        let params = {
          ...data.formState,
          productPackageLists: productPackageLists,
          id: data.id,
          name: data.formData.name,
        };
        holdShop(params).then((res) => {
          if (res.code == 200) {
            message.success("保存成功,可在后台订购列表查看");
            console.log(`成功`);

            data.selectIds = [];
            selectedRowKeys.value = [];
            data.checkedStatus = false;
            data.loading = false;
            data.tableData = [];
            data.previewVisible = false;
            data.formData = {
              name: "",
            };
          } else {
            data.loading = false;
            data.previewVisible = false;
          }
        });
      });
    };
    const toAI = () => {
      router.push({
        query: {
          type: 10,
        },
        name: "newAllProject",
      });
    };
    const toSelf = () => {
      data.previewVisible = true;
      data.modelType = 1;
      counterStore.aiBuyListStroe = {};
    };
    const seekContent = () => {};
    const seekContent1 = (event) => {
      if (data.name == "") {
        message.error("请输入搜索内容");
        return false;
      }
      data.historyShow = false;
      // 点击搜索关闭webscoket连接
      if (recorderCom.value) {
        console.log("关闭webscoket连接");
        recorderCom.value.closeConnection();
      }
      router.push({
        path: "/newProject/newProject",
      });
      localStorage.setItem("seekName", data.name);
      localStorage.setItem("isFromHome", true);
    };
    const handleFocus = (value) => {
      data.historyShow = value;
    };
    // 语音输入
    const handleAudio = (text) => {
      console.log("text", text);
      data.name = text;
    };
    getDate();
    return {
      ...toRefs(data),
      born,
      handleAudio,
      handleFocus,
      seekContent1,
      seekContent,
      toAI,
      toSelf,
      pagination,
      getDate,
      groupForm,
      conShowData,
      deletePro1,
      onSubmit,
      submit,
      checkedAll,
      deletePro,
      route,
      handleSpecChange,
      counterStore,
      onSelectionChange,
      selectedRowKeys,
      comfirmDelete,
      isShowToolTip,
      rules,
      getList,
      formRefTop,
      showGuid,
      close,
      getTypeList,
      holdData,
      formRef,
      router,
      getDateTime,
    };
  },
});
</script>
<style lang="scss" scoped>
::v-deep(.ant-table-pagination.ant-pagination) {
  display: none !important;
}

::v-deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  border: 1px solid #d9d9d9 !important;
}

::v-deep(.ant-select) {
  width: 150px;
}

@import "./index.scss";
</style>