.ombine-btn {
    float: right;
}

.listContent {
    margin-top: 124px;
    padding: 24px;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
    border-radius: 8px 8px 8px 8px;
    .line {
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 6, 14, 0.08);
    }
    .title {
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        .sub-count {
            display: inline-block;
            margin-left: 12px;
            font-weight: 400;
            font-size: 14px;
            color: #00060E;
        }
    }
}

.tabContent {
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    position: relative;
    .loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }
    .cardContent {
        width: 100%;
        margin-bottom: 24px;
        position: relative;
    }
    .cardContent::-webkit-scrollbar {
        display: none;
    }
    .tab_text {
        font-size: 18px;
        color: #262626;
        cursor: pointer;
        height: 40px;
    }
    .activeTab {
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
        padding-top: 32px;
    }
    .topTab {
        margin-top: 0;
        background-image: url("@/assets/images/home/<USER>");
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
    }
     :deep(.ant-tag) {
        font-weight: 500;
        font-size: 12px;
        color: #2E7FFF;
        height: 20px;
        display: flex;
        align-items: center;
    }
    .card_total {
        // border-bottom: 1px solid #DAE2F5;
    }
    .card_content {
        height: 163px;
        border-radius: 10px;
        margin-top: 24px;
        cursor: pointer;
        position: relative;
        background: #F5F7FC;
    }
    .newImg {
        position: absolute;
        top: 0px;
    }
    .cart-button {
        position: absolute;
        right: 10px;
        bottom: 10px;
        width: 80px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        border: 1px solid red;
        color: red;
        ;
    }
    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }
    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 117px;
        margin-left: 12px;
        width: 80%
    }
    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        .card_tag {
            // display: flex;
            // align-items: center;
            .cardTag {
                display: inline-block;
                padding-left: 8px;
                padding-right: 8px;
                margin-right: 8px;
            }
            .author {
                margin-right: 40px;
            }
        }
        .card_title {
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            margin-bottom: 4px;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }
         :deep(.cityStyle) {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;
            .add-icon {
                width: 16px;
            }
            .add {
                font-weight: 500;
                font-size: 12px;
                color: #0C70EB;
            }
        }
    }
    .card_des {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
    }
    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }
}

.buttons {
    padding: 24px;
    margin-top: 36px;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
    border-radius: 8px 8px 8px 8px;
    .left {
    	align-items: center;
         :deep(.ant-btn) {
            height: 36px;
            border-radius: 4px;
            padding: 4px 0px;
            border: none !important;
            margin-right: 12px;
            font-weight: 500;
            font-size: 16px;
            color: #0C70EB;
            box-shadow: none
        }
        
        .AIadvise {
        	width: 226px;
        	height: 59px;
        	background-image: url(../../../assets/images/AIadvise.png);
        	position: relative;
        	cursor: pointer;
        	.AItitle {
        		font-weight: 600;
        		font-size: 16px;
        		color: #0C70EB;
        		position: absolute;
        		top: 18px;
        		left: 66px;
        	}
        }
    }
    .right {
    	align-items: center;
         :deep(.ant-btn) {
            height: 36px;
            border-radius: 4px;
            padding: 4px 24px;
            margin-left: 20px;
            border: none !important;
            font-weight: 500;
            font-size: 16px;
            color: #0C70EB;
            text-align: center;
            background: rgba(12, 112, 235, 0.08);
        }
        .apply {
            background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
            color: #fff;
        }
    }
}

.emptyPhoto {
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
    margin: 24px;
    .tip {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
    }
    .select {
        color: #1E63FF;
        cursor: pointer;
    }
}

:deep(.ant-image-img) {
    height: 117px !important;
}

.btncontent {
    display: flex;
    width: 100%;
    justify-content: center;
    .btn {
        padding-left: 12px;
        padding-right: 12px;
        height: 32px;
        line-height: 32px;
        color: #0C70EB;
        background: #ECF4FD;
        margin-top: 24px;
        cursor: pointer;
    }
}