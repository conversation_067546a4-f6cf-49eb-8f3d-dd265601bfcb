<template>
  <div class="AIBody">
    <div class="title">本方案总有{{allNumber}}个方案场景，经麒麟AI助手智能分析，预估可使用公司自由能力{{ownAbilityNum}}个，自有产品{{ownProductNum}}个。</div>
    <div>
    	<a-table :columns="columns" 
    		:rowKey="(record, index) => { return index }"  :data-source="tableList" bordered :pagination="false">
    		
		  </a-table>
    </div>
    <div class="btn_box">
    	<div>
    		<span class="refuse" @click="refuse">取消</span>
    		<span class="submit" @click="upload">下载</span>
    	</div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch } from "vue";
import { useRouter } from "vue-router";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { showAICore, uploadAICore } from "@/api/combine/combine.js";

import eventBus from "@/utils/eventBus";
import { select } from "ranui";
export default defineComponent({
  components: {},
  props: {
  	showAIIdList: {
      type: Array,
      default: [],
    }
  },
  setup(props, { emit }) {
  	const renderContent = ({text,index}) => {
    	const obj = {
    		children: text,
    		props: {
    			rowSpan : data.showRow[index],
    			colSpan : 1
    		},
    	};
    	return obj;
    }
    const data = reactive({
    	loadingShow: false,
    	allNumber: 0,
    	ownAbilityNum: 0,
    	ownProductNum: 0,
    	columns: [
    		{title: '序号', dataIndex: 'index', width: 50, customRender: renderContent, customHeaderCell: ()=>({
    			style: {
    				backgroundColor: '#99A6B6',
    				color: 'white',
    				textAlign: "center"
    			}
    		}), customCell: ()=>({
    			style: {
    				backgroundColor: '#99A6B6',
    				color: 'white',
    				textAlign: "center"
    			}
    		})},
    		{title: '方案场景名称', dataIndex: 'implName', width:100, customRender: renderContent, customHeaderCell: ()=>({
    			style: {
    				backgroundColor: '#89A9F9',
    				color: 'white',
    			}
    		})},
    		{title: '方案场景介绍', dataIndex: 'implDesc', width:200, customRender: renderContent, customHeaderCell: ()=>({
    			style: {
    				backgroundColor: '#89A9F9',
    				color: 'white',
    			}
    		})},
    		{title: '自有能力名称', dataIndex: 'implAbilityName', width:100, customHeaderCell: ()=>({
    			style: {
    				backgroundColor: '#67B1ED',
    				color: 'white',
    			}
    		})},
    		{title: '自有能力介绍', dataIndex: 'implAbilityDesc', width:200, customHeaderCell: ()=>({
    			style: {
    				backgroundColor: '#67B1ED',
    				color: 'white',
    			}
    		})},
    		{title: '标准产品名称', dataIndex: 'implProductName', width:100, customHeaderCell: ()=>({
    			style: {
    				backgroundColor: '#33C0DC',
    				color: 'white',
    			}
    		})},
    		{title: '标准产品介绍', dataIndex: 'implProductDesc', width:200, customHeaderCell: ()=>({
    			style: {
    				backgroundColor: '#33C0DC',
    				color: 'white',
    			}
    		})},
    	],
    	tableList: [],
    	chongList: [],
    	chongNum: {},
    	showRow: [],
    });
    const getList = () => {
      data.loadingShow = true;
      showAICore({
    		solutionId: props.showAIIdList.join(',')
    	}).then(res => {
    		if(res.code == 200){
    			data.loadingShow = false;
    			data.tableList = res.data.vo;
    			data.allNumber = res.data.modelSum;
    			data.ownAbilityNum = res.data.abilitySum;
    			data.ownProductNum = res.data.productSum;
    			
		    	data.chongList = [];
		    	data.chongNum = {};
		    	let indexNum = 0
		    	let indexInfo = 1
		    	data.tableList.forEach(item => {
		    		//item.index = ++indexNum
		    		data.chongList.push(item.implId);
		    		for(let i in item){
		    			if(item[i] == null){
		    				item[i] = "-"
		    			}
		    		}
		    	})
		    	data.chongList = [...new Set(data.chongList)];
		    	data.chongList.forEach(item => {
		    		data.chongNum[item] = 0
		    	})
		    	data.tableList.forEach(item => {
		    		data.chongNum[item.implId]++
		    	})
		    	data.showRow = [];
		    	for(let i in data.chongNum){
		    		data.tableList[indexNum].index = indexInfo;
		    		let num = [];
		    		if(data.chongNum[i]>1){
			    		for(let j=0;j<data.chongNum[i]-1;j++){
			    			num.push(0);
			    			indexNum++
			    			data.tableList[indexNum].index = indexInfo;
			    		}
		    		}
		    		indexNum++
		    		indexInfo++
		    		data.showRow = [
		    			...data.showRow,
		    			data.chongNum[i],
		    			...num
		    		]
		    	}
    		}
    	})
    };
    getList();
    eventBus.on("moduleRefresh", getList);
    
    const upload = () => {
    	uploadAICore({
    		solutionId: props.showAIIdList.join(',')
    	}).then(res => {
    		window.open(res)
    		return false;
    		let blob = new Blob([res], {
					type: "application/vnd.ms-excel;charset=UTF-8",
				});
    		let downloadElement = document.createElement("a");
	      let href = window.URL.createObjectURL(blob); //创建下载的链接
	      downloadElement.href = href;
	      document.body.appendChild(downloadElement);
	      downloadElement.click(); //点击下载
	      document.body.removeChild(downloadElement); //下载完成移除元素
	      window.URL.revokeObjectURL(href); //释放掉blob对象
    	})
    }
    
    const refuse = () => {
      emit("close");
    };
    
    return {
      ...toRefs(data),
      getList,
      refuse,
      upload
    };
  },
});
</script>

<style lang="scss" scoped src="./AITable.scss"></style>

<style lang="scss">
	
</style>