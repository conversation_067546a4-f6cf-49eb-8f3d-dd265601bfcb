<template>
  <div class="loginTop">
  	<div class="beian">苏ICP备2022032515号-2</div>

    <div class="logoSite">
      <!-- <img class="logo" src="@/assets/images/home/<USER>" /> -->
      <img class="logo" src="@/assets/images/newProject/logoB.svg" />
    </div>

    <div class="loginInfo">
      <div class="loginTitle">麒麟能力平台</div>
      <a-tabs v-model:activeKey="loginType" size="large" class="loginTabs" :class="[!ecologyShow ? 'ecology-theme' : 'default-theme']">
        <a-tab-pane key="1" :tab="ecologyShow ? '生态用户登录' : '账号登录'">
          <a-form ref="loginForm" :model="loginState">
            <a-form-item name="username" :rules="[{ required: true, message: '请输入用户名' }]">
              <a-input v-model:value="loginState.username" style="height: 50px" placeholder="请输入用户名" autocomplete="username">
                <template #prefix>
                  <img v-if="!ecologyShow" style="width: 22px; height: 22px;" src="@/assets/images/login/userName.png" />
                  <img v-else style="width: 22px; height: 22px;" src="@/assets/images/login/userNameNew.png" />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item name="inputPwd" :rules="[{ required: true, message: '请输入密码' }]">
              <a-input-password v-model:value="loginState.inputPwd" style="height: 50px" placeholder="请输入密码" autocomplete="new-password">
                <template #prefix>
                  <img v-if="!ecologyShow" style="width: 22px; height: 22px;" src="@/assets/images/login/passward.png" />
                  <img v-else style="width: 22px; height: 22px;" src="@/assets/images/login/passwardNew.png" />
                </template>
              </a-input-password>
            </a-form-item>

            <a-form-item name="captcha" :rules="[{ required: true, message: '请输入验证码' }]">
              <div class="authCode">
                <a-input v-model:value="loginState.captcha" style="width: 230px; height: 50px; margin-right: 16px"
                  placeholder="请输入验证码" :maxlength="4">
                  <template #prefix>
                    <img v-if="!ecologyShow" style="width: 22px; height: 22px" src="@/assets/images/login/verify.png" />
                    <img v-else style="width: 22px; height: 22px;" src="@/assets/images/login/verifyNew.png" />
                  </template>
                </a-input>
                <img style="width: 130px; height: 48px" :src="`data:image/jpg;base64,${imgSource}`"
                  @click="refreshCode" />
              </div>
            </a-form-item>
          </a-form>
          <div class="loginBtn">
            <a-button v-if="!ecologyShow" @click="submit" type="primary" html-type="submit">登录</a-button>
            <a-button v-else @click="submit" type="primary" html-type="submit" style="background: #00B3B9;">登录</a-button>
            <div class="apply">
              <div v-if="!ecologyShow" @click="backPassword">找回密码</div>
              <div v-else @click="backPassword" style="color: #00B3B9;">找回密码</div>
              <div v-if="!ecologyShow" @click="addDialog">账号开通申请</div>
            </div>
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="短信验证">
          <a-form ref="loginForm2" :model="loginState">
            <a-form-item name="phone" :rules="[{ required: true, trigger: 'change', validator: validateTel }]">
              <a-input v-model:value="loginState.phone" style="height: 50px" placeholder="请输入手机号码">
                <template #prefix>
                  <img v-if="!ecologyShow" style="width: 22px; height: 22px;" src="@/assets/images/login/phone.png" />
                  <img v-else style="width: 22px; height: 22px;" src="@/assets/images/login/phoneNew.png" />
                </template>
              </a-input>
            </a-form-item>

            <a-form-item name="captchaCode" style="position: relative;"
              :rules="[{ required: true, message: '请输入验证码' }]">
              <a-input v-model:value="loginState.captchaCode" style="height: 50px" placeholder="请输入验证码">
                <template #prefix>
                  <img v-if="!ecologyShow" style="width: 22px; height: 22px" src="@/assets/images/login/verify.png" />
                  <img v-else style="width: 22px; height: 22px;" src="@/assets/images/login/verifyNew.png" />
                </template>
              </a-input>
              <a-button v-if="codeSet" @click="openCode" class="codeBut">
              	<span v-if="!ecologyShow">获取验证码</span>
              	<span v-else style="color: #00B3B9;">获取验证码</span>
              </a-button>
              <a-button v-else class="codeBut" style="color: #666666;" disabled>{{ mins }}s</a-button>
            </a-form-item>
            <a-form-item style="height: 50px"></a-form-item>
          </a-form>
          <div class="loginBtn">
            <a-button v-if="!ecologyShow" @click="submitCode" type="primary" html-type="submit">登录</a-button>
            <a-button v-else @click="submitCode" type="primary" html-type="submit" style="background: #00B3B9;">登录</a-button>
            <div class="apply">
              <div v-if="!ecologyShow" @click="backPassword">找回密码</div>
              <div v-else @click="backPassword" style="color: #00B3B9;">找回密码</div>
            </div>

          </div>
        </a-tab-pane>
      </a-tabs>
      <div class="iparterApply apply">
        <div @click="ecologyLogin" v-if="!ecologyShow" style="color: #00B3B9;">我是合作方</div>
        <div @click="userLogin" v-else>我是员工</div>
      </div>
      <div class="version">版本号：{{version}}</div>

    </div>
  </div>

  <addForm :addData="addSource" :form="formData" :originList="originList"></addForm>

  <resetPassword v-if="resetPass.isShow" :resetPass="resetPass" :form="resetData"></resetPassword>

  <a-modal :visible="previewVisible" centered :footer="null" @cancel="handleCancel">
    <div class="authCode">
      <a-input v-model:value="phoneCode" style="width: 230px; height: 50px; margin-right: 16px" placeholder="请输入验证码"
        :maxlength="4">
        <template #prefix>
          <img v-if="!ecologyShow" style="width: 22px; height: 22px" src="@/assets/images/login/verify.png" />
          <img v-else style="width: 22px; height: 22px;" src="@/assets/images/login/verifyNew.png" />
        </template>
      </a-input>
      <img style="width: 130px; height: 48px" :src="`data:image/jpg;base64,${imgSource}`" @click="refreshCode" />
    </div>
    <div style="margin-top: 10px;">
      <a-button v-if="!ecologyShow" @click="getCodeInfo" type="primary">确定</a-button>
      <a-button v-else @click="getCodeInfo" type="primary" style="background: #00B3B9;">确定</a-button>
    </div>
  </a-modal>

</template>

<script>
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import AES from "@/utils/cryptoPwd.js";
import addForm from "./addForm.vue";
import resetPassword from "./resetPassword.vue";
import { getIdentifyCode, login, getUserInfo, getCode, freeLogin } from "@/api/login/login.js";
import { getTree } from "@/api/system/team";
import { useRouter, useRoute } from "vue-router";
import { message } from "ant-design-vue";

export default defineComponent({
  components: {
    addForm,
    resetPassword
  },
  setup() {
    const Router = useRouter();
    const Route = useRoute();
    const loginForm = ref(null);
    const loginForm2 = ref(null);

    const data = reactive({
      version:'V3.1.0',
      previewVisible: false,
      formData: {},
      loginState: {
        username: "",
        password: "",
        inputPwd: "",
        captcha: "",
        captchaId: "",
        phone: "",
        captchaCode: ""
      },
      phoneCode: "",
      imgSource: "",
      loginType: "1",
      codeSet: true,
      ecologyShow: false,
      mins: 60,
      addSource: {
        isShow: false,
        action: "",
        type: 1,
        userIds: "",
      },
      originList: [],
      resetPass: {
      	fromType: 1,
        procedure: 1,
        isShow: false,
      },
      resetData: {}
    });

    // 生成图形中的验证码
    const refreshCode = () => {
      console.log(`刷新验证码`);
      getIdentifyCode().then((res) => {
        if (res.code === 200) {
          data.imgSource = res.data.captcha;
          data.loginState.captchaId = res.data.captchaId;
        }
      });
    };
    refreshCode();

    const validateTel = async (rule, value) => {
      let reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!value) return Promise.reject("请输入手机号码");

      if (!reg.test(value)) return Promise.reject("请输入正确的手机号码");

      return Promise.resolve();
    };

    const openCode = () => {
      let reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!data.loginState.phone) {
        message.error("请输入手机号码");
        return false;
      }
      if (!reg.test(data.loginState.phone)) {
        message.error("请输入正确的手机号码");
        return false;
      }
      refreshCode();
      data.previewVisible = true;
    }

    const getCodeInfo = () => {
      if (!data.phoneCode) {
        message.error("请输入验证码");
        return false;
      }
      getCode({
        phone: data.loginState.phone,
        captchaId: data.loginState.captchaId,
        captcha: data.phoneCode
      }).then(res => {
      	refreshCode();
        if (res.code == 200) {
          message.success("验证码发送成功");
          data.previewVisible = false;
          data.codeSet = false;
          let timeCode = setInterval(() => {
            data.mins--;
            if (data.mins === 0) {
              data.codeSet = true;
              data.mins = 60;
              clearInterval(timeCode);
            }
          }, 1000)
        }
      })
    }

    const getRole = () => {
      getTree().then((res) => {
        if (res.code === 200) {
          data.originList = res.data;
        }
      });
    };
    // getRole();

    const addDialog = () => {
      data.addSource.isShow = true;
      data.addSource.type = 1;
      data.addSource.action = "operate";
      data.formData = {};
    };

    const backPassword = () => {
      data.resetPass.isShow = true;
      data.resetPass.procedure = 1;
      data.formData = {};
    }
    
    const replaceRouter = () => {
    	getUserInfo().then((res) => {
    		if (res.code === 200) {
    			let userInfo = JSON.stringify(res.data);
    			window.localStorage.setItem("userInfo", userInfo);
    			if (data.loginState.userType == 0) {
    				Router.replace({
    					path: "/home",
    					replace: true
    				});
    				return
    			}
    			// 合作方跳转到调度中心工作台页面
    			Router.replace({
    				path: "/dispatchCenter/workbench",
    				query: {
    					active: "调度中心"
    				},
    				replace: true
    			});
    		}
    	});
    }

    const submit = () => {
      data.loginState.userType = data.ecologyShow ? 1 : 0
      loginForm.value.validate().then(() => {
        data.loginState.password = AES.encrypt(
          "yd@10086qwerty!@",
          "yd@10086qwerty!@",
          data.loginState.inputPwd
        );
        data.loginState.userType = data.ecologyShow ? 1 : 0;
        delete data.loginState.inputPwd
        let headers = {
          version:data.version
        }
        login(data.loginState,headers).then((res) => {
        	refreshCode();
          if (res.code === 200) {
            // 存token
            window.localStorage.setItem("token", res.data);
            replaceRouter();
          }
//        {
//        	if (data.loginState.userType == 0) {
//        		// 存token
//	            window.localStorage.setItem("token", res.data);
//	            getUserInfo().then((res) => {
//	              if (res.code === 200) {
//	                let userInfo = JSON.stringify(res.data);
//	                window.localStorage.setItem("userInfo", userInfo);
//		              Router.replace({
//	                  path: "/home",
//	                  replace: true
//	                });
//	                return
//	              }
//	            });
//        	} else {
//        		const token = encodeURIComponent(res.data);
//        		const ipartnerUrl = "https://ipartner.jsdict.cn/static/checkLoginSupply";
//        		// const ipartnerUrl = "http://***********:8013/static/checkLoginSupply";
//        		window.open(`${ipartnerUrl}?token=${token}`, "_blank");
//        		//window.location.replace("http://**************:8013/static/checkLogin?token="+res.data);
//        	}
//        }
        });
      });
    };

    const submitCode = () => {
    	data.loginState.userType = data.ecologyShow ? 1 : 0;
      loginForm2.value.validate().then(() => {
        login({
          captcha: data.loginState.captchaCode,
          phone: data.loginState.phone,
          type: 1,
          userType: data.loginState.userType
        }).then((res) => {
          if (res.code === 200) {
          	window.localStorage.setItem("token", res.data);
            replaceRouter();
          } else if (res.code === 500) {
            refreshCode();
          }
        });
      });
    }

    // 在组件挂载时或者combineList更新时确保长度足够
    onMounted(() => {
      console.log(Route.query.token);
      if (!Route.query.token) return false;
      freeLogin({
        token: Route.query.token
      }).then(res => {
        if (res.code == 200) {
          window.localStorage.setItem("token", res.data);
          replaceRouter();
        } else {
          if (res.msg == "该手机账号不存在") {
            addDialog();
          }
        }
      })
    });

    const handleCancel = () => {
      data.previewVisible = false;
    };

    const ecologyLogin = () => {
    	data.loginType = "1";
      data.ecologyShow = true;
    }

    const userLogin = () => {
      data.ecologyShow = false;
    }

    window.history.pushState(null, null, window.location.href);

    return {
      ...toRefs(data),
      loginForm,
      loginForm2,
      submit,
      getCodeInfo,
      refreshCode,
      addDialog,
      backPassword,
      validateTel,
      submitCode,
      handleCancel,
      openCode,
      ecologyLogin,
      userLogin,
      replaceRouter
    };
  },
});
</script>
<style lang="scss" scoped>
.loginTop {
  // background-image: url("@/assets/images/login/loginBg.png");
  background-image: url('@/assets/images/newProject/loginBg.png');
  width: 100vw;
  height: 100vh;
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .beian {
  	position: fixed;
  	bottom: 10px;
  	text-align: center;
  	width: 100%;
  	color: #FFFFFF;
  	font-size: 16px;
  }

  .logoSite {
    padding: 48px 0 0 60px;
  }

  .logo {
    width: 160px;
    /*height: 42px;*/
  }

  .loginTabs {
    position: relative;
  }

  .iparterApply {
    position: absolute;
    right: 56px;
    top: 123px;
  }

  .loginInfo {
    position: absolute;
    right: 280px;
    top: 50%;
    transform: translateY(-50%);
    background: #ffffff;
    box-shadow: 0px 10px 30px 0px rgba(127, 152, 215, 0.24);
    border-radius: 16px;
    padding: 0 56px 10px 56px;
    width: 500px;

    .loginTitle {
      font-weight: bold;
      font-size: 28px;
      color: rgba(0, 0, 0, 0.85);
      margin: 54px 0 10px 0;
      text-align: center;
    }
  }

  .version {
    text-align: center;
    font-size: 16px;
    margin-bottom: 10px;
  }

  .apply {
    margin-top: 4px;
    font-size: 16px;
    color: #1890ff;
    display: flex;
    justify-content: space-between;

    >div {
      cursor: pointer;
    }
  }

  .authCode {
    display: flex;
    align-items: center;
  }

  :deep(.ant-checkbox-inner) {
    width: 14px;
    height: 14px;
    border-radius: 2px;
  }

  :deep(.ant-input-affix-wrapper) {
    width: 100%;
    border-radius: 8px;
  }

  :deep(.ant-form-item) {
    margin-bottom: 28px;
  }

  :deep(.ant-input) {
    font-size: 16px;
  }

  .loginBtn {
    button {
      background: linear-gradient(135deg, #1e78ff 0%, #005fec 100%);
      border-radius: 8px;
      width: 100%;
      height: 50px;
      font-weight: 500;
      font-size: 20px;
    }

    margin: 32px 0 60px 0;
  }


  :deep(.ant-tabs-bar) {
    border: none;
    margin-bottom: 24px;

    .ant-tabs-nav {
      font-size: 18px;

      .ant-tabs-tab-active {
        font-weight: 600;
      }
    }
  }

  .codeBut {
    position: absolute;
    top: 10px;
    right: 20px;
    border: none;
    color: #0066FE;
    width: 100px;
    font-size: 16px;
  }
}

.overlayBac {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.4);
  z-index: 9999;
  display: flex;
  justify-content: center;
  align-items: center;

  .tip {
    width: 700px;
    height: 460px;
    border-radius: 8px;
    background-color: #FFFFFF;

    .banner {
      width: 100%;
      height: 130px;
      background-image: url("@/assets/images/newProject/password.png");
      background-size: 100% 100%;
    }

    .AItitle {
      margin-top: 20px;
      display: flex;
      justify-content: center;

      img {
        width: 40px;
        height: 50px;
        margin-right: 14px;
      }

      .title {
        width: 370px;
        height: 40px;
        border-radius: 0 10px 10px 10px;
        background: linear-gradient(90deg, rgba(77, 154, 242, 0.2) 0%, rgba(53, 112, 242, 0.2) 56%, rgba(122, 53, 242, 0.2) 95%);
        text-align: center;
        line-height: 40px;
        color: #4249C5;
      }
    }

    .formDom {
      width: 80%;
      margin: 0 auto;

      :deep(.ant-form-item-label) {
        width: 100px;
      }
    }
  }
}

:deep(.ecology-theme .ant-tabs-tab-active) {
	color: #0066FE !important;
}

:deep(.ecology-theme .ant-tabs-ink-bar) {
  background-color: #0066FE !important; /* 下划线颜色 */
}

:deep(.default-theme .ant-tabs-tab-active) {
	color: #00B3B9 !important;
}

:deep(.default-theme .ant-tabs-ink-bar) {
  background-color: #00B3B9 !important;
}
</style>
