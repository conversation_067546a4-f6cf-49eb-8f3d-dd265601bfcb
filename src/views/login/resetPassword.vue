<template>
  <a-modal v-model:visible="resetPass.isShow" title="" :footer="null" :width="modalWidth" :closable="false"
    bodyStyle="padding:0 0 46px 0;" :maskClosable="false" :keyboard="false">
    <div class="banner"></div>
    <div class="AItitle" v-if="addSource.fromType == 2">
    	<img src="@/assets/images/newProject/robot.png" alt="" style="width: 40px; height: 50px" />
    	<div class="title">您使用的仍是系统默认初始密码或已90天未登录，请修改密码后登录！</div>
    </div>
    <div v-if="addSource.procedure == 1">
      <a-form ref="computerInfoElem" :model="formData" labelAlign="right" class="operation"
        :label-col="{ style: { width: '80px' } }" :wrapper_col="{ style: { width: '300px' } }">
        <a-row class="passWardClass">
          <a-col :span="18" v-if="addSource.fromType == 1">
            <a-form-item name="phone" :rules="[{ required: true, message: '请输入手机号码', trigger: 'change', validator: validateTel }]">
              <a-input v-model:value="formData.phone" style="height: 50px" placeholder="请输入手机号码">
                <template #prefix>
                  <img style="width: 22px; height: 22px" src="@/assets/images/login/phone.png" />
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="18" v-if="addSource.fromType == 2">
            <a-form-item name="phone" :rules="[{ required: true, trigger: 'change', validator: validateTel }]">
              <a-input v-model:value="formData.phone" disabled type="text" allowClear></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="18">
            <a-form-item name="captcha" :rules="[{ required: true, message: '请输入图形验证码' }]">
              <div class="authCode">
                <a-input v-model:value="formData.captcha" style="height: 50px" placeholder="请输入图形验证码">
                  <template #prefix>
                    <img style="width: 22px; height: 22px" src="@/assets/images/login/verify.png" />
                  </template>
                </a-input>
                <img style="width: 130px; height: 48px" :src="`data:image/jpg;base64,${imgSource}`"
                  @click="refreshCode" />
              </div>
            </a-form-item>
          </a-col>
          <a-col :span="18">
            <a-form-item name="captchaCode" style="position: relative;"
              :rules="[{ required: true, message: '请输入短信验证码' }]">
              <a-input v-model:value="formData.captchaCode" style="height: 50px" placeholder="请输入短信验证码">
                <template #prefix>
                  <img style="width: 22px; height: 22px" src="@/assets/images/login/verify.png" />
                </template>
                <template #suffix>
                  <a-button v-if="codeSet" @click="getCodeInfo" class="codeBut"
                    :disabled="!formData.phone || !formData.captcha"
                    :style="{ color: (!formData.phone || !formData.captcha) ? '#999999' : '#0066FE' }">获取验证码</a-button>
                  <a-button v-else class="codeBut" style="color: #666666;" disabled>{{ mins }}s</a-button>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center">
        <a-button class="margin_r_10" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">下一步</a-button>
      </div>
    </div>
    <div v-if="addSource.procedure == 2">
      <a-form ref="formRef" :model="formData" labelAlign="right" :rules="rules" :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }">
        <a-row class="passWardClass">
          <a-col :span="18">
            <a-form-item label="手机号" name="phone" :colon="false" :wrapper-col="{ offset: 1, span: 16 }">
              <a-input v-model:value="formData.phone" disabled type="text" allowClear>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="18">
            <a-form-item label="新密码" name="newPassword" :colon="false" :wrapper-col="{ offset: 1, span: 16 }">
              <a-input v-model:value="formData.newPassword" placeholder="请输入新密码" :type="newInput ? 'password' : 'text'"
                allowClear>
                <template #suffix>
                  <i :class="['iconfont', 'pointer', newInput ? 'icon-hide' : 'icon-display',]" style="color: #0c70eb"
                    @click.stop="typeInput('new')"></i>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="18">
            <a-form-item label="确认新密码" name="okPass" :colon="false" :wrapper-col="{ offset: 1, span: 16 }">
              <a-input v-model:value="formData.okPass" placeholder="请确认新密码" :type="okInput ? 'password' : 'text'"
                allowClear>
                <template #suffix>
                  <i :class="['iconfont', 'pointer', okInput ? 'icon-hide' : 'icon-display',]" style="color: #0c70eb"
                    @click.stop="typeInput('ok')"></i>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center">
        <a-button class="margin_r_10" @click="cancel">取消</a-button>
        <a-button class="margin_r_10" type="primary" @click="back">上一步</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, computed, watch, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { createRole, revampRole, revampMoreRole, createPen } from "@/api/system/user";
import { getCode, resetPass, getIdentifyCode, getLogOut } from "@/api/login/login.js";
import { message } from "ant-design-vue";
import { CloseOutlined } from "@ant-design/icons-vue";
import AES from "@/utils/cryptoPwd.js";

export default defineComponent({
  components: {
    CloseOutlined,
  },
  props: {
    form: {
      type: Object,
      default() {
        return {};
      },
    },
    resetPass: {
      type: Object,
      default() {
        return {};
      },
    }
  },

  setup(props, { emit }) {
  	const Router = useRouter();
    const computerInfoElem = ref(null);
    const formRef = ref(null)
    const form = props.form;
    const addSource = props.resetPass;

    const validateTel = async (rule, value) => {
      let reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!value) return Promise.reject("请输入手机号码");

      if (!reg.test(value)) return Promise.reject("请输入正确的手机号码");

      return Promise.resolve();
    };
    const validateEmail = async (rule, value) => {
      let reg =
        /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
      if (!value) return;

      if (!reg.test(value)) return Promise.reject("请输入正确的邮箱");

      return Promise.resolve();
    };
    // 检测密码中是否包含键盘连续模式
    const detectKeyboardPatterns = (pwd) => {
      const patterns = [];
      const lowerPwd = pwd.toLowerCase();
      // 键盘连续模式检测（横向、纵向、对角线）
      let keyboardSequences = [
        'qwertyuiop', 'asdfghjkl', 'zxcvbnm', // 横向
        '1234567890', // 数字横向
        'qaz', 'wsx', 'edc', 'rfv', 'tgb', 'yhn', 'ujm', 'ik', 'ol', // 斜向
        'qwertz', 'asdfgh', 'yxcvbn', // 其他布局
        '1qaz', '2wsx', '3edc', '4rfv', '5tgb', '6yhn', '7ujm', '8ik', '9ol', '0p' // 数字+字母
      ];  
      // 检测连续3个字符以上的序列
      keyboardSequences.forEach(seq => {
        for (let i = 0; i <= seq.length - 3; i++) {
          const substring = seq.substring(i, i + 3);
          if (lowerPwd.includes(substring)) {
            patterns.push(substring);
          }
        }
      });
          
      return [...new Set(patterns)]; // 去重
    };
    const validatePass = async (rule, value) => {
      let reg = /^(?=.*[A-Za-z\W])(?=.*\d)[A-Za-z\d\W]{6,16}$/;
      if (!value) return Promise.reject("请输入新密码");
      // 密码规则验证
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumber = /[0-9]/.test(value);
      const pwdlength = value.length < 6 || value.length >16;
      // const hasSpecialChar = /[!@#$%^&*()_+\-=$$$${};':"\\|,.<>\/?]+/.test(value);
      const hasKeyboardPattern = detectKeyboardPatterns(value).length > 0;
      // console.log('===',hasUpperCase,hasLowerCase,hasNumber,pwdlength,hasKeyboardPattern);
      
      // !reg.test(value)
      if (!hasUpperCase || !hasLowerCase || !hasNumber || hasKeyboardPattern || pwdlength)
        return Promise.reject(
          // "密码必须是6-16位数字、字母或字符组合（不能是纯数字）"
          "密码必须是6-16位数字、大小写字母或字符组合（不能是纯数字）且不能存在键盘规律（例：qwe、asd）"
        );

      return Promise.resolve();
    };
    const validateNewPass = async (rule, value) => {
      if (!value) return Promise.reject("请输入新密码");

      if (value != data.formData.newPassword)
        return Promise.reject("两次密码不一致");

      return Promise.resolve();
    };

    const data = reactive({
      addLoading: false,
      fieldNames: { value: "id", label: "name" },
      fieldRole: { value: "id", label: "name" },
      formData: form,
      belongSubList: [],
      sexList: [
        { label: "男", value: "男" },
        { label: "女", value: "女" },
      ],
      rules: {
        newPassword: { required: true, validator: validatePass },
        okPass: { required: true, validator: validateNewPass },
      },
      roleList: props.roleList,
      addSource: addSource,
      orgId: "",
      charactarList: [],
      codeSet: true,
      mins: 60,
      newInput: true,
      oldInput: true,
      okInput: true,
      imgSource: "",
    });
    
    if(data.addSource.fromType == 2){
    	const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    	data.formData.phone = userInfo.phone;
    }

    const modalWidth = computed(() => {
      return "600px";
    });

    const dialogTitle = computed(() => {
      if (addSource.procedure == "1") return "手机短信验证";
      if (addSource.procedure == "2") return "修改密码";
    });

    const originChange = (value) => {
      data.formData.originId = value;
      data.orgId = value[value.length - 1];
      console.log(data.formData);
    };

    // 生成图形验证码
    const refreshCode = () => {
      getIdentifyCode().then((res) => {
        if (res.code === 200) {
          data.imgSource = res.data.captcha;
          data.formData.captchaId = res.data.captchaId;
        }
      });
    };

    const getCodeInfo = () => {
      let reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!data.formData.phone) {
        message.error("请输入手机号码");
        return false;
      }
      if (!reg.test(data.formData.phone)) {
        message.error("请输入正确的手机号码");
        return false;
      }
      if (!data.formData.captcha) {
        message.error("请输入图形验证码");
        return false;
      }
      getCode({
        phone: data.formData.phone,
        captchaId: data.formData.captchaId,
        captcha: data.formData.captcha
      }).then(res => {
      	// refreshCode();
        if (res.code == 200) {
          message.success("验证码发送成功");
          data.codeSet = false;
          let timeCode = setInterval(() => {
            data.mins--;
            if (data.mins === 0) {
              data.codeSet = true;
              data.mins = 60;
              clearInterval(timeCode);
            }
          }, 1000);
        } else {
        	refreshCode();
        }
      });
    };

    const back = () => {
      addSource.procedure = "1"
    }

    const submit = () => {
      console.log(data.formData);
      if (addSource.procedure == "1") {
        let reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
        if (!data.formData.phone) {
          message.error("请输入手机号码");
          return false;
        }
        if (!reg.test(data.formData.phone)) {
          message.error("请输入正确的手机号码");
          return false;
        }
        if (!data.formData.captchaCode) {
          message.error("请输入验证码");
          return false;
        }
        addSource.procedure = "2"
      } else {
        formRef.value.validate().then(() => {
          let param = {
            phone: data.formData.phone,
            code: data.formData.captchaCode,
            newPassword: AES.encrypt(
              "yd@10086qwerty!@",
              "yd@10086qwerty!@",
              data.formData.newPassword
            ),
          };
          resetPass(param).then((res) => {
            if (res.code === 200) {
              message.success("修改成功");
              cancel();
            }
          });
        })
      }
    };
    const cancel = () => {
      addSource.isShow = false;
      addSource.procedure = "1";
      // computerInfoElem.value.clearValidate();
      if(addSource.fromType == 2){
      	getLogOut().then((res) => {
	        // 清缓存
	        window.localStorage.setItem("token", "");
	        window.localStorage.setItem("userInfo", "");
	        Router.replace({
	          path: "/login",
	        });
	      });
      }
    };

    const dataDeal = (value) => {
      if (value) return value;
      return "-";
    };

    const typeInput = (type) => {
      if (type == "old") data.oldInput = !data.oldInput;
      if (type == "new") data.newInput = !data.newInput;
      if (type == "ok") data.okInput = !data.okInput;
    };

    onMounted(() => {
      refreshCode();
    });

    return {
      ...toRefs(data),
      formRef,
      detectKeyboardPatterns,
      originChange,
      back,
      submit,
      cancel,
      computerInfoElem,
      dialogTitle,
      modalWidth,
      dataDeal,
      typeInput,
      getCodeInfo,
      refreshCode
    };
  },
});
</script>

<style lang="scss" scoped>
.operation {
  padding: 0 30px;
  position: relative;
  transition: all 0.3s ease;
}

.codeBut {
  position: absolute;
  top: 10px;
  right: 20px;
  border: none;
  width: 100px;
  font-size: 16px;
  cursor: pointer;
  background: transparent !important;
  padding: 0 !important;
  height: auto !important;

  &:disabled {
    cursor: not-allowed;
    background: transparent !important;
  }
}

.ant-col .ant-form-item-label {
  width: 90px;
}

.ant-row {
  justify-content: center;

  .ant-col {
    text-align: left;

    .ant-form-item {
      margin-right: 16px;
    }
  }
}

.required .ant-form-item-label::before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.cabinet {
  background: #f4f8fc;
  padding: 5px 16px;
}

.params-content {
  border: 1px solid #ccc;
  padding: 20px;
}

.add-button {
  width: 100%;
  background-color: #f1f8ff;
  border: 1px dashed rgba(0, 126, 255, 0.6);
  color: #007eff;
}

.item-content {
  background-color: rgba(250, 250, 250, 1);
  margin-bottom: 10px;
  height: 48px;
}

.ant-input {
  color: #00060e;
  font-weight: 500;
  border: none !important;
  border-radius: 4px !important;
  background: #f5f6fa !important;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
  height: 50px !important;

  &::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    width: 0;
    height: 2px;
    background: linear-gradient(90deg, #0066FE, #4249C5);
    transition: all 0.3s ease;
    transform: translateX(-50%);
  }

  &:focus {
    background: #ffffff !important;
    box-shadow: 0 0 0 2px rgba(0, 102, 254, 0.2);

    &::after {
      width: 100%;
    }
  }

  &:hover {
    background: #ffffff !important;
  }

  :deep(.ant-input-prefix) {
    margin-right: 8px;
  }

  :deep(.ant-input-suffix) {
    margin-left: 8px;
  }
}

:deep(.ant-select-selector) {
  border: none !important;
  border-radius: 4px !important;
  background: #f5f6fa !important;

  .ant-select-selection-item {
    color: #00060e;
    font-weight: 500;
  }
}

:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  margin: 24px 0 24px 32px;
  gap: 24px 0;
}

:deep(.ant-checkbox-wrapper) {
  font-weight: 500;
  color: #00060e;
  position: relative;
}

:deep(.ant-checkbox-wrapper > .ant-checkbox) {
  position: absolute;
  left: 276px;
}

:deep(.ant-checkbox-group > .ant-checkbox-wrapper) {
  color: rgba(0, 6, 14, 0.6);
}

:deep(.ant-checkbox-wrapper) {
  position: relative;
}

:deep(.ant-checkbox-group > .ant-checkbox-wrapper > .ant-checkbox) {
  position: absolute;
  left: 260px;
}

.AItitle {
  display: flex;
  justify-content: center;
  margin-bottom: 10px;

  img {
    width: 40px;
    height: 50px;
    margin-right: 14px;
  }

  .title {
    width: 500px;
    height: 40px;
    border-radius: 0 10px 10px 10px;
    background: linear-gradient(90deg, rgba(77, 154, 242, 0.2) 0%, rgba(53, 112, 242, 0.2) 56%, rgba(122, 53, 242, 0.2) 95%);
    text-align: center;
    line-height: 40px;
    color: #4249C5;
  }
}


.banner {
  width: 100%;
  height: 130px;
  background-image: url("@/assets/images/newProject/password.png");
  background-size: 100% 100%;
  margin-bottom: 30px;
}

.authCode {
  display: flex;
  align-items: center;
  gap: 16px;
  transition: all 0.3s ease;

  img {
    transition: all 0.3s ease;
    cursor: pointer;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
    height: 48px;
    width: 130px;

    &:hover {
      transform: scale(1.05);
      box-shadow: 0 4px 12px rgba(0, 102, 254, 0.2);
    }
  }
}

.passWardClass {

  .ant-col {
    opacity: 0;
    transform: translateY(20px);
    animation: slideIn 0.5s ease forwards;

    @for $i from 1 through 3 {
      &:nth-child(#{$i}) {
        animation-delay: #{$i * 0.1}s;
      }
    }
  }
}

.ant-form-item {
  margin-bottom: 24px !important;
}

.flex.just-center {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  padding: 0 30px;
  background: #fff;
  z-index: 1;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }

  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>