<template>
  <a-modal
    v-model:visible="addData.isShow"
    :title="dialogTitle"
    :footer="null"
    :width="modalWidth"
    :closable="false"
    :maskClosable="false"
  >
    <div v-if="addSource.action == 'operate'">
    	<div class="AItitle">
  				<img src="@/assets/images/newProject/robot.png" alt="" style="width: 40px;height: 50px;"/>
  				<div class="title">请填写账号信息，待人工审核后完成账号创建。</div>
  		</div>
      <a-form
        ref="computerInfoElem"
        :model="formData"
        labelAlign="right"
        :rules="rules"
        class="operation"
        :label-col="{ style: { width: '80px' } }"
        :wrapper_col="{ style: { width: '300px' } }"
      >
        <a-row class="required">
          <a-col :span="12">
            <a-form-item label="姓名" name="realName">
              <a-input
                v-model:value="formData.realName"
                placeholder="请输入姓名"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="手机号码" name="phone">
              <a-input
                v-model:value="formData.phone"
                placeholder="请输入手机号码"
              ></a-input>
            </a-form-item>
          </a-col>

          <a-col :span="12">
            <a-form-item label="邮箱" name="mail">
              <a-input
                v-model:value="formData.mail"
                placeholder="请输入邮箱"
              ></a-input>
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="所属组织" name="orgName">
            	<a-input
                v-model:value="formData.orgName"
                placeholder="请输入所属组织"
              ></a-input>
              <!--<a-cascader
                v-model:value="formData.originId"
                :options="originList"
                :fieldNames="fieldNames"
                placeholder="请选择所属组织"
                @change="originChange"
              />-->
            </a-form-item>
          </a-col>
          <a-col :span="12">
            <a-form-item label="性别" name="sex">
              <a-select
                v-model:value="formData.sex"
                placeholder="请选择性别"
                :options="sexList"
              />
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center">
        <a-button class="margin_r_10" @click="cancel">取消</a-button>
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, computed, watch } from "vue";
import { createRole, revampRole, revampMoreRole, createPen } from "@/api/system/user";
import eventBus from "@/utils/eventBus";
import { getTargetId } from "@/utils";
import { message } from "ant-design-vue";
import { CloseOutlined } from "@ant-design/icons-vue";
import { useRouter, useRoute } from "vue-router";

export default defineComponent({
  components: {
    CloseOutlined,
  },
  props: {
    form: {
      type: Object,
      default() {
        return {};
      },
    },
    addData: {
      type: Object,
      default() {
        return {};
      },
    },
    originList: {
      type: Array,
      default() {
        return [];
      },
    },
    roleList: {
      type: Array,
      default() {
        return [];
      },
    },
  },

  setup(props, { emit }) {
    const computerInfoElem = ref(null);
    const form = props.form;
    const addSource = props.addData;
    const Route = useRoute();
    const Router = useRouter();

    const validateTel = async (rule, value) => {
      let reg = /^1(3|4|5|6|7|8|9)\d{9}$/;
      if (!value) return Promise.reject("请输入手机号码");

      if (!reg.test(value)) return Promise.reject("请输入正确的手机号码");

      return Promise.resolve();
    };
    const validateEmail = async (rule, value) => {
      let reg = /^\w+((-\w+)|(\.\w+))*\@[A-Za-z0-9]+((\.|-)[A-Za-z0-9]+)*\.[A-Za-z0-9]+$/;
      if (!value) return;
      
      if (!value.includes('@js.chinamobile.com')) return Promise.reject("请输入正确的邮箱,如：@js.chinamobile.com");

      if (!reg.test(value)) return Promise.reject("请输入正确的邮箱");

      return Promise.resolve();
    };
    
    watch(
      () => props.originList,
      (value) => {
        if (value) {
          data.originList = value;
        }
      }
    );

    const data = reactive({
      addLoading: false,
      fieldNames: { value: "id", label: "name" },
      fieldRole: { value: "id", label: "name" },
      formData: form,
      belongSubList: [],
      sexList: [
        { label: "男", value: "男" },
        { label: "女", value: "女" },
      ],
      rules: {
        realName: {
          required: true,
          message: "请输入用户姓名",
          trigger: "change",
        },
        phone: {
          required: true,
          trigger: "change",
          validator: validateTel,
        },
        mail: [
          { required: true, message: "请输入用户邮箱", trigger: "change" },
          { trigger: "change", validator: validateEmail },
        ],
        orgName: {
          required: true,
          message: "请输入用户所属组织",
          trigger: "change",
        },
      },
      originList: props.originList,
      roleList: props.roleList,
      addSource: addSource,
      orgId: "",
      charactarList: [],
    });

    const modalWidth = computed(() => {
      if (addSource.type == "5") return "380px";
      return "750px";
    });

    const dialogTitle = computed(() => {
      if (addSource.type == "1") return "账号开通申请";
    });

    const originChange = (value) => {
      data.formData.originId = value;
      data.orgId = value[value.length - 1];
      console.log(data.formData);
    };

    const submit = () => {
    	console.log(data.formData);
      computerInfoElem.value
        .validate()
        .then(() => {
          data.addLoading = true;
          if (addSource.type == "1") {
            let params = { ...data.formData, orgId: data.orgId };
            delete params.originId;
            createPen(params).then((res) => {
              if (res.code === 200) {
                data.addLoading = false;
                addSource.isShow = false;
                Object.keys(data.formData).forEach((key) => {
                  data.formData[key] = "";
                });
                message.success("您的账号开通申请已成功提交，请耐心等待审核!");
                if(Route.query.token){
					    		Router.replace({
					    			path: "/login",
					    		});
					    	}
              }
            });
          }
        })
        .catch(() => {
          data.addLoading = false;
        });
    };
    const cancel = () => {
      addSource.isShow = false;
      computerInfoElem.value.clearValidate();
      if(Route.query.token){
    		Router.replace({
    			path: "/login",
    		});
    	}
    };

    const dataDeal = (value) => {
      if (value) return value;
      return "-";
    };
    return {
      ...toRefs(data),
      originChange,
      submit,
      cancel,
      computerInfoElem,
      dialogTitle,
      modalWidth,
      dataDeal,
    };
  },
});
</script>

<style lang="scss" scoped>
.operation {
  padding: 0 30px;
}
:deep(.detailForm .ant-form-item-label) {
  width: 75px !important;
  text-align: right;
}
.allotText {
  color: #a2abb5;
}

.roleCase {
  background: rgba(0, 6, 14, 0.08);
  border-radius: 4px;
  font-size: 12px;
  color: #00060e;
  padding: 2px 20px 2px 12px;
}
.removeBtn {
  width: 13px;
  height: 13px;
  font-size: 11px;
  right: 4px;
  top: 4px;
  border-radius: 0 0 0 40%;
}

.ant-col .ant-form-item-label {
  width: 90px;
}

.viewForm .ant-form-item-label {
  font-weight: 600 !important;
  color: rgba(0, 0, 0, 0.85);
}

.roleContent {
  flex-wrap: wrap;
}

.viewForm .ant-form-item {
  margin-bottom: 0px;
}
.ant-row {
  .ant-col {
    text-align: left;
    .ant-form-item {
      margin-right: 16px;
    }
  }
}
.required .ant-form-item-label::before {
  content: "*";
  color: #ff4949;
  margin-right: 4px;
}

.cabinet {
  background: #f4f8fc;
  padding: 5px 16px;
}

.params-content {
  border: 1px solid #ccc;
  padding: 20px;
}

.add-button {
  width: 100%;
  background-color: #f1f8ff;
  border: 1px dashed rgba(0, 126, 255, 0.6);
  color: #007eff;
}

.item-content {
  background-color: rgba(250, 250, 250, 1);
  margin-bottom: 10px;
  height: 48px;
}
.ant-input {
  color: #00060e;
  font-weight: 500;
  border: none !important;
  border-radius: 4px !important;
  background: #f5f6fa !important;
}

:deep(.ant-select-selector) {
  border: none !important;
  border-radius: 4px !important;
  background: #f5f6fa !important;
  .ant-select-selection-item {
    color: #00060e;
    font-weight: 500;
  }
}
:deep(.ant-checkbox-group) {
  display: flex;
  flex-direction: column;
  margin: 24px 0 24px 32px;
  gap: 24px 0;
}
:deep(.ant-checkbox-wrapper) {
  font-weight: 500;
  color: #00060e;
  position: relative;
}
:deep(.ant-checkbox-wrapper > .ant-checkbox) {
  position: absolute;
  left: 276px;
}
:deep(.ant-checkbox-group > .ant-checkbox-wrapper) {
  color: rgba(0, 6, 14, 0.6);
}
:deep(.ant-checkbox-wrapper) {
  position: relative;
}
:deep(.ant-checkbox-group > .ant-checkbox-wrapper > .ant-checkbox) {
  position: absolute;
  left: 260px;
}
.AItitle {
			display: flex;
			justify-content: center;
			margin-bottom: 10px;
			img {
				width: 40px;
				height: 50px;
				margin-right: 14px;
			}
			.title {
				width: 370px;
				height: 40px;
				border-radius: 0 10px 10px 10px;
				background: linear-gradient( 90deg, rgba(77, 154, 242, 0.2) 0%, rgba(53, 112, 242, 0.2) 56%, rgba(122,53,242,0.2) 95%);
				text-align: center;
				line-height: 40px;
				color: #4249C5;
			}
		}
</style>