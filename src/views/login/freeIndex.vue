<template>
	<div class="loginTop">
		<img src="@/assets/images/login/freeLogin.gif" alt="" />
  </div>
  <addForm
  	:addData="addSource"
  	:form="formData"
  	:originList="originList"
  ></addForm>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import AES from "@/utils/cryptoPwd.js";
import addForm from "./addForm.vue";
import { getUserInfo, freeLogin } from "@/api/login/login.js";
import { getTree } from "@/api/system/team";
import { useRouter, useRoute } from "vue-router";
import { message } from "ant-design-vue";
export default defineComponent({
  components: {
  	 addForm
  },
  setup() {
    const Router = useRouter();
    const Route = useRoute();

    const data = reactive({
      addSource: {
        isShow: false,
        action: "",
        type: 1,
        userIds: "",
      },
      originList: [],
    });

    const getRole = () => {
      getTree().then((res) => {
        if (res.code === 200) {
          data.originList = res.data;
        }
      });
    };
    getRole();

    const addDialog = () => {
      data.addSource.isShow = true;
      data.addSource.type = 1;
      data.addSource.action = "operate";
      data.formData = {};
    };
    
    // 在组件挂载时或者combineList更新时确保长度足够
    onMounted(() => {
    	console.log(Route.query.token);
    	if(!Route.query.token) return false;
    	freeLogin({
    		token: Route.query.token
    	}).then(res => {
    		if(res.code == 200){
    			window.localStorage.setItem("token", res.data);
    			getUserInfo().then((res) => {
    				if (res.code === 200) {
    					let userInfo = JSON.stringify(res.data);
    					window.localStorage.setItem("userInfo", userInfo);
    					Router.replace({
    						path: "/home",
    					});
    				}
    			});
    		} else {
    			if (res.msg == "该手机账号不存在"){
    				addDialog();
    			}
    		}
    	})
    });

    return {
      ...toRefs(data),
      addDialog,
    };
  },
});
</script>
<style lang="scss" scoped>
.loginTop {
  width: 100vw;
  height: 100vh;
  background-color: #fefefe;
  img {
  	position: fixed;
  	top: 50%;
  	left: 50%;
  	transform: translate(-50%, -50%);
  }
}

</style>
