<template>
  <div id="lay_content">
    <div class="infoMation relative">
      <div class="avatorSize absolute">
        <img v-if="formData.sex == '女'" src="@/assets/images/woman.png" />
        <img v-else src="@/assets/images/man.png" />
      </div>
      <div class="nameInfo">
        <div class="realNameInfo">
          {{ dataDeal(formData.realName) }}
        </div>
        <div class="userNameInfo">
          {{ dataDeal(formData.username) }}
        </div>
      </div>
    </div>

    <div class="personalInfo">
      <div class="flex commonInfo">
        <div class="text_name">手机号码</div>
        <div class="text_content">{{ dataDeal(formData.phone) }}</div>
      </div>
      <div class="flex commonInfo">
        <div class="text_name">邮箱</div>
        <div class="text_content">{{ dataDeal(formData.mail) }}</div>
      </div>
      <div class="flex commonInfo">
        <div class="text_name">性别</div>
        <div class="text_content">{{ dataDeal(formData.sex) }}</div>
      </div>
      <div class="flex commonInfo">
        <div class="text_name">所属组织</div>
        <div class="text_content">{{ dataDeal(formData.orgName) }}</div>
      </div>
      <div class="flex commonInfo">
        <div class="text_name">角色</div>
        <div class="text_content">{{ roleWith(formData.roleNameList) }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";

export default defineComponent({
  setup() {
    const data = reactive({
      formData: JSON.parse(localStorage.getItem("userInfo")),
    });
    const dataDeal = (value) => {
      if (value) return value;
      return "-";
    };
    const roleWith = (value) => {
      if (value && value.length > 0) return value.join("、");
      return "-";
    };
    return {
      ...toRefs(data),
      dataDeal,
      roleWith,
    };
  },
});
</script>

<style lang="scss" scoped>
#lay_content {
  background: #ffffff;
  border-radius: 8px;
  padding-bottom: 30px;
}
.personalInfo {
  margin-top: 120px;
  margin-left: 120px;
}

.infoMation {
  background-image: url("@/assets/images/layout/personInfo.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 180px;
}
.avatorSize {
  top: 120px;
  left: 100px;
  img {
    width: 140px;
    height: 140px;
  }
}

.text_content {
  font-weight: 500;
  color: rgba(0, 6, 14, 0.8);
  margin-left: 32px;
}

.nameInfo {
  padding-top: 192px;
  margin-left: 248px;

  .realNameInfo {
    font-weight: 500;
    font-size: 18px;
    color: rgba(0, 6, 14, 0.8);
  }
  .userNameInfo {
    color: rgba(0, 6, 14, 0.8);
    font-weight: 400;
  }
}

.text_name {
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  width: 56px;
}

.commonInfo {
  border-bottom: 1px dashed rgba(0, 6, 14, 0.08);
  padding: 20px 0;
}
</style>