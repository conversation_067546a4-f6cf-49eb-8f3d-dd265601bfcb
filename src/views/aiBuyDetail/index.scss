.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
    /* 半透明遮罩 */
    z-index: 9999;
}
.con {
    margin: 0 auto;
    // width: 595px;
    width: 794px;
    // background: #F5F7FC;
    border-radius: 0px 0px 0px 0px;
    background: linear-gradient(135deg, #ABC2FF 0%, #ACE8FF 51%, #5779FF 100%);
    // padding-top: 32px;
    font-weight: 500;
    font-size: 18px;
    color: #2E3852;
    line-height: 21px;
    // margin-bottom: 40px;
    height: 1122px;
    display: flex;
    flex-flow: column nowrap;
    justify-content: space-between;

    .top {
        justify-content: space-between;
        margin: 32px 48px 16px 48px;

        .left_img {
            width: 164px;
            height: 32px;
        }

        .right_img {
            width: 112px;
            height: 32px;
        }
    }

    .title {
        font-weight: bold;
        font-size: 24px;
        color: #126EE0;
        line-height: 56px;
        text-align: center;
        margin: 0 48px 32px 48px;
    }

    .list {
        position: relative;
        margin: 32px 32px;

        .list_tit {
            background: linear-gradient(90deg, #02A2F7 0%, #0C70EB 100%);
            box-shadow: 0px 4px 12px 0px rgba(0, 153, 236, 0.32);
            position: absolute;
            font-weight: 500;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 38px;
            left: 266px;
            width: 200px;
            text-align: center;
            padding: 0 6px;
            top: -20px;
        }

        .list_tit::before {
            width: 0;
            height: 0;
            border-top: 30px solid transparent;
            border-left: 30px solid red;
            border-bottom: 30px solid transparent;

        }

        .box {
            // width: 531px;
            background: #FFFFFF;
            border-radius: 24px 24px 24px 24px;
            padding: 20px 20px 20px;
            margin-top: 46px;
            border-bottom: 6px solid #097CEE;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            .demandNameBox{
                width: 100%;
                display: flex;
                justify-content: center;
                align-items: center;
                .demandName{
                    width: 272px;
                    height: 30px;
                    line-height: 30px;
                    text-align: center;
                    margin: 20px 0;
                    font-weight: 500;
                    font-size: 14px;
                    color: #2E3852;
                    background: linear-gradient( 90deg, rgba(85,161,255,0) 0%, #b8d8ff 50%, rgba(85,161,255,0) 100%);
                }
            }
            .cell {
                width: 320px;
                margin-right: 31px;
                margin-bottom: 24px;
                // height: 88px;
                background: rgba(85, 161, 255, 0.1);
                border-radius: 8px 8px 8px 8px;
                padding: 12px;
                height: 100px;

                .cell_top {
                    width: 272px;
                    display: flex;
                    align-items: center;
                    height: 32px;
                    background: linear-gradient(90deg, #FFFFFF 39%, rgba(255, 255, 255, 0) 100%);
                    border-radius: 8px 8px 8px 8px;
                    padding: 6px;
                    margin-bottom: 4px;

                    .logo {
                        width: 14px;
                        height: 14px;
                        margin-right: 6px;
                    }
                }
            }

            .cell:nth-child(3n) {
                margin-right: 0;
            }
        }
    }

    .boot {
        background-image: url('../../assets/images/buyList/bottom.png');
        width: 600px;
        height: 30px;
        padding: 6px 49px 0 49px;
        display: flex;
        justify-content: space-between;
        margin: 0 auto;

        .box {
            font-weight: 500;
            font-size: 10px;
            width: 152px;
            height: 28px;
            color: #2E3852;
            line-height: 28px;
            // padding: 13px 20px;
            border-radius: 8px;
            border: 2px solid #FFFFFF;
            display: flex;
            align-items: center;
            border-left: none;

            .left_tit {
                // min-width: 66px;
                background-color: #EEF6FF;
                padding: 0 6px;
                border-bottom-left-radius: 8px;
                border-top-left-radius: 8px;
            }

            .left_con {
                background-color: #FFFFFF;
                padding-left: 12px;
                flex: 1;
                border-bottom-right-radius: 8px;
                border-top-right-radius: 8px;
                // padding: 13px;
            }
        }
    }
}


::v-deep(.ant-table-thead>tr>th) {
    background: linear-gradient(180deg, #02A2F7 0%, #0C70EB 100%);
    font-weight: 500;
    font-size: 12px;
    color: #FFFFFF;
    line-height: 23px;
    padding: 8px;
    text-align: center;
}

::v-deep(.ant-table-thead>tr) {
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;

}

::v-deep(.ant-table .ant-table-border-wrapper .ant-table-body) {
    border: 2px solid #85B7F5;
    /* 红色边框 */
}

::v-deep(.ant-table .ant-table-tbody > tr > td) {
    border-color: #85B7F5;
    /* 单元格边框颜色 */
    font-size: 10px;
}

::v-deep(.ant-table-bordered .ant-table-thead > tr > th) {
    border-right: 1px solid #85B7F5;
    border-left: 1px solid #85B7F5;
}

::v-deep(.ant-table-bordered .ant-table-body > table) {
    border-color: #85B7F5;
}

.name {
    font-weight: bold;
    font-size: 10px;
    color: #126EE0;
    line-height: 19px;
}

.desc {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    overflow: hidden;
    text-overflow: ellipsis;
}