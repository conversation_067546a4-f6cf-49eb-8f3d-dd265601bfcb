.con {
    margin: 0 auto;
    width: 1200px;
    background: #F5F7FC;
    border-radius: 0px 0px 0px 0px;
    background: linear-gradient(135deg, #ABC2FF 0%, #ACE8FF 51%, #5779FF 100%);
    padding-top: 32px;
    font-weight: 500;
    font-size: 18px;
    color: #2E3852;
    line-height: 21px;

    .top {
        justify-content: space-between;
        margin-bottom: 16px;
        margin: 32px 48px;

        .left_img {
            width: 322px;
            height: 48px;
        }

        .right_img {
            width: 154px;
            height: 48px;
        }
    }

    .title {
        font-weight: bold;
        font-size: 48px;
        color: #126EE0;
        line-height: 56px;
        text-align: center;
        margin-bottom: 32px;
        margin: 32px 48px;
    }

    .list {
        position: relative;
        margin: 32px 48px;

        .list_tit {
            background: linear-gradient(90deg, #02A2F7 0%, #0C70EB 100%);
            box-shadow: 0px 4px 12px 0px rgba(0, 153, 236, 0.32);
            position: absolute;
            font-weight: 500;
            font-size: 32px;
            color: #FFFFFF;
            line-height: 38px;
            left: 369px;
            width: 346px;
            text-align: center;
            padding: 6px;
            top: -25px;
        }

        .list_tit::before {
            width: 0;
            height: 0;
            border-top: 30px solid transparent;
            border-left: 30px solid red;
            border-bottom: 30px solid transparent;

        }

        .box {
            width: 1104px;
            background: #FFFFFF;
            border-radius: 24px 24px 24px 24px;
            padding: 48px 40px;
            margin-top: 56px;
            border-bottom: 6px solid #097CEE;
            display: flex;
            align-items: center;
            justify-content: space-between;
            flex-wrap: wrap;
            .cell {
                width: 320px;
                margin-right: 31px;
                margin-bottom: 24px;
                // height: 88px;
                background: rgba(85, 161, 255, 0.1);
                border-radius: 8px 8px 8px 8px;
                padding: 12px;
                height: 100px;
                .cell_top {
                    width: 272px;
                    display: flex;
                    align-items: center;
                    height: 32px;
                    background: linear-gradient(90deg, #FFFFFF 39%, rgba(255, 255, 255, 0) 100%);
                    border-radius: 8px 8px 8px 8px;
                    padding: 6px;
                    margin-bottom: 4px;

                    .logo {
                        width: 14px;
                        height: 14px;
                        margin-right: 6px;
                    }
                }
            }

            .cell:nth-child(3n) {
                margin-right: 0;
            }
        }
    }

    .boot {
        background-image: url('../../assets/images/buyList/bottom.png');
        width: 1200px;
        height: 364px;
        padding: 24px 49px;
        display: flex;
        justify-content: space-between;

        .box {
            font-weight: 500;
            font-size: 18px;
            width: 346px;
            height: 55px;
            color: #2E3852;
            line-height: 21px;
            // padding: 13px 20px;
            border-radius: 8px 8px 8px 8px;
            border: 2px solid #FFFFFF;
            display: flex;

            .left_tit {
                background-color: #EEF6FF;
                padding: 13px;
            }

            .left_con {
                background-color: #FFFFFF;
                padding-left: 24px;
                flex: 1;
                padding: 13px;
            }
        }
    }
}


::v-deep(.ant-table-thead>tr>th) {
    background: linear-gradient(180deg, #02A2F7 0%, #0C70EB 100%);
    font-weight: 500;
    font-size: 20px;
    color: #FFFFFF;
    line-height: 23px;
    padding: 8px;
    text-align: center;
}

::v-deep(.ant-table-thead>tr) {
    border-top-left-radius: 18px;
    border-top-right-radius: 18px;

}

::v-deep(.ant-table .ant-table-border-wrapper .ant-table-body) {
    border: 2px solid #85B7F5;
    /* 红色边框 */
}

::v-deep(.ant-table .ant-table-tbody > tr > td) {
    border-color: #85B7F5;
    /* 单元格边框颜色 */
}

::v-deep(.ant-table-bordered .ant-table-thead > tr > th) {
    border-right: 1px solid #85B7F5;
    border-left: 1px solid #85B7F5;
}

::v-deep(.ant-table-bordered .ant-table-body > table) {
    border-color: #85B7F5;
}

.name {
    font-weight: bold;
    font-size: 16px;
    color: #126EE0;
    line-height: 19px;
}