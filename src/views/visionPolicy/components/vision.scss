.top_nav {
    padding-left: 120px;
    height: 60px;
    background-color: #f5f7fc;
    width: 100%;
    margin-top: 8px;
    padding-right: 70px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    top: 50px;
    z-index: 21;
    padding-top: 20px;

    div {
        display: inline-block;
    }

    .left_nav {
        padding-bottom: 8px;

        .title {
            font-weight: 400;
            font-size: 12px;
            color: #84899a;
            line-height: 20px;
            margin-right: 8px;
            cursor: pointer;
        }

        .current {
            font-weight: 400;
            font-size: 12px;
            color: #2e3852;
            line-height: 20px;
        }
    }

    .right_nav {
        color: #2e7fff;
        cursor: pointer;
    }
}

.emptyPhoto {
    text-align: center;
    padding: 20px 0;

    img {
        width: 240px;
        height: 248px;
    }
}

.contentZone {
    margin: 70px auto 20px;
    width: 1200px;
    background-color: #fff;
    box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
    border-radius: 8px;

    .text {
        font-weight: 500;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        text-align: center;
        padding-top: 56px;
        padding-bottom: 24px;
        position: relative;
    }

    .ticket {
        position: absolute;
        right: 80px;
        top: 56px;
    }

    .classify {
        border-top: 1px solid #dae2f5;
        border-bottom: 1px solid #dae2f5;
        display: flex;
        justify-content: space-between;

        .title {
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
        }

        .time {
            color: rgba(0, 0, 0, 0.45);
            padding: 14px 0;
            flex: none;
        }
    }

    .description {
        padding: 40px 0 10px;
    }

    .fileInfo {
        margin: 0 80px;
        padding-bottom: 10px;
    }
}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
    /* 半透明遮罩 */
    z-index: 9999;
}

.belowContent {
    margin: 28px auto 24px;
    width: 1200px;

    .textProject {
        font-weight: bold;
        font-size: 32px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 38px;
    }
}

.cardContent {
    background: #ffffff;
    border-radius: 10px;
    margin-top: 24px;
}

.card_total {
    display: flex;
    flex-wrap: wrap;
    align-items: flex-start;
    border-bottom: 1px solid #dae2f5;
}

.emptyPhoto {
    margin: auto;

    img {
        width: 240px;
        height: 248px;
    }
}

.card_content {
    position: relative;
    width: 50%;
    height: 163px;
    border-right: 1px solid #dae2f5;
    border-bottom: 1px solid #dae2f5;
    cursor: pointer;
}

.rightActive {
    border-right: none;
}

.cardActive {
    background-color: #ffffff;
    transition: all 0.2s;
    box-shadow: 0 0 10px #dae2f5;
}

.bottomLine {
    border-bottom: none;
}

.cardObvious {
    border-bottom: none;
}

.card_center {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    // height: 105px;
    margin-left: 12px;
    // min-width: 66%;
    flex: 1;
}

.card_text {
    display: flex;
    align-items: center;
    justify-content: space-between;

    .card_tag {
        display: flex;
        align-items: center;
    }

    .card_title {
        font-weight: bold;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        overflow: hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 1;
    }

    :deep(.cityStyle) {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        background-color: transparent;
        // border: none;
        // margin-right: -6px;
    }
}

.card_des {
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    /* 控制显示的行数 */
    overflow: hidden;
    text-overflow: ellipsis;
    margin-right: 4px;
    word-break: break-all;
}

.layPage {
    width: 100%;
    text-align: center;
    padding: 15px 0;
}