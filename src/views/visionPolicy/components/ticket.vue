<template>
  <div class="loading-overlay" v-if="viewLoading">
    <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
  </div>
  <a-form
    ref="ticket"
    :model="formData"
    labelAlign="right"
    :rules="rules"
    class="operation"
  >
    <a-form-item label="工单标题" name="name">
      <a-input
        v-model:value="formData.name"
        placeholder="请输入工单标题"
        maxlength="32"
      ></a-input>
    </a-form-item>

    <a-form-item
      label="派单类型"
      name="typeName"
      :rules="[{ required: true, message: '请选择派单类型' }]"
    >
      <a-select placeholder="请选择派单类型" v-model:value="formData.typeName">
        <template v-for="(opt, index) in typeOptions" :key="index">
          <a-select-option :value="opt.value">
            {{ opt.label }}
          </a-select-option>
        </template>
      </a-select>
    </a-form-item>

    <a-form-item
      label="政策"
      name="policyId"
      :rules="[{ required: true, message: '请选择政策' }]"
    >
      <a-select
        placeholder="请选择政策"
        v-model:value="formData.policyId"
        @change="policyChoice"
      >
        <template v-for="(opt, index) in policyList" :key="index">
          <a-select-option :value="opt.id">
            {{ opt.name }}
          </a-select-option>
        </template>
      </a-select>
    </a-form-item>

    <a-form-item label="政策信息" v-if="formData.policyId">
      <div class="itemForm">
        <div class="description" v-html="detailData.details"></div>
      </div>
    </a-form-item>
    <a-form-item label="政策附件" v-if="formData.policyId">
      <div class="fileInfo">
        <div
          v-for="(item, index) in detailData.fileList"
          :key="index"
          style="margin-bottom: 10px"
        >
          <img
            src="@/assets/images/solution/detail/text.png"
            alt=""
            style="width: 40px; height: 40px"
          />
          <a @click="previewFile(item)">{{ item.name }}</a>
        </div>
      </div>
    </a-form-item>
    <a-form-item label="相关方案" v-if="formData.policyId">
      <div class="belowContent">
        <div class="cardContent" v-if="tableList && tableList.length > 0">
          <div class="card_total flex-1">
            <template v-for="(item, index) in paginatedData" :key="index">
              <div
                :class="[
                  'card_content',
                  {
                    cardActive: cardActive == index,
                    rightActive: index % 2 != 0,
                    cardObvious: index < 2 && tableList.length < 3,
                    bottomLine:
                      (index == tableList.length - 1 ||
                        index == tableList.length - 2) &&
                      index > 1,
                  },
                ]"
                @mouseenter="contentColor(index)"
                @mouseleave="contentLeave"
                @click="proDetail(item)"
              >
                <div style="display: flex; margin: 24px">
                  <div>
                    <div
                      style="
                        width: 168px;
                        height: 105px;
                        text-align: center;
                        position: relative;
                      "
                      :style="backgroundStyles()"
                    >
                      <p
                        style="
                          font-weight: 700;
                          display: block;
                          color: #1f82c8;
                          position: absolute;
                          left: 50%;
                          top: 50%;
                          transform: translate(-50%, -50%);
                          font-size: 10px;
                        "
                      >
                        {{ item.name }}
                      </p>
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div class="card_tag">
                        <div class="card_title">{{ item.name }}</div>
                      </div>
                      <div class="cityStyle" v-if="item.provider">
                        {{ item.provider }}
                      </div>
                    </div>
                    <div class="flex" style="justify-content: space-between">
                      <div class="flex">
                        <a-tag
                          color="#D7E6FF"
                          v-if="item.labelName && item.labelName[0]"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ item.labelName[0] }}</a-tag
                        >
                        <a-tag
                          color="#D7E6FF"
                          v-if="item.labelName && item.labelName[1]"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ item.labelName[1] }}</a-tag
                        >
                      </div>
                    </div>
                    <div class="card_des">
                      {{ item.description }}
                    </div>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-right: 5px;
                        "
                      >
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            margin-right: 18px;
                          "
                        >
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.viewCount"
                            >{{ item.viewCount }}</span
                          >
                          <span v-else>-</span>
                        </div>
                        <div style="display: flex; align-items: center">
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.downloadCount"
                            >{{ item.downloadCount }}</span
                          >
                          <span v-else>-</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </template>
          </div>
          <div class="layPage">
            <a-pagination
              v-model:pageSize="pageItemSize"
              v-model:current="currentPage"
              :pageSizeOptions="pageSizeOptions"
              show-quick-jumper
              show-size-changer
              :total="tableList.length"
              class="mypage"
            />
          </div>
        </div>
        <div v-if="emptyShow" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div>
      </div>
    </a-form-item>
    <a-form-item label="工单内容" name="detail">
      <a-textarea
        v-model:value="formData.detail"
        placeholder="请输入工单内容(限300字)"
        maxlength="300"
        :rows="7"
      />
    </a-form-item>

    <a-form-item
      label="客户经理"
      name="customer"
      :rules="[{ required: true, message: '请选择客户经理' }]"
    >
      <a-select placeholder="请选择客户经理" v-model:value="formData.customer">
        <template v-for="(opt, index) in customerList" :key="index">
          <a-select-option :value="opt.id">
            {{ opt.name }}
          </a-select-option>
        </template>
      </a-select>
    </a-form-item>
  </a-form>
  <div class="flex just-center margin_t_16">
    <a-button @click="conserve" class="margin_r_12">
      <span>取消</span>
    </a-button>
    <a-button type="primary" @click="submit" :loading="addLoading">
      <span>提交</span>
    </a-button>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, computed } from "vue";
import { allVisionList } from "@/api/visionPolicy/visionList";
import { policyDetail } from "@/api/visionPolicy/visionList";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import bac from "@/assets/images/noDataBac.png";
import { useRouter } from "vue-router";

export default defineComponent({
  emits: ["ticketDetail", "cancelTicket"],
  setup(props, { emit }) {
    const ticket = ref(null);
    const Router = useRouter();
    const data = reactive({
      formData: {
        typeName: 1,
      },
      typeOptions: [{ label: "政策工单", value: 1 }],
      policyList: [],
      pageSizeOptions: ["6", "20", "30", "50"],
      tableList: [],
      customerList: [],
      addLoading: false,
      cardActive: "-1",
      backgroundImage: bac,
      rules: {
        name: [{ required: true, message: "请输入工单标题", trigger: "blur" }],
        detail: [
          { required: true, message: "请输入工单内容", trigger: "blur" },
        ],
      },
      currentPage: 1,
      pageItemSize: 6,
      emptyShow: false,
      viewLoading: false,
      detailData: {},
    });
    const paginatedData = computed(() => {
      const start = (data.currentPage - 1) * data.pageItemSize;
      const end = start + data.pageItemSize;
      return data.tableList.slice(start, end);
    });
    const getPolicy = () => {
      let parmas = {
        shelfStatus: 1,
        industryId: -1,
      };
      allVisionList(parmas).then((res) => {
        data.policyList = res.data;
      });
    };
    getPolicy();

    const policyChoice = (val) => {
      policyDetail(val).then((res) => {
        data.detailData = res.data;
        data.tableList = res.data.solutionList;
        if (data.tableList.length === 0) {
          data.emptyShow = true;
        } else {
          data.emptyShow = false;
        }
      });
    };

    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };

    const previewFile = (val) => {
      let iamgesList = ["png", "jpg", "jpeg", "bmp", "gif", "webp", "svg"];
      if (iamgesList.includes(val.name.split(".")[1])) {
        let a = document.createElement("a");
        a.setAttribute("href", val.url);
        a.setAttribute("target", "_blank");
        a.setAttribute("download", val.name);
        document.body.appendChild(a);
        a.click();
        document.body.removeChild(a);
        return;
      }
      let windowOrigin = window.location.origin;
		  let token = localStorage.getItem("token");
      if (val.name.split(".")[1] == "pdf") {
      	let newHref = val.url;
		    if(val.url.includes(windowOrigin)){
		     	newHref = "/portal" + val.url.split(windowOrigin)[1]
		    }
        let previewUrl = Router.resolve({
          name: "lookPdf",
		      query: {
		        urlMsg: encodeURIComponent(
		         	windowOrigin + newHref + "?token=" + token
		        ),
		        urlName: val.name,
		      },
        });
        window.open(previewUrl.href, "_blank");
        return;
      }
      data.viewLoading = true;
      pptTopdf({
        filePath: val.path,
        fileUrl: val.url,
      }).then((res) => {
        data.viewLoading = false;
        if (res.code == 200) {
        	let newHref = res.data;
			    if(res.data.includes(windowOrigin)){
			     	newHref = "/portal" + res.data.split(windowOrigin)[1]
			    }
		      const newpage = Router.resolve({
		        name: "lookPdf",
		        query: {
		          urlMsg: encodeURIComponent(
		          	windowOrigin + newHref + "?token=" + token
		          ),
		          urlName: val.name,
		        },
		      });
          window.open(newpage.href, "_blank");
        }
      });
    };

    const contentColor = (index) => {
      data.cardActive = index;
    };

    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const proDetail = (val) => {
      emit("ticketDetail");
      Router.push({
        query: {
          id: val.id,
        },
        name: "solveDetailNew",
      });
    };

    const conserve = () => {
      emit("cancelTicket");
    };

    const submit = async () => {
      ticket.value.validate().then(() => {});
    };

    return {
      ...toRefs(data),
      ticket,
      submit,
      proDetail,
      conserve,
      backgroundStyles,
      paginatedData,
      previewFile,
      policyChoice,
      contentColor,
      contentLeave,
    };
  },
});
</script>

<style lang="scss" scoped>
:deep(.ant-form-item-label) {
  width: 100px;
}
.layPage {
  width: 100%;
  text-align: center;
  padding-top: 15px;
}
.cardContent {
  background: #ffffff;
  border-radius: 10px;
}

.card_total {
  display: flex;
  flex-wrap: wrap;
  align-items: flex-start;
  border-bottom: 1px solid #dae2f5;
}

.emptyPhoto {
  text-align: center;

  img {
    width: 240px;
    height: 248px;
  }
}

.card_content {
  position: relative;
  width: 50%;
  height: 163px;
  border-right: 1px solid #dae2f5;
  border-bottom: 1px solid #dae2f5;
  cursor: pointer;
}

.rightActive {
  border-right: none;
}

.cardActive {
  background-color: #ffffff;
  transition: all 0.2s;
  box-shadow: 0 0 10px #dae2f5;
}

.bottomLine {
  border-bottom: none;
}

.cardObvious {
  border-bottom: none;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}

.card_center {
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  // height: 105px;
  margin-left: 12px;
  // min-width: 66%;
  flex: 1;
}

.card_text {
  display: flex;
  align-items: center;
  justify-content: space-between;

  .card_tag {
    display: flex;
    align-items: center;
  }

  .card_title {
    font-weight: bold;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.85);
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
  }

  :deep(.cityStyle) {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.45);
    background-color: transparent;
    // border: none;
    // margin-right: -6px;
  }
}

.card_des {
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  /* 控制显示的行数 */
  overflow: hidden;
  text-overflow: ellipsis;
  margin-right: 4px;
  word-break: break-all;
}
.itemForm {
  padding: 24px;
  border: 1px solid rgba(0, 6, 14, 0.08);
}
.fileInfo {
  padding: 24px 24px 14px;
  border: 1px solid rgba(0, 6, 14, 0.08);
}
.belowContent {
  padding: 0 0 15px;
  border: 1px solid rgba(0, 6, 14, 0.08);
}
</style>

<style lang="scss">
.ant-select:not(.ant-select-customize-input) .ant-select-selector {
  border: 1px solid #d9d9d9 !important;
}
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}
</style>