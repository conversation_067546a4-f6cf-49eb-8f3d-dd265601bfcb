.topBac {
    min-height: 172px;
    background-image: url("@/assets/images/visionPolicy/contentBaner.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .policyText {
        font-weight: bold;
        font-size: 32px;
        color: #FFFFFF;
        padding: 32px 0 16px 40px;
    }

    .vocationPull {
        display: flex;
        align-items: center;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
        margin: 0 0 24px 40px;
    }

    .commonBtn {
        background-color: #0C70EB;
    }

    .seekInfo {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 120px;
        height: 56px;
        cursor: pointer;

        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .inputClass {
        border: none;
        width: 1001px;
        height: 100%;
        box-shadow: none !important;
    }

    .selectData {
        margin: 0 40px;
        position: relative;
        padding-bottom: 24px;

        .showMore {
            max-height: 400px !important;
            overflow-y: auto;
        }

        .showHidden {
            // overflow: auto;
            max-height: 400px !important;
        }

        .selcet_box {
            border-top: 1px solid rgba(218, 226, 245, .2);
            border-bottom: 1px solid rgba(218, 226, 245, .2);
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 16px;
            display: flex;
            // align-items: center;
            max-height: 44px;
            /* 设置最大高度 */
            overflow: hidden;
            /* 超出部分隐藏 */
            position: relative;
            /* 伪元素的定位需要 */
            line-height: 1.5em;
            /* 行高用于计算总高度 */
            transition: max-height 0.3s ease-out;

            /* 动画效果 */
            .left_select {
                font-weight: 500;
                color: #FFFFFF;
                background: rgba(245, 247, 252, .2);
                padding: 11px 16px;
                min-width: 110px;
            }

            .right_select {
                display: flex;
                flex-wrap: wrap;
                align-items: flex-start;
                // justify-content: left;
                // background-color: #FFFFFF;
                padding-left: 12px;
                width: 100%;

                span {
                    width: 120px;
                    padding: 11px 2px;
                    color: rgba(255, 255, 255, 0.85);
                    // margin-right: 44px;
                    cursor: pointer;
                    text-align: center;
                }

                .activeBtn {
                    position: relative;
                    // background: #F3F8FF;
                    font-weight: bold;
                    font-size: 14px;
                    color: #FFFFFF;
                    //height: 49px;
                }
            }
        }

        .more {
            background: rgba(255, 255, 255, 0.08);
            //border-radius: 4px 4px 4px 4px;
            padding: 5px 12px;
            // font-weight: 500;
            // font-size: 16px;
            font-weight: 500;
            font-size: 16px;
            color: #FFFFFF;
            line-height: 22px;
            width: 85px;
            //height: 32px;
            align-items: center;
            //margin-top: 9px;
            cursor: pointer;

            img {
                width: 8px;
                height: 4px;
                margin-left: 4px;
            }
        }

        .select_boot {
            margin: 11px 0;
            padding-left: 12px;
            justify-content: space-between;
            padding: 5px;
            background-color: #FFFFFF;
            border-top-left-radius: 10px;
            border-top-right-radius: 10px;
            padding-left: 20px;
            padding-right: 20px;

            .right_con {
                color: #9A9A9A;

                span {
                    color: #333333;
                    margin: 0 6px;
                    font-weight: 500;
                }
            }
        }

        .label {
            margin-left: 24px;
        }

        .right_btn {
            width: 70px;
            height: 32px;
            justify-content: center;
            align-items: center;
            background: rgba(12, 112, 235, 0.08);
            font-weight: 500;
            font-size: 16px;
            color: #0C70EB;
            line-height: 22px;
            margin-top: 6px;
            cursor: pointer;
        }
    }

    .last_data_top {
        width: 135px;
        text-align: center;
        height: 50px;
        line-height: 49px;
        border: 1px solid #00000032;
        border-bottom: none;
        color: #2E7FFF;
        background-color: #F3F8FF;
        position: absolute;
        top: 0;
        left: 0;
        z-index: 3;
        cursor: pointer;
    }

    .last_data {
        display: flex;
        flex-wrap: wrap;
        // max-width: 1085px;
        // min-width: 645px;
        width: 1000px;
        border: 1px solid #00000032;
        color: rgba(0, 0, 0, 0.65);
        background-color: #F3F8FF;
        position: absolute;
        z-index: 2;

        .activeBtn {
            font-weight: 500;
            font-size: 14px;
            color: #2E7FFF;
        }
    }

    .second_line {
        text-align: center;
        font-weight: 500;
        font-size: 16px;
        color: #0C70EB;
        line-height: 22px;
        margin-top: 11px;
        display: flex;
        justify-content: center;

        span {
            cursor: pointer;
        }

        .img_box {
            padding-top: 8px;
            margin-left: 4px;

            img {
                width: 8px;
                height: 4px;
                display: block;
            }
        }
    }
}

.policyContain {
    background-color: #FFFFFF;
    position: relative;

    .policyContent {
        display: flex;
        padding: 36px 40px 0 40px;

        .leftContent {
            margin-right: 46px;

            .topNum {
                font-weight: bold;
                font-size: 40px;
                color: rgba(0, 0, 0, 0.45);
                line-height: 66px;
                color: #0C70EB;
                text-align: center;
                background: #F5F7FC;
                height: 66px;
                border-bottom-left-radius: 6px;
                border-bottom-right-radius: 6px;
            }

            .botNum {
                border-top-left-radius: 6px;
                border-top-right-radius: 6px;
                font-weight: bold;
                font-size: 16px;
                color: rgba(255, 255, 255, 0.85);
                line-height: 34px;
                text-align: center;
                background: linear-gradient(166deg, #2888FF 0%, #0C70EB 100%);
                width: 100px;
                height: 34px;
            }
        }

        .rightContent {
            width: 100%;

            .title {
                font-weight: 500;
                font-size: 20px;
                color: rgba(0, 0, 0, 0.85);
            }

            .lineHead {
                width: 24px;
                height: 2px;
                background: #0C70EB;
                margin-top: 8px;
                margin-bottom: 16px;
            }

            .desc {
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
                margin-bottom: 32px;
                overflow: hidden;
                text-overflow: ellipsis;
                display: -webkit-box;
                -webkit-box-orient: vertical;
                -webkit-line-clamp: 2;
            }

            .botLine {
                width: 100%;
                height: 1px;
                background-color: #DAE2F5;
            }
        }
    }

    .layPage {
        width: 100%;
        text-align: center;
        padding: 24px 0;
    }
}

.gennralPolicy {
    margin-top: 40px;
    height: 112px;
    background-image: url("@/assets/images/visionPolicy/commonBac.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;

    .text {
        font-weight: bold;
        font-size: 32px;
        color: #FFFFFF;
        padding: 32px 40px;
    }
}

.readContent {
    background: #F5F7FC;
    border-radius: 10px 10px 10px 10px;
    padding: 0 40px;
    overflow: hidden;

    .text {
        font-weight: 500;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
        padding-top: 40px;
    }

    .date {
        color: rgba(0, 0, 0, 0.45);
        font-size: 14px;
        margin-top: 16px;
        line-height: 20px;
    }

    .desc {
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 34px;
        margin-top: 24px;
        overflow: hidden;
        height: 160px;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 5;
    }

    .file {
        font-size: 14px;
        color: #0C70EB;
        line-height: 22px;
        margin-top: 16px;
    }

    .operation {
        padding: 32px 0;
        display: flex;
        justify-content: center;
    }

}

.emptyPhoto {
    text-align: center;
    padding: 20px 0;

    img {
        width: 240px;
        height: 248px;
    }
}

.loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
}

.generalEmpty {
    background-color: #FFFFFF;
}