<template>
  <div class="visionContent">
    <div class="visionBac">
      <!-- <div class="visionText">政策洞察</div>
      <div class="visionDes">
        政策热点跟踪与深度解析，洞察行业风向，引领未来趋势。
      </div> -->
      <div style="margin-top: 20px">
        <table-list />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { useRouter, useRoute } from "vue-router";
import tableList from "./components/tableList.vue";

const router = useRouter();
const route = useRoute();
defineOptions({
  name: "visionPolicy",
});
</script>

<style lang="scss">
.visionContent {
  width: 1200px;
  margin: 0 auto;

  .visionBac {
    height: 400px;
    background-image: url("@/assets/images/visionPolicy/topBac.png");
    background-repeat: no-repeat;
    background-size: 100% 100%;
  }

  .visionText {
    font-weight: bold;
    font-size: 42px;
    color: #122c6c;
    padding-top: 80px;
  }

  .visionDes {
    font-size: 16px;
    color: #2b3f66;
    margin-top: 24px;
  }
}
</style>