<template>
  <div class="topContent"></div>
  <div class="centerContent">
    <template v-for="(item, index) in centerList" :key="index">
      <div class="listClass flex align-center just-sb">
        <div class="flex">
          <div>
            <div class="flex align-center" style="width: max-content">
              <div class="dateClass">{{ item.date }}</div>
              <div class="line"></div>
            </div>
            <div class="yearStyle">2023</div>
          </div>

          <div style="margin: 2px 63px 30px 0">
            <div>
              <p class="text">燃气自动化能力的全面提升与系统集成方面的探索</p>
              <p class="desText">
                关注于通过先进的自动化技术和智能系统集成，提高燃气供应的效率与安全性。此方向包括实施智能监控系统、远程控制技术以及数据分析工具，从而优化燃气生产、输送和分配过程。
              </p>
              <a-tag color="rgba(30,99,255,0.06)">
                <template #icon>
                  <img
                    src="@/assets/images/home/<USER>"
                    alt=""
                    style="width: 16px; height: 16px"
                  />
                </template>
                <span style="margin-left: 4px">300</span>
              </a-tag>
            </div>
          </div>
        </div>
        <a-image :width="200" :height="110" :preview="false" />
      </div>
    </template>

    <div class="layPage">
      <pagination
        :totalItemCount="totalItemCount"
        @size-change="sizeChange"
        @page-change="pageChange"
      />
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import pagination from "@/components/pagination/index.vue";

export default defineComponent({
  components: {
    pagination,
  },
  setup() {
    const data = reactive({
      centerList: [{ date: "10-21" }, { date: "10-14" }, { date: "09-26" }],
      totalItemCount: 0,
    });
    const pageChange = (page, pageSize) => {};
    const sizeChange = (current, size) => {};

    return {
      ...toRefs(data),
      pageChange,
      sizeChange,
    };
  },
});
</script>

<style lang="scss" scoped>
.topContent {
  width: 100%;
  height: 180px;
  background-image: url("@/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-size: cover;
}

.centerContent {
  margin: 30px 120px;
  background: #ffffff;

  .listClass {
    padding-top: 31px;
    margin: 0 40px 0 59px;
    border-bottom: 1px solid #dddddd;
  }
  :deep(.ant-tag) {
    font-size: 12px;
    color: rgba(0, 0, 0, 0.65);
    display: inline-flex;
    align-items: center;
  }
  .line {
    width: 24px;
    height: 1px;
    background-color: #cccccc;
    margin: 0 18px;
  }
  .dateClass {
    font-family: DIN Alternate, DIN Alternate;
    font-weight: bold;
    font-size: 24px;
    color: rgba(0, 0, 0, 0.65);
  }
  .yearStyle {
    width: 40px;
    height: 18px;
    border: 1px solid #cccccc;
    color: #cccccc;
    text-align: center;
    line-height: 18px;
    margin-top: 12px;
    margin-left: 15px;
  }
  .text {
    font-weight: 500;
    font-size: 18px;
    color: rgba(0, 0, 0, 0.85);
  }
  .desText {
    color: rgba(0, 0, 0, 0.65);
    margin-top: 24px;
    margin-bottom: 16px;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .layPage {
    margin-top: 40px;
    padding-bottom: 50px;
  }
}
</style>
