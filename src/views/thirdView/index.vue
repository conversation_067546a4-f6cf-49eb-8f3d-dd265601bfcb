<!--
 * @Description: 
 * @Author: xiuji
 * @Date: 2024-06-27 14:28:40
 * @LastEditTime: 2024-06-27 14:52:18
 * @LastEditors: Do not edit
-->
<template>
  <div class="iframe-container">
    <a-spin v-if="isLoading" class="loading" />
    <iframe v-if="iframeSrc" :src="iframeSrc" scrolling="auto" @load="handleIframeLoad" />
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import { AESsolve } from "@/utils/aes.js";
import { currentTab } from "@/store";
export default defineComponent({
  setup() {
    const getLink = currentTab();
    const token = encodeURIComponent(AESsolve());

    const data = reactive({
      iframeSrc: "",
      isLoading: true,
    });
    const handleIframeLoad = () => {
      data.isLoading = false;
    };
   const close = () => {
      data.isLoading = true;
    };

    watch(
      () => getLink.linkUrl,
      (val) => {
        data.iframeSrc = getLink.type ? val.replace("xxxx", token) : val;
      },
      { immediate: true, deep: true }
    );
    return {
      ...toRefs(data),
      handleIframeLoad,
      close
    };
  },
});
</script>

<style lang="scss" scoped>
.iframe-container {
  width: 100%;
  height: calc(100vh - 60px);

  iframe {
    height: 100%;
    width: 100%;
    border: none;
  }
  .loading{
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%,-50%);

  }
}
</style>