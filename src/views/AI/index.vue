<template>
  <div class="page-content">
    <div class="title" v-if="!isShowTitle">
      <img class="tipsimg" src=" @/assets/images/AI/tips.png" />
      <img class="titleimg" src=" @/assets/images/AI/title.png" />
    </div>
    <div class="chat-container" :style="isShowTitle?'height:calc(100vh - 260px);':''">
      <!-- 消息显示区域 -->
      <div class="chat-box" ref="contRef">
        <div class="tips flex" v-if="!isShowTitle">
          <img class="avatar" src=" @/assets/images/AI/ai.png" alt="AI头像" />
          <div> 您好，我是麒麟能力助手，可以为您快速精准配，小助手可以提供能力，助力业务创新，提升工作效率，有什么可以帮助您的吗?</div>
        </div>
        <div v-for="(message, index) in messages" :key="index" :class="message.isUser ? 'user-message' : 'ai-message'">
          <!-- <Message :text="message.text" :isUser="message.isUser" :isTyping="message.isTyping"
            @duplicated="customDuplicated" @productDuplicated="productDuplicated" :isLoading="message.isLoading"
            :modelList="message.modelList" :dataType="message.dataType" :tips="message.tips" :messageTips="messageTips"
            :productBagName="productBagName" @changeVal="changeVal" /> -->
          <new-message :isShowTitle="isShowTitle"  :text="message.text" :isUser="message.isUser" :isTyping="message.isTyping"
            @duplicated="customDuplicated" @close="close" @productDuplicated="productDuplicated" :isLoading="message.isLoading"
            :modelList="message.modelList" :newMessage="message.newMessage" :dataType="message.dataType"
            :tips="message.tips" :messageTips="messageTips" :productBagName="productBagName" @changeVal="changeVal" />
        </div>
        <div v-if="loadingAnswer" class="loading-spinner"></div>
        <div v-else style="height:20px;margin-top:20px"> </div>
      </div>

      <!-- 用户输入区域 -->
      <div class="flex" :style="isShowTitle?'width:calc(100% - 300px);margin:0 auto;':''">
        <!-- <div class="chooseBtn">
          <div class="Btns flex just-center align-center">
            <div class="eachBtn pointer" :class="type == 0 ? 'choosed' : ''" @click="chooseThis(0)">查询</div>
            <div class="eachBtn pointer" :class="type == 4 ? 'choosed' : ''" @click="chooseThis(4)">定制</div>
          </div>
        </div> -->
        <div class="input-box">
          <!-- <div>
          12234
        </div> -->
          <a-input v-model:value="userInput" @keyup.enter="sendMessage()" placeholder="请输入您的需求"
            style="width: 100%; height: 44px" />
          <voiceRecorder style="margin-right: 50px;height: 44px;" :isTranslating="isTranslating" :canBtnUse="canBtnUse"
            @audioReady="handleAudio" />
          <i class="sendbtn iconfont icon-fasong pointer" style="color: #0C70EB;" v-if="canSendMessage"
            @click="sendMessage()"></i>
          <i class="sendbtn iconfont icon-fasong" v-else></i>
          <!-- <a-button class="sendbtn" :disabled="!canSendMessage" @click="sendMessage()" type="link">
            <img class="vector" :class="[isTyping ? 'isTyping' : '']" src=" @/assets/images/AI/vector.png" /></a-button> -->

        </div>
      </div>

    </div>
    <a-modal v-model:visible="visible" title="提示" @ok="handleOk">
      <p>已存在定制组合，请确认是否替换已有组合?</p>
    </a-modal>
    <a-modal v-model:visible="productVisible" title="提示" @ok="productHandleOk">
      <p>已存在封装产品，请确认是否替换已有封装?</p>
    </a-modal>
    <a-modal v-model:visible="detailsVisible" title="提示" @ok="detailsHandleOk">
      <p>已存在封装产品，请确认是否替换已有封装?</p>
    </a-modal>
  </div>

</template>

<script>
import { defineComponent, reactive, toRefs, nextTick, ref, onActivated } from "vue";
import Message from "./components/message.vue";
import newMessage from "./components/newMessage.vue";
import { getReplay, getModelList, getAssociate, AIvoice } from "@/api/AI/ai.js";
import { toCombinePage } from "@/api/combine/shoppingCart.js";
import { getsolutionList } from "@/api/solutionNew/home";
import { useRouter } from "vue-router";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import { toShopList } from "@/api/buyList/index";
import { message } from "ant-design-vue";
import eventBus from "@/utils/eventBus";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";
export default defineComponent({
  name: 'AI',
  components: {
    Message,
    newMessage,
    voiceRecorder,
  },
  props: {
    showUrl: {
      type: Boolean,
      default: false,
    }
  },
  setup(props, { emit }) {
    // 进入页面滚动到最底部
    onActivated(() => {
      setTimeout(() => {
        let scrollElem = contRef.value;
        console.log(scrollElem)
        scrollElem.scrollTo({
          top: scrollElem.scrollHeight,
          behavior: "smooth",
        });
      }, 300)
    })

    const router = useRouter();

    const data = reactive({
    	isShowTitle: props.showUrl,
      isRecording: false,
      loadingAnswer: false,
      visible: false,
      productVisible: false,
      detailsVisible: false,
      productArr: [],
      userInput: "",
      messages: [], //{ text: '', isUser: false, isTyping: false },
      isTyping: false,
      myMessage: {},
      filteredSuggestions: [], // 根据用户输入实时过滤的推荐项
      hotProjectList: [], //热门方案列表
      hotModelList: [], //热门模块列表
      joinedProjectId: null, //已加购方案
      joinedModelId: [], //已加购模块
      messageType: 1,
      customArr: [],
      flag: false,
      messageTips: '',
      isUserScrolling: false,
      scrollTimeout: null,
      isTranslating: false,
      canBtnUse: false,
      productBagName: '',
      allMessageData: {},
      type: 0,
      canSendMessage: true,
    });
    
    console.log(data.isShowTitle);

    const init = () => {
      getsolutionList({
        pageNo: 1,
        pageSize: 3,
        sortType: 2,
        addCart: false,
        name: "",
        categoryId: "",
        provider: "",
      }).then((res) => {
        data.hotProjectList = res.data.rows.slice(0, 3);
        data.hotProjectList = data.hotProjectList.map((item) => {
          return { ...item, btnText: item.addCart ? "已加入" : "加入预选" };
        });
      });
      getModelList({
        pageNo: 1,
        pageSize: 3,
        sortType: 2,
        addCart: false,
        name: "",
        industry: "",
        provider: "",
      }).then((res) => {
        data.hotModelList = res.data.rows.slice(0, 3);
        data.hotModelList = data.hotModelList.map((item) => {
          return { ...item, btnText: item.addCart ? "已加入" : "加入预选" };
        });
      });
    };
    // init();
    //跳转到方案/模块 详情
    const jump = (id, type) => {
      if (type === "project") {
        //跳转到方案详情
        router.push({ name: "solveDetailNew", query: { id: id } });
      } else if (type === "model") {
        //跳转到模块详情
        router.push({ name: "modulelNew", query: { id: id } });
      }
    };
    //点击按钮， 将方案加入购物车
    const jionProject = (id) => {
      data.hotProjectList.forEach((el) => {
        el.btnText = "加入预选";
      });
      addShoppingCart({
        schemeId: id,
        type: 1,
      }).then((res) => {
        eventBus.emit("cartRefresh");
        init();
      });
    };

    //点击按钮  将模块加入购物车
    const jionModel = (id) => {
      addShoppingCart({
        schemeId: id,
        type: 2,
      }).then((res) => {
        eventBus.emit("cartRefresh");
        init();
      });
    };

    // 处理选择推荐项
    const handleSelect = (value) => {

      data.messageType = 1;
      sendMessage(str); // 自动发送消息
    };
    const forceUpdate = () => nextTick();

    // 模拟打字机效果
    const typeWriterEffect = (messageText, callback) => {
      let displayedText = "";
      let index = 0;
      data.scrollTimeout = setInterval(() => {
        console.log('xxxxxx')
        let scrollElem = contRef.value;
        // 触发鼠标滚轮时间时，清除定时器，使之在打字的过程中不会一直往下滚动导致不能向上翻阅
        scrollElem.addEventListener("wheel", onUserScroll);
        scrollElem.scrollTo({
          top: scrollElem.scrollHeight,
          behavior: "smooth",
        });
      }, 100)
      const interval = setInterval(() => {
        if (index < messageText.length) {
          displayedText += messageText[index];
          index++;
          // console.log('displayedText', displayedText);


          callback(displayedText);
          forceUpdate()
        } else {
          clearInterval(interval);
          clearInterval(data.scrollTimeout)
          nextTick(() => {
            callback(displayedText, true); // 打字完成后，回调时带上完成标志
          });
        }
        // debugger
      }, 20); // 每100ms显示一个字符
      // 打完字（完成一条回复）后，重置定时器，不影响下次对话的定时器效果
      data.isUserScrolling = false;

    };
    const prompt = (res) => {
      let replayText = ''
      let allLength = 0
      // let solutionLength = 0
      let replayTextArr = []
      if (res.data == {}) {
        replayText = '很抱歉，未能为您找到相关内容';
      } else {
        if (res.data.resultType == "1") {
          replayText = res.data.chatMessage;
        }
        if (res.data.resultType == "0") {
          if (res.data.solutionList.length !== 0) {
            for (let i of res.data.solutionList) {
              allLength += i.solution.length
              if (i.solution.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (res.data.moduleList.length !== 0) {
            for (let i of res.data.moduleList) {
              allLength += i.ability.length
              if (i.ability.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (res.data.productBagList.length !== 0) {
            for (let i of res.data.productBagList) {
              allLength += i.productBag.length
              if (i.productBag.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (res.data.productList.length !== 0) {
            for (let i of res.data.productList) {
              allLength += i.product.length
              if (i.product.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (res.data.solutionModelList.length !== 0) {
            for (let i of res.data.solutionModelList) {
              allLength += i.scene.length
              if (i.scene.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (allLength == 0) {
            replayText = '很抱歉，未能为您找到相关内容'
          } else {
            if (replayTextArr.length !== 0) {
              replayText = "经过分析，为您推荐以下相关内容。" + "请注意" + replayTextArr.join("、") + "未查询到，将不作推荐"
            } else {
              replayText = '经过分析，为您推荐以下相关内容';
            }
          }

        }
        if (res.data.resultType == "4") {
          let solutionArr = []
          if (res.data.moduleCombine.solutionList.length !== 0) {
            for (let i of res.data.moduleCombine.solutionList) {
              allLength += i.solution.length
              // solutionLength += i.solution.length
              if (i.solution.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (res.data.moduleCombine.moduleList.length !== 0) {
            for (let i of res.data.moduleCombine.moduleList) {
              allLength += i.ability.length
              if (i.ability.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (res.data.moduleCombine.productBagList.length !== 0) {
            for (let i of res.data.moduleCombine.productBagList) {
              allLength += i.productBag.length
              if (i.productBag.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (res.data.moduleCombine.productList.length !== 0) {
            for (let i of res.data.moduleCombine.productList) {
              allLength += i.product.length
              if (i.product.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          if (res.data.moduleCombine.sceneList.length !== 0) {
            for (let i of res.data.moduleCombine.sceneList) {
              allLength += i.scene.length
              if (i.scene.length == 0) {
                replayTextArr.push(i.key + i.name)
              }
            }
          }
          for (let i of res.data.moduleCombine.solutionList) {
            if (i.solution.length !== 0) {
              solutionArr.push(i)
            }
          }
          if (allLength == 0) {
            replayText = '很抱歉，未能为您找到相关内容'
          } else {
            if (solutionArr.length == 0) {
              replayText = '很抱歉，没有为您查到相关方案，所以无法定制'
            } else if (solutionArr.length > 1) {
              replayText = '很抱歉，只能基于一个方案进行定制，请您重新询问'
            } else {
              if (replayTextArr.length !== 0) {
                replayText = "经过分析，为您推荐以下相关内容。" + "请注意" + replayTextArr.join("、") + "未查询到，将不作推荐"
              } else {
                replayText = '经过分析，为您推荐以下相关内容';
              }
            }

          }

        }
        if (res.data.resultType == "7") {
          if (res.data.moduleCombine.productList.length == 0) {
            if (res.data.moduleCombine.moduleList.length !== 0 && res.data.moduleCombine.sceneList.length == 0) {
              replayText = '很抱歉，能力无法被定制';
            } else if (res.data.moduleCombine.sceneList.length !== 0 && res.data.moduleCombine.moduleList.length == 0) {
              replayText = '很抱歉，场景无法被定制';
            } else if (res.data.moduleCombine.moduleList.length !== 0 && res.data.moduleCombine.sceneList.length !== 0) {
              replayText = '很抱歉，能力和场景无法被定制';
            }
          } else {
            if (res.data.moduleCombine.solutionList.length !== 0) {
              for (let i of res.data.moduleCombine.solutionList) {
                allLength += i.solution.length
                // solutionLength += i.solution.length
                if (i.solution.length == 0) {
                  replayTextArr.push(i.key + i.name)
                }
              }
            }
            if (res.data.moduleCombine.moduleList.length !== 0) {
              for (let i of res.data.moduleCombine.moduleList) {
                allLength += i.ability.length
                if (i.ability.length == 0) {
                  replayTextArr.push(i.key + i.name)
                }
              }
            }
            if (res.data.moduleCombine.productBagList.length !== 0) {
              for (let i of res.data.moduleCombine.productBagList) {
                allLength += i.productBag.length
                if (i.productBag.length == 0) {
                  replayTextArr.push(i.key + i.name)
                }
              }
            }
            if (res.data.moduleCombine.productList.length !== 0) {
              for (let i of res.data.moduleCombine.productList) {
                allLength += i.product.length
                if (i.product.length == 0) {
                  replayTextArr.push(i.key + i.name)
                }
              }
            }
            if (res.data.moduleCombine.sceneList.length !== 0) {
              for (let i of res.data.moduleCombine.sceneList) {
                allLength += i.scene.length
                if (i.scene.length == 0) {
                  replayTextArr.push(i.key + i.name)
                }
              }
            }

            if (allLength == 0) {
              replayText = '很抱歉，未能为您找到相关内容'
            } else {
              if (replayTextArr.length !== 0) {
                // replayText = '经过分析，为您推荐以下相关内容' + '请注意' + replayTextArr.join("、") + "未查询到，将不作推荐"
                replayText = "经过分析，为您推荐以下相关内容。" + "请注意" + replayTextArr.join("、") + "未查询到，将不作推荐"
              } else {
                replayText = '经过分析，为您推荐以下相关内容';
              }
            }
          }
        }
      }







      // if (res.data.resultType === "0" || res.data.resultType === "4" || res.data.resultType === "7") {
      //   replayText = '经过分析，为您推荐以下相关内容';
      // } else {
      //   replayText = '很抱歉，未能为您找到相关内容';
      // }
      return replayText;
    }
    // 发送消息的方法
    const sendMessage = (theMessage = "", messageType, cahngeValue) => {
      // let replayText = "";
      let replyList = [];
      let newMessage = {};
      let groupArr = []
      let question = JSON.parse(JSON.stringify(data.userInput.trim()))
      // console.log('question', question);

      if (question !== '') {
        if (data.canSendMessage) {

          data.loadingAnswer = true
          data.canSendMessage = false
          handleUserMessage(theMessage, messageType);
          getAssociate({
            question: question,
            isChange: false,
            // type: data.type,
          }).then((res) => {
            console.log(res)
            data.loadingAnswer = false
            // replayText = prompt(res)
            let replayText = prompt(res);
            if (res.data.resultType == "0") {
              for (let i of res.data.solutionList) {
                // 方案
                for (let j of i.solution) {
                  if (j.addCart) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '方案'
                }
              }
              for (let i of res.data.solutionModelList) {
                // 方案场景
                for (let j of i.scene) {
                  if (j.addCart) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '方案场景'
                }
              }
              for (let i of res.data.moduleList) {
                // 能力
                for (let j of i.ability) {
                  if (j.addCart) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '能力'

                }
              }
              for (let i of res.data.productList) {
                // 产品
                for (let j of i.product) {
                  if (j.addOrder == 1) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '产品'

                }
              }
              for (let i of res.data.productBagList) {
                // 场景
                for (let j of i.productBag) {
                  if (j.addOrder == 1) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '场景'

                }
              }
              newMessage = res.data
            } else if (res.data.resultType == "1") {
              // replayText = res.data.chatMessage;
            } else if (res.data.resultType == "4") {
              for (let i of res.data.moduleCombine.solutionList) {
                // 方案
                for (let j of i.solution) {
                  if (j.addCart) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '方案'

                }
              }
              for (let i of res.data.moduleCombine.sceneList) {
                // 方案场景
                for (let j of i.scene) {
                  if (j.addCart) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '方案场景'

                }
              }
              for (let i of res.data.moduleCombine.moduleList) {
                // 能力
                for (let j of i.ability) {
                  if (j.addCart) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '能力'

                }
              }
              for (let i of res.data.moduleCombine.productList) {
                // 产品
                for (let j of i.product) {
                  if (j.addOrder == 1) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '产品'
                }
              }
              for (let i of res.data.moduleCombine.productBagList) {
                // 场景
                for (let j of i.productBag) {
                  if (j.addOrder == 1) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '场景'
                }
              }
              newMessage = res.data
              newMessage.groupList = []
              groupArr = res.data.moduleCombine.sceneList.concat(res.data.moduleCombine.moduleList, res.data.moduleCombine.productList)
              console.log('groupArr666666', groupArr)
              for (let i of groupArr) {
                if (i.name == '方案场景') {
                  for (let j of i.scene) {
                    newMessage.groupList.push(j)
                  }
                }
                if (i.name == '能力') {
                  for (let j of i.ability) {
                    newMessage.groupList.push(j)
                  }
                }
                if (i.name == '产品') {
                  for (let j of i.product) {
                    newMessage.groupList.push(j)
                  }
                }
              }
              console.log('newMessage.groupList666666', newMessage.groupList)
            } else if (res.data.resultType == "7") {
              newMessage = res.data
              newMessage.originList = []//原有的场景
              newMessage.artificialList = []//额外语义搜的
              for (let i of newMessage.moduleCombine.productList) {
                // 产品
                for (let j of i.product) {
                  if (j.addOrder == 1) {
                    j.btnText = '已加入'
                  } else {
                    j.btnText = '加入预选'
                  }
                  j.specialTitle = '产品'
                }

                if (i.keyFlag == '0') {
                  for (let a of i.product) {
                    a.bagName = i.key
                  }
                  newMessage.originList.push(i)
                } else if (i.keyFlag == '1') {
                  for (let z of i.product) {
                    newMessage.artificialList.push(z)
                  }
                }
              }
              console.log('newMessage.originList', newMessage.originList)
              console.log('newMessage.artificialList', newMessage.artificialList)
            }

            data.myMessage.dataType = res.data.resultType;
            handleAiReplay(question, messageType, replayText, replyList, newMessage);
          }).catch((err) => {
            data.canSendMessage = true
          });
        } else {
          message.config({
            maxCount: 1,
          });
          message.loading("请耐心等待上一条消息回复完毕");
        }
      } else {
        message.error("用户输入不能为空");
      }

    };

    const customDuplicated = (customArr) => {
      data.customArr = customArr;
      data.visible = true;
    };
    const productDuplicated = (productArr) => {
      data.productArr = productArr;
      data.productVisible = true;
    }
    const handleUserMessage = (message = "",
      messageType) => {
      const inputMessage = message !== "" ? message : data.userInput.trim();
      if (inputMessage != "") {
        // 添加用户的消息
        data.messages.push({
          text: inputMessage,
          isUser: true,
          isTyping: false,
          isLoading: false,
          // modelList: arr,
        });
        // 清空输入框
        data.userInput = "";
        // data.filteredSuggestions = []; // 隐藏推荐列表
        // console.log('contRef.value',contRef.value);
        // debugger
        setTimeout(() => {
          let scrollElem = contRef.value;
          scrollElem.scrollTo({
            top: scrollElem.scrollHeight,
            behavior: "smooth",
          });
        }, 10)

      }
    };
    const handleAiReplay = (
      message = "",
      messageType,
      replayText,
      replyList,
      newMessage,
    ) => {
      let arr = replyList;
      arr.forEach((el) => {
        if (el.addOrder) {
          el.btnText = el.addOrder === 1 ? '已加入' : '加入预选';
        } else {
          el.btnText = el.addCart ? '已加入' : '加入预选';
        }
      }); // 确保传递的是字符串，避免传递事件对象
      const inputMessage = message !== "" ? message : data.userInput.trim();
      if (inputMessage != "") {
        // 模拟AI的打字机回复
        const aiMessage = reactive({
          text: "",
          isUser: false,
          isTyping: false,
          isLoading: true,
          modelList: arr,
          dataType: data.myMessage.dataType,
          tips: '',
          newMessage: newMessage,
        });
        data.isTyping = true;

        data.messages.push(aiMessage);
        // debugger
        setTimeout(() => {
          aiMessage.isLoading = false; // 隐藏加载动画
          const fullAiMessage = replayText;
          // 打字机效果
          typeWriterEffect(fullAiMessage, (partialText, isFinished = false) => {
            nextTick(() => {
              // console.log('partialText',partialText);

              aiMessage.text = partialText;
              aiMessage.tips = partialText;

              // console.log('aiMessage111',aiMessage);

              if (isFinished) {
                data.canSendMessage = true
                aiMessage.isTyping = true; // 打字完成后更新isTyping
                data.isTyping = false;
                setTimeout(() => {
                  let scrollElem = contRef.value;
                  smoothScrollToBottom(scrollElem, 2000); // 滚动到底部，设置为2秒
                })
              }
            });
          });
        }, 500);

      }
    };
    const handleOk = () => {
      toCombinePage({ list: data.customArr, source: '3' }).then((res) => {
        data.visible = false;
        if(!data.isShowTitle){
	        router.push({
	          path: "/customized/customizedList?",
	          query: { active: "定制" },
	        });
	        eventBus.emit("urlRefresh");
        } else {
        	emit("close");
        }
      })
    }
    const close = () => {
    	emit("close");
    }
    const productHandleOk = () => {
      toShopList({
        productShoppingCarts: data.productArr,
        source: '3'
      }).then((res) => {
        data.productVisible = false
        if(!data.isShowTitle){
	        eventBus.emit("buyListRefresh");
	        router.push({
	          name: "buyListPage",
	        });
        }
      })
    }
    const detailsHandleOk = () => {

    }
    const changeVal = (e) => {
      sendMessage("", "", e);
    };
    const contRef = ref(null);
    // 滚动事件
    const smoothScrollToBottom = (element, duration = 1000) => {
      if (data.isUserScrolling) return
      const start = element.scrollTop;
      const end = element.scrollHeight;
      const distance = end - start;
      const stepTime = 16; // 每帧16ms（大约60帧每秒）
      const steps = duration / stepTime;
      let currentStep = 0;

      const scrollStep = () => {
        if (currentStep < steps) {
          currentStep++;
          const progress = currentStep / steps;
          const scrollPosition = start + distance * progress;
          element.scrollTop = scrollPosition;
          requestAnimationFrame(scrollStep);
        }
      };

      scrollStep();
    };
    // 监听滚轮事件
    const onUserScroll = () => {
      data.isUserScrolling = true;
      // 清除自动滚动的计时器
      clearTimeout(data.scrollTimeout);
    };
    // 语音输入
    const handleAudio = (audioBlob) => {
      const formData = new FormData();
      formData.append('file', audioBlob, 'recording.wav'); // 上传文件
      console.log('ssssssss', formData)
      // 调用 AIvoice 函数并传递音频数据
      data.isTranslating = true
      data.canBtnUse = true
      AIvoice(formData).then(res => {
        data.isTranslating = false
        data.canBtnUse = false
        console.log(res)
        if (res.code == 200) {
          data.userInput = res.msg
          // sendMessage()
        }
      });
    };
    const chooseThis = (type) => {
      data.type = type
      console.log('data.type', data.type)
    }
    return {
      ...toRefs(data),
      handleOk,
      productHandleOk,
      detailsHandleOk,
      customDuplicated,
      productDuplicated,
      contRef,
      sendMessage,
      typeWriterEffect,
      changeVal,
      handleSelect,
      init,
      jionProject,
      jionModel,
      handleAiReplay,
      jump,
      router, handleUserMessage,
      handleAudio,
      chooseThis,
      close
    };
  },
});
</script>

<style lang="scss" scoped>
.page-content {
  width: 100%;
  height: 100%;
  padding-bottom: 24px;
  background-color: rgba(245, 247, 252);
  display: flex;
  justify-content: center;
  align-items: center;
  flex-direction: column;
}

.title {
  width: 100%;
  // text-align: center;
  padding-left: 200px;
  font-size: 20px;
  margin: 10px;
  font-weight: 500;

  .tipsimg {
    width: 15px;
    height: 8px;
  }

  .titleimg {
    width: 118px;
    height: 19px;
  }
}

.click {
  color: #007bff;
  cursor: pointer;
}

.tips {
  // margin-left: 40px;
  width: fit-content;
  padding: 24px;
  background-color: #fff;
  border-radius: 0px 30px 30px 30px;
  box-shadow: 0px 8px 24px 0px rgba(116, 157, 219, 0.24);
}

.chat-container {
  background-color: #F5F7FC;
  display: flex;
  flex-direction: column;
  height: calc(100vh - 144px);
  justify-content: flex-end;
  width: calc(100vw - 400px);
  // margin: auto;
  // border: 1px solid #ccc;
  border-radius: 10px;
  overflow: hidden;
}

.chat-box {
  flex-grow: 1;
  overflow-y: auto;
  padding: 10px;
  background-color: #F5F7FC;
}

.input-box {
  position: relative;
  display: flex;
  flex: 1;
  // border-top: 1px solid #ccc;
  padding: 10px;
  margin-bottom: 10px;
  border-radius: 24px 24px 24px 24px;
  background-color: #fff;
}

.chooseBtn {
  width: 176px;
  height: 62px;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: #fff;
  box-shadow: 0px 8px 24px 0px rgba(116, 157, 219, 0.24);
  border-radius: 24px 24px 24px 24px;
  margin-right: 24px;

  .Btns {
    width: 160px;
    height: 40px;
    background: linear-gradient(180deg, #E7EFFC 0%, #FFFFFF 100%);
    border-radius: 16px 16px 16px 16px;
    border: 1px solid #F5F7FC;

    .eachBtn {
      width: 50%;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 16px;
      color: #0C70EB;
      font-weight: 500;

    }

    .choosed {
      color: #fff;
      background: linear-gradient(360deg, #0142FD 0%, #458CFF 100%);
      border-radius: 16px 16px 16px 16px;
    }
  }
}

input {
  flex-grow: 1;
  padding: 10px;
  height: 56px;
  border: none;
  border-radius: 5px;
  outline: none;
}

.ant-btn {
  margin-left: 10px;
}

.user-message {
  text-align: right;
}

.ai-message {
  text-align: left;
}

.cardTitle {
  margin-top: 10px;
  margin-bottom: 10px;
}

.projectConten {
  display: flex;

  .card_content {
    display: flex;
    position: relative;
    padding: 24px;
    background-color: #fff;
    margin-right: 10px;
    width: 288px;
    max-height: 120px;

    overflow: hidden;

    :deep(.ant-image-img) {
      // margin: 10px
      margin-right: 12px;
    }

    .name {
      margin-top: 6px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      word-break: break-all;
    }

    .add {
      width: 16px;
      margin-bottom: 3px;
      margin-right: 6px;
      margin-left: -6px;
    }

    .joinBtn {
      position: absolute;
      bottom: 24px;
      left: 110px;
    }
  }
}

.modelConten {
  display: flex;

  .card_content {
    display: flex;
    position: relative;
    padding: 24px;
    background-color: #fff;
    margin-right: 10px;
    width: 288px;
    max-height: 120px;

    overflow: hidden;

    :deep(.ant-image-img) {
      // margin: 10px
      margin-right: 12px;
    }

    .name {
      margin-top: 6px;
      display: -webkit-box;
      -webkit-box-orient: vertical;
      overflow: hidden;
      text-overflow: ellipsis;
      -webkit-line-clamp: 2;
      line-clamp: 2;
      word-break: break-all;
    }

    .add {
      width: 16px;
      margin-bottom: 3px;
      margin-right: 6px;
      margin-left: -6px;
    }

    .joinBtn {
      position: absolute;
      bottom: 24px;
      left: 114px;
    }
  }
}

.avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  margin-right: 24px;
}

.joined {
  // opacity: 0.9 !important;
}

.vector {
  width: 24px;
  height: 16px;
}

.isTyping {
  color: black;
}

.sendbtn {
  position: absolute;
  top: 20px;
  right: 24px;
}

:deep(.ant-select-selection-search input) {
  height: 56px;
  border: none;
}

:deep(.ant-select-selection-placeholder) {
  height: 56px;
  line-height: 56px !important
}

:deep(.ant-select-selector) {
  margin-top: -6px;
}

:deep(.ant-input:focus) {
  box-shadow: none;
}

/* 加载动画 */
.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #007bff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  margin-top: 20px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}
</style>