<template>
    <div class="eachLine" v-if="items.length > 0">
        <div class="eachLineTitle flex align-center margin_b_8 font_18" v-if="showTitle">{{ title }}</div>
        <div class="eachSolution margin_l_32" v-for="(item, index) in items" :key="index">
            <div class="eachSolutionTitle margin_b_10" v-if="item.initialData.length > 0">
                {{ item.key }}：
            </div>
            <div class="flex" style="flex-wrap: wrap;">
                <div class="eachSolutionCard margin_b_16 margin_r_16 pointer" style="width: 288px;"
                    v-for="(lineItem, lineKey) in item.initialData" :key="lineKey"
                    @click.stop="jump(lineItem.id, item.name)">
                    <div class="eachCardContent flex">
                        <div class="imgContainer">
                            <img class="imgItem" v-if="lineItem[imgName]" :src="lineItem[imgName]" alt="" />
                            <img class="imgItem" v-else
                                src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                                alt="" />
                        </div>
                        <div style="flex: 1" class="margin_l_6">
                            <a-tooltip>
                                <template #title>
                                    <span>{{ lineItem.name }}</span>
                                </template>
                                <div class="name desc margin_l_6">{{ lineItem.name ? lineItem.name : '暂无名称'
                                    }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.description }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '方案'">
                                    {{ lineItem.description }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.summary }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '方案场景'">
                                    {{ lineItem.summary }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.abilityIntro }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '能力'">
                                    {{ lineItem.abilityIntro }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.introduction }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '产品'">
                                    {{ lineItem.introduction }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.introduce }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '场景'">
                                    {{ lineItem.introduce }}
                                </div>
                            </a-tooltip>
                            <a-button size="small" type="link" @click.stop="joinShoppingCar(lineItem.id, item.name)"
                                :disabled="lineItem.btnText === '已加入'">
                                <img class="add" src="@/assets/images/AI/isadded.png"
                                    v-if="lineItem.btnText === '加入预选'" />
                                {{ lineItem.btnText }}
                            </a-button>
                        </div>
                    </div>
                    <div class="identification">
                        <img v-if="lineItem.specialTitle == '产品'" src="../../../assets/images/AI/productTip.png" alt="">
                        <img v-if="lineItem.specialTitle == '能力'" src="../../../assets/images/AI/moduleTip.png" alt="">
                        <img v-if="lineItem.specialTitle == '方案场景'" src="../../../assets/images/AI/sceneTip.png" alt="">
                    </div>
                </div>
            </div>
            <div class="flex" style="flex-wrap: wrap;" v-if="item.showMoreData">
                <div class="eachSolutionCard margin_b_16 margin_r_16 pointer" style="width: 288px;"
                    v-for="(lineItem, lineKey) in item.moreData" :key="lineKey"
                    @click.stop="jump(lineItem.id, item.name)">
                    <div class="eachCardContent flex">
                        <div class="imgContainer">
                            <img class="imgItem" v-if="lineItem[imgName]" :src="lineItem[imgName]" alt="" />
                            <img class="imgItem" v-else
                                src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                                alt="" />
                        </div>
                        <div style="flex: 1" class="margin_l_6">
                            <a-tooltip>
                                <template #title>
                                    <span>{{ lineItem.name }}</span>
                                </template>
                                <div class="name desc margin_l_6">{{ lineItem.name ? lineItem.name : '暂无名称'
                                    }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.description }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '方案'">
                                    {{ lineItem.description }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.summary }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '方案场景'">
                                    {{ lineItem.summary }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.abilityIntro }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '能力'">
                                    {{ lineItem.abilityIntro }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.introduction }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '产品'">
                                    {{ lineItem.introduction }}
                                </div>
                            </a-tooltip>
                            <a-tooltip>
                                <template #title>{{ lineItem.introduce }}</template>
                                <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                    v-if="lineItem.specialTitle === '场景'">
                                    {{ lineItem.introduce }}
                                </div>
                            </a-tooltip>
                            <a-button size="small" type="link" @click.stop="joinShoppingCar(lineItem.id, item.name)"
                                :disabled="lineItem.btnText === '已加入'">
                                <img class="add" src="@/assets/images/AI/isadded.png"
                                    v-if="lineItem.btnText === '加入预选'" />
                                {{ lineItem.btnText }}
                            </a-button>
                        </div>
                    </div>
                    <div class="identification">
                        <img v-if="lineItem.specialTitle == '产品'" src="../../../assets/images/AI/productTip.png" alt="">
                        <img v-if="lineItem.specialTitle == '能力'" src="../../../assets/images/AI/moduleTip.png" alt="">
                        <img v-if="lineItem.specialTitle == '方案场景'" src="../../../assets/images/AI/sceneTip.png" alt="">
                    </div>
                </div>
            </div>
            <div class="maxWidth margin_b_10 flex just-sb" v-if="item[arrayName].length > 6">
                <div></div>
                <div class="getMore pointer margin_r_74" @click="getMore(item)">
                    {{ item.openMoreName }}
                </div>
            </div>

        </div>
    </div>
</template>

<script>
import { dataType } from "element-plus/es/components/table-v2/src/common.mjs";
import { tr } from "element-plus/es/locale/index.mjs";
import { defineComponent, reactive, toRefs } from "vue";

export default defineComponent({
    emits: ['joinShoppingCar', 'jump'],
    props: {
        title: {
            type: String,
            required: '',
        },
        dataType: {
            type: String,
            required: "0",
        },
        arrayName: {
            type: String,
            required: '',
        },
        imgName: {
            type: String,
            required: '',
        },
        subTitle: {
            type: String,
            required: '',
        },
        items: {
            type: Array,
            required: [],
        },
        defaultImage: {
            type: String,
            default: 'https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png',
        },
    },
    setup(props, { emit }) {
        const data = reactive({
            items: props.items,
            subTitle: props.subTitle,
            title: props.title,
            defaultImage: props.defaultImage,
            arrayName: props.arrayName,
            dataType: props.dataType,
            // moreData: [],
            // initialData: [],
            // showMoreData: false,
            openMoreName: '展开更多→',
            showTitle: true,
        });
        const joinShoppingCar = (id, type) => {
            emit('joinShoppingCar', id, type);
        };
        const jump = (id, type) => {
            emit('jump', id, type);
        };
        // console.log('1222222212121', props.items)
        const getMore = (item) => {
            console.log('展开更多')
            if (!item.showMoreData) {
                item.showMoreData = true
                item.openMoreName = '收起←'
            } else {
                item.showMoreData = false
                item.openMoreName = '展开更多→'
            }

        }
        const handleDataList = () => {
            let dataLength = 0
            console.log('22222222', props.items)
            for (let i of props.items) {
                console.log('11111111', i[props.arrayName])
                dataLength += i[props.arrayName].length
                if (i[props.arrayName].length > 6) {
                    i.moreData = i[props.arrayName].slice(6)
                    i.initialData = i[props.arrayName].slice(0, 6)
                    i.showMoreData = false
                    i.openMoreName = '展开更多→'
                } else {
                    i.initialData = i[props.arrayName]
                }
            }
            if (dataLength == 0) {
                data.showTitle = false
            } else {
                data.showTitle = true
            }
        }
        handleDataList()
        return {
            ...toRefs(data),
            joinShoppingCar,
            jump,
            getMore,
            handleDataList,
        };
    },
});
</script>

<style lang="scss" scoped>
.eachLine {
    .eachLineTitle {
        height: 40px;
        background: linear-gradient(90deg, #F5F7FC 0%, rgba(245, 247, 252, 0) 100%);
        padding-left: 16px;
        font-weight: bold;
        border-radius: 10px 0 0 10px;
    }

    .eachSolution {
        .eachSolutionCard {
            position: relative;

            .eachCardContent {
                padding: 24px 24px 24px 24px;
                // box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.3);
                background-image: url(../../../assets/images/AI/groupBg.png);
                background-repeat: no-repeat;
                background-size: cover;
                border-radius: 10px;

                .imgContainer {
                    width: 83px;
                    height: 72px;
                    margin-right: 8px;

                    .imgItem {
                        width: 100%;
                        height: 100%;
                    }
                }

                .add {
                    width: 16px;
                    height: 16px;
                    margin-bottom: 3px;
                    margin-right: 6px;
                }

                .desc {
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 2;
                    line-clamp: 2;
                    word-break: break-all;
                }

                .name {
                    display: -webkit-box;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    -webkit-line-clamp: 1;
                    line-clamp: 2;
                    word-break: break-all;
                }
            }

            .identification {
                width: 56px;
                height: 30px;
                position: absolute;
                top: 6px;
                left: -8px;
                // background-image: url(../../../assets/images/AI/blueTip.png);

                img {
                    width: 100%;
                    height: 100%;
                }
            }
        }
    }
}

.ellipsis-text {
    width: 150px;
    font-size: 12px;
    /* 设置容器宽度 */
    white-space: nowrap;
    /* 防止文字换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
    /* 超出部分显示省略号 */
}

.getMore {
    font-weight: bold;
    color: #007bff;
    text-decoration: underline;
}
</style>