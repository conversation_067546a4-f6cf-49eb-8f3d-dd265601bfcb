<template>
    <div :class="['message-container', isUser ? 'user-message' : 'ai-message']">
        <!-- 用户消息，头像在右边 -->
        <div v-if="isUser" class="user-message">
            <p>
                <span>{{ text }}</span>
                <img class="avatar margin_l_24" src=" @/assets/images/AI/user.png" alt="用户头像" />
            </p>
        </div>
        <!-- AI消息，头像在左边 -->
        <div v-else class="ai-message flex">
            <p v-if="!isLoading">
                <!-- <img class="avatar" src="" alt="AI头像" /> -->
                <img style="width: 40px;height: 40px;margin-right: 24px;" src="../../../assets/images/AI/ai.png" alt="">
            <div style="width:100%">
                <div class="flex align-center margin_b_16 font_14" style="display: inline-block;">{{ tips }}<span
                        v-if="!isTyping">...</span></div>
                <div class="projectConten" v-if="isTyping">
                    <div class="list" style="margin-bottom: 24rpx;width:100%;">


                        <div class="con">
                            <!-- 查询模式  resultType="0"-->
                            <div class="newCard" v-if="dataType == '0'">
                                <!-- 方案 -->
                                <each-line v-if="newMessage.solutionList.length > 0" :title="'标准方案'"
                                    :dataType="dataType" :subTitle="'方案'" :items="newMessage.solutionList"
                                    arrayName="solution" imgName="logo" @joinShoppingCar="joinShoppingCar"
                                    @jump="jump" />
                                <!-- 应用场景 -->
                                <each-line v-if="newMessage.solutionModelList.length > 0" :title="'应用场景'"
                                    :dataType="dataType" :subTitle="'场景'" :items="newMessage.solutionModelList"
                                    arrayName="scene" imgName="image" @joinShoppingCar="joinShoppingCar" @jump="jump" />

                                <!-- 能力 -->
                                <each-line v-if="newMessage.moduleList.length > 0" :title="'能力'" :subTitle="'能力'"
                                    :dataType="dataType" :items="newMessage.moduleList" arrayName="ability"
                                    imgName="abilityPicture" @joinShoppingCar="joinShoppingCar" @jump="jump" />

                                <!-- 产品 -->
                                <each-line v-if="newMessage.productList.length > 0" :title="'产品'" :subTitle="'产品'"
                                    :dataType="dataType" :items="newMessage.productList" arrayName="product"
                                    imgName="image" @joinShoppingCar="joinShoppingCar" @jump="jump" />

                                <!-- 场景 -->
                                <each-line v-if="newMessage.productBagList.length > 0" :title="'场景'" :subTitle="'场景'"
                                    :dataType="dataType" :items="newMessage.productBagList" arrayName="productBag"
                                    imgName="mainImg" @joinShoppingCar="joinShoppingCar" @jump="jump" />
                            </div>




                            <!-- 定制模式（方案、能力、产品） resultType="4" -->
                            <div class="newCard" v-if="dataType == '4' && Object.keys(nowChoosedSolution).length !== 0">
                                <div class="eachLine">
                                    <div class="eachLineTitle flex align-center margin_b_8 font_18">标准方案</div>
                                    <div class="eachSolution margin_l_32">
                                        <div class="eachSolutionTitle margin_b_10">
                                            {{ nowChoosedSolution.key + nowChoosedSolution.name }}：
                                        </div>
                                        <div class="flex" style="flex-wrap: wrap;">
                                            <div class="eachSolutionCard margin_b_16 margin_r_16 pointer"
                                                v-for="(lineItem, lineKey) in nowChoosedSolution.solution"
                                                :key="lineKey" @click.stop="jump(lineItem.id, lineItem.specialTitle)">
                                                <div class="eachCardContent flex">
                                                    <div class="imgContainer">
                                                        <img class="imgItem" v-if="lineItem.logo" :src="lineItem.logo"
                                                            alt="" />
                                                        <img class="imgItem" v-else
                                                            src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                                                            alt="" />
                                                    </div>
                                                    <div style="flex: 1" class="margin_l_6">
                                                        <a-tooltip>
                                                            <template #title>
                                                                <span>{{ lineItem.name }}</span>
                                                            </template>
                                                            <div class="name desc margin_l_6">{{
                                                                lineItem.name ?
                                                                    lineItem.name : '暂无名称'
                                                            }}
                                                            </div>
                                                        </a-tooltip>
                                                        <a-tooltip>
                                                            <template #title>{{ lineItem.description }}</template>
                                                            <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                                                v-if="lineItem.specialTitle === '方案'">
                                                                {{ lineItem.description }}
                                                            </div>
                                                        </a-tooltip>
                                                        <div>
                                                            <a-button size="small" type="link"
                                                                @click.stop="joinShoppingCar(lineItem.id, lineItem.specialTitle)"
                                                                :disabled="lineItem.btnText === '已加入'">
                                                                <img class="add" src="@/assets/images/AI/isadded.png"
                                                                    v-if="lineItem.btnText === '加入预选'" />
                                                                {{ lineItem.btnText }}
                                                            </a-button>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                                <!-- 应用场景 -->
                                <div class="eachLine">
                                    <div class="eachLineTitle flex align-center margin_b_8 font_18">应用场景</div>
                                    <div class="eachSolution margin_l_32">
                                        <div class="flex" style="flex-wrap: wrap;">
                                            <div class="eachSolutionCard  margin_b_16 margin_r_16 pointer"
                                                v-for="(item, index) in newMessage.groupList" :key="index"
                                                @click.stop="jump(item.id, item.specialTitle)">
                                                <div class="eachCardContent flex">
                                                    <div class="imgContainer">
                                                        <img class="imgItem"
                                                            v-if="item.image || item.abilityPicture || item.image"
                                                            :src="item.image || item.abilityPicture || item.image"
                                                            alt="" />
                                                        <img class="imgItem" v-else
                                                            src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                                                            alt="" />
                                                    </div>
                                                    <div style="flex: 1" class="margin_l_6">
                                                        <a-tooltip>
                                                            <template #title>
                                                                <span>{{ item.name }}</span>
                                                            </template>
                                                            <div class="name desc margin_l_6">{{
                                                                item.name ?
                                                                    item.name : '暂无名称'
                                                            }}
                                                            </div>
                                                        </a-tooltip>
                                                        <a-tooltip>
                                                            <template #title>{{ item.summary }}</template>
                                                            <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                                                v-if="item.specialTitle === '方案场景'">
                                                                {{ item.summary }}
                                                            </div>
                                                        </a-tooltip>
                                                        <a-tooltip>
                                                            <template #title>{{ item.abilityIntro }}</template>
                                                            <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                                                v-if="item.specialTitle === '能力'">
                                                                {{ item.abilityIntro }}
                                                            </div>
                                                        </a-tooltip>
                                                        <a-tooltip>
                                                            <template #title>{{ item.introduction }}</template>
                                                            <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                                                v-if="item.specialTitle === '产品'">
                                                                {{ item.introduction }}
                                                            </div>
                                                        </a-tooltip>
                                                        <div>
                                                            <a-button size="small" type="link"
                                                                @click.stop="joinShoppingCar(item.id, item.specialTitle, item.name)"
                                                                :disabled="item.btnText === '已加入'">
                                                                <img class="add" src="@/assets/images/AI/isadded.png"
                                                                    v-if="item.btnText === '加入预选'" />
                                                                {{ item.btnText }}
                                                            </a-button>
                                                            <a-button v-if="canDelete(item)" size="small" type="link"
                                                                class="deleteBtn" @click.stop="deleteThisCard(index)">
                                                                <img class="add"
                                                                    src="@/assets/images/AI/deleteCard.png" /></a-button>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="identification">
                                                    <img v-if="item.specialTitle == '产品'"
                                                        src="../../../assets/images/AI/productTip.png" alt="">
                                                    <img v-if="item.specialTitle == '能力'"
                                                        src="../../../assets/images/AI/moduleTip.png" alt="">
                                                    <img v-if="item.specialTitle == '方案场景'"
                                                        src="../../../assets/images/AI/sceneTip.png" alt="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="maxWidth flex">
                                    <div class="customized pointer flex align-center" @click="addCustomized()">
                                        <img src="../../../assets/images/AI/Group <EMAIL>" alt="">
                                        <div>
                                            加入定制
                                        </div>
                                    </div>
                                </div>
                            </div>






                            <!-- 定制模式（场景） resultType="7" -->
                            <div class="newCard" v-if="dataType == '7'">
                                <div class="eachLine" v-for="(item, index) in newMessage.originList" :key="index">
                                    <div v-if="item.product.length > 0"
                                        class="eachLineTitle flex align-center margin_b_8 font_18">{{ item.key +
                                            item.name
                                        }}：
                                    </div>
                                    <div class="eachSolution">
                                        <div class="flex" style="flex-wrap: wrap;">
                                            <div class="eachSolutionCard margin_b_16 margin_r_16 pointer"
                                                style="width: 288px;" v-for="(lineItem, lineKey) in item.product"
                                                :key="lineKey" @click.stop="jump(lineItem.id, item.name)">
                                                <div class="eachCardContent flex">
                                                    <div class="imgContainer">
                                                        <img class="imgItem" v-if="lineItem.image" :src="lineItem.image"
                                                            alt="" />
                                                        <img class="imgItem" v-else
                                                            src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                                                            alt="" />
                                                    </div>
                                                    <div style="flex: 1" class="margin_l_6">
                                                        <a-tooltip>
                                                            <template #title>
                                                                <span>{{ lineItem.name }}</span>
                                                            </template>
                                                            <div class="name desc margin_l_6">{{
                                                                lineItem.name ? lineItem.name : '暂无名称'
                                                            }}
                                                            </div>
                                                        </a-tooltip>
                                                        <a-tooltip>
                                                            <template #title>{{ lineItem.introduction }}</template>
                                                            <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                                                v-if="lineItem.specialTitle === '产品'">
                                                                {{ lineItem.introduction }}
                                                            </div>
                                                        </a-tooltip>
                                                        <div>
                                                            <a-button size="small" type="link"
                                                                @click.stop="joinShoppingCar(lineItem.id)"
                                                                :disabled="lineItem.btnText === '已加入'">
                                                                <img class="add" src="@/assets/images/AI/isadded.png"
                                                                    v-if="lineItem.btnText === '加入预选'" />
                                                                {{ lineItem.btnText }}
                                                            </a-button>
                                                            <a-button v-if="canDelete(item)" size="small" type="link"
                                                                class="deleteBtn"
                                                                @click.stop="deleteThisCard(lineKey, '1', lineItem.bagName)">
                                                                <img class="add"
                                                                    src="@/assets/images/AI/deleteCard.png" /></a-button>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="identification">
                                                    <img v-if="lineItem.specialTitle == '产品'"
                                                        src="../../../assets/images/AI/productTip.png" alt="">
                                                    <img v-if="lineItem.specialTitle == '能力'"
                                                        src="../../../assets/images/AI/moduleTip.png" alt="">
                                                    <img v-if="lineItem.specialTitle == '方案场景'"
                                                        src="../../../assets/images/AI/sceneTip.png" alt="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="eachLine">
                                    <div v-if="newMessage.artificialList.length > 0"
                                        class="eachLineTitle flex align-center margin_b_8 font_18">
                                        外加产品：
                                    </div>
                                    <div class="eachSolution">
                                        <div class="flex" style="flex-wrap: wrap;">
                                            <div class="eachSolutionCard margin_b_16 margin_r_16 pointer"
                                                style="width: 288px;" v-for="(item, index) in newMessage.artificialList"
                                                :key="key" @click.stop="jump(item.id, '产品')">
                                                <div class="eachCardContent flex">
                                                    <div class="imgContainer">
                                                        <img class="imgItem" v-if="item.image" :src="item.image"
                                                            alt="" />
                                                        <img class="imgItem" v-else
                                                            src="https://smart.jsisi.cn:8099/portal/resource/2024/09/22/6e4d3993-d2dc-4e5a-bb9f-2ce05edffae2.png"
                                                            alt="" />
                                                    </div>
                                                    <div style="flex: 1" class="margin_l_6">
                                                        <a-tooltip>
                                                            <template #title>
                                                                <span>{{ item.name }}</span>
                                                            </template>
                                                            <div class="name desc margin_l_6">{{
                                                                item.name ? item.name : '暂无名称'
                                                            }}
                                                            </div>
                                                        </a-tooltip>
                                                        <a-tooltip>
                                                            <template #title>{{ item.introduction }}</template>
                                                            <div class="margin_t_6 margin_l_8 margin_b_6 ellipsis-text"
                                                                v-if="item.specialTitle === '产品'">
                                                                {{ item.introduction }}
                                                            </div>
                                                        </a-tooltip>
                                                        <div>
                                                            <a-button size="small" type="link"
                                                                @click.stop="joinShoppingCar(item.id, item.name)"
                                                                :disabled="item.btnText === '已加入'">
                                                                <img class="add" src="@/assets/images/AI/isadded.png"
                                                                    v-if="item.btnText === '加入预选'" />
                                                                {{ item.btnText }}
                                                            </a-button>
                                                            <a-button v-if="canDelete(item)" size="small" type="link"
                                                                class="deleteBtn"
                                                                @click.stop="deleteThisCard(index, '2')">
                                                                <img class="add"
                                                                    src="@/assets/images/AI/deleteCard.png" /></a-button>
                                                        </div>

                                                    </div>
                                                </div>
                                                <div class="identification">
                                                    <img v-if="item.specialTitle == '产品'"
                                                        src="../../../assets/images/AI/productTip.png" alt="">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="maxWidth flex"
                                    v-if="newMessage.originList.length + newMessage.artificialList.length !== 0">
                                    <div class="customized pointer flex align-center" @click="addProduct()">
                                        <img src="../../../assets/images/AI/Group <EMAIL>" alt="">
                                        <div>
                                            加入定制
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            </p>
            <!-- 显示完成的消息 -->
            <!-- <div v-else class="loading-spinner"></div> -->
            <!-- 显示加载动画 -->
        </div>
        <a-modal v-model:visible="visible" title="提示" @ok="handleOk">
            <p>已存在定制组合，请确认是否替换已有组合?</p>
        </a-modal>
    </div>
</template>

<script>
import { defineComponent, onMounted, reactive, toRefs, watch } from "vue";
import { useRouter } from "vue-router";
import eventBus from "@/utils/eventBus";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import { toCombinePage } from "@/api/combine/shoppingCart.js";
import { myCombineList } from "@/api/combine/combine.js";
import { addShop } from "@/api/buyList/index.js";
import { fa, tr } from "element-plus/es/locale/index.mjs";
import { dataType } from "element-plus/es/components/table-v2/src/common.mjs";
import { toShopList } from "@/api/buyList/index";
import eachLine from "./eachLine.vue";
import { init } from "echarts";
import { all } from "axios";
export default defineComponent({
    components: {
        eachLine,
    },
    emits: ['duplicated', 'productDuplicated', 'close'],
    props: {
        newMessage: {
            type: Object,
            required: {},
        },
        text: {
            type: String,
            required: true,
        },
        isUser: {
            type: Boolean,
            required: true,
        },
        isTyping: {
            type: Boolean,
            default: false,
        },
        isLoading: {
            type: Boolean,
            default: false,
        },
        dataType: {
            type: String,
            default: "",
        },
        tips: {
            type: String,
            default: "",
        },
        messageTips: {
            type: String,
            default: ''
        },
        productBagName: {
            type: String,
            default: ''
        },
        isShowTitle: {
		    	type: Boolean,
		      default: false,
		    }
    },
    setup(props, { emit }) {
        const router = useRouter();
        const data = {
            tips: props.tips,
            aiAvatar: null,
            userAvatar: null,
            customizeArr: [],
            addProductArr: [],
            productBagName: props.productBagName,
            solutionList: [],
            sceneList: [],
            moduleList: [],
            productList: [],
            newMessage: props.newMessage,
            nowChoosedSolution: {},
            nowProductBagList: [],
        };
        onMounted(() => {
        })
        const init = () => {
            let solutionArr = []
            if (data.newMessage && props.dataType == '4') {
                // data.nowChoosedSolution = data.newMessage.moduleCombine.solutionList.find(item => item.solution.length !== 0)
                for (let i of data.newMessage.moduleCombine.solutionList) {
                    if (i.solution.length !== 0) {
                        solutionArr.push(i)
                    }
                }
                if (solutionArr.length !== 0) {
                    data.nowChoosedSolution = solutionArr[0]
                } else {
                    data.nowChoosedSolution = {}
                }
                // data.nowChoosedSolution = data.newMessage.moduleCombine.solutionList[0]
                data.newMessage.groupList = data.newMessage.groupList.filter(item => item.specialTitle !== '产品')

                console.log('data.newMessage.groupList', data.newMessage.groupList)
            } else if (data.newMessage && props.dataType == '7') {
                console.log('data.newMessage.', data.newMessage)
            }
        }
        init()
        //点击按钮， 将方案加入购物车
        const joinShoppingCar = (id, type, name) => {
            console.log('id', id, 'type', type)
            // console.log('newMessage', data.newMessage)
            if (props.dataType == '0') {
                if (type == '方案') {
                    addShoppingCart({
                        schemeId: id,
                        type: 1,
                    }).then((res) => {
                        for (let i of data.newMessage.solutionList) {
                            for (let j of i.solution) {
                                if (j.id == id) {
                                    j.btnText = '已加入'
                                }
                            }
                        }
                        eventBus.emit("cartRefresh");
                    });
                } else if (type == '方案场景') {
                    addShoppingCart({
                        schemeId: id,
                        type: 3,
                    }).then((res) => {
                        for (let i of data.newMessage.solutionModelList) {
                            for (let j of i.scene) {
                                if (j.id == id) {
                                    j.btnText = '已加入'
                                }
                            }
                        }
                        eventBus.emit("cartRefresh");
                    });
                } else if (type == '能力') {
                    addShoppingCart({
                        schemeId: id,
                        type: 2,
                    }).then((res) => {
                        for (let i of data.newMessage.moduleList) {
                            for (let j of i.ability) {
                                if (j.id == id) {
                                    j.btnText = '已加入'
                                }
                            }
                        }
                        eventBus.emit("cartRefresh");
                    });
                } else if (type == '产品') {
                    addShop({
                        productId: id,
                        type: 2
                    }).then((res) => {
                        for (let i of data.newMessage.productList) {
                            for (let j of i.product) {
                                if (j.id == id) {
                                    j.btnText = '已加入'
                                }
                            }
                        }
                        eventBus.emit("cartRefresh");
                    })
                } else if (type == '场景') {
                    addShop({
                        productId: id,
                        type: 1
                    }).then((res) => {
                        for (let i of data.newMessage.productBagList) {
                            for (let j of i.productBag) {
                                if (j.id == id) {
                                    j.btnText = '已加入'
                                }
                            }
                        }
                        eventBus.emit("cartRefresh");
                    })
                }
            } else if (props.dataType == '4') {
                if (type == '方案') {
                    addShoppingCart({
                        schemeId: id,
                        type: 1,
                    }).then((res) => {
                        data.nowChoosedSolution.solution[0].btnText = '已加入'
                        eventBus.emit("cartRefresh");
                    });
                } else if (type == '方案场景') {
                    addShoppingCart({
                        schemeId: id,
                        type: 3,
                    }).then((res) => {
                        for (let i of data.newMessage.groupList) {
                            if (i.id == id && i.name == name) {
                                i.btnText = '已加入'
                            }
                        }
                        eventBus.emit("cartRefresh");
                    });

                } else if (type == '能力') {
                    addShoppingCart({
                        schemeId: id,
                        type: 2,
                    }).then((res) => {
                        for (let i of data.newMessage.groupList) {
                            if (i.id == id && i.name == name) {
                                i.btnText = '已加入'
                            }
                        }
                        eventBus.emit("cartRefresh");
                    });
                } else if (type == '产品') {
                    addShop({
                        productId: id,
                        type: 2
                    }).then((res) => {
                        for (let i of data.newMessage.groupList) {
                            if (i.id == id && i.name == name) {
                                i.btnText = '已加入'
                            }
                        }
                        eventBus.emit("cartRefresh");
                    })
                }
            } else if (props.dataType == '7') {
                addShop({
                    productId: id,
                    type: 2
                }).then((res) => {
                    for (let i of data.newMessage.originList) {
                        for (let j of i.product) {
                            if (j.id == id) {
                                j.btnText = '已加入'
                            }
                        }
                    }
                    for (let i of data.newMessage.artificialList) {
                        if (i.id == id) {
                            i.btnText = '已加入'
                        }
                    }
                    eventBus.emit("cartRefresh");
                })
            }
        };

        const jump = (id, type) => {
            console.log('id', id)
            console.log('type', type)
            if (type === "方案") {
                //跳转到方案详情
                router.push({ name: "solveDetailNew", query: { id: id } });
            } else if (type === "能力") {
                //跳转到模块详情
                router.push({ name: "modulelNew", query: { id: id } });
            } else if (type === "场景") {
                //跳转到场景详情
                router.push({ name: "scenarioDetail", query: { id: id } });
            } else if (type === "产品") {
                //跳转到产品详情
                router.push({ name: "productDetail", query: { id: id } });
            } else if (type === "方案场景") {
                //跳转到产品详情
                router.push({ name: "applyNew", query: { id: id } });
            }
        };
        // 删除组合里的当前卡片
        const deleteThisCard = (key, type, bagName) => {
            if (props.dataType == '4') {
                data.newMessage.groupList.splice(key, 1)
            } else if (props.dataType == '7') {
                if (type == '1') {
                    for (let i of data.newMessage.originList) {
                        if (i.key == bagName) {
                            i.product.splice(key, 1)
                        }
                    }
                } else if (type == '2') {
                    data.newMessage.artificialList.splice(key, 1)
                }
            }
        }
        // 方案能力加入定制 dataType==4
        const addCustomized = () => {
            data.customizeArr = []
            // 方案加入定制type=1
            data.customizeArr.push({
                schemeId: data.nowChoosedSolution.id,
                type: 1
            })
            // 把方案里的政策背景、需求分析...掏出来加入定制，type=3
            for (let i of data.nowChoosedSolution.solution[0].moduleBody) {
                if (i.type !== 5) {
                    for (let y of i.moduleList) {
                        data.customizeArr.push({
                            schemeId: y.id,
                            type: 3
                        })
                    }
                }
            }
            // 把groupList里的场景、能力、产品分别加type加入定制
            for (let i of data.newMessage.groupList) {
                if (i.specialTitle == '方案场景') {
                    data.customizeArr.push({
                        schemeId: i.id,
                        type: 3
                    })
                }
                if (i.specialTitle == '能力') {
                    data.customizeArr.push({
                        schemeId: i.id,
                        type: 2
                    })
                }
                if (i.specialTitle == '产品') {
                    data.customizeArr.push({
                        schemeId: i.id,
                        type: 4
                    })
                }
            }
            console.log('data.customizeArr', data.customizeArr)
            if (data.customizeArr.length != 0) {
                myCombineList()
                    .then((res) => {
                        // debugger
                        // 提示
                        if (
                            res.data.list.some((item) => item.list && item.list.length > 0)
                        ) {
                            console.log("fndifsn")
                            emit("duplicated", data.customizeArr)
                        } else {
                            toCombinePage({ list: data.customizeArr, source: '3' }).then((res) => {
                                data.visible = false;
                                if(!props.isShowTitle){
								                	router.push({
									                  // name: "customizedList",
									                  path: "/customized/customizedList?",
									                  query: { active: "定制" },
									                });
									                eventBus.emit("urlRefresh");
								                } else {
								                	emit("close");
								                }
                            })
                        }
                    })
                    .catch((error) => { });
            }

        }
        // 场景加入定制dataType==7
        const addProduct = () => {
            data.addProductArr = []
            for (let i of data.newMessage.originList) {
                for (let j of i.product) {
                    data.addProductArr.push({
                        productId: j.id,
                        type: 2
                    })
                }
            }
            for (let i of data.newMessage.artificialList) {
                data.addProductArr.push({
                    productId: i.id,
                    type: 2
                })
            }
            console.log('data.addProductArr', data.addProductArr)
            emit("productDuplicated", data.addProductArr)
        }

        const canDelete = () => {
            if (props.dataType == '4') {
                // 11.28 9:00 保证应用场景里最少有一个
                if (data.newMessage.groupList.length > 1) {
                    return true
                } else {
                    return false
                }
            } else if (props.dataType == '7') {
                let allLength = 0
                for (let i of data.newMessage.originList) {
                    allLength += i.product.length
                }
                allLength += data.newMessage.artificialList.length
                if (allLength > 1) {
                    return true
                } else {
                    return false
                }
            }
        }
        watch(
            () => props.tips,
            (newV) => {
                data.tips = newV
                // console.log('data.tips', data.tips);
            }
        )
        return {
            ...toRefs(data),
            jump,
            joinShoppingCar,
            router,
            deleteThisCard,
            addCustomized,
            canDelete,
            addProduct,
        };
    },
});
</script>

<style lang="scss" scoped>
.message-container {
    display: flex;
    align-items: center;
    margin: 10px 0;
}

/* 用户消息的样式，头像在右边 */
.user-message {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    width: 100%;

    p {
        margin-right: 10px;
        background-color: #EBF0FF;
        box-shadow: 0px 8px 24px 0px rgba(116, 157, 219, 0.24);
        display: flex;
        align-items: center;

        padding: 24px span {
            align-items: center;
            height: 100%;
        }
    }
}


.user-message p {
    color: rgba(0, 0, 0, 0.85);
    padding: 20px 20px 20px 40px;
    border-radius: 10px;
    max-width: 75%;
    margin-left: 10px;
}

.right {
    position: relative;

    span {
        position: absolute;
        bottom: 0;
    }
}

/* AI消息的样式，头像在左边 */
.ai-message {
    display: flex;
    align-items: center;
    justify-content: flex-start;
    width: 100%;

    p {
        display: flex;
        // align-items: center;
        padding: 24px;
    }

    .avatar {
        margin-right: 8px;
    }
}

.ai-message p {
    background-color: #fff;
    color: black;
    padding: 24px;
    border-radius: 10px;
    max-width: 75%;
    margin-right: 10px;
    border-radius: 0px 30px 30px 30px;
    box-shadow: 0px 8px 24px 0px rgba(116, 157, 219, 0.24);
}

/* 头像样式 */
.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
}

.avatar {
    font-size: 20px;
    color: #007bff;
    text-align: center;
}

/* 打字状态的样式 */
.typing {
    font-style: italic;
    color: #aaa;
}

/* 打字状态的样式 */
.typing {
    font-style: italic;
    color: #aaa;
}

/* 加载动画 */
.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
    margin-top: -20px;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.projectConten {
    display: flex;
    /*max-width: 930px;*/

    .card {
        display: flex;
        position: relative;
        padding: 24px;
        // background-color: #fff;
        background-image: url(../../../assets/images/AI/groupBg.png);
        background-repeat: no-repeat;
        background-size: cover;
        margin-right: 10px;
        width: 288px;
        max-height: 120px;
        // overflow: hidden;
        margin-right: 16px;
        margin-bottom: 16px;

        img {
            width: 88px;
            height: 72px;
            margin-right: 12px;
        }

        .desc {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 2;
            line-clamp: 2;
            word-break: break-all;
        }

        .name {
            display: -webkit-box;
            -webkit-box-orient: vertical;
            overflow: hidden;
            text-overflow: ellipsis;
            -webkit-line-clamp: 1;
            line-clamp: 2;
            word-break: break-all;
        }

        .add {
            width: 16px;
            height: 16px;
            margin-bottom: 3px;
            margin-right: 6px;
        }

        .joinBtn {
            position: absolute;
            padding: 0;
            bottom: 0;
            // left: 114px;
        }

        .identification {
            width: 56px;
            height: 30px;
            position: absolute;
            top: 6px;
            left: -8px;
            // background-image: url(../../../assets/images/AI/blueTip.png);

            img {
                width: 100%;
                height: 100%;
            }
        }
    }

    // .newCard {
    //     // display: flex;
    //     // position: relative;
    //     // padding: 24px;
    //     // background-color: #fff;
    //     margin-right: 10px;
    //     margin-top: 24px;
    //     max-width: 930px;
    //     min-height: 140px;
    //     // max-height: 120px;

    //     .customized {
    //         clear: both;
    //         font-weight: bold;
    //         color: #007bff;

    //         img {
    //             width: 14px;
    //             height: 14px;
    //         }
    //     }


    //     .eachNewCard {
    //         width: 288px;
    //         height: 110px;
    //         margin-right: 16px;
    //         margin-bottom: 30px;
    //         position: relative;

    // .eachContent {
    //     padding: 24px 24px 24px 24px;
    //     // box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.3);
    //     background-image: url(../../../assets/images/AI/groupBg.png);
    //     background-repeat: no-repeat;
    //     background-size: cover;
    //     border-radius: 10px;

    //     .imgContainer {
    //         width: 83px;
    //         height: 72px;
    //         margin-right: 8px;

    //         .imgItem {
    //             width: 100%;
    //             height: 100%;
    //         }
    //     }
    // }

    //         .identification {
    //             width: 56px;
    //             height: 30px;
    //             position: absolute;
    //             top: 6px;
    //             left: -8px;
    //             // background-image: url(../../../assets/images/AI/blueTip.png);

    //             img {
    //                 width: 100%;
    //                 height: 100%;
    //             }
    //         }
    //     }

    //     img {
    //         width: 88px;
    //         height: 72px;
    //         margin-right: 12px;
    //     }

    //     .desc {
    //         display: -webkit-box;
    //         -webkit-box-orient: vertical;
    //         overflow: hidden;
    //         text-overflow: ellipsis;
    //         -webkit-line-clamp: 2;
    //         line-clamp: 2;
    //         word-break: break-all;
    //     }

    //     .name {
    //         display: -webkit-box;
    //         -webkit-box-orient: vertical;
    //         overflow: hidden;
    //         text-overflow: ellipsis;
    //         -webkit-line-clamp: 1;
    //         line-clamp: 2;
    //         word-break: break-all;
    //     }

    //     .add {
    //         width: 16px;
    //         height: 16px;
    //         margin-bottom: 3px;
    //         margin-right: 6px;
    //     }

    //     .joinBtn {
    //         // position: absolute;
    //         // padding: 0;
    //         // bottom: 0;
    //         // left: 114px;
    //     }
    // }
}

.joined {
    opacity: 0.3 !important;
}

.bg_FFF {
    background-color: #fff;
}

.customized {
    clear: both;
    font-weight: bold;
    color: #007bff;

    img {
        width: 14px;
        height: 14px;
    }
}

.ellipsis-text {
    width: 150px;
    font-size: 12px;
    /* 设置容器宽度 */
    white-space: nowrap;
    /* 防止文字换行 */
    overflow: hidden;
    /* 超出部分隐藏 */
    text-overflow: ellipsis;
    /* 超出部分显示省略号 */
}





.newCard {
    margin-right: 10px;

    .eachLine {
        .eachLineTitle {
            height: 40px;
            background: linear-gradient(90deg, #F5F7FC 0%, rgba(245, 247, 252, 0) 100%);
            padding-left: 16px;
            font-weight: bold;
            border-radius: 10px 0 0 10px;
        }

        .eachSolution {
            .eachSolutionCard {
                width: 288px;
                position: relative;

                .eachCardContent {
                    padding: 24px 24px 24px 24px;
                    // box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.3);
                    background-image: url(../../../assets/images/AI/groupBg.png);
                    background-repeat: no-repeat;
                    background-size: cover;
                    border-radius: 10px;

                    .imgContainer {
                        width: 83px;
                        height: 72px;
                        margin-right: 8px;

                        .imgItem {
                            width: 100%;
                            height: 100%;
                        }
                    }

                    .add {
                        width: 16px;
                        height: 16px;
                        margin-bottom: 3px;
                        margin-right: 6px;
                    }

                    .desc {
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 2;
                        line-clamp: 2;
                        word-break: break-all;
                    }

                    .name {
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        -webkit-line-clamp: 1;
                        line-clamp: 2;
                        word-break: break-all;
                    }
                }

                .identification {
                    width: 56px;
                    height: 30px;
                    position: absolute;
                    top: 6px;
                    left: -8px;
                    // background-image: url(../../../assets/images/AI/blueTip.png);

                    img {
                        width: 100%;
                        height: 100%;
                    }
                }
            }
        }
    }
}
</style>