<template>
  <div class="content">
    <div class="banner"></div>
    <div class="nav">
      <span class="last" @click="back(1)" v-if="isNotice">通知公告</span>
      <span class="last" @click="back(2)" v-else>动态中心</span>
      <span class="line">/</span>
      <span class="current">{{ detailData.title }}</span>
    </div>
    <div class="detail">
      <div class="top">
        <div class="title">{{ detailData.title }}</div>
        <div class="info">
          <span class="left">
            <img src="../../assets/images/IfLogin/date.png" alt="" />
            <span class="date">{{ detailData.time }}</span>
          </span>
          <span class="center">
            <img src="../../assets/images/IfLogin/user.png" alt="" />
            <span class="creater" v-if="detailData.creater == null">--</span>
            <span class="creater" v-else>{{ detailData.creater }}</span>
          </span>
          <span class="right">
            <img src="../../assets/images/IfLogin/eye.png" alt="" />
            <span class="date">{{ detailData.readCount }}</span>
          </span>
        </div>
        <div class="line"></div>
      </div>
      <div class="article">
        <div class="first">{{ detailData.summary }}</div>
        <div class="second" v-html="detailData.content"></div>
      </div>
      <div class="round" @click="toTop">
        <img src="../../assets/images/IfLogin/toTop.png" alt="" />
      </div>
    </div>
  </div>
</template>
<script >
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import { RouterView, useRouter, useRoute } from "vue-router";
import { detail } from "../../api/IfLogin/unLogin";
import { noticeDetail } from "../../api/IfLogin/login";
import { getMakeUrl } from "@/utils/getUrl.js";
export default defineComponent({
  components: {},
  setup() {
    const data = reactive({
      detailData: [],
      isNotice: false,
    });
    const baseURL = getMakeUrl();
    const route = useRoute();
    const toTop = () => {
      document.getElementById("layout_content").scrollTo({
        top: 0,
        behavior: "smooth",
      });
    };
    const getDetail = () => {
      if (route.query.type == 2) {
        detail(route.query.id).then((res) => {
          data.detailData = [];
          let Data = [];
          data.isNotice = false;
          Data.push({
            title: res.data.title,
            time: res.data.updateTime.slice(0, 10),
            creater: res.data.createName,
            readCount: res.data.readingVolume,
            content: res.data.content,
            cover: res.data.articleCover,
            summary: res.data.articleSummary,
          });
          data.detailData = Data[0];
        });
      } else {
        noticeDetail(route.query.id).then((res) => {
          data.detailData = [];
          data.isNotice = true;
          let Data = [];
          Data.push({
            title: res.data.title,
            time: res.data.updateTime.slice(0, 10),
            creater: res.data.realName,
            readCount: res.data.readCount,
            content: res.data.content,
            cover: res.data.cover,
            summary: res.data.summary,
          });
          data.detailData = Data[0];
        });
      }
    };
    getDetail();
    const Router = useRouter();
    const back = (e) => {
      Router.push({
        query: {
          type: e,
        },
        name: "isLogin",
      });
      if (route.query.ifLogin || route.query.ifLogin == "9") {
        Router.push({
          query: {
            type: e,
          },
          name: "unLogin",
        });
      }
    };

    return {
      ...toRefs(data),
      toTop,
      route,
      getDetail,
      back,
      baseURL,
      Router,
    };
  },
});
</script>
<style lang="scss" scoped>
.content {
  width: 100%;
  height: 100%;
  background-color: #f7f8fe;
  .banner {
    width: 100%;
    height: 245px;
    background-image: url("../../assets/images/IfLogin/unLogin.png");
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }
  .nav {
    margin: 22px 0 26px 141px;
    font-weight: 400;
    font-size: 12px;
    color: #84899a;
    line-height: 20px;
    .line {
      margin: 0 8px;
    }
    .current {
      font-weight: 400;
      font-size: 12px;
      color: #2e3852;
      line-height: 20px;
    }
    .last {
      cursor: pointer;
    }
  }
  .detail {
    margin: 0 120px;
    padding: 60px 100px;
    background-color: #ffffff;
    position: relative;
    margin-bottom: 102px;
    .top {
      text-align: center;
      .title {
        font-weight: 500;
        font-size: 28px;
        color: #24456a;
        line-height: 40px;
        margin-bottom: 30px;
      }
      .info {
        font-weight: 400;
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        line-height: 14px;
        img {
          width: 16px;
          height: 16px;
          margin-bottom: 3px;
          margin-right: 2px;
        }
        span {
          margin-right: 56px;
        }
        span:last-child {
          margin-right: 0;
        }
      }
      .line {
        width: 100%;
        background-color: #dddddd;
        height: 1px;
        margin-top: 25px;
        margin-bottom: 30px;
      }
    }
    .article {
      padding: 0 90px;
      font-weight: 400;
      font-size: 16px;
      color: #325a88;
      line-height: 32px;
      text-indent: 2em;
      .first {
        padding: 35px;
        background-color: #f1f6ff;
        margin-bottom: 36px;
        text-indent: 0;
      }
      .image {
        text-align: center;
        margin-top: 34px;
        margin-bottom: 53px;
      }
      img {
        width: 72%;
        height: 350px;
      }
    }
    .round {
      cursor: pointer;
      position: fixed;
      width: 50px;
      height: 50px;
      border-radius: 50%;
      border: 1px solid #cccccc;
      right: 247px;
      bottom: 140px;
      text-align: center;
      img {
        width: 20px;
        height: 20px;
        margin-top: 13px;
      }
    }
  }
}
</style>