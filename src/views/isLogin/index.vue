<template>
  <div class="content">
    <div class="banner">
      <img src="../../assets/images/IfLogin/Login.png" alt="" />
    </div>
    <div class="tabs">
      <a-tabs
        v-model:activeKey="activeKey"
        :tabBarStyle="{ borderBottom: 'unset' }"
        @tab-click="tabsChange"
      >
        <a-tab-pane key="1" tab="通知公告">
          <div class="list" v-if="noticeData.length != 0">
            <ul class="cardList">
              <li
                class="card"
                v-for="(value, key) in noticeData"
                :key="key"
                @click="getDetail(value.noticeId)"
              >
                <div class="ulList_con flex">
                  <div class="left">
                    <span class="mounth">{{
                      value.updateTime.slice(5, 10)
                    }}</span>
                    <span class="year">{{ value.updateTime.slice(0, 4) }}</span>
                    <div class="line"></div>
                  </div>
                  <div class="center">
                    <div class="tit">{{ value.title }}</div>
                    <div class="con">
                      {{ value.summary }}
                    </div>
                    <div class="con_bot">
                      <img
                        src="../../assets/images/IfLogin/listEye.png"
                        alt=""
                      />
                      <span class="num">{{ value.readCount }}</span>
                    </div>
                  </div>
                  <div class="image">
                    <img :src="`${value.cover}`" alt="" />
                  </div>
                </div>
              </li>
            </ul>
            <div class="layPage">
              <pagination
                :totalItemCount="totalItemCount"
                :currentPage="pageNo"
                :pageItemSize="pageSize"
                @size-change="sizeChange"
                @page-change="pageChange"
              />
            </div>
          </div>
          <div
            class="flex just-center"
            style="margin-top: 26px"
            v-if="noticeData.length == 0"
          >
            <img
              src="@/assets/images/make/noData.png"
              alt=""
              width="240"
              height="244"
            />
          </div>
        </a-tab-pane>
        <a-tab-pane key="2" tab="动态中心" force-render>
          <!-- <ul
            v-if="dataSource.length != 0"
            class="ulList"
            style="margin-top: 30px"
          >
            <li
              v-for="(item, key) in dataSource"
              :class="['card', { isClick: isActive == item.id }]"
              :key="key"
              @click="getActiveDetail(item.id)"
            >
              <div class="ulList_con flex">
                <div class="left">
                  <span class="tit_img" v-if="key == 0">
                    <img src="../../assets/images/home/<USER>" alt="" />
                  </span>
                  <span class="tit_img" v-if="key == 1">
                    <img src="../../assets/images/home/<USER>" alt="" />
                  </span>
                  <span class="tit_img" v-if="key == 2">
                    <img src="../../assets/images/home/<USER>" alt="" />
                  </span>
                  <span
                    class="tit"
                    v-if="key != 0 && key != 1 && key != 2 && key < 9"
                    >0{{ key + 1 }}</span
                  >
                  <span v-if="key >= 9">{{ key + 1 }}</span>
                </div>
                <div class="con">
                  <div class="top">
                    <div class="tit">{{ item.title }}</div>
                    <div class="date">{{ item.createTime.slice(0, 10) }}</div>
                  </div>
                  <div class="con_content">{{ item.articleSummary }}</div>
                  <div class="con_bot">
                    <img src="../../assets/images/IfLogin/listEye.png" alt="" />
                    <span class="num">{{ item.readingVolume }}</span>
                  </div>
                </div>
                <div class="image">
                  <img :src="`${baseURL}${item.articleCover}`" alt="" />
                </div>
              </div>
            </li>
          </ul>
          <div v-if="dataSource.length != 0" class="layPage">
            <pagination
              :totalItemCount="totalItemCount"
              :currentPage="pageNo"
              :pageItemSize="pageSize"
              @size-change="sizeChange"
              @page-change="pageChange"
            />
          </div>
          <div
            class="flex just-center"
            style="margin-top: 26px"
            v-if="dataSource.length == 0"
          >
            <img
              src="@/assets/images/make/noData.png"
              alt=""
              width="240"
              height="244"
            /></div
        > -->
          <div class="list" v-if="dataSource.length != 0">
            <ul class="cardList">
              <li
                class="card"
                v-for="(value, key) in dataSource"
                :key="key"
                @click="getActiveDetail(value.id)"
              >
                <div class="ulList_con flex">
                  <div class="left">
                    <span class="mounth">{{
                      value.createTime.slice(5, 10)
                    }}</span>
                    <span class="year">{{ value.createTime.slice(0, 4) }}</span>
                    <div class="line"></div>
                  </div>
                  <div class="center">
                    <div class="tit">{{ value.title }}</div>
                    <div class="con">
                      {{ value.articleSummary }}
                    </div>
                    <div class="con_bot">
                      <img
                        src="../../assets/images/IfLogin/listEye.png"
                        alt=""
                      />
                      <span class="num">{{ value.readingVolume }}</span>
                    </div>
                  </div>
                  <div class="image">
                    <img :src="`${value.articleCover}`" alt="" />
                  </div>
                </div>
              </li>
            </ul>
            <div class="layPage">
              <pagination
                :totalItemCount="totalItemCount"
                :currentPage="pageNo"
                :pageItemSize="pageSize"
                @size-change="sizeChange"
                @page-change="pageChange"
              />
            </div>
          </div>
          <div
            class="flex just-center"
            style="margin-top: 26px"
            v-if="noticeData.length == 0"
          >
            <img
              src="@/assets/images/make/noData.png"
              alt=""
              width="240"
              height="244"
            />
          </div>
        </a-tab-pane>
      </a-tabs>
      <div class="line"></div>
    </div>
  </div>
</template>
<script >
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import pagination from "../../components/pagination/index.vue";
import { getList } from "../../api/IfLogin/unLogin";
import { getNoticeList } from "../../api/IfLogin/login";
import { getMakeUrl } from "@/utils/getUrl.js";
import { useRouter, useRoute } from "vue-router";
export default defineComponent({
  components: {
    pagination,
  },
  setup() {
    const data = reactive({
      totalItemCount: 0,
      pageNo: 1,
      pageSize: 10,
      dataSource: [],
      noticeData: [],
      pageSizeOptions: ["10", "20", "30", "50"],
      isActive: "",
      activeKey: "1",
      activekey: "1",
    });
    const router = useRouter();
    const baseURL = getMakeUrl();
    const getDataList = () => {
      let noticeParams = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
      };
      getNoticeList(noticeParams)
        .then((res) => {
          data.noticeData = res.data.rows;
          data.totalItemCount = res.data.totalRows;
        })
        .catch((err) => {
          console.log(err);
        });
    };
    const getActiveList = () => {
      let params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        status: 2,
      };
      getList(params)
        .then((res) => {
          data.dataSource = res.data.rows;
          data.totalItemCount = res.data.totalRows;
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };
    const tabsChange = (key) => {
      data.pageNo = 1;
      data.pageSize = 10;
      data.activekey = key;
      if (key == 1) {
        getDataList();
      }
      if (key == 2) {
        getActiveList();
      }
    };
    const getDetail = (e) => {
      data.isActive = e;
      router.push({
        query: {
          id: e,
          type: 1,
        },
        name: "detail",
      });
    };
    const route = useRoute();
    const tabChange = () => {
      if (route.query.active) {
        data.activeKey = route.query.active;
        if (route.query.active == 2) {
          getActiveList();
        }
      }
      if (route.query.type) {
        data.activeKey = route.query.type;
        if (route.query.type == 2) {
          getActiveList();
        } else if (route.query.type == 1) {
          getDataList();
        }
      }
    };
    tabChange();
    const getActiveDetail = (e) => {
      data.isActive = e;
      router.push({
        query: {
          id: e,
          type: 2,
        },
        name: "detail",
      });
    };
    onMounted(() => {});
    getDataList();
    const pageChange = (page) => {
      data.pageNo = page;
      if (data.activekey == 1) {
        getDataList();
      } else {
        getActiveList();
      }
    };
    const sizeChange = (size) => {
      data.pageSize = size;
      if (data.activekey == 1) {
        getDataList();
      } else {
        getActiveList();
      }
    };
    return {
      ...toRefs(data),
      pageChange,
      sizeChange,
      getDataList,
      getDetail,
      getActiveDetail,
      baseURL,
      router,
      tabsChange,
      getActiveList,
      tabChange,
      route,
    };
  },
});
</script>
<style lang="scss" scoped>
ul {
  list-style: none;
}
.content {
  width: 100%;
  height: 100%;
  position: relative;
  background-color: #f7f8fe;
  .banner {
    img {
      width: 100%;
    }
    height: 245px;
  }
  .list {
    margin: 30px 0px 70px 0px;
    background-color: #ffffff;
    li {
      padding: 42px 70px 0 70px;
    }
    .cardList {
      padding-left: 0;
      .ulList_con {
        width: 100%;
        border-bottom: 1px solid #dddddd;
        padding-bottom: 34px;
      }
      //   padding: 0px 50px;
      .card {
        display: flex;
        padding-top: 33px;
        padding-left: 70px;
        .left {
          position: relative;
          width: 142px;
          span {
            display: block;
          }
          .mounth {
            font-weight: 600;
            font-size: 24px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 40px;
            position: absolute;
            top: -10px;
          }
          .line {
            width: 24px;
            height: 0px;
            border: 1px solid #cccccc;
            position: absolute;
            top: 9px;
            right: 25px;
          }
          .year {
            width: 40px;
            height: 18px;
            border-radius: 0px 0px 0px 0px;
            border: 1px solid #cccccc;
            font-weight: 400;
            font-size: 14px;
            color: #cccccc;
            line-height: 18px;
            text-align: center;
            margin-left: 15px;
            margin-top: 10px;
            position: absolute;
            top: 26px;
          }
        }
        .center {
          width: 75%;
          margin-right: 70px;
          .tit {
            font-weight: 500;
            font-size: 18px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 23px;
            margin-bottom: 20px;
          }
          .con {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 26px;
            margin-bottom: 16px;
            // display: -webkit-box;
            // -webkit-line-clamp: 3;
            // -webkit-box-orient: vertical;
            // overflow: hidden;
            // text-overflow: ellipsis;
          }
          .con_bot {
            width: 56px;
            height: 20px;
            background: rgba(30, 99, 255, 0.06);
            text-align: center;
            img {
              width: 16px;
              height: 16px;
              margin-bottom: 4px;
              margin-right: 3px;
            }
            .num {
              font-weight: 400;
              font-size: 12px;
              color: rgba(0, 0, 0, 0.65);
              line-height: 14px;
            }
          }
        }
        .image {
          img {
            width: 200px;
          }
        }
      }
      .isClick {
        margin: 0;
        background-color: #f7f8fe;
        .ulList_con {
          width: 100%;
          border-bottom: none;
        }
        .mounth {
          font-weight: 600;
          font-size: 24px;
          color: #1e63ff !important;
        }
      }
    }
  }
  .layPage {
    width: 100%;
    text-align: right;
    // margin: 15px 0;
    background-color: #ffffff;
    padding-top: 3vh;
    padding-bottom: 2.5vh;
    padding-right: 24px;
    height: 9vh;
  }
}
.ulList {
  list-style: none;
  font-weight: bold;
  font-size: 14px;
  color: #325a88;
  line-height: 16px;
  padding-left: 0;
  background-color: #ffffff;
  margin-bottom: 0;
  .ulList_con {
    width: 100%;
    border-bottom: 1px solid #dddddd;
    padding-bottom: 34px;
  }
  li {
    padding: 42px 70px 0 70px;
  }
  .isClick {
    margin: 0;
    background-color: #f7f8fe;
    .ulList_con {
      width: 100%;
      border-bottom: none;
    }
  }
  .card {
    display: flex;
    font-weight: 400;
    font-size: 14px;
    color: rgba(0, 0, 0, 0.65);

    .left {
      width: 5%;
      text-align: right;
      margin-right: 30px;
      display: inline-block;
      .tit {
        font-weight: 400;
        font-size: 16px;
        color: #325a88;
      }
      .tit_img {
        display: inline-block;
        img {
          width: 24px;
          height: 26px;
        }
      }
    }
    .con {
      width: 75%;
      margin-right: 70px;
      .top {
        display: flex;
        justify-content: space-between;
        margin-bottom: 24px;
        .tit {
          font-weight: 500;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.85);
        }
      }
      .con_content {
        margin-bottom: 17px;
      }
      .con_bot {
        width: 56px;
        height: 20px;
        background: rgba(30, 99, 255, 0.06);
        text-align: center;
        img {
          width: 16px;
          height: 16px;
          margin-bottom: 2.5px;
          margin-right: 3px;
        }
        .num {
          font-weight: 400;
          font-size: 12px;
          color: rgba(0, 0, 0, 0.65);
          line-height: 14px;
        }
      }
    }
    .image {
      img {
        width: 200px;
      }
    }
  }
}
.tabs {
  margin: 40px 120px;
  position: relative;

  ::v-deep(.ant-tabs-tab) {
    font-weight: 400;
    font-size: 16px;
    color: #24456a;
    line-height: 12px;
    height: 55px !important;
  }
  ::v-deep(.ant-tabs-ink-bar) {
    position: absolute;
    bottom: 11px;
    height: 3px;
    background-color: #236cff;
  }
  ::v-deep(.ant-tabs-nav) {
    position: relative;
    margin-left: 36px;
  }
  ::v-deep(.ant-tabs-tab-active) {
    font-weight: 500;
    font-size: 16px;
    color: #24456a;
    line-height: 12px;
  }

  .ant-tabs-tab-active {
    position: relative;
  }
  ::v-deep(.ant-tabs-tab-active::after) {
    content: "";
    position: absolute;
    bottom: 5px;
    /* 调整三角形位置 */
    left: 50%;
    transform: translateX(-50%);
    width: 0;
    height: 0;
    z-index: 100;
    border-left: 7px solid transparent;
    border-right: 7px solid transparent;
    border-top: 7px solid #236cff;
  }
}
.line {
  height: 2px;
  background-color: #d1dfef;
  width: 100%;
  position: absolute;
  top: 42px;
}
</style>