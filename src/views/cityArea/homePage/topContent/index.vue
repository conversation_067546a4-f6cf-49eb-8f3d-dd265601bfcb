<template>
  <div class="topImage">
    <div class="topImage-cityArea">
      <div class="topImage-cityArea-content">
        <img src="@/assets/images/product/cityAreaTips.png" />
        <div class="topImage-cityArea-content-name">
          {{ route.query.active ? route.query.active.split("专")[0] : "" }}专区
        </div>
        <div class="topImage-cityArea-content-admin" v-if="cityAreaAdmin != ''">
          地市管理员：{{ cityAreaAdmin }}
        </div>
      </div>
    </div>
    <div class="topImage_content">
      <div class="topImage_title">{{ title }}</div>
      <div class="topImage_details">{{ description }}</div>
    </div>

    <div class="flex just-sb align-center" style="margin: 43px 0 0 0">
      <template v-for="(item, index) in projectList" :key="index">
        <div
          class="cardBac"
          :style="{ backgroundImage: `url('${item.backgroundImageUrl}')` }"
          @click="reGet(index)"
        >
          <div class="card_dec">
            <!-- <div class="card_num"> -->
            <p
              style="
                font-family: DIN, DIN;
                font-weight: bold;
                text-align: center;
                font-size: 20px;
                color: rgba(0, 0, 0, 0.65);
                margin: 0 6px 0 8px;
              "
            >
              {{ item.num }}
            </p>
            <!-- </div> -->
            <div
              style="
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.45);
                display: flex;
                align-items: center;
                margin-top: 11px;
              "
            >
              <p>{{ item.title }}</p>
            </div>
          </div>
          <div style="margin-right: 24px">
            <img :src="item.image" style="width: 88px; height: 88px" />
            <div v-show="!item.rate" style="margin-top: 8px">&nbsp</div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <div class="totaoText">
    <!-- <span @click="tabChange('1')" :class="{ activeBtn: sourceType === '1' }"
      >基础方案</span
    >
    <span @click="tabChange('2')" :class="{ activeBtn: sourceType === '2' }"
      >项目案例</span
    > -->
  </div>

  <table-list :sourceType="sourceType" :reGetList="reGetList"></table-list>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import tableList from "./components/tableList.vue";
import { getCount } from "@/api/solutionNew/home";
import { cityOverview } from "@/api/cityArea/index";
import backgroundImageUrl1 from "@/assets/images/home/<USER>";
import backgroundImageUrl2 from "@/assets/images/home/<USER>";
import backgroundImageUrl3 from "@/assets/images/home/<USER>";
import backgroundImageUrl4 from "@/assets/images/home/<USER>";
import image1 from "@/assets/images/home/<USER>";
import image2 from "@/assets/images/home/<USER>";
import image3 from "@/assets/images/home/<USER>";
import image4 from "@/assets/images/home/<USER>";
import { useRoute } from "vue-router";
export default defineComponent({
  name: "solveNew",
  components: {
    tableList,
  },
  setup(props, { emit }) {
    const route = useRoute();

//  watch(route, (newVal, oldVal) => {
//    if (newVal) {
//      // console.log('=====');
//      getCountDate();
//      getNavigate();
//    }
//  });
    const data = reactive({
      cityAreaAdmin: "",
      title: "",
      description: "",
      reGetList: false,
      projectList: [
        // {
        //   number: "",
        //   text: "",
        //   num: "",
        //   title: "方案上架数",
        //   textDec: "近七天上架方案数",
        //   backgroundImageUrl: backgroundImageUrl1,
        //   image: image1,
        // },
        {
          number: "",
          text: "",
          num: "",
          rate: "",
          title: "案例上架数",
          textDec: "近七天上架案例数",
          // rateText: '复制率',
          backgroundImageUrl: backgroundImageUrl1,
          image: image1,
        },
        // {
        //   number: "",
        //   text: "",
        //   num: "",
        //   rate: "",
        //   title: "方案查阅总次数",
        //   textDec: "近七天查阅数",
        //   backgroundImageUrl: backgroundImageUrl3,
        //   image: image3,
        // },
        {
          number: "",
          text: "",
          num: "",
          title: "案例查询总次数",
          textDec: "近七天查询总次数",
          backgroundImageUrl: backgroundImageUrl4,
          image: image3,
        },
        {
          number: "",
          text: "",
          num: "",
          title: "案例应用次数",
          textDec: "近七天查询总次数",
          backgroundImageUrl: backgroundImageUrl4,
          image: image4,
        },
      ],
      sourceType: "2",
    });
    const getCountDate = () => {
      if (route.query.activeNum) {
        data.sourceType = route.query.activeNum;
      }
      let cityName = route.query.active
        ? route.query.active.split("专")[0]
        : "";
      cityOverview({ city: cityName == "地市" ? "" : cityName }).then((res) => {
        // data.projectList[0].num =
        //   res.data.solutionUpCount.slice(0, -1) == 0
        //     ? "-"
        //     : res.data.solutionUpCount;
        data.projectList[0].num =
          res.data.caseUpCount.slice(0, -1) == 0 ? "-" : res.data.caseUpCount;
        // data.projectList[2].num =
        //   res.data.solutionViewCount.slice(0, -1) == 0
        //     ? "-"
        //     : res.data.solutionViewCount;
        data.projectList[1].num =
          res.data.caseViewCount.slice(0, -1) == 0
            ? "-"
            : res.data.caseViewCount;
        data.projectList[2].num =
          res.data.caseuseCount.slice(0, -1) == 0 ? "-" : res.data.caseuseCount;
        if (res.data.cityAdminNames && res.data.cityAdminNames.length > 0) {
          data.cityAreaAdmin = res.data.cityAdminNames.split(",")[0];
        }
      });
    };

    const getNavigate = () => {
      let urlList = [];
      if (localStorage.getItem("urlList")) {
        urlList = JSON.parse(localStorage.getItem("urlList"));
      }
      // console.log('urlList',urlList);
      urlList.forEach((item) => {
        //if (item.name == '地市专区') {
        //if (item.linkUrl.indexOf(route.name) > -1) {
        if (
          item.name.includes("专区") &&
          item.name.length > 2 &&
          item.description != null
        ) {
          data.title = item.title;
          data.description = item.description;
        }
      });
    };

    watch(
      () => route.query.activeNum,
      (newVal) => {
        data.sourceType = newVal;
      }
    );
    const tabChange = (type) => {
      data.sourceType = type;
    };

    const reGet = (index) => {
      console.log(index);
      if (index === 0) {
        data.reGetList = true;
      } else if (index === 1) {
        data.reGetList = true;
      } else {
        data.reGetList = false;
      }
      setTimeout(() => {
        data.reGetList = false;
      }, 1000);
    };
    watch(route,(newVal,oldVal)=>{
      if(newVal){
        // console.log('val',newVal);
        getCountDate()
        reGet(0)
      }
    })
    getCountDate();
    getNavigate();
    return {
      ...toRefs(data),
      tabChange,
      reGet,
      route,
    };
  },
});
</script>

<style lang="scss" scoped>
.topImage {
  /*width: 100%;*/
  width: 1200px;
  margin: 0 auto;
  height: 400px;
  background-image: url("@/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  padding-top: 36px;

  .topImage-cityArea {
    width: 520px;
    height: 68px;
    border-left: 2px solid rgba(47, 117, 230, 1);
    background-image: url("@/assets/images/product/cityAreaName.png");
    background-size: cover;

    > .topImage-cityArea-content {
      display: flex;
      align-items: center;
      > img {
        height: 68px;
      }
      > .topImage-cityArea-content-name {
        padding: 0 12px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: bold;
        font-size: 28px;
        color: #122c6c;
      }
      > .topImage-cityArea-content-admin {
        padding-left: 12px;
        font-family: Source Han Sans CN, Source Han Sans CN;
        font-weight: 400;
        font-size: 14px;
        color: rgba(18, 44, 108, 0.65);
      }
    }
  }

  .topImage_content {
    padding: 26px 0 16px 0;

    .topImage_title {
      font-weight: bold;
      font-size: 42px;
      color: #122c6c;
    }

    .topImage_details {
      width: 603px;
      font-size: 16px;
      color: #2b3f66;
      line-height: 32px;
      margin-top: 16px;
    }
  }

  .cardBac:nth-child(1) {
    margin-left: -18px;
  }

  .cardBac:nth-child(4) {
    margin-right: -18px;
  }

  .cardBac {
    flex: 1;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    &:hover {
      cursor: pointer;
    }

    .card_num {
      display: flex;
      align-items: flex-start;
    }

    .card_dec {
      margin: 24px 0 24px 24px;

      .dec_num {
        font-size: 40px;
        font-weight: bold;
        font-family: DIN, DIN;
        color: #2e7fff;
        text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
      }

      .dec_box {
        background-color: #ff5b00;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-weight: 500;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        line-height: 18px;
      }
    }
  }
}

.totaoText {
  font-weight: bold;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  margin-top: 130px;
  padding-bottom: 32px;
  margin-left: 118px;

  // border-bottom: 1px solid #DAE2F5;
  span {
    cursor: pointer;
    margin-right: 96px;
    padding-bottom: 16px;
  }

  .activeBtn {
    color: #0c70eb;
    border-bottom: 3px solid #0c70eb;
  }
}
</style>
