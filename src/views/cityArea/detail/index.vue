<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">地市方案</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.name }}</span>
      </div>
      <div class="right_nav">
        <div @click="back" style="margin-right: 20px">返回</div>
        <!-- <div @click="collectById" :class="{ active: collectActive }">
          <img
            width="22px"
            height="22px"
            v-if="detailData.collect == 0"
            src="@/assets/images/solution/detail/notCollect.png"
          />
          <img
            v-else
            width="22px"
            height="22px"
            src="@/assets/images/solution/detail/isCollect.png"
          />
        </div> -->
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" id="bacPhoto">
        <div class="top_card">
          <div class="left">
            <div class="left_tit">
              <p>{{ detailData.name }}</p>
              <!-- <div class="score">
                <div class="scoreIcon"><img src="../../../assets/images/score.png" alt="" /></div>
                <span class="scoreTitle">方案评分：</span>
                <span class="scoreNum">{{ ecologyDeal(detailData.score) }}</span>
              </div> -->
            </div>
            <div class="left_middle">
              <!-- <p class="info">
                {{ detailData.description }}
              </p> -->
              <a-tooltip overlayClassName="tooltip_class">
                <template
                  v-if="isShowToolTip(detailData.description, 174)"
                  #title
                >
                  {{ detailData.description }}</template
                >
                <p class="info">{{ detailData.description }}</p>
              </a-tooltip>
              <div class="bottom1" style="display: block">
                <div
                  class="label flex"
                  style="display: inline-block; margin-right: 24px"
                >
                  <span
                    style="margin-right: 6px"
                    v-for="(item, key) in detailData.labelList"
                    :key="key"
                    >{{ item.labelName }}</span
                  >
                </div>
                <div style="margin-left: 0; display: inline-block" class="tips">
                  <p v-if="detailData.phone != null">
                    <img
                      src="@/assets/images/solution/detail/eyes.png"
                      alt=""
                    />
                    {{ detailData.viewCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/eyes.png"
                      alt=""
                    />
                    -
                  </p>
                </div>
                <div style="display: inline-block" class="tips">
                  <p v-if="detailData.downloadCount != null">
                    <img
                      src="@/assets/images/solution/detail/left_down.png"
                      alt=""
                    />
                    {{ detailData.downloadCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/left_down.png"
                      alt=""
                    />-
                  </p>
                </div>
                <!-- <div style="display: inline-block" class="tips">
                  <p v-if="detailData.collectCount != null">
                    <img
                      src="@/assets/images/solution/detail/star.png"
                      alt=""
                    />
                    {{ detailData.collectCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/star.png"
                      alt=""
                    />
                    -
                  </p>
                </div> -->
                <div class="info_bottom" style="display: flex; margin-left: 0">
                  <p v-if="detailData.contact != null">
                    联系人：{{ detailData.contact }}
                  </p>
                  <p v-else>联系人：-</p>
                  <p v-if="detailData.phone != null">
                    联系电话：{{ detailData.phone }}
                  </p>
                  <p v-else>联系电话：-</p>
                  <p v-if="detailData.mail != null">
                    联系邮箱：{{ detailData.mail }}
                  </p>
                  <p v-else>联系邮箱：-</p>
                </div>
                <div class="info_bottom" style="display: flex; margin-left: 0">
                  <p>提供方： {{ detailData.provider }}</p>
                  <p>
                    首次上架：
                    {{
                      detailData.shelfTime
                        ? shelfTimeWith(detailData.shelfTime)
                        : detailData.createTime
                    }}
                  </p>
                </div>
                <!-- <div class="info_bottom" style="display: flex; margin-left: 0" v-if="detailData.isEcology == 1">
                	<p>生态合作方： {{ detailData.ecologyPartnerName }}</p>
                  <p>生态联系人： {{ detailData.ecologyContact }}</p>
                  <p>生态联系方式： {{ detailData.ecologyPhone }}</p>
                </div> -->
              </div>
              <div
                style="display: block; margin-left: 0; margin-bottom: 8px"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div class="anchors">
        <a-anchor
          direction="horizontal"
          :affix="false"
          v-for="(item, key) in anchorList"
          @click="handleClick"
        >
          <a-anchor-link
            :class="{ currentActive: isActive === key }"
            @click="change(key)"
            :href="item.href"
            :title="item.title"
          />
        </a-anchor>
      </div>
      <div class="content" id="anchorContent">
        <div
          v-for="(file, index) in detailData.videoList"
          :key="index"
          style="margin-bottom: 56px; display: flex; justify-content: center"
          :style="videoData.style"
        >
          <video
            id="video"
            ref="videoRef"
            :style="videoData.style"
            controls
            :src="file.url"
          />
        </div>

        <!-- 方案概述 -->
        <div class="card applyCard" id="#desc">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">方案概述</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div class="cards flex">
              <img
                v-if="detailData.summaryImage == '' || detailData.summaryImage == null"
                src="@/assets/images/ability/adlityDetail/bac.png"
                alt=""
              />
              <img v-else v-lazy="`${detailData.summaryImage}`" alt="" />
              <div class="right">
                <!-- <div
                  style="font-size: 18px; font-weight: 600; line-height: 40px"
                >
                  {{ detailData.name }}
                </div> -->
                <div class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template
                      v-if="isShowToolTip(detailData.summary, 250)"
                      #title
                    >
                      {{ detailData.summary }}</template
                    >
                    {{ detailData.summary }}
                  </a-tooltip>
                </div>
                <div class="file flex" style="margin-top: 16px" v-if="detailData.summaryPath && detailData.summaryPath != null">
                  <img src="@/assets/images/solution/detail/text.png" alt=""
                    style="width: 40px; height: 40px" />
                  <p @click="fileShow(detailData.summaryPath)" style="font-size:14px;color: #236CFF">
                    {{ fileNameWith({type:4}) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 应用案例 -->
        <div
          class="card applyCard"
          id="#caseList"
          v-if="detailData.caseList && detailData.caseList.length > 0"
          style="margin-top: 24px"
        >
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">应用案例</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>
            <div
              class="cards flex"
              v-for="(item, key) in detailData.caseList"
              :key="key + 1"
              style="margin-bottom: 16px"
            >
              <img
                v-if="item.imageUrl == '' || item.imageUrl == null"
                src="@/assets/images/ability/adlityDetail/bac.png"
                alt=""
              />
              <img
                v-else
                :key="index"
                v-lazy="`${item.imageUrlList[0]}`"
                alt=""
              />
              <div class="right">
                <!-- <div style="font-size: 18px; font-weight: 600; line-height: 40px">
                  {{ item.caseName }}
                </div> -->
                <div class="caseListItem">
                  <p class="right_name">{{ item.caseName }}</p>
                  <p
                    class="right_name"
                    v-if="item.projectAmount && item.projectAmount != ''"
                  >
                    规模：{{ item.projectAmount }}（万元）
                  </p>
                  <p
                    class="right_name"
                    v-if="item.projectTime && item.projectTime != ''"
                  >
                    时间：{{ item.projectTime }}
                  </p>
                  <div class="desc">
                    <a-tooltip overlayClassName="tooltip_class">
                      <template
                        v-if="isShowToolTip(item.constructionContent, 130)"
                        #title
                      >
                        {{ item.constructionContent }}</template
                      >
                      {{ item.constructionContent }}
                    </a-tooltip>
                  </div>
                </div>
                <div
                  class="file flex"
                  style="margin-top: 10px"
                  v-if="item.pptPath"
                >
                  <img
                    src="@/assets/images/solution/detail/text.png"
                    alt=""
                    style="width: 40px; height: 40px"
                  />
                  <p
                    @click="fileShow(item.pptPath)"
                    style="font-size: 14px; color: #236cff"
                  >
                    {{ fileNameWith({type:8}) }}
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 方案生态 -->
        <div class="card applyCard" id="#ecopartner" v-if="
          detailData.ecopartnerList && detailData.ecopartnerList.length > 0
        ">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">方案生态</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>
          </div>
          <div class="provider_con" style="width: 1200px">
            <div>
              <div
                class="pro"
                v-for="(value, key) in detailData.ecopartnerList"
                :key="key"
                @click="goHtml(value)"
              >
                <div class="score">
                  <div class="scoreIcon">
                    <img src="../../../assets/images/score.png" alt="" />
                  </div>
                  <div class="scoreBody" v-if="value.totalScore">
                    <div class="scoreTitle">评分：</div>
                    <div class="scoreNum">
                      {{ ecologyDeal(value.totalScore) }}
                    </div>
                  </div>
                  <div class="scoreBody" v-else>
                    <div
                      class="scoreNum"
                      style="font-size: 14px; margin-left: 10px"
                    >
                      暂无评分
                    </div>
                  </div>
                </div>
                <a-tooltip>
                  <template #title>点击查看详情</template>
                  <div class="flex align-center" style="margin-bottom: 10px">
                    <img
                      style="width: 26px; height: 26px; margin-right: 8px"
                      src="@/assets/images/solution/detail/pro_icon.png"
                      alt=""
                    />
                    <div
                      style="
                        width: 44%;
                        font-weight: 400;
                        font-size: 16px;
                        line-height: 28px;
                        color: #2e3852;
                      "
                    >
                      生态合作方：{{ dealData(value.ecopartnerName) }}
                    </div>
                  </div>
                  <div class="flex align-center" style="margin-left: 34px">
                    <div
                      style="
                        width: 24%;
                        font-weight: 400;
                        font-size: 16px;
                        line-height: 28px;
                        color: #2e3852;
                      "
                    >
                      联系人：{{ dealData(value.contactList[0].contactName) }}
                    </div>
                    <div
                      style="
                        width: 34%;
                        font-weight: 400;
                        font-size: 16px;
                        color: #2e3852;
                        line-height: 28px;
                      "
                    >
                      联系方式：{{
                        dealData(value.contactList[0].contactPhone)
                      }}
                    </div>
                    <div
                      style="
                        width: 34%;
                        font-weight: 400;
                        font-size: 16px;
                        color: #2e3852;
                        line-height: 28px;
                      "
                    >
                      负责区域：{{
                        areaData(value.contactList[0].contactAddress)
                      }}
                    </div>
                  </div>
                </a-tooltip>
              </div>
            </div>
          </div>
        </div>

        <div
          class="tab_content"
          id="#download"
          v-if="detailData.fileList && detailData.fileList.length > 0"
          style="margin-top: 32px"
        >
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">方案附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
        </div>
        <ul class="list" v-if="detailData.solutionFileList && detailData.solutionFileList.length > 0">
          <li v-for="(item, key) in detailData.solutionFileList" class="li">
            <div class="li_box" @click="fileShow(item.path)">
              <div class="left_box">
                <img
                  src="../../../assets/images/solution/detail/word.png"
                  alt=""
                  style="width: 40px; height: 40px"
                />
                <p class="fileText">{{ item.name }}</p>
              </div>

              <img
                src="../../../assets/images/solution/detail/download.png"
                alt=""
                @click.stop="downloadBtn(item)"
                style="cursor: pointer"
              />
            </div>
          </li>
        </ul>
      </div>
      <!-- <view-list :id="viewId" :type="viewType"></view-list> -->
      <img
        class="top"
        src="../../../assets/images/solution/detail/toTap.png"
        alt=""
        @click="scrollUp"
      />
      <div class="bottomEmpty"></div>
    </div>
  </div>
  <a-modal
    v-model:visible="showDownloadModal"
    title="" :closable="false"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="450px"
  >
    <promptBox
      @downloadModalCancel="downloadModalCancel"
      @downloadModalConfirm="downloadModalConfirm"
      :msgObj="msgObj"
    />
  </a-modal>
  <a-modal
    v-model:visible="showDownloadForm"
    title="新增工单"
    :mask-closable="false"
    :footer="null"
    :destroyOnClose="true"
    width="600px"
  >
    <reviewForm
      @downloadFormCancel="downloadFormCancel"
      @downloadFormConfirm="downloadFormConfirm"
    />
  </a-modal>
</template>

<script setup>
import { onMounted, ref, reactive, computed } from "vue";
import { isShowToolTip } from "@/utils/index.js";
import { useRouter, useRoute } from "vue-router";
import {
  cancelCollect,
  collect,
  getNewDownCount,
} from "@/api/solutionNew/detail";
import { cityParamDetail } from "@/api/cityArea/index.js";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { getEcologicalDetails } from "@/api/login/login.js";
import viewList from "@/views/product/detail/viewList.vue";
import { getTradeList } from "@/api/solutionNew/home";
import { message } from "ant-design-vue";
import eventBus from "@/utils/eventBus";
import promptBox from "@/components/promptBox/index.vue";
import reviewForm from "@/components/reviewForm/index.vue";
const route = useRoute();
const Router = useRouter();
const viewId = ref(route.query.id);
const viewType = ref("1");
const loadShow = ref(false);
const showDownloadModal = ref(false);
const showDownloadForm = ref(false);
const videoData = reactive({
  style: "position: fixed;right:200%;",
});
const isActive = ref(0);
const collectActive = ref(false);
const videoRef = ref(null);
const tabList = ref([]);
const currentAnchor = ref("#desc");
const isShow = ref("desc");
const anchorList = ref([]);
const detailData = ref({});
const valueData = ref([]);
const msgObj = reactive({
  applyTimes:'',
  msg:'',
  fullPath:''
})

const shelfTimeWith = computed(() => {
  return function (value) {
    if (value) {
      return value.slice(0, 10);
    }
    return "-";
  };
});
const dealData = computed(() => {
  return function (value) {
    if (value) {
      return value;
    } else {
      return "-";
    }
  };
});
const areaData = computed(() => {
  return function (value) {
    if (value) {
      return value;
    } else {
      return "江苏省";
    }
  };
});
const ecologyDeal = computed(() => {
  return function (value) {
    if (value) {
      return value;
    }
    return "-";
  };
});

onMounted(() => {
  getData();
  getTarde();
  setTimeout(() => {
    const videoDom = document.getElementById("video");
    if (videoDom.videoHeight > videoDom.videoWidth) {
      videoData.style = "height:400px;";
    } else {
      videoData.style = "width: 100%;";
    }
  }, 2000);
});

const change = (e) => {
  isActive.value = e;
};
const downloadBtn = (e) => {
  // console.log("下载超限");
  getNewDownCount({
    businessId: route.query.id,
    businessType: 12,
  })
    .then((res) => {
      if (res.code == 200) {
        if (res.data) {
          const href = e.url;
          const downName = e.name;
          let windowOrigin = window.location.origin;
					let token = localStorage.getItem("token");
					let newHref = href;
					if(href.includes(windowOrigin)){
					  newHref = "/portal" + href.split(windowOrigin)[1]
					}
				  window.open(windowOrigin + newHref + "?token=" + token);
          return false;
        } else {
          if(res.code == 200){
            if(res.msg.includes('5')){
              msgObj.applyTimes = 1
              msgObj.msg = res.msg
              msgObj.fullPath = route.fullPath
            }else if(res.msg.includes('10')){
              msgObj.applyTimes = 1
              msgObj.msg = res.msg
              msgObj.fullPath = route.fullPath
            }else if(res.msg.includes('额外下载次数已满') && !res.msg.includes('今日不可再提交')){
              msgObj.applyTimes = 2
              msgObj.msg = res.msg
              msgObj.fullPath = route.fullPath
            }else if(res.msg.includes('今日不可再提交')){
              msgObj.applyTimes = 3
              msgObj.msg = res.msg
              msgObj.fullPath = route.fullPath
            }else{}
            showDownloadModal.value = true;
          }
        }
      }
    })
    .catch((err) => {
      console.log(err);
    });
};

// 转PDF的下载
const fileShow = (val) => {
  // console.log("下载超限");
  loadShow.value = true;
  pptTopdf({
    filePath: val,
  }).then((res) => {
    loadShow.value = false;
    if (res.code == 200) {
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = res.data;
			if(res.data.includes(windowOrigin)){
			  newHref = "/portal" + res.data.split(windowOrigin)[1]
			}
      const newpage = Router.resolve({
        name: "lookPdf",
        query: {
          urlMsg: encodeURIComponent(
          	windowOrigin + newHref + "?token=" + token
          ),
          urlName: val.name,
        },
      });
      window.open(newpage.href, "_blank");
    }
  });
  return false;
};

const getTarde = () => {
  let tradeParams = {};
  getTradeList(tradeParams).then((result) => {
    result.data.map((item) => {
      tabList.value.push({
        name: item.name,
        id: item.id.toString(),
      });
    });
  });
};
const toolTipShow = (item, num) => {
  let wordNum = "";
  item.map((val) => {
    wordNum += val.summary + "\n";
  });
  return wordNum && wordNum.length >= num;
};
const fileNameWith = (value) => {
  let createShow = false;
  let shelfTime = "";
  if (detailData.value.shelfTime) {
    shelfTime = detailData.value.shelfTime.slice(0, 10).replace(/-/g, "");
  }
  createShow = Number(shelfTime) < 20241226;
  let fileName = {
    1: createShow ? "政策背景" : "方案概述",
    2: "市场分析",
    3: "需求分析",
    4: "方案概述",
    5: "应用场景",
    6: "部署方案",
    7: "合作模式",
    8: "应用案例",
  };
  return fileName[value.type];
};
const collectById = () => {
  if (detailData.value.collect == 1) {
    cancelCollect(route.query.id)
      .then(() => {
        message.success("取消收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  } else {
    collect(route.query.id)
      .then(() => {
        message.success("收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  }
  collectActive.value = !collectActive.value;
};
const getCurrentAnchor = () => {
  return currentAnchor.value;
};
const toDetail = (value) => {
  Router.push({
    query: {
      id: value.id,
    },
    name: "cityAreaApplyNew",
  });
};
const back = () => {
  Router.back();
  return false;
};

// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#desc";
  getCurrentAnchor();
  isActive.value = 0;
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};

const anchorPoints = ref([
  {
    key: "desc",
    href: "#desc",
    title: "方案概述",
    type: 1,
  },
  // {
  //   key: "market",
  //   href: "#market",
  //   title: "市场分析",
  //   type: 2,
  // },
  // {
  //   key: "need",
  //   href: "#need",
  //   title: "需求分析",
  //   type: 3,
  // },
  // {
  //   key: "desc",
  //   href: "#desc",
  //   title: "方案概述",
  //   type: 4,
  // },

  {
    key: "apply",
    href: "#apply",
    title: "应用场景",
    type: 5,
  },
  // {
  //   key: "ability",
  //   href: "#ability",
  //   title: "部署方案",
  //   type: 6,
  // },
  // {
  //   key: "team",
  //   href: "#team",
  //   title: "合作模式",
  //   type: 7,
  // },
  {
    key: "caseList",
    href: "#caseList",
    title: "应用案例",
    type: 8,
  },
]);

const transformEcoPartnerList = (originalList) => {
  // 边界情况处理
  if (!originalList || !Array.isArray(originalList)) {
    return [];
  }

  // 使用Map实现高性能分组
  const groupMap = originalList.reduce((map, item) => {
    // 跳过无效元素
    if (!item || typeof item !== "object") return map;

    try {
      // 安全提取字段（防止undefined报错）
      const { ecopartnerName, ecopartnerId } = item;
      const key = `${ecopartnerName}|${ecopartnerId}`;

      // 自动跳过缺失关键字段的元素
      if (!key.includes("undefined")) {
        if (!map.has(key)) {
          map.set(key, {
            ecopartnerName,
            ecopartnerId,
            children: [],
          });
        }
        // 安全提取子字段
        map.get(key).children.push({
          ecologyContact: item.ecologyContact || "",
          ecologyPhone: item.ecologyPhone || "",
          ecologyJob: item.ecologyJob || "",
        });
      }
    } catch (e) {
      console.warn("Invalid data format:", e);
    }

    return map;
  }, new Map());

  return Array.from(groupMap.values());
};

const getData = () => {
  if (route.query.id) {
    cityParamDetail(route.query.id).then(async (res) => {
      anchorList.value = [
        {
          key: "desc",
          href: "#desc",
          title: "方案概述",
          type: 1,
        },
      ];
      if (res.data.caseList && res.data.caseList.length > 0) {
        anchorList.value = [
          {
            key: "desc",
            href: "#desc",
            title: "方案概述",
            type: 1,
          },
          {
            key: "caseList",
            href: "#caseList",
            title: "应用案例",
            type: 8,
          },
        ];
        res.data.caseList.forEach((item) => {
          if (item.imageUrl && item.imageUrl != "") {
            item.imageUrlList = item.imageUrl.split(",");
          }
        });
      }
      res.data.videoList = res.data.fileList.filter(
        (item) => item.category == 6 || item.type == 5
      );
      res.data.videoList.forEach(item => {
		      	item.url = window.location.origin + item.url + "?token=" + localStorage.getItem("token")
		      })
      res.data.solutionFileList = res.data.fileList.filter(
        (item) => item.category != 6 && item.type != 5
      );
      res.data.createTime = res.data.createTime.slice(0, 10);
      // res.data.moduleBody.sort((a, b) => a.type - b.type);
      // res.data.moduleBody.forEach((item) => {
      //   const anchor = anchorPoints.value.find((a) => a.type === item.type);
      //   if (anchor) {
      //     anchorList.value.push(anchor);
      //   }
      // });

      if (res.data.videoList && res.data.videoList.length > 0) {
        anchorList.value.unshift({
          key: "video",
          href: "#video",
          title: "方案视频",
          type: 10,
        });
      }
      if (res.data.ecopartnerList && res.data.ecopartnerList.length > 0) {
        anchorList.value = [
          ...anchorList.value,
          {
            key: "ecopartner",
            href: "#ecopartner",
            title: "方案生态",
            type: 0,
          },
        ];
        if (res.data.enterpriseList && res.data.enterpriseList.length > 0) {
          let dataIds = [];
          res.data.enterpriseList.forEach((item) => {
            dataIds.push(item.enterpriseId);
          });
          let result = await getEcologicalDetails(dataIds);
          let ecopartnerNewList = [];
          result.data.forEach((item) => {
            let children = [];
            item.basicContactInfos.forEach((itemChild) => {
              children.push({
                ecologyContact: itemChild.contactName,
                ecologyPhone: itemChild.contactPhone,
                ecologyJob: itemChild.contactPosition,
              });
            });
            ecopartnerNewList.push({
              ecopartnerName: item.enterpriseName,
              ecopartnerId: item.id,
              children: children,
            });
          });
          res.data.moduleBody = [
            {
              type: 0,
              moduleList: ecopartnerNewList,
            },
            ...res.data.moduleBody,
          ];
        }
        if (res.data.ecopartnerList && res.data.ecopartnerList.length > 0) {
        }
      }
      if (res.data.solutionFileList && res.data.solutionFileList.length > 0) {
        anchorList.value.push({
          key: "download",
          href: "#download",
          title: "方案附件",
          type: 9,
        });
      } else {
        res.data.fileList = false;
      }

      // res.data.labelName = res.data.labelName.split(",");
      res.data.provider = res.data.provider.split("/")[1];
      detailData.value = res.data;
      // console.log('detailData',detailData.value);
      return;
      res.data.moduleBody.forEach((element) => {
        if (element.type == 8) {
          let text = element.moduleList[0].summary;
          let caseInfo = {};
          let keywords = {
            name: "项目名称[：:](.*?)\\n",
            size: "规模[：:](.*?)\\n",
            time: "时间[：:](.*?)\\n",
            people: "联系人[：:](.*?)\\n",
            intro: "(案例介绍|项目介绍)[：:](.*)", // 匹配“案例介绍”或“项目介绍”后的内容
          };
          for (let key in keywords) {
            let regex = new RegExp(keywords[key], "s");
            let match = text.match(regex);
            if (match) {
              caseInfo[key] =
                key === "intro"
                  ? (match[2] || "").trim()
                  : (match[1] || "").trim();
            } else {
              caseInfo[key] = "";
              // 如果您不想在找不到匹配时保留该属性，可以取消注释下一行
              // delete caseInfo[key];
            }
          }
          element.caseInfo = caseInfo;
        }
      });
      let markList = res.data.moduleBody.filter((item) => {
        return item.type == 2;
      });
      let needList = res.data.moduleBody.filter((item) => {
        return item.type == 3;
      });
      let descList = res.data.moduleBody.filter((item) => {
        return item.type == 4;
      });
      res.data.moduleBody.map((value) => {
        if (value.type == 1) {
          if (markList && markList.length > 0)
            value.moduleList = [...value.moduleList, ...markList[0].moduleList];
          if (needList && needList.length > 0)
            value.moduleList = [...value.moduleList, ...needList[0].moduleList];
          if (descList && descList.length > 0)
            value.moduleList = [...value.moduleList, ...descList[0].moduleList];
        }
      });
      console.log(res.data.moduleBody, `oooo`);

      valueData.value = res.data.moduleBody;
    });
  }
};

const handleClick = (e, link) => {
  const href = link.href.replace("#", "");
  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);
  srcolls &&
    srcolls.scrollIntoView({
      block: "center",
      behavior: "smooth",
    });
  isShow.value = href;
};
eventBus.on("solutionDetailRefresh", getData);
// 下载超限提示弹窗取消按钮
const downloadModalCancel = () => {
  showDownloadModal.value = false;
};
// 下载超限提示弹窗确认按钮
const downloadModalConfirm = () => {
  showDownloadModal.value = false;
  showDownloadForm.value = true;
};
const downloadFormCancel = () => {
  showDownloadForm.value = false;
};
const downloadFormConfirm = () => {
  showDownloadForm.value = false;
};

const goHtml = (item) => {
  console.log(item);
  if (item.ecopartnerId > 10000) {
    window.open(
      "https://ipartner.jsdict.cn/static/detail?partner_id=" +
        item.ecopartnerId +
        "&token=bhubh3333ugy",
      "_blank"
    );
  } else {
    // console.log('=====');
    let url =
      item.contactList && item.contactList.length > 0
        ? item.contactList[0].enterpriseId
        : "";
    // return
    window.open(
      "https://ipartner.jsdict.cn/static/detail?partner_id=" +
        url +
        "&token=bhubh3333ugy",
      "_blank"
    );
  }
};
</script>


<style lang="scss" scoped>
@import "./index.scss";
</style>

<style lang="scss">
.cards{
  .right{
    > .desc {
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 5;
      /* 控制显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.caseListItem {
  font-weight: 400;
  font-size: 16px;
  color: rgba(46, 56, 82, 0.85);
  > .desc {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 3;
    /* 控制显示的行数 */
    overflow: hidden;
    text-overflow: ellipsis;
    >span{
      display: -webkit-box;
      -webkit-box-orient: vertical;
      -webkit-line-clamp: 3;
      /* 控制显示的行数 */
      overflow: hidden;
      text-overflow: ellipsis;
    }
  }
}
.label {
  span {
    display: inline-block;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #236cff;
    font-weight: 400;
    font-size: 14px;
    color: #236cff;
    line-height: 25px;
    padding: 0 10px;
  }
}

.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }

  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }

  .ant-modal-content {
    height: 395px;
    padding: 0;
  }

  .ant-form {
    width: 100%;
  }

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: rgba(214, 228, 255, 0.6);
    border-radius: 2px 2px 2px 2px;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  :deep(.ant-tabs-nav-wrap) {
    margin-top: 16px;
    width: 236px !important;
    // overflow-x: auto;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }

  .ant-tabs-tab {
    // width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d6886 !important;
  }

  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}
.right_name {
  margin-bottom: 0;
}
.caseinfo {
  display: -webkit-box;
  -webkit-line-clamp: 3;
  /* 限制为3行 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  /* 隐藏超出部分 */
  text-overflow: ellipsis;
}

.right_name {
  margin-bottom: 0;
}

:deep(.ant-tabs-nav-wrap) {
  margin-top: 16px;
  width: 236px !important;
  overflow-x: auto !important;
  height: 48px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #ffffff;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}
</style>
