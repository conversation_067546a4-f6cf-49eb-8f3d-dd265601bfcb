<template>
  <div id="lay_content">
    <div class="infoMation"></div>

    <div style="margin-top: 72px">
      <a-form
        ref="formRef"
        :model="formData"
        labelAlign="right"
        :rules="rules"
        :label-col="{ span: 6 }"
        :wrapper-col="{ span: 18 }"
      >
        <a-row class="passWardClass">
          <!--<a-col :span="13">
            <a-form-item
              label="旧密码"
              name="oldPassword"
              :wrapper-col="{ offset: 1, span: 16 }"
              :colon="false"
            >
              <a-input
                v-model:value="formData.oldPassword"
                placeholder="请输入旧密码"
                :type="oldInput ? 'password' : 'text'"
                allowClear
              >
                <template #suffix>
                  <i
                    :class="[
                      'iconfont',
                      'pointer',
                      oldInput ? 'icon-hide' : 'icon-display',
                    ]"
                    style="color: #0c70eb"
                    @click.stop="typeInput('old')"
                  ></i>
                </template>
              </a-input>
            </a-form-item>
          </a-col>-->
          <a-col :span="13">
            <a-form-item
              label="新密码"
              name="newPassword"
              :colon="false"
              :wrapper-col="{ offset: 1, span: 16 }"
            >
              <a-input
                v-model:value="formData.newPassword"
                placeholder="请输入新密码"
                :type="newInput ? 'password' : 'text'"
                allowClear
              >
                <template #suffix>
                  <i
                    :class="[
                      'iconfont',
                      'pointer',
                      newInput ? 'icon-hide' : 'icon-display',
                    ]"
                    style="color: #0c70eb"
                    @click.stop="typeInput('new')"
                  ></i>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
          <a-col :span="13">
            <a-form-item
              label="确认新密码"
              name="okPass"
              :colon="false"
              :wrapper-col="{ offset: 1, span: 16 }"
            >
              <a-input
                v-model:value="formData.okPass"
                placeholder="请确认新密码"
                :type="okInput ? 'password' : 'text'"
                allowClear
              >
                <template #suffix>
                  <i
                    :class="[
                      'iconfont',
                      'pointer',
                      okInput ? 'icon-hide' : 'icon-display',
                    ]"
                    style="color: #0c70eb"
                    @click.stop="typeInput('ok')"
                  ></i>
                </template>
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center botBtn">
        <a-button type="primary" @click="submit">确定</a-button>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { useRouter, useRoute } from "vue-router";
import { resetPwds } from "@/api/info";
import { getLogOut } from "@/api/login/login.js";
import { message } from "ant-design-vue";
import { useHomeStore } from "@/store";
import AES from "@/utils/cryptoPwd.js";

export default defineComponent({
  setup() {
  	const Router = useRouter();
  	const counterStore = useHomeStore();
    const formRef = ref();
    const getUserInfo = JSON.parse(localStorage.getItem("userInfo"));

    // 检测密码中是否包含键盘连续模式
    const detectKeyboardPatterns = (pwd) => {
      const patterns = [];
      const lowerPwd = pwd.toLowerCase();
      // 键盘连续模式检测（横向、纵向、对角线）
      let keyboardSequences = [
        'qwertyuiop', 'asdfghjkl', 'zxcvbnm', // 横向
        '1234567890', // 数字横向
        'qaz', 'wsx', 'edc', 'rfv', 'tgb', 'yhn', 'ujm', 'ik', 'ol', // 斜向
        'qwertz', 'asdfgh', 'yxcvbn', // 其他布局
        '1qaz', '2wsx', '3edc', '4rfv', '5tgb', '6yhn', '7ujm', '8ik', '9ol', '0p' // 数字+字母
      ];  
      // 检测连续3个字符以上的序列
      keyboardSequences.forEach(seq => {
        for (let i = 0; i <= seq.length - 3; i++) {
          const substring = seq.substring(i, i + 3);
          if (lowerPwd.includes(substring)) {
            patterns.push(substring);
          }
        }
      });
          
      return [...new Set(patterns)]; // 去重
    };

    const validatePass = async (rule, value) => {
      let reg = /^(?=.*[A-Za-z\W])(?=.*\d)[A-Za-z\d\W]{6,16}$/;
      if (!value) return Promise.reject("请输入新密码");
      // 密码规则验证
      const hasUpperCase = /[A-Z]/.test(value);
      const hasLowerCase = /[a-z]/.test(value);
      const hasNumber = /[0-9]/.test(value);
      const pwdlength = value.length < 6 || value.length >16;
      // const hasSpecialChar = /[!@#$%^&*()_+\-=$$$${};':"\\|,.<>\/?]+/.test(value);
      const hasKeyboardPattern = detectKeyboardPatterns(value).length > 0;
      console.log('===',hasUpperCase,hasLowerCase,hasNumber,pwdlength,hasKeyboardPattern);
      
      // !reg.test(value)
      if (!hasUpperCase || !hasLowerCase || !hasNumber || hasKeyboardPattern || pwdlength)
        return Promise.reject(
          // "密码必须是6-16位数字、字母或字符组合（不能是纯数字）"
          "密码必须是6-16位数字、大小写字母或字符组合（不能是纯数字）且不能存在键盘规律（例：qwe、asd）"
        );

      return Promise.resolve();
    };
    const validateNewPass = async (rule, value) => {
      if (!value) return Promise.reject("请输入新密码");

      if (value != data.formData.newPassword)
        return Promise.reject("两次密码不一致");

      return Promise.resolve();
    };
    const data = reactive({
      formData: {},
      rules: {
        // oldPassword: { required: true, message: "请输入旧密码" },
        newPassword: { required: true, validator: validatePass },
        okPass: { required: true, validator: validateNewPass },
      },
      newInput: true,
      oldInput: true,
      okInput: true,
    });

    const typeInput = (type) => {
      if (type == "old") data.oldInput = !data.oldInput;
      if (type == "new") data.newInput = !data.newInput;
      if (type == "ok") data.okInput = !data.okInput;
    };

    const submit = () => {
      formRef.value
        .validate()
        .then(() => {
          let param = {
            id: getUserInfo.id,
            //oldPassword: AES.encrypt(
            //  "yd@10086qwerty!@",
            //  "yd@10086qwerty!@",
            //  data.formData.oldPassword
            //),
            newPassword: AES.encrypt(
              "yd@10086qwerty!@",
              "yd@10086qwerty!@",
              data.formData.newPassword
            ),
          };
          resetPwds(param).then((res) => {
            if (res.code === 200) {
              message.success("修改成功");
              getLogOut().then((res) => {
				        // 清缓存
				        window.localStorage.setItem("token", "");
				        window.localStorage.setItem("userInfo", "");
				        counterStore.username = "0";
				        counterStore.aiBuyListStroe = {};
				        counterStore.parentId = "";
				        counterStore.productList = [];
				        counterStore.showTop = false;
				        Router.replace({
				          path: "/login",
				        });
				      });
            }
          });
        })
        .catch(() => {});
    };

    return {
      ...toRefs(data),
      detectKeyboardPatterns,
      submit,
      Router,
      formRef,
      typeInput,
      counterStore
    };
  },
});
</script>
<style lang="scss" scoped>
#lay_content {
  background: #ffffff;
  border-radius: 8px;
}
.passWardClass {
  display: flex;
  justify-content: center;
}

.infoMation {
  background-image: url("@/assets/images/layout/psd.png");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 180px;
}
.botBtn {
  padding: 40px 0;
}
:deep(.ant-input-affix-wrapper) {
  color: #00060e;
  font-weight: 500;
  border: none !important;
  border-radius: 4px !important;
  background: #f5f6fa !important;
}

:deep(.ant-input) {
  background: #f5f6fa !important;
}
:deep(.ant-form-item-label > label) {
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
}
</style>