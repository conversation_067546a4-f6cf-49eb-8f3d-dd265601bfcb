<template>
  <div class="topImage">
    <div class="topImage_content">
      <div class="topImage_title">{{title}}</div>
      <div class="topImage_details">{{description}}</div>
    </div>
  </div>
  <table-list :sourceType="sourceType" :reGetList="reGetList"></table-list>
</template>

<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import tableList from "./components/tableList.vue";
import backgroundImageUrl1 from "@/assets/images/home/<USER>";
import backgroundImageUrl3 from "@/assets/images/home/<USER>";
import backgroundImageUrl4 from "@/assets/images/home/<USER>";
import image1 from "@/assets/images/home/<USER>";
import image3 from "@/assets/images/home/<USER>";
import image4 from "@/assets/images/home/<USER>";
import { useRoute } from "vue-router";

export default defineComponent({
  name: "topContent",
  components: {
    tableList,
  },
  setup() {
    const data = reactive({
    	title: "",
    	description: "",
      reGetList: false,
      projectList: [
        {
          number: "",
          text: "",
          num: "",
          title: "标准方案上架数",
          textDec: "近七天上架方案数",
          backgroundImageUrl: backgroundImageUrl1,
          image: image1,
        },
        {
          number: "",
          text: "",
          num: "",
          rate: "",
          title: "方案场景上架数",
          textDec: "近七天复制数",
          // rateText: '复制率',
          backgroundImageUrl: backgroundImageUrl1,
          image: image1,
        },
        {
          number: "",
          text: "",
          num: "",
          rate: "",
          title: "方案查阅总次数",
          textDec: "近七天查阅数",
          backgroundImageUrl: backgroundImageUrl3,
          image: image3,
        },
        {
          number: "",
          text: "",
          num: "",
          title: "方案收藏总数",
          textDec: "近期支撑项目数",
          backgroundImageUrl: backgroundImageUrl4,
          image: image4,
        },
      ],
      sourceType: "1",
    });
    const route = useRoute();
    watch(
      () => route.query.activeNum,
      (newVal) => {
        data.sourceType = newVal;
      }
    );
    const tabChange = (type) => {
      data.sourceType = type;
    };
    
    const getNavigate = () => {
    	let urlList = [];
    	if(localStorage.getItem("urlList")){
    		urlList = JSON.parse(localStorage.getItem("urlList"))
    	}
    	urlList.forEach(item => {
    		if(item.linkUrl.indexOf(route.name)>-1){
    			data.title = item.title;
    			data.description = item.description;
    		}
    	})
    };
    getNavigate();

    const reGet = (index) => {
      if (index === 0) {
        data.reGetList = true;
      } else if (index === 1) {
        data.reGetList = true;
      } else {
        data.reGetList = false;
      }
      setTimeout(() => {
        data.reGetList = false;
      }, 1000);
    };
    
    return {
      ...toRefs(data),
      tabChange,
      reGet,
      route,
    };
  },
});
</script>

<style lang="scss" scoped>
.topImage {
  /*width: 100%;*/
  width: 1200px;
  margin: 0 auto;
  height: 400px;
  background-image: url("@/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-size: 100% 100%;

  .topImage_content {
    padding: 63px 0 16px 0;

    .topImage_title {
      font-weight: bold;
      font-size: 42px;
      color: #122c6c;
    }

    .topImage_details {
      width: 603px;
      font-size: 16px;
      color: #2b3f66;
      line-height: 32px;
      margin-top: 16px;
    }
  }

  .cardBac {
    flex: 1;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .card_num {
      display: flex;
      align-items: flex-start;
    }

    .card_dec {
      margin: 24px 0 24px 24px;

      .dec_num {
        font-size: 40px;
        font-weight: bold;
        color: #2e7fff;
        text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
      }

      .dec_box {
        background-color: #ff5b00;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-weight: 500;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        line-height: 18px;
      }
    }
  }
}

.totaoText {
  font-weight: bold;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  margin-top: 80px;
  padding-bottom: 32px;
  margin-left: 118px;
  // border-bottom: 1px solid #DAE2F5;
  span {
    cursor: pointer;
    margin-right: 96px;
    padding-bottom: 16px;
  }
  .activeBtn {
    color: #0c70eb;
    border-bottom: 3px solid #0c70eb;
  }
}
</style>