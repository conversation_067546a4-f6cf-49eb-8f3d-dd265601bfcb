<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" tip="附件加载中"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">行业方案</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.name }}</span>
      </div>
      <div class="right_nav">
        <div @click="back" style="margin-right: 20px">返回</div>
        <div @click="collectById" :class="{ active: collectActive }">
          <img
            width="22px"
            height="22px"
            v-if="detailData.collect == 0"
            alt=""
            src="@/assets/images/solution/detail/notCollect.png"
          />
          <img
            v-else
            width="22px"
            height="22px"
            alt=""
            src="@/assets/images/solution/detail/isCollect.png"
          />
        </div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" id="bacPhoto">
        <div class="top_card">
          <div class="left">
            <div class="left_tit">
              <p>{{ detailData.projectName }}</p>
            </div>
            <div class="left_middle">
              <a-tooltip overlayClassName="tooltip_class">
                <template
                  v-if="isShowToolTip(detailData.description, 174)"
                  #title
                >
                  {{ detailData.description }}</template
                >
                <p class="info">{{ detailData.description }}</p>
              </a-tooltip>
              <div class="bottom1" style="display: block">
                <div
                  class="label flex"
                  style="display: inline-block; margin-right: 24px"
                >
                  <span
                    style="margin-right: 6px"
                    v-for="(item, key) in detailData.labelName"
                    :key="key"
                    >{{ item }}</span
                  >
                </div>
                <div style="margin-left: 0; display: inline-block" class="tips">
                  <p v-if="detailData.phone != null">
                    <img
                      src="@/assets/images/solution/detail/eyes.png"
                      alt=""
                    />
                    {{ detailData.viewCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/eyes.png"
                      alt=""
                    />
                    -
                  </p>
                </div>
                <div style="display: inline-block" class="tips">
                  <p v-if="detailData.downloadCount != null">
                    <img
                      src="@/assets/images/solution/detail/left_down.png"
                      alt=""
                    />
                    {{ detailData.downloadCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/left_down.png"
                      alt=""
                    />-
                  </p>
                </div>
                <div style="display: inline-block" class="tips">
                  <p v-if="detailData.collectCount != null">
                    <img
                      src="@/assets/images/solution/detail/star.png"
                      alt=""
                    />
                    {{ detailData.collectCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/star.png"
                      alt=""
                    />
                    -
                  </p>
                </div>
                <!--<div style="display: inline-block" class="tips">
                  <p v-if="detailData.combineCount != null">
                    <img
                      src="@/assets/images/solution/detail/yinru.png"
                      alt=""
                    />{{ detailData.combineCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/yinru.png"
                      alt=""
                    />-
                  </p>
                </div>-->
                <div class="info_bottom" style="display: flex; margin-left: 0">
                  <p v-if="detailData.contact != null">
                    联系人：{{ detailData.contact }}
                  </p>
                  <p v-else>联系人：-</p>
                  <p v-if="detailData.phone != null">
                    联系电话：{{ detailData.phone }}
                  </p>
                  <p v-else>联系电话：-</p>
                  <p v-if="detailData.mail != null">
                    联系邮箱：{{ detailData.mail }}
                  </p>
                  <p v-else>联系邮箱：-</p>
                </div>
                <div class="info_bottom" style="display: flex; margin-left: 0">
              
                  <p>方案提供方： {{ detailData.provider }}</p>

                  <p>上架时间： {{ detailData.createTime }}</p>
                </div>

                <div class="addCar">
                  <button v-if="detailData.addCart" class="disabled">
                    已加入
                  </button>
                  <button v-else @click="add">加入预选</button>
                  <button style="margin-left: 20px;" @click="addAI">去定制</button>
                </div>
              </div>
              <div
                style="display: block; margin-left: 0; margin-bottom: 8px"
              ></div>
            </div>
          </div>
        </div>
      </div>

      <div class="anchors">
        <a-anchor
          direction="horizontal"
          :affix="false"
          v-for="(item, key) in anchorList"
          @click="handleClick"
          :key="key"
        >
          <a-anchor-link
            :class="{ currentActive: isActive === key }"
            @click="change(key)"
            :href="item.href"
            :title="item.title"
          />
        </a-anchor>
      </div>
      <div class="content" id="anchorContent">
        <div
          class="card applyCard"
          :id="cardItem.href"
          v-for="(cardItem,key) in anchorList"
          :key="key"
        >
          <div v-for="(item, index) in valueData" :key="index">
            <div
              class="card_content"
              style="padding-top: 0px; display: block"
              v-if="item.type == cardItem.type"
            >
              <div class="tab_content">
                <img
                  src="@/assets/images/solution/detail/leftIcon.png"
                  style="width: 33px; height: 22px"
                  alt=""
                />
                <div class="tit">{{ cardItem.title }}</div>
                <img
                  src="@/assets/images/solution/detail/rightIcon.png"
                  style="width: 33px; height: 22px"
                  alt=""
                />
              </div>
              <div class="cards">
                <div v-if="item.type == 5" class="apply_list">
                  <div
                    v-for="(value, key) in item.moduleList"
                    :key="key"
                    class="oneStyle"
                  >
                    <div class="list_top flex">
                      <div class="top_left">
                        <img
                          v-if="value.image"
                          :src="`${value.image}`"
                          alt=""
                        />
                        <img
                          v-else
                          src="@/assets/images/solution/detail/noneData.png"
                          alt=""
                        />
                      </div>
                      <div class="top_right">
                        <div class="tit" @click="toDetail(value)">
                          {{ value.name }}
                        </div>
                        <a-tooltip overlayClassName="tooltip_class" >
                          <template
                            v-if="isShowToolTip(value.summary, 246)"
                            #title
                          >
                            {{ value.summary }}
                          </template>
                          <span class="con">{{ value.summary }}</span>
                        </a-tooltip>
                        <div
                          class="file flex"
                          style="margin-top: 16px"
                          v-if="value.fileName != null"
                        >
                          <img
                            src="@/assets/images/solution/detail/text.png"
                            alt=""
                            style="width: 40px; height: 40px"
                          />
                          <p @click="fileShow(value.filePath)">
                            {{ value.fileName }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div v-else-if="item.type == 8" class="apply_list">
                  <div
                    v-for="(value, key) in item.moduleList"
                    :key="key"
                    class="oneStyle"
                  >
                    <div class="list_top flex">
                      <div class="top_left">
                        <img
                          v-if="value.image"
                          :src="`${value.image}`"
                          alt=""
                        />
                        <img
                          v-else
                          src="@/assets/images/solution/detail/noneData.png"
                          alt=""
                        />
                      </div>
                      <div class="top_right">
                      	<p class="con" v-if="item.type == 8">
			                    <p class="right_name">项目名称：{{ value.projectName }}</p>
			                    <p class="right_name" v-if="value.scale && value.scale != ''">规模：{{ value.scale }}</p>
			                    <p class="right_name" v-if="value.time && value.time != ''">时间：{{ value.time }}</p>
			                    <a-tooltip overlayClassName="tooltip_class">
			                      <template v-if="isShowToolTip(value.projectIntroduction, 136)" #title >
			                          {{ value.projectIntroduction }}  </template>
			                      <span class="right_name caseinfo">{{ value.projectIntroduction}}</span> 
			                    </a-tooltip>
			                  </p>
                        <div
                          class="file flex"
                          style="margin-top: 16px"
                          v-if="value.fileName != null"
                        >
                          <img
                            src="@/assets/images/solution/detail/text.png"
                            alt=""
                            style="width: 40px; height: 40px"
                          />
                          <p @click="fileShow(value.filePath)">
                            {{ value.fileName }}
                          </p>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                <div class="oneCards" v-else>
                  <img
                    v-if="
                      item.moduleList[0].image == '' ||
                      item.moduleList[0].image == null
                    "
                    src="../../../assets/images/ability/adlityDetail/bac.png"
                    alt=""
                  />
                  <img v-else :src="`${item.moduleList[0].image}`" alt="" />
                  <div class="right">
                    <a-tooltip overlayClassName="tooltip_class">
                      <template
                        v-if="isShowToolTip(item.moduleList[0].summary, 246)"
                        #title
                      >
                        {{ item.moduleList[0].summary }}
                      </template>
                      <span class="desc">{{ item.moduleList[0].summary }}</span>
                    </a-tooltip>
                    <div
                      class="file flex"
                      style="margin-top: 16px"
                      v-if="item.moduleList[0].fileName != null"
                    >
                      <img
                        src="@/assets/images/solution/detail/text.png"
                        alt=""
                        style="width: 40px; height: 40px"
                      />
                      <p @click="fileShow(item.moduleList[0].filePath)">
                        {{ item.moduleList[0].fileName }}
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        <div
          class="tab_content"
          id="#download"
          v-if="detailData.fileList && detailData.fileList.length > 0"
        >
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">方案附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
        </div>
        <ul class="list" v-if="detailData.solutionFileList && detailData.solutionFileList.length > 0">
          <li v-for="(item, key) in detailData.solutionFileList" class="li">
            <div class="li_box" @click="fileShow(item.path)">
              <div class="left_box">
                <img
                  src="../../../assets/images/solution/detail/word.png"
                  alt=""
                  @click="doladFile"
                  style="width: 40px; height: 40px"
                />
                <p class="fileText">{{ item.name }}</p>
              </div>
              <img
                src="../../../assets/images/solution/detail/download.png"
                alt=""
                @click.stop="downloadBtn(item)"
                style="cursor: pointer"
              />
            </div>
          </li>
        </ul>
      </div>
      <view-list :id="viewId" :type="viewType"></view-list>
      <img
        class="top"
        src="../../../assets/images/solution/detail/toTap.png"
        alt=""
        @click="scrollUp"
      />
      <div class="bottom"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref } from "vue";
import { isShowToolTip } from "../../../utils/index.js";
import { useRouter, useRoute } from "vue-router";
import {
  getDetail,
  getDownCount,
  cancelCollect,
  collect,
} from "../../../api/solutionNew/detail";
import { pptTopdf } from "../../../api/fileUpload/uploadFile.js";
import { getTradeList } from "../../../api/solutionNew/home";
import { addShoppingCart, toCombinePage } from "../../../api/combine/shoppingCart.js";
import axios from "axios";
import { message } from "ant-design-vue";
import eventBus from "../../../utils/eventBus";
interface FormState {
  name: string;
  code: string;
  categoryId: string | undefined;
  estimatedAmount: string;
  solutionId: string;
  status: number;
}
const route = useRoute();
const viewId = ref(route.query.id);
const viewType = ref("1");
const loadShow = ref(false);
onMounted(() => {
  getData();
});
const functionKey = ref(1);
const isActive = ref(0);
const collectActive = ref(false);
function change(v) {
  isActive.value = v;
}
const downloadBtn = (e) => {
  getDownCount(route.query.id)
    .then((res) => {
      if (res.code == 200) {
        const href = e.url;
        const downName = e.name;
        let windowOrigin = window.location.origin;
				let token = localStorage.getItem("token");
				let newHref = href;
	      if(href.includes(windowOrigin)){
	        newHref = "/portal" + href.split(windowOrigin)[1]
	      }
			  window.open(windowOrigin + newHref + "?token=" + token);
      }
    })
    .catch(() => {
    });
};

const addAI = () => {
	let addParams = {
		schemeId: route.query.id,
		type: "1",
	};
	localStorage.removeItem("seekName");
	toCombinePage({
		conclusion: "谢谢聆听",
		cover: detailData.name,
		list: [addParams],
		source: "2"
	}).then(() => {
		Router.push({
			query: {
				type: 2,
			},
			name: "newAllProject",
		});
	});
}

const fileShow = (val) => {
  loadShow.value = true;
  pptTopdf({
    filePath: val,
  }).then((res) => {
    loadShow.value = false;
    if (res.code == 200) {
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = res.data;
	    if(res.data.includes(windowOrigin)){
	      newHref = "/portal" + res.data.split(windowOrigin)[1]
	    }
      const newpage = Router.resolve({
        name: "lookPdf",
        query: {
          urlMsg: encodeURIComponent(
          	windowOrigin + newHref + "?token=" + token
          ),
          urlName: val.name,
        },
      });
      window.open(newpage.href, "_blank");
    }
  });
  return false;
};
const tabList = ref([]);
const getTarde = () => {
  let tradeParams = {};
  getTradeList(tradeParams).then((result) => {
    result.data.map((item) => {
      tabList.value.push({
        name: item.name,
        id: item.id.toString(),
      });
    });
  });
};
getTarde();

const collectById = () => {
  if (detailData.value.collect == 1) {
    cancelCollect(route.query.id)
      .then(() => {
        message.success("取消收藏成功");
        getData();
      })
      .catch(() => {
      });
  } else {
    collect(route.query.id)
      .then(() => {
        message.success("收藏成功");
        getData();
      })
      .catch(() => {
      });
  }
  collectActive.value = !collectActive.value;
};
const getCurrentAnchor = () => {
  return currentAnchor.value;
};
const toDetail = (value) => {
  Router.push({
    query: {
      id: value.id,
    },
    name: "applyNew",
  });
};
const currentAnchor = ref("#desc");
const back = () => {
	Router.back();
};
// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#desc";
  getCurrentAnchor();
  isActive.value = 0;
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
const detailData = ref({});
const valueData = ref([]);
const add = () => {
  addShoppingCart({
    schemeId: route.query.id,
    type: "1",
  }).then(() => {
    getData();
    eventBus.emit("cartRefresh");
  });
};

const anchorPoints = ref([
  {
    key: "bac",
    href: "#bac",
    title: "政策背景",
    type: 1,
  },
  {
    key: "market",
    href: "#market",
    title: "市场分析",
    type: 2,
  },
  {
    key: "need",
    href: "#need",
    title: "需求分析",
    type: 3,
  },
  {
    key: "desc",
    href: "#desc",
    title: "方案概述",
    type: 4,
  },

  {
    key: "apply",
    href: "#apply",
    title: "应用场景",
    type: 5,
  },
  {
    key: "ability",
    href: "#ability",
    title: "部署方案",
    type: 6,
  },
  {
    key: "team",
    href: "#team",
    title: "合作模式",
    type: 7,
  },
  {
    key: "example",
    href: "#example",
    title: "应用案例",
    type: 8,
  },
]);

const getData = () => {
  if (route.query.id) {
    getDetail(route.query.id).then((res) => {
      anchorList.value = [];
      res.data.createTime = res.data.createTime.slice(0, 10);
      res.data.moduleBody.sort((a, b) => a.type - b.type);
      res.data.moduleBody.forEach((item) => {
        const anchor = anchorPoints.value.find((a) => a.type === item.type);
        if (anchor) {
          anchorList.value.push(anchor);
        }
      });
      if (res.data.solutionFileList.length > 0) {
        anchorList.value.push({
          key: "download",
          href: "#download",
          title: "方案附件",
          type: 9,
        });
      } else {
        res.data.fileList = false;
      }
      res.data.labelName = res.data.labelName.split(",");
      // res.data.provider = res.data.provider.split("/")[1];
      detailData.value = res.data;
      res.data.moduleBody.forEach((element) => {
        if (element.type == 8) {
          let text = element.moduleList[0].summary;
          let caseInfo = {};
          let keywords = {
            name: "项目名称[：:](.*?)\\n",
            size: "规模[：:](.*?)\\n",
            time: "时间[：:](.*?)\\n",
            people: "联系人[：:](.*?)\\n",
            intro: "(案例介绍|项目介绍)[：:](.*)", // 匹配“案例介绍”或“项目介绍”后的内容
          };
          for (let key in keywords) {
            let regex = new RegExp(keywords[key], "s");
            let match = text.match(regex);
            if (match) {
              caseInfo[key] =
                key === "intro"
                  ? (match[2] || "").trim()
                  : (match[1] || "").trim();
            } else {
              caseInfo[key] = "";
              // 如果您不想在找不到匹配时保留该属性，可以取消注释下一行
              // delete caseInfo[key];
            }
          }
          element.caseInfo = caseInfo;
        }
      });
      valueData.value = res.data.moduleBody;
    });
  }
};
const isShow = ref("desc");
const anchorList = ref([]);
const Router = useRouter();
const handleClick = (e, link) => {
  const href = link.href.replace("#", "");
  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);
  srcolls &&
    srcolls.scrollIntoView({
      block: "center",
      behavior: "smooth",
    });
  isShow.value = href;
};
eventBus.on("solutionDetailRefresh", getData);
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>

<style lang="scss">
.label {
  span {
    display: inline-block;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #236cff;
    font-weight: 400;
    font-size: 14px;
    color: #236cff;
    line-height: 25px;
    padding: 0 10px;
  }
}
.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }
  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }
  .ant-modal-content {
    height: 395px;
    padding: 0;
  }
  .ant-form {
    width: 100%;
  }
  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: rgba(214, 228, 255, 0.6);
    border-radius: 2px 2px 2px 2px;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  ::v-deep(.ant-tabs-nav-wrap) {
    margin-top: 16px;
    width: 236px !important;
    // overflow-x: auto;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }
  .ant-tabs-tab {
    // width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d6886 !important;
  }
  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}
.caseinfo {
  display: -webkit-box;
  -webkit-line-clamp: 2; /* 限制为3行 */
  -webkit-box-orient: vertical;
  overflow: hidden; /* 隐藏超出部分 */
  text-overflow: ellipsis;
}
.right_name {
  margin-bottom: 0;
}
::v-deep(.ant-tabs-nav-wrap) {
  margin-top: 16px;
  width: 236px !important;
  overflow-x: auto !important;
  height: 48px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #ffffff;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}
</style>
