<template>
  <file-preview :typeFile="docxFile" />
</template>

<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import filePreview from "@/components/filePreview/index.vue";
import { useRoute } from "vue-router";

export default defineComponent({
  components: {
    filePreview,
  },

  setup() {
    const route = useRoute();
    const docxFile = ref("");
    const data = reactive({});

    const getData = () => {
      docxFile.value = route.query.docxFile;
    };
    getData();
    return {
      ...toRefs(data),
      docxFile,
      route,
    };
  },
});
</script>