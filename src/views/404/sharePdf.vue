<!-- 分享链接用 -->
<template>
  <div class="box">
  	<iframe :src="srcUrl" width="100%" height="100%"></iframe>
  </div>
</template>
<script setup>
import { onMounted, ref, computed, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute()

const srcUrl = ref("")
onMounted(() => {
  if(route.query.urlMsg){
  	srcUrl.value = decodeURIComponent(route.query.urlMsg)
  }
//if(route.query.urlName){
//	document.title = route.query.urlName
//}
});
</script>

<style lang="scss">
.box {
	width: 100vw;
	height: 100vh;
}
</style>