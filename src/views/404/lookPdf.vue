<template>
  <div class="box">
  	<iframe :src="srcUrl" width="100%" height="100%"></iframe>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed, toRaw, UnwrapRef, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";

const router = useRouter();
const route = useRoute()

const srcUrl = ref("")
onMounted(() => {
  if(route.query.urlMsg){
  	srcUrl.value = decodeURIComponent(route.query.urlMsg) + "&&v=" + new Date().getTime()/1000
  }
//if(route.query.urlName){
//	document.title = route.query.urlName
//}
});
</script>

<style lang="scss">
.box {
	width: 100vw;
	height: 100vh;
}
</style>