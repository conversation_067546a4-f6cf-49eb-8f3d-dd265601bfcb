<template>
  <div class="working">
    <img src="@/assets/images/login/ing.png" alt="" width="420" height="360" />
    <p class="text">功能正在建设中。。。</p>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";

export default defineComponent({
  setup() {
    const data = reactive({});
    return {
      ...toRefs(data),
    };
  },
});
</script>

<style lang="scss" scoped>
.working {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
  text-align: center;
}
.text {
  font-size: 18px;
  color: #84899a;
}
</style>