<template>
  <div class="topImage">
    <div class="topImage_content">
      <div class="topImage_title" >能力图谱</div>
      <a-tag class="top_tag">个性化</a-tag>
      <a-tag class="top_tag">高性能</a-tag>
      <div class="topImage_details">
        为满足不同行业客户的特定需求，我们将网络、云、平台、应用和终端等多种技术融合在一起，贯穿整个销售、交付和服务等全流程。通过能力汇聚管理重点支撑能力的生产使用和属地化运营，通过汇聚全网能力、属地能力、生态能力，支撑集中调用、客户侧部署等多类能力的使用和计量，同步建立属地运营管理流程。可独立提供全流程服务，并支持快速组合调用，为客户提供更加个性化、高效的能力支持服务。
      </div>
    </div>
  </div>
  <div class="card_content flex">
    <div
      class="card_dec"
      style="
         {
          backgroundimage: `url('@/assets/images/ability/mask.png');
        }
      "
    >
      <img class="count_img" src="@/assets/images/ability/card1.png" alt="" />
      <div class="line1">
        <span>能力累计</span><span class="simplified_text">上架数 </span>
        <div>
          <span class="main_count"> {{ countList[0].number }}</span
          ><span class="unit"> 个 </span>
        </div>
      </div>
      <p class="line2">
        <span class="sub_text">近七天发能力数</span>
        <span class="sub_count"> {{ countList[0].num }}</span> <span> 个</span>
      </p>
    </div>

    <div class="card_dec">
      <img class="count_img" src="@/assets/images/ability/card2.png" />
      <div class="line1">
        <span class="simplified_text">能力查阅</span><span>总次数 </span>
        <div>
          <span class="main_count">{{ countList[1].number }}</span
          ><span class="unit"> 次 </span>
        </div>
      </div>
      <div class="line2">
        <span class="sub_text">近七天查阅数量</span>
        <span class="sub_count"> {{ countList[1].num }}</span> <span> 次</span>
      </div>
    </div>

    <div class="card_dec">
      <img class="count_img" src="@/assets/images/ability/card3.png" />
      <div class="line1">
        <span>能力累计</span><span class="simplified_text">支撑项目数 </span>
        <div>
          <span class="main_count"> {{ countList[2].number }}</span
          ><span class="unit"> 个 </span>
        </div>
      </div>
      <p class="line2">
        <span class="sub_text">近七天支撑项目数 </span>
        <span class="sub_count"> {{ countList[2].num }} </span>
        <span> 个</span>
      </p>
    </div>

    <div class="card_dec" @click="countAdd">
      <img class="count_img" src="@/assets/images/ability/card4.png" />
      <div class="line1">
        <span>能力应用项目累计</span
        ><span class="simplified_text">签约额 </span>
        <div>
          <span class="main_count">{{ countList[3].number }}</span
          ><span class="unit"> 万 </span>
        </div>
      </div>
      <p class="line2">
        <span class="sub_text">近七天签约额</span>
        <span class="sub_count"> {{ countList[3].num }} </span> <span> 万</span>
      </p>
    </div>
  </div>

  <a-modal
    :visible="previewVisible"
    @cancel="closeModal2"
    :width="600"
    @ok="handleOk"
    :destroyOnClose="true"
    :maskClosable="false"
  >
    <div ref="add">
      累计上架数量：<a-input v-model:value="virtually.listingNum" />
      7天发布数量：
      <a-input v-model:value="virtually.abilityNum" /> 支撑项目数：
      <a-input v-model:value="virtually.projectNum" /> 7天支撑项目数：<a-input
        v-model:value="virtually.projectFrequency"
      />
      签约额：<a-input v-model:value="virtually.contractVolume" /> 7天签约额：
      <a-input v-model:value="virtually.recentSignAmount" />
    </div>
  </a-modal>

  <!-- 能力列表 -->
  <table-list ref="tableListRef" />
</template>

<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import tableList from "./components/tableList.vue";
import { getCount, countUpdate } from "@/api/ability/home";


export default defineComponent({
  name: "topContent",
  components: {
    tableList,
  
  },
  setup() {
    const tableListRef = ref();
    const data = reactive({
      previewVisible: false,
      virtually: {},
      countsList: [],
      visible: false,
      countClick: 0,
      countList: [
        {
          number: "",
          num: "",
          title: "能力累计上架数",
          textDec: "近七天发布能力数",
        },
        {
          number: "",
          num: "",
          title: "能力查阅总次数",
          textDec: "近七天查阅数",
        },
        {
          number: "",
          num: "",
          title: "能力累计支撑项目数",
          textDec: "近七天支撑项目数",
        },
        {
          number: "",
          num: "",
          title: "能力应用项目累计签约额",
          textDec: "近七天签约额",
        },
      ],
    });

   
    // 个数修改
    const countAdd = () => {
      data.countClick++;
      if (data.countClick == 3) {
        data.previewVisible = true;
        data.countClick = 0;
      }
    };
    const handleOk = () => {
      data.virtually.id = 1;
      countUpdate(data.virtually).then((res) => {
        data.previewVisible = false;
        getCountDate();
      });
    };



    const getCountDate = () => {
      getCount().then((res) => {
        for (let i = 0; i < data.countList.length; i++) {
          for (let j = 0; j < res.data.length; j++) {
            if (data.countList[i].title in res.data[j]) {
              let value1 = res.data[j][data.countList[i].title];
              let value2 = res.data[j][data.countList[i].textDec];
              data.countList[i].number = value1;
              data.countList[i].num = value2.substring(0, value2.length - 1);
            }
          }
        }
      });
    };
    getCountDate();
  
    const closeModal2 = () => {
      data.previewVisible = false;
    };

    return {
      ...toRefs(data),
      countAdd,
      handleOk,

      closeModal2,
    };
  },
});
</script>

<style lang="scss" scoped>
.topImage {
  width: 100%;
  height: 400px;
  background-image: url("@/assets/images/ability/topBg.jpg");
  background-repeat: no-repeat;
  background-size: cover;

  .topImage_content {
    padding: 63px 0 16px 0;
    margin: 0 auto;
    width: 80%;

    .topImage_title {
      font-weight: bold;
      font-size: 42px;
      color: #122c6c;
      margin-bottom: 26px;
    }

    .topImage_details {
      width: 640px;
      font-size: 14px;
      color: #2b3f66;
      line-height: 24px;
      margin-top: 8px;
    }

    .top_tag {
      border-color: #236cff;
      color: #236cff;
      border-radius: 0;
    }
  }
}

.card_content {
  width: 80%;
  margin: -40px auto 0 auto;
  justify-content: space-between;

  .card_dec {
    width: 23%;
    background: #fff;
    box-shadow: 0px 4px 19px 0px rgba(63, 68, 74, 0.08);
    border-radius: 6px;
    padding-bottom: 12px;
    background-size: cover;
    background-repeat: no-repeat;

    .count_img {
      display: block;
      margin: 20px auto;
      width: 62px;
      height: 62px;
    }
  }

  .line1,
  .line2 {
    text-align: center;
    padding-bottom: 10px;
    margin: 10px 35px;
  }

  .line1 {
    font-family: Source Han Sans SC, Source Han Sans SC;
    font-size: 16px;
    color: rgba(0, 0, 0, 0.55);
    border-bottom: 1px dashed #125688;
  }

  .line2 {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.35);
  }

  .simplified_text {
    font-weight: 600;
    font-size: 16px;
    color: #24456a;
  }

  .sub_text :hover {
    cursor: pointer;
    color: rgba(0, 0, 0, 0.65);
  }

  .main_count {
    font-weight: 600;
    font-family: PingFang SC, PingFang SC;
    font-size: 20px;
    color: #24456a;
    display: inline-block;
    margin-left: 8px;
  }

  .unit {
    font-family: PingFang SC, PingFang SC;
    font-weight: 400;
    font-size: 12px;
    color: rgba(0, 0, 0, 0.55);
  }

  .sub_count {
    font-size: 16px;
    font-weight: 600;
    color: rgba(36, 69, 106, 0.45);
    display: inline-block;
    margin-left: 6px;
  }
}

.totaoText {
  font-weight: bold;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
}
</style>