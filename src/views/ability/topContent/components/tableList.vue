
<template >
  <div id="ability_list">
    <div class="searchInfo">
      <div class="vocationPull">
        <a-select
          :value="pageParams.provider"
          style="width: 180px"
          :bordered="false"
          @change="companyChange"
        >
          <a-select-option
            v-for="item in companyListSelect"
            :key="item.id"
            :value="item.companyName"
            >{{ item.companyName }}</a-select-option
          >
          <template #suffixIcon>
            <img
              src="@/assets/images/solution/home/<USER>"
              style="width: 16px; height: 16px; margin-top: -5px"
            />
          </template>
        </a-select>
        <div class="lines"></div>
        <a-input
          v-model:value="pageParams.keyword"
          allow-clear
          @keyup.enter="getList"
          :bordered="false"
          placeholder="请输入名称、概述、场景关键字进行检索"
          class="inputClass"
        />

        <div class="seekInfo" @click="getList">
          <img src="@/assets/images/home/<USER>" />
          <div>搜索</div>
        </div>
      </div>
    </div>

    <div class="tabContent">
      <div class="tabModel">
        <a-divider type="vertical" class="divider" />
        <a-tree
          class="treeMenu"
          :tree-data="treeData"
          v-if="treeData.length"
          default-expand-all
          v-model:expandedKeys="expandedKeys"
          v-model:selectedKeys="selectedKeys"
          v-model:checkedKeys="checkedKeys"
          @select="changeNode"
        >
          <template v-slot:title="treeItem">
            <div :class="[{ selectedTree: treeItem.selected }]">
              <span
                :class="{
                  parentType: treeItem.key == '01' || treeItem.key == '02',
                }"
                >{{ treeItem.title }}
                <span v-if="treeItem.key == '01' || treeItem.key == '02'"
                  >({{ treeItem.count }})</span
                ></span
              >
              <img
                class="active_tree"
                v-show="selectedKeys[0] == treeItem.key"
                src="@/assets/images/ability/menu_icon.svg"
              />
            </div>
          </template>
        </a-tree>
      </div>
      <div
        v-if="!loading && tableList && tableList.length > 0"
        style="width: 100%"
      >
        <div class="cardContent">
          <template v-for="(item, index) in tableList" :key="index">
            <div
              :class="[
                'card_content',
                {
                  cardActive: cardActive == index,
                },
              ]"
              @mouseenter="contentColor(index)"
              @mouseleave="contentLeave"
              @click="proDetail(item)"
            >
              <a-image
                width="100%"
                height="150"
                :preview="false"
                v-if="item.abilityPictureUrl"
                :src="`${item.abilityPictureUrl}`"
              />
              <img
                src="@/assets/images/ability/empty.jpg"
                style="width: 100%; height: 160px"
                v-else
              />
              <div class="card_center flex-1">
                <div class="card_text">
                  <div class="card_tag">
                    <div class="card_title">{{ item.abilityName }}</div>
                  </div>
                  <a-tag
                    :bordered="false"
                    :class="{
                      cityStyle: true,
                      common: item.abilityType == '通用能力',
                      industry: item.abilityType == '行业能力',
                    }"
                    >{{ item.abilityType.slice(0, 2) }}</a-tag
                  >
                </div>
                <div class="card_des">
                  {{ item.abilityDesc }}
                </div>
                <div class="footer">
                  <a-tag color="#D7E6FF">{{ item.abilityAreas }}</a-tag>
                  <div>
                    <img
                      src="@/assets/images/ability/browse.svg"
                      style="width: 15px; height: 15px; margin-top: 2px"
                    />
                    <span class="browse"> {{ item.viewsNum }}</span>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
        <div class="layPage">
          <a-pagination
            v-model:pageSize="pageParams.pageSize"
            v-model:current="pageParams.pageNo"
            show-quick-jumper
            :total="totalItemCount"
            :show-total="(total) => `共 ${totalItemCount} 条`"
            @change="pageChange"
            @showSizeChange="sizeChange"
            class="mypage"
          />
        </div>
      </div>
      <div
        v-if="!loading && tableList && tableList.length == 0"
        class="emptyPhoto"
      >
        <img src="@/assets/images/solution/home/<USER>" />
      </div>
      <a-spin v-if="loading" class="loading" />
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, toRaw } from "vue";
import { useRouter } from "vue-router";
import { getProjectList, treeCount, getCompanyList } from "@/api/ability/home";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import {  cloneDeep } from "lodash-es";
import menuList from "./menuList";
import { getMakeUrl } from "@/utils/getUrl";
import checkModel from "../../../ability/detail/check.vue";
export default defineComponent({
  components: {
    checkModel,
  },
  setup() {
    const baseURL = getMakeUrl();
    const vocation = ref("");
    const data = reactive({
      documentsList: [],
      deleteShow: false,
      pageParams: {
        pageNo: 1,
        pageSize: 9,
        provider: "",
        keyword: "",
        abilityAreas: "",
      },
      companyListSelect: [],
      searchHistory: "",
      expandedKeys: ["0", "01", "02"],
      checkedKeys: [],
      selectedKeys: ["0"],
      cardActive: "-1",
      totalItemCount: 0,

      treeData: [],
      tableList: [],
    });
    const getList = () => {
      data.loading = true;
        let params = cloneDeep(data.pageParams);
 
      params.provider = params.provider === "全部提供方" ? "" : params.provider;
      getProjectList(params).then((res) => {
        data.loading = false;
        data.tableList = [];
        data.tableList = res.data.rows;
        data.totalItemCount = res.data.totalRows;
      });
    };
    const company = () => {
      getCompanyList().then((res) => {
        data.companyListSelect = Object.values(res.data);
        data.companyListSelect.unshift({
          id: "0",
          companyName: "全部提供方",
        });
        data.pageParams.provider = "全部提供方";
      });
    };
    company();
    const cancel = (e) => {};
    const geTreeCount = () => {
      treeCount().then((res) => {
        menuList[0].children[0].count = res.data.industryTotal;
        menuList[0].children[1].count = res.data.universalTotal;
        data.treeData = menuList;
      });
    };
    geTreeCount();
    getList();
    const companyChange = (val) => {
      data.pageParams.provider = val;
      getList();
    };
    const changeNode = (selectedKeys, { selected: bool, selectedNodes }) => {
      if (selectedNodes.length > 0) {
        data.pageParams.abilityType = data.pageParams.abilityAreas = "";
        if (selectedKeys[0] == "01" || selectedKeys[0] == "02") {
          data.pageParams.abilityType = selectedNodes[0].props.dataRef.title;
        } else {
          data.pageParams.abilityAreas =
            selectedNodes[0].props.dataRef.title == "全部"
              ? ""
              : selectedNodes[0].props.dataRef.title;
        }
        data.pageParams.pageNo = 1;
      } else {
        data.pageParams.abilityType = data.pageParams.abilityAreas = "";
      }
      getList();
    };
    const expand = (expandedKeys, { expanded: bool, node }) => {};

    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (val) => {
      router.push({
        query: {
          id: val.id,
        },
        name: "abilityDetail",
      });
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };
    const pageChange = (page, pageSize) => {
      data.pageParams.pageNo = page;
      getList();
    };

    const sizeChange = (current, size) => {
      data.pageParams.pageSize = size;
    };

    return {
      ...toRefs(data),
      vocation,
      cancel,
      companyChange,
      changeNode,
      expand,
      contentColor,
      contentLeave,
      proDetail,
      router,
      pageChange,
      sizeChange,
      zhCN,
      company,
      getList,
      baseURL,
    };
  },
});
</script>

<style lang="scss" scoped src="./tableList.scss"></style>

<style  lang="scss">
.ant-modal-header {
  height: 55px;
  background-image: url("../../../../assets/images/ability/alertBgc.png");
  border-bottom: 0;
  background-size: 100% 100%;
  .top {
    margin-top: 16px;
    margin-bottom: 24px;
    position: relative;
    .tit {
      font-weight: bold;
      font-size: 24px;
      color: #24456a;
      line-height: 28px;
      margin-right: 8px;
    }
    .num {
      display: inline-block;
      font-weight: 400;
      background: #d7e6ff;
      border-radius: 4px 4px 4px 4px;
      font-size: 14px;
      color: #236cff;
      line-height: 16px;
      padding: 4px 8px;
      position: absolute;
      bottom: 2px;
    }
  }
  .ant-input-affix-wrapper {
    border-radius: 6px;
  }
}
.ant-table-body {
  .ant-table-thead {
    tr {
      th {
        background: #f4f8ff;
        font-weight: 400;
        font-size: 14px;
        line-height: 16px;
        color: #8c98a6;
      }
    }
  }
}
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  :deep(.ant-select-selector) {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #2e7fff;
    border-radius: 0;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #2e7fff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

.treeMenu {
  font-size: 16px;
  margin-left: 5px;

  .ant-tree-list-holder-inner div:first-child .ant-tree-switcher {
    display: none;
  }

  .ant-tree-treenode {
    width: 180px;
    height: 45px;
    line-height: 45px;
    position: relative;
    cursor: pointer;
  }

  .ant-tree-treenode:hover {
    background: #f0f2fe !important;
  }

  .ant-tree-node-content-wrapper {
    width: 80%;
    padding: 9px 0 0 8px;
    height: 36px !important;
    line-height: 36px !important;
    margin-top: -6px !important;
  }

  .ant-tree-node-content-wrapper:hover {
    background-color: transparent !important;
  }

  .drop_down {
    position: absolute;
  }

  .ant-tree-switcher_close {
    background: url("@/assets/images/ability/close.svg") no-repeat !important;

    i {
      display: none;
    }
  }

  .ant-tree-switcher_open {
    background: url("@/assets/images/ability/expand.svg") no-repeat !important;

    i {
      display: none;
    }
  }

  .ant-tree-switcher {
    top: 12px;
    width: 18px;
    height: 18px;

    svg {
      display: none;
    }
  }

  li:first-child > .ant-tree-switcher_open:first-child {
    display: none;
  }
  li:first-child > ul > li:first-child > .ant-tree-switcher_open {
    display: block;
    position: absolute;
    top: 47px;
  }
  li:first-child > ul > li:first-child > .ant-tree-node-content-wrapper-open {
    margin-left: 24px;
  }
  li:first-child > .ant-tree-switcher_close:first-child {
    display: none;
  }
  li:first-child > ul > li:first-child > .ant-tree-switcher_close {
    display: block;
    position: absolute;
    top: 47px;
  }
  li:first-child > ul > li:first-child > .ant-tree-node-content-wrapper-close {
    margin-left: 24px;
  }
  .ant-tree-treenode-selected > .ant-tree-node-content-wrapper {
    background: linear-gradient(
      90deg,
      rgba(15, 84, 244, 0.2) 0%,
      rgba(217, 231, 255, 0) 100%
    ) !important;
    height: 36px;
    line-height: 36px;
  }
  .ant-tree-node-selected {
    color: #236cff !important;
  }

  .ant-tree-node-selected {
    background: none !important;

    :hover {
      background: none !important;
    }
  }
}
</style>