<template>
  <a-checkbox-group v-model:value="checkedList">
    <a-checkbox
      v-for="(item, key) in dataSource"
      :key="item.value"
      :value="item.value"
    >
      <img
        class="model-image"
        src="../../../assets/images/ability/adlityDetail/word.png"
        alt=""
      />
      <span v-if="item.label.length >= 25">
        <a-tooltip>
          <template #title>{{ item.label}}</template>
          {{ item.label.slice(0, 25) }}...
        </a-tooltip>
      </span>
      <span v-else>{{ item.label }} </span>
      <span class="detail" @click="fileShow(item)">详情</span>
    </a-checkbox>
  </a-checkbox-group>
  <div class="foot">
    <span class="all" @click="selectAll">
      <a-checkbox
        v-model:checked="checkAll"
        :indeterminate="indeterminate"
        @change="onCheckAllChange"
      >
        全选</a-checkbox
      ></span
    >
    <span class="null" @click="handleInvertCheckChange">反选</span>
    <span class="down" @click="loadFile">下载</span>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, toRefs, watch, onMounted, ref } from "vue";
import axios from "axios";
import { message } from "ant-design-vue";
import { getMakeUrl } from "../../../utils/getUrl";
import { useRouter } from "vue-router";
const plainOptions = [];
export default defineComponent({
  props: {
    data: Array,
  },

  setup(props) {
    const state = reactive({
      indeterminate: false,
      checkAll: false,
      checkedList: [],
    });
    const onCheckAllChange = (e: any) => {
      if (e.target.checked) {
        state.checkedList = dataSource.value.map((item) => item.value);
        state.indeterminate = true;
      } else {
        state.checkedList = [];
        state.indeterminate = false;
      }
    };
    const baseURL = getMakeUrl();
    const loadFile = () => {
      state.checkedList.forEach((ele) => {
    
        dataSource.value.forEach((element) => {
          if (element.value == ele) {
            const href = baseURL + ele;
            window.open(href);
            
//          const downName = element.label;
//          axios
//            .get(href, { responseType: "blob" })
//            .then((res) => {
//              const blob = new Blob([res.data]);
//              const link = document.createElement("a");
//              link.href = URL.createObjectURL(blob);
//              link.download = downName;
//              link.click();
//              URL.revokeObjectURL(link.href);
//            })
//            .catch(console.error);
          }
        });
      });
    };
    // 预览
    const Router = useRouter();
    const fileShow = (val) => {
      let typeFile = val.value.split(".").pop();
      if (typeFile == "ppt" || typeFile == "pptx") {
        message.warning("该文件类型暂不支持在线预览");
        return;
      }
      let previewUrl = Router.resolve({
        name: "viewFile",
        query: {
          docxFile: `${baseURL}${val.value}`,
        },
      });
      window.open(previewUrl.href, "_blank");
    };
    const handleInvertCheckChange = () => {
      const reverseInstitute = [];
      dataSource.value.forEach((v) => {
        const index = state.checkedList.findIndex((x) => x === v.value);
        if (index === -1) {
          reverseInstitute.push(v.value);
        }
      });
      state.checkedList = reverseInstitute;
    };
    const dataSource = ref([]);
    const getData = () => {
      props.data.forEach((ele: any) => {
        dataSource.value.push({
          label: ele.name,
          value: ele.path,
        });
      });
    };
    onMounted(() => {
      getData();
    });
    watch(
      () => state.checkedList,
      (val) => {
        state.indeterminate =
          !!val.length && val.length < dataSource.value.length;
        state.checkAll = val.length === dataSource.value.length;
      }
    );

    return {
      ...toRefs(state),
      props,
      dataSource,
      plainOptions,
      onCheckAllChange,
      loadFile,
      handleInvertCheckChange,
      baseURL,
      fileShow,
      Router,
    };
  },
});
</script>

<style lang="scss" scoped>
.ant-checkbox-group {
  width: 100%;
  padding-left: 20px;
  ::v-deep(.ant-checkbox-wrapper) {
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    width: 45%;
    display: inline-block;
    padding: 4px 24px 4px 24px;
    margin-bottom: 24px;
    ::v-deep(.ant-checkbox::after) {
      background-image: url("../../../assets/images/ability/adlityDetail/word.png");
    }
  }
}
.model-image {
  width: 60px;
  height: 60px;
}
.detail {
  margin-left: 16px;
  display: inline-block;
  color: #39cc85;
}
.foot {
  margin-top: 20px;
  text-align: center;
  span {
    width: 77px;
    display: inline-block;
    height: 31px;
    font-size: 16px;
    line-height: 29px;
    color: #ffffff;
    margin: 0 16px;
    cursor: pointer;
  }
  .all {
    background-color: #1863fa;
    label {
      color: #ffffff;
    }
  }
  .null {
    background-color: gray;
  }
  .down {
    background-color: #ffbf00;
  }
}
</style>
