<template>
  <div class="contents">
    <div class="dispatch-center">
      <div class="top-summary">
        <div class="card-row">
          <div class="card pending">
            <div class="content">
              <div class="number">
                {{ todoCount }}<span class="unit">个</span>
              </div>
              <div class="title">待办工单</div>
            </div>
          </div>
          <div class="card processed">
            <div class="content">
              <div class="number">
                {{ finishedCount }}<span class="unit">个</span>
              </div>
              <div class="title">已办工单</div>
            </div>
          </div>
          <div class="card scheduled">
            <div class="content">
              <div class="number">
                {{ scheduleCount }}<span class="unit">个</span>
              </div>
              <div class="title">调度工单</div>
            </div>
          </div>
        </div>

        <!-- <div class="chart-row">
          <div class="bar-chart-container">
            <BarChart :weekData="weekData" />
          </div>
          <div class="pie-chart-container">
            <div class="pie-chart-title">
              <a-tabs
                v-model:activeKey="activePieTab"
                @change="handlePieTabChange"
              >
                <a-tab-pane key="preSale" tab="售前工单"></a-tab-pane>
                <a-tab-pane key="inWork" tab="售中工单"></a-tab-pane>
                <a-tab-pane key="afterSale" tab="售后工单"></a-tab-pane>
              </a-tabs>
            </div>
            <PieChart :data="pieChartData" :tab-type="activePieTab" />
          </div>
        </div> -->
      </div>

      <div class="sale-tabs">
        <PreSale />
        <!-- <a-tabs
          v-model:activeKey="activeSaleType"
          @change="handleSaleTypeChange"
        >
          <a-tab-pane key="preSale" tab="售前调度"></a-tab-pane>
          <a-tab-pane key="midSale" tab="售中调度"></a-tab-pane>
        </a-tabs> -->
        <!-- <component :is="currentSaleComponent" /> -->
      </div>
    </div>
  </div>
</template>
<script setup>
import { ref, computed, onMounted, watch, provide } from "vue";
import PreSale from "./components/PreSale.vue";
import MidSale from "./components/MidSale.vue";
// import BarChart from "./components/BarChart.vue";
// import PieChart from "./components/PieChart.vue";
import { getStatisticsData } from "@/api/dispatchCenter/backlog&completed.js";
defineOptions({
  name: "DispatchCenter",
});
/*

  统计数据处理

*/

//饼状图（
// const activePieTab = ref("preSale");

const statistics = ref({
  preTodoCount: 0, // 售前待办
  preFinishedCount: 0, // 售前已办
  midTodoCount: 0, // 售中待办
  midFinishedCount: 0, // 售中已办
  afterTodoCount: 0, // 售后待办
  afterFinishedCount: 0, // 售后已办
  // weeks: "", // 近一周工单数
});
const fetchData = async () => {
  try {
    const res = await getStatisticsData();
    if (res.code === 200 && res.data) {
      statistics.value = res.data;
    } else {
      console.error("获取数据失败:", res.msg);
    }
  } catch (error) {
    console.error("接口请求异常:", error);
  }
};

const todoCountReal = computed(() => {
  return (
    statistics.value.preTodoCount +
    statistics.value.midTodoCount +
    statistics.value.afterTodoCount
  );
});
const finishedCountReal = computed(() => {
  return (
    statistics.value.preFinishedCount +
    statistics.value.midFinishedCount +
    statistics.value.afterFinishedCount
  );
});
const scheduleCountReal = computed(() => {
  return todoCountReal.value + finishedCountReal.value;
});

// 处理饼图 tabs 切换
// const handlePieTabChange = (key) => {
//   activePieTab.value = key;
// };
const todoCount = ref(0);
const finishedCount = ref(0);
const scheduleCount = ref(0);
function animateCount(targetRef, endValue, duration = 500) {
  const startValue = targetRef.value;
  const range = endValue - startValue;
  if (range === 0) return;
  const startTime = performance.now();

  function step(currentTime) {
    const elapsed = currentTime - startTime;
    if (elapsed < duration) {
      const progress = elapsed / duration;
      targetRef.value = Math.floor(startValue + range * progress);
      requestAnimationFrame(step);
    } else {
      targetRef.value = endValue;
    }
  }
  requestAnimationFrame(step);
}
watch(
  () => todoCountReal.value,
  (newVal) => {
    animateCount(todoCount, newVal);
  }
);
watch(
  () => finishedCountReal.value,
  (newVal) => {
    animateCount(finishedCount, newVal);
  }
);
watch(
  () => scheduleCountReal.value,
  (newVal) => {
    animateCount(scheduleCount, newVal);
  }
);

// 近一周工单数传给柱状图
// const weekData = computed(() => statistics.value.weeks || {});

// 根据 activePieTab 返回对应售前/售中/售后代办及已办数据，用于饼状图
// const pieChartData = computed(() => {
//   let todo = 0,
//     finished = 0;
//   switch (activePieTab.value) {
//     case "preSale":
//       todo = statistics.value.preTodoCount;
//       finished = statistics.value.preFinishedCount;
//       break;
//     case "inWork":
//       todo = statistics.value.midTodoCount;
//       finished = statistics.value.midFinishedCount;
//       break;
//     case "afterSale":
//       todo = statistics.value.afterTodoCount;
//       finished = statistics.value.afterFinishedCount;
//       break;
//   }
//   return {
//     todo,
//     finished,
//     total: todo + finished,
//   };
// });
/*

  统计数据处理

*/
// 当前激活的Tab

// const STORAGE_KEY_SALE_TYPE = "dispatch_center_activeSaleType"

// const activeSaleType  = ref(sessionStorage.getItem(STORAGE_KEY_SALE_TYPE) || "preSale");
// const currentSaleComponent = computed(() => {
//   if (activeSaleType.value === "preSale") return PreSale;
//   else return MidSale;
// });
// 处理售前/售中切换
// const handleSaleTypeChange = (key) => {
//   activeSaleType.value = key;
//   sessionStorage.setItem(STORAGE_KEY_SALE_TYPE, key);
// };
provide('refreshDashboard', fetchData);
onMounted(() => {
  fetchData();
});
</script>
<style lang="scss" scoped>
.contents {
  background-image: url("@/assets/images/dispatchCenter/dispatchCenterBg.png");
  background-attachment: fixed;
  background-size: cover;
  min-height: 100vh;
}
.dispatch-center {
  width: 1200px;
  margin: 0 auto;
  padding: 20px 0;
  .top-summary {
    display: grid;
    grid-template-columns: repeat(6, 1fr);
    grid-column-gap: 20px;
    grid-row-gap: 20px;
    .card-row {
      display: contents;

      .card {
        grid-column: span 2;
        position: relative;
        border-radius: 6px;
        overflow: hidden;

        &::before {
          content: "";
          position: absolute;
          inset: 0;
          background-repeat: no-repeat;
          background-size: cover;
          background-position: center;
          z-index: 0;
        }

        > .content {
          position: relative;
          z-index: 1;
          padding: 25px;
        }
        .number {
          font-size: 40px;
          font-weight: bold;
          color: #2e7fff;
          text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
          display: inline-flex;
          align-items: baseline;
          gap: 4px;
        }
        .number .unit {
          font-weight: 400;
          font-size: 18px;
          color: rgba(35, 61, 91, 0.65);
          line-height: 22px;
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
        .title {
          font-weight: bold;
          font-size: 18px;
          color: rgba(0, 0, 0, 0.85);
          text-align: left;
          font-style: normal;
          text-transform: none;
        }
      }
      // 待办卡片
      .pending::before {
        background-image: url("@/assets/images/dispatchCenter/pendingBg.png");
      }

      // 已办卡片
      .processed::before {
        background-image: url("@/assets/images/dispatchCenter/completedBg.png");
      }

      // 调度卡片
      .scheduled::before {
        background-image: url("@/assets/images/dispatchCenter/dispatchBg.png");
      }
    }

    // .chart-row {
    //   display: contents;

    //   .bar-chart-container {
    //     grid-column: span 4;
    //     background: linear-gradient(138deg, #ffffff 0%, #e9f0ff 100%);
    //     border-radius: 8px;
    //     border: 2px solid #ffffff;
    //     height: 314px;
    //     box-sizing: border-box;
    //   }
    //   .pie-chart-container {
    //     grid-column: span 2;
    //     background: linear-gradient(138deg, #ffffff 0%, #e9f0ff 100%);
    //     border-radius: 8px;
    //     border: 2px solid #ffffff;
    //     height: 314px;
    //     box-sizing: border-box;
    //   }
    //   .pie-chart-title {
    //     padding: 0 10px;
    //   }
    // }
  }

  .sale-tabs {
    background: #fff;
    padding: 10px;
    border-radius: 8px;
    margin: 20px 0;
  }

  .table-area {
    // padding: 20px;
    margin-top: 8px;
  }
}
</style>


