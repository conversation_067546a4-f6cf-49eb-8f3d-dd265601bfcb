<template>
  <div>
    <div class="content bg_fff">
      <qilin-table
        ref="tableRef"
        v-model:tableConfig="tableConfig"
        :loading="tableLoading"
        @changeCurrentPage="pageChange"
        @changeCurrentSize="sizeChange"
      >
        <template #title="slotData">
          <div
            v-html="slotData.data.scope.row.title || '-'"
            class="ellipsis"
          ></div>
        </template>
        <template #type="slotData">
          {{ getTypeName(slotData.data.scope.row.type) }}
        </template>
        <template #step="slotData">
          {{ stepChange(slotData.data.scope.row.auditStep) }}
        </template>
        <template #dealName="slotData">
          <a-popover title="" :trigger="['hover', 'focus']">
            <template #content>
              <p style="margin-bottom: 0">
                联系电话：{{ dealData(slotData.data.scope.row.dealPhone) }}
              </p>
              <p style="margin-bottom: 0">
                所属部门：{{ dealData(slotData.data.scope.row.dealDeptName) }}
              </p>
            </template>
            {{ dealData(slotData.data.scope.row.dealName) }}
          </a-popover>
        </template>
        <template #businessType="slotData">
          {{
            getBusinessTypeName(
              slotData.data.scope.row.type,
              slotData.data.scope.row.businessType
            )
          }}
        </template>
        <template #status="slotData">
          <a-tag
            v-if="slotData.data.scope.row.status === 0"
            class="tag timePublish font_14"
          >
            审核中
          </a-tag>
          <a-tag v-else class="isPublish"> 已审核 </a-tag>
        </template>
        <template #provider="slotData">
          {{ providerWith(slotData.data.scope.row.provider) }}
        </template>
        <template #action="slotData">
          <span
            class="font_0C70EB margin_r_12"
            @click="toDetail(slotData.data.scope.row)"
          >
            评分
          </span>
        </template>
      </qilin-table>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import QilinTable from "../components/table.vue";
import { useRouter } from "vue-router";
import { getList} from "@/api/ticket/ticket.js";
import eventBus from "@/utils/eventBus";

export default defineComponent({
  name: "waitWork",
  props: {
    searchParams: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    QilinTable,
  },

  setup(props) {
    const Router = useRouter();
    const getUserInfo = JSON.parse(window.localStorage.getItem("userInfo"));
    const data = reactive({
      tableConfig: {
        headerConfig: [
          {
            label: "工单标题",
            prop: "title",
            type: "slot",
            slotName: "title",
            align: "center",
            ellipsis: true,
            width: 360,
          },
          {
            label: "工单类型",
            prop: "type",
            type: "slot",
            slotName: "type",
            align: "center",
            width: 160,
          },
          {
            label: "工单流程",
            prop: "step",
            type: "slot",
            slotName: "step",
            align: "center",
            width: 100,
          },
          {
            label: "业务模块",
            prop: "businessType",
            type: "slot",
            slotName: "businessType",
            align: "center",
            width: 160,
          },
          {
            label: "工单状态",
            prop: "status",
            type: "slot",
            slotName: "status",
            align: "center",
            width: 85,
          },
          {
            label: "处理人",
            prop: "dealName",
            type: "slot",
            slotName: "dealName",
            align: "center",
            width: 180,
          },
          {
            label: "提供方",
            prop: "provider",
            type: "slot",
            slotName: "provider",
            align: "center",
            width: 180,
          },
          {
            label: "提交人员",
            prop: "createName",
            type: "text",
            align: "center",
            width: 120,
          },
          {
            label: "提交时间",
            prop: "createTime",
            type: "text",
            align: "center",
            width: 160,
          },
          {
            label: "操作",
            type: "slot",
            slotName: "action",
            align: "center",
            width: 120,
            fixed: "right",
          },
        ],
        tableData: [],
        paginationsObj: {
          currentPage: 1,
          pageSize: 10,
          pageSizes: [10, 20, 30],
          total: 0,
          layout: "total,sizes,prev,pager,next,jumper",
        },
      },
      tableLoading: false,
    });
    // 工单类型转化
    const getTypeName = (type) => {
      if (type === 9) return "售中调度";
      return "-";
    };

    // 工单步骤转化
    const stepChange = (step) => {
      let stepStr = {
        0: "初审",
        1: "终审",
        3: "行业经理审批",
      };
      return stepStr[step];
    };

    const getBusinessTypeName = (type, businessType) => {
      const typeStr = {
        0: "下载模块",
        1: "解决方案",
        2: "自有能力",
        3: "场景",
        4: "标准产品",
        5: "专区",
        6: "专区政策",
        7: "撰写",
        8: "需求方案",
        9: "政策洞察",
        10: "场景方案",
        11: "调度支撑",
        12: "地市方案",
        13: "地市案例",
        14: "企业资料审批",
        15: "省级案例",
        21: "Hdict产品",
      };
      const typeStr2 = {
        1: "生态资料",
        2: "生态资质",
        3: "生态方案",
        4: "生态案例",
      };
      if (type === 6) return typeStr2[businessType] || "-";
      return typeStr[businessType] || "-";
    };
    const dealData = (v) => {
      if (v) {
        return v;
      } else {
        return "-";
      }
    };
    const mergeQueryParams = () => {
      return {
        ...props.searchParams,
        pageNo: data.tableConfig.paginationsObj.currentPage,
        pageSize: data.tableConfig.paginationsObj.pageSize,
        dealBy: getUserInfo.id,
      };
    };
    const getTableData = () => {
      data.tableLoading = true;
      const params = mergeQueryParams();
      getList(params)
        .then((res) => {
          data.tableLoading = false;
          data.tableConfig.tableData = res.data.rows;
          data.tableConfig.tableData.forEach(function (element) {
            element.isTop = element.isTop == 1 ? true : false;
            for (let key in element) {
              if (element[key] == null) {
                element[key] = "-";
              }
            }
          });
          data.tableConfig.paginationsObj.total = res.data.totalRows;
        })
        .catch((err) => {
          data.tableLoading = false;
          console.log(err);
        });
    };
    getTableData();
    eventBus.on("getWaitWorkTable", getTableData);

    const toDetail = (item) => {
      // type9 售中调度
      if (item.type == 9) {
        Router.push({
          name: "coordination",
          query: {
            coordinationId: item.businessId,
            workOrderId: item.id,
            action: "score",
            active: "调度中心"
          },
        });
      }
    };


    //分页
    const pageChange = (page) => {
      data.tableConfig.paginationsObj.currentPage = page;
      getTableData();
    };
    const sizeChange = (size) => {
      data.tableConfig.paginationsObj.pageSize = size;
      data.tableConfig.paginationsObj.currentPage = 1;
      getTableData();
    };


    const providerWith = (value) => {
      if (!value) return "";
      const parts = value.split("/");
      const [first, second, ...rest] = parts;
      if (first == "江苏公司") {
        return rest.length > 0 ? `${second}/${rest.join("/")}` : second;
      }
      return value;
    };
    watch(
      () => props.searchParams,
      () => {
        data.tableConfig.paginationsObj.currentPage = 1;
        getTableData();
      },
      { deep: true }
    );
    return {
      ...toRefs(data),
      providerWith,
      getTableData,
      getBusinessTypeName,
      toDetail,
      pageChange,
      sizeChange,
      getTypeName,
      stepChange,
      dealData,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 24px 24px 24px;
  background: #fff;
  border-radius: 8px;
  overflow: hidden;


  .action {
    cursor: pointer;
  }
  .font_0C70EB {
    color: #0c70eb !important;
  }

  .tag {
    border-radius: 4px;
    border: none;
    padding: 2px 8px;
  }

  .isPublish {
    background: rgba(0, 189, 98, 0.1);
    color: #00bd62;
  }

  .notPublish {
    background: rgba(245, 29, 15, 0.1);
    color: #f51d0f;
  }

  .timePublish {
    background: rgba(246, 118, 0, 0.1);
    color: #f67600;
  }
}
</style>
