<template>
  <div>
    <div class="content">
      <QilinTable
        v-model:tableConfig="tableConfig"
        :loading="tableLoading"
        @changeCurrentPage="pageChange"
        @changeCurrentSize="sizeChange"
      >
        <template #status="slotData">
          <a-tag
            class="tag isPublish font_14"
            v-if="slotData.data.scope.row.status == 1"
          >
            已审核
          </a-tag>
          <a-tag
            class="tag timePublish font_14"
            v-if="slotData.data.scope.row.status == 0"
          >
            审核中
          </a-tag>
        </template>

        <template #type="slotData">
          {{ typeChange(slotData.data.scope.row.type) }}
        </template>

        <template #businessType="slotData">
          {{
            businessChange(
              slotData.data.scope.row.type,
              slotData.data.scope.row.businessType
            )
          }}
        </template>
        <template #dealName="slotData">
          <a-popover title="" :trigger="['hover', 'focus']">
            <template #content>
              <p style="margin-bottom: 0">
                联系电话：{{ dealData(slotData.data.scope.row.dealPhone) }}
              </p>
              <p style="margin-bottom: 0">
                所属部门：{{ dealData(slotData.data.scope.row.dealDeptName) }}
              </p>
            </template>
            {{ dealData(slotData.data.scope.row.dealName) }}
          </a-popover>
        </template>
        <template #provider="slotData">
          {{ providerWith(slotData.data.scope.row.provider) }}
        </template>

        <template #action="slotData">
          <div class="action font_14">
            <span
              class="font_0C70EB margin_r_12"
              @click="toDetail(slotData.data.scope.row)"
            >
              详情
            </span>
          </div>
        </template>
      </QilinTable>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import QilinTable from "../components/table.vue";
import { useRouter } from "vue-router";
import { getTotalList } from "@/api/ticket/ticket.js";
import eventBus from "@/utils/eventBus";

export default defineComponent({
  name: "finishWork",
  props: {
    searchParams: {
      type: Object,
      default: () => ({}),
    },
  },
  components: {
    QilinTable,
  },

  setup(props) {
    const Router = useRouter();
    const data = reactive({
      tableLoading: false,
      tableConfig: {
        elTableConfig: {
          border: false,
        },
        headerConfig: [
          {
            label: "工单标题",
            prop: "title",
            align: "center",
            width: 360,
            ellipsis: true,
          },
          {
            label: "工单类型",
            prop: "type",
            type: "slot",
            slotName: "type",
            align: "center",
            width: 160,
            ellipsis: true,
          },
          {
            label: "业务模块",
            prop: "businessType",
            type: "slot",
            slotName: "businessType",
            align: "center",
            width: 160,
            ellipsis: true,
          },
          {
            label: "工单状态",
            prop: "status",
            type: "slot",
            slotName: "status",
            align: "center",
            width: 85,
          },
          {
            label: "处理人",
            prop: "dealName",
            type: "slot",
            slotName: "dealName",
            align: "center",
            width: 180,
          },
          {
            label: "提供方",
            prop: "provider",
            type: "slot",
            slotName: "provider",
            align: "center",
            width: 180,
            ellipsis: true,
          },
          {
            label: "提交人员",
            prop: "createName",
            align: "center",
            width: 120,
            ellipsis: true,
          },
          {
            label: "提交时间",
            prop: "createTime",
            align: "center",
            width: 160,
            ellipsis: true,
          },
          {
            label: "操作",
            type: "slot",
            slotName: "action",
            align: "center",
            fixed: "right",
            width: 120,
          },
        ],
        tableData: [],
        paginationsObj: {
          currentPage: 1,
          pageSize: 10,
          pageSizes: [10, 20, 30],
          total: 0,
          layout: "total,sizes,prev,pager,next,jumper",
        },
      },
    });
    // 工单类型转化
    const typeChange = (type) => {
      const typeMap = {
        0: "下架审核",
        1: "上架审核",
        2: "能力撰写",
        3: "修改审核",
        4: "下载申请",
        5: "生态关联审核",
        6: "企业资料审批",
        7: "商机工单",
        9: "售中调度",
        11: "生态审核更新",
      };
      return typeMap[type] || "";
    };
    const businessChange = (type, businesstype) => {
      const businessMap = {
        0: "下载模块",
        1: "解决方案",
        2: "自有能力",
        3: "场景",
        4: "标准产品",
        5: "专区",
        6: "专区政策",
        7: "撰写",
        8: "需求方案",
        9: "政策洞察",
        10: "场景方案",
        11: "调度支撑",
        12: "地市方案",
        13: "地市案例",
        14: "企业资料审批",
        15: "省级案例",
      };
      const ecoBusinessMap = {
        1: "生态资料",
        2: "生态资质",
        3: "生态方案",
        4: "生态案例",
      };
      return type == 6
        ? ecoBusinessMap[businesstype] || ""
        : businessMap[businesstype] || "";
    };
    const dealData = (v) => {
      if (v) {
        return v;
      } else {
        return "-";
      }
    };
    const mergeQueryParams = () => {
      return {
        ...props.searchParams,
        pageNo: data.tableConfig.paginationsObj.currentPage,
        pageSize: data.tableConfig.paginationsObj.pageSize,
      };
    };
    const getTableData = () => {
      data.tableLoading = true;
      // Object.assign(data.searchParams, searchData);
      const params = mergeQueryParams();
      getTotalList(params)
        .then((res) => {
          // 处理空值
          const processedData = res.data.rows.map((item) => {
            return Object.fromEntries(
              Object.entries(item).map(([key, value]) => [
                key,
                value === null ? "-" : value,
              ])
            );
          });

          data.tableConfig.tableData = processedData;
          data.tableConfig.paginationsObj.total = res.data.totalRows;
          data.tableLoading = false;
        })
        .catch((err) => {
          data.tableLoading = false;
          console.log(err);
        });
    };
    getTableData();
    eventBus.on("getFinishWorkTable", getTableData);

    const toDetail = (item) => {
      // type9 售中调度
      if (item.type == 9) {
        Router.push({
          name: "coordination",
          query: {
            coordinationId: item.businessId,
            workOrderId: item.id,
            action: "detail",
            active: "调度中心"
          },
        });
        return false;
      }
    };

    //分页
    const pageChange = (page) => {
      data.tableConfig.paginationsObj.currentPage = page;
      getTableData();
    };
    const sizeChange = (size) => {
      data.tableConfig.paginationsObj.pageSize = size;
      data.tableConfig.paginationsObj.currentPage = 1;
      getTableData();
    };

    const providerWith = (value) => {
      if (!value) return "";
      const parts = value.split("/");
      const [first, second, ...rest] = parts;
      if (first == "江苏公司") {
        return rest.length > 0 ? `${second}/${rest.join("/")}` : second;
      }
      return value;
    };
    watch(
      () => props.searchParams,
      () => {
        data.tableConfig.paginationsObj.currentPage = 1;
        getTableData();
      },
      { deep: true }
    );

    return {
      ...toRefs(data),
      providerWith,
      getTableData,
      businessChange,
      toDetail,
      pageChange,
      sizeChange,
      typeChange,
      dealData,
    };
  },
});
</script>

<style lang="scss" scoped>
.content {
  padding: 20px 24px 24px 24px;
  background: #fff;
  //   box-shadow: 4px 4px 8px 0px #f4f5f5;
  border-radius: 8px;
  overflow: hidden;

  .space {
    margin-left: -24px;
    width: 110%;
    height: 1px;
    background: rgba(0, 6, 14, 0.08);
  }

  .action {
    cursor: pointer;
  }
  .font_0C70EB {
    color: #0c70eb !important;
  }
  .font_14vw {
    font-size: 0.73vw;
    // font-size: 1.5vh;
  }

  .tag {
    border-radius: 4px;
    border: none;
    padding: 2px 8px;
    font-size: 14px;
  }

  .isPublish {
    background: rgba(0, 189, 98, 0.1);
    color: #00bd62;
  }

  .notPublish {
    background: rgba(245, 29, 15, 0.1);
    color: #f51d0f;
  }

  .timePublish {
    background: rgba(246, 118, 0, 0.1);
    color: #f67600;
  }
}
</style>
