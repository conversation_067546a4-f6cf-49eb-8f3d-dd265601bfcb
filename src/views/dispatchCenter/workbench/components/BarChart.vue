<template>
  <div ref="chart" style="width: 100%; height: 314px"></div>
</template>

<script setup>
import { ref, onMounted } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  weekData: {
    type: Object,
    default: () => ({}),
  },
});

const chart = ref(null);
let myChart = null;
const processWeekData = (weekData) => {
  const dates = Object.keys(weekData).sort();

  const xAxisData = dates.map((d) => {
    return d.slice(5);
  });

  const preData = [];
  const midData = [];
  const afterData = [];

  dates.forEach((date) => {
    const item = weekData[date] || {};
    preData.push(item.preCount || 0);
    midData.push(item.midCount || 0);
    afterData.push(item.afterCount || 0);
  });

  return { xAxisData, preData, midData, afterData };
};
const initChart = () => {
  if (!chart.value) return;
  myChart = echarts.init(chart.value);
};
const updateChart = () => {
  if (!myChart) return;
  const { xAxisData, preData, midData, afterData } = processWeekData(
    props.weekData
  );
  const option = {
    title: {
      text: "近一星期工单趋势",
      left: "center",
      top: 20,
      textStyle: {
        color: "rgba(0,0,0,0.85)",
        fontSize: 16,
        fontWeight: "bold",
      },
    },
    tooltip: {
      trigger: "axis",
      axisPointer: { type: "shadow" },
    },
    legend: {
      data: ["售前工单", "售中工单", "售后工单"],
      top: 50,
      textStyle: {
        fontWeight: 400,
        fontSize: 14,
        color: "rgba(46, 56, 82, 0.65)",
      },
    },
    grid: {
      left: "3%",
      right: "3%",
      bottom: "10%",
      top: 90,
      containLabel: true,
    },
    xAxis: [
      {
        type: "category",
        data: xAxisData,
        axisTick: { show: false },
        axisLabel: {
          color: "rgba(46, 56, 82, 0.65)",
          fontWeight: 400,
          fontSize: 14,
        },
        axisLine: {
          lineStyle: { color: "rgba(46, 56, 82, 0.35)" },
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        splitLine: { lineStyle: { type: "dashed", color: "#d9e3fb" } },
        axisLine: { show: false },
        axisLabel: {
          color: "rgba(46, 56, 82, 0.65)",
          fontWeight: 400,
          fontSize: 14,
        },
      },
    ],
    series: [
      {
        name: "售前工单",
        type: "bar",
        data: preData,
        barWidth: 15,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#4EA7FF" },
            { offset: 1, color: "#3D51E9" },
          ]),
          barBorderRadius: [12, 12, 0, 0],
        },
        label: {
          show: true,
          position: "top",
          color: "#24456A",
          fontWeight: "400",
          fontSize: 12,
        },
      },
      {
        name: "售中工单",
        type: "bar",
        data: midData,
        barWidth: 15,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#74B8FF" },
            { offset: 1, color: "#2E94FE" },
          ]),
          barBorderRadius: [12, 12, 0, 0],
        },
        label: {
          show: true,
          position: "top",
          color: "#24456A",
          fontWeight: "400",
          fontSize: 12,
        },
      },
      {
        name: "售后工单",
        type: "bar",
        data: afterData,
        barWidth: 15,
        itemStyle: {
          color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
            { offset: 0, color: "#FFCB74" },
            { offset: 1, color: "#FF9948" },
          ]),
          barBorderRadius: [12, 12, 0, 0],
        },
        label: {
          show: true,
          position: "top",
          color: "#24456A",
          fontWeight: "400",
          fontSize: 12,
        },
      },
    ],
  };
  myChart.setOption(option);
};

onMounted(() => {
  initChart();
  updateChart();
});
watch(
  () => props.weekData,
  (newVal) => {
    if (myChart && newVal) {
      updateChart();
    }
  },
  { deep: true }
);
</script>