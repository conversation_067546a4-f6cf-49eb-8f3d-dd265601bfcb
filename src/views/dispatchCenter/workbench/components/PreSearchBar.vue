<template>
  <div class="search_form">
    <a-form class="basicForm" labelAlign="left" :model="formData">
      <div class="flex search_field just-start">
        <a-form-item class="form_item">
          <a-input
            @keyup.enter="submit"
            v-model:value="formData.title"
            placeholder="请输入工单标题"
            class="inner-text"
          />
        </a-form-item>

        <a-form-item class="form_item">
          <a-input
            @keyup.enter="submit"
            v-model:value="formData.createName"
            placeholder="请输入提交人员姓名"
            class="inner-text"
          />
        </a-form-item>

        <a-form-item class="form_item">
          <a-select
            placeholder="请选择工单类型"
            v-model:value="formData.type"
            allowClear
          >
            <template v-for="(opt, index) in typeOptions" :key="index">
              <a-select-option :value="opt.value">
                {{ opt.label }}
              </a-select-option>
            </template>
          </a-select>
        </a-form-item>
<!-- 
        <a-form-item class="form_item">
          <a-select
            placeholder="请选择业务模块"
            v-model:value="formData.businessType"
            allowClear
          >
            <template v-for="(opt, index) in businessTypeOptions" :key="index">
              <a-select-option :value="opt.value">
                {{ opt.label }}
              </a-select-option>
            </template>
          </a-select>
        </a-form-item> -->
      </div>
    </a-form>
    <div class="search">
      <a-button class="reset_btn default_btn" @click="reset"> 重置 </a-button>
      <a-button type="primary" @click="submit"> 搜索 </a-button>
    </div>
  </div>
</template>

<script>
import { reactive, toRefs, defineComponent } from "vue";
export default defineComponent({
  name: "SearchForm",
  emits: ["form-search"],
  components: {},
  props: {},
  setup(props, { emit }) {
    const data = reactive({
      timeRange: [],
      formData: {},

      typeOptions: [
        { label: "售前工单", value: 8 },
        { label: "售中工单", value: 9 },
      ],
      // businessTypeOptions: [
      //   { label: "调度支撑", value: 11 },
      // ],
    });

    // 重置
    const reset = () => {
      data.timeRange = [];
      data.formData = {
        title: "",
        startTime: null,
        endTime: null,
        type: null,
        businessType: null,
        createName: null,
      };
      emit("form-search", data.formData);
    };

    const submit = () => {
      data.formData.startTime = data.timeRange[0];
      data.formData.endTime = data.timeRange[1];
      emit("form-search", data.formData);
    };

    return {
      ...toRefs(data),
      submit,
      reset,
      close,
    };
  },
});
</script>

<style lang="scss" scoped>
.search_form {
  position: relative;
  background: #ffffff;
  border-radius: 8px;
  padding: 4px 180px 8px 24px;
  margin-bottom: 20px;
  .search_field {
    flex-wrap: wrap;
  }

  .form_item {
    border: none;
    margin-bottom: 16px;
    margin-right: 2%;
    width: 23%;
  }

  .ant-form-item-control-input {
    border-radius: 4px;
  }
  .ant-form-item {
    background: #f5f6fa !important;
  }

  .ant-input,
  .ant-picker {
    background: #f5f6fa;
    border: none;
  }
  .search {
    position: absolute;
    right: 24px;
    bottom: 24px;
  }
}

.reset_btn {
  margin-right: 16px;
}
.default_btn {
    background: rgba(12, 112, 235, 0.08);
    border-radius: 4px 4px 4px 4px;
    color: #0C70EB;
    border-radius: 4px;
    border: none;
}

.default_btn:hover {
    background: #d3e8ff !important;
    color: #0C70EB;
}

.default_btn:focus {
    background: rgba(12, 112, 235, 0.08) !important;
    color: #0C70EB;
}

.ant-btn-primary {
    background: #0C70EB;
    border-radius: 4px;
    color: #fff !important;
    border: none;
}

.ant-btn-primary:hover {
    background: #509fff !important
}

.ant-btn-primary:focus {
    background: #0C70EB !important
}

.ant-switch-checked {
    background-color: #0C70EB;
}

:deep(.ant-select-selector) {
  border: none !important;
  border-radius: 4px !important;
  background: #f5f6fa !important;
}
</style>
