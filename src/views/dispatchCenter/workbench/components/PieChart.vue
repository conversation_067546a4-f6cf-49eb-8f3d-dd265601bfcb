<template>
  <div style="position: relative; width: 100%; height: 255px">
    <div ref="chart" style="width: 100%; height: 100%"></div>
    <div
      id="chart3"
      class="center-circle"
      style="
        position: absolute;
        left: 35%;
        top: 50%;
        transform: translate(-50%, -50%);
        z-index: 10;
      "
    >
      <div class="circle-content">
        <div class="total">{{ animatedTotal }}</div>
        <div class="title">工单总数</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted, nextTick } from "vue";
import * as echarts from "echarts";

const props = defineProps({
  data: {
    type: Object,
    default: () => ({ todo: 0, finished: 0, total: 0 }),
  },
  tabType: {
    type: String,
    default: "preSale",
  },
});

const chart = ref(null);
let myChart = null;
const animatedTotal = ref(0);
const initChart = () => {
  if (!chart.value) return;
  myChart = echarts.init(chart.value);
};
const updateChart  = () => {
  if (!myChart) return;
  const data = [
    { name: "已办工单", value: props.data.finished, colorStart: "#4C91FF", colorEnd: "#74A9FF" },
    { name: "待办工单", value: props.data.todo, colorStart: "#FF655C", colorEnd: "#FF8F88" },
  ];
  const option = {
    tooltip: {
      trigger: "item",
      formatter: function (params) {
        const color = params.color;
        const dataMap = {
          已办工单: "#4C91FF",
          待办工单: "#FF655C",
        };
        const numColor = dataMap[params.name] || "#000";
        return `${params.name}: <span style="color: ${numColor}; font-weight: 700;">${params.value}</span>`;
      },
    },
    legend: {
      orient: "vertical",
      right: "5%",
      top: "center",
      itemWidth: 14,
      itemHeight: 14,
      textStyle: {
        fontSize: 16,
        color: "#2E3852",
        rich: {
          blue: { color: "#4C91FF", fontWeight: "bold" },
          red: { color: "#FF655C", fontWeight: "bold" },
        },
      },
      formatter(name) {
        const val = data.find((d) => d.name === name)?.value || "";
        if (name === "已办工单") {
          return `{name|${name}}  {blue|${val}}`;
        } else if (name === "待办工单") {
          return `{name|${name}}  {red|${val}}`;
        }
        return name + "  " + val;
      },
      textStyle: {
        rich: {
          name: { color: "#2E3852" },
          blue: { color: "#4C91FF", fontWeight: "bold" },
          red: { color: "#FF655C", fontWeight: "bold" },
        },
      },
    },
    series: [
      {
        name: "工单状态",
        type: "pie",
        radius: ["0%", "70%"],
        center: ["35%", "50%"],
        label: { show: false },
        data: data.map((item) => ({
          value: item.value,
          name: item.name,
          itemStyle: {
            color: new echarts.graphic.LinearGradient(0, 0, 1, 1, [
              { offset: 0, color: item.colorStart },
              { offset: 1, color: item.colorEnd },
            ]),
          },
        })),
      },
      {
        type: "pie",
        radius: ["72%", "83%"], 
        center: ["35%", "50%"],
        silent: true,
        label: { show: false },
        data: [
          {
            value: 1,
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: "rgba(235,237,241,1)" },
                { offset: 1, color: "rgba(255,255,255,1)" },
              ]),
            },
          },
        ],
      },
    ],
  };
  myChart.setOption(option);
};
function animateCount(targetRef, endValue, duration = 500) {
  const startValue = targetRef.value;
  const range = endValue - startValue;
  if (range === 0) return;
  const startTime = performance.now();

  function step(currentTime) {
    const elapsed = currentTime - startTime;
    if (elapsed < duration) {
      const progress = elapsed / duration;
      targetRef.value = Math.floor(startValue + range * progress);
      requestAnimationFrame(step);
    } else {
      targetRef.value = endValue;
    }
  }
  requestAnimationFrame(step);
}
onMounted(() => {
  initChart();
  updateChart();
});
watch(
  () => props.data,
  (newData) => {
    updateChart();
    animateCount(animatedTotal, newData.total);
  },
  { deep: true }
);
</script>
<style scoped>
.center-circle {
  width: 81px;
  height: 81px;
  border-radius: 50%;
  background: linear-gradient(
    151deg,
    rgba(255, 255, 255, 0.5) 0%,
    rgba(255, 255, 255, 0.2) 100%
  );
  box-shadow: 0px 4px 16px 0px rgba(41, 56, 104, 0.15);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  text-align: center;
  user-select: none;
}
.circle-content {
  color: #fff;
  text-align: center;
  text-shadow: 0px 1px 0px rgba(0, 0, 0, 0.15);
  font-style: normal;
  text-transform: none;
}

.total {
  font-size: 24px;
  font-weight: bold;
  line-height: 1;
  margin-bottom: 4px;
}

.title {
  font-weight: 500;
  font-size: 14px;
}
</style>