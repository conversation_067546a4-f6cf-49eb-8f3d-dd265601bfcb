.abTitle {
  margin-left: 24px;
}

.box {
  :deep(.ant-select-selector) {
    width: 140px !important;
    // margin-right: 24px;
  }
}

.reason {
  background: rgba(245, 29, 15, 0.1);
  padding: 16px 24px;
  border-radius: 8px 8px 8px 8px;
  min-height: 80px;
  max-height: 180px;
  border: 1px solid #f51d0f;
  margin-bottom: 24px;
  color: #000000;

  p {
    margin-bottom: 0;
  }

  .tip {
    font-weight: 500 !important;
  }

  .content {
    font-weight: 400;
    max-height: 160px;
    word-wrap: break-word;
  }
}

.abInfo {
  margin: 10px 24px;
  padding: 24px;
  border: 1px solid rgba(0, 6, 14, 0.08);

  .top {
    p {
      display: inline-block;
    }

    .tit {
      font-weight: 700;
      // margin-left: 20px;
    }
  }

  .center {
    display: flex;
    align-items: start;
    flex-wrap: wrap;

    p {
      display: inline-block;
      width: 24%;
    }
  }
}

:deep(.ant-form-item-label) {
  width: 85px;
}

:deep(.file-list) {
  width: 100%;
}

:deep(.ant-select-arrow) {
  display: none;
}

.ant-input:focus {
  box-shadow: none !important;
}

:deep(textarea:focus) {
  box-shadow: none;
}

:deep(.ant-form-item-label) {
  width: 107px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}

:deep(.ant-upload) {
  border-radius: 4px;
}

:deep(.ant-upload-list) {
  width: 25%;
}

:deep(.ant-input) {
  color: rgba(0, 6, 14, 0.8);
  font-weight: 500;
}

:deep(textarea) {
  color: rgba(0, 6, 14, 0.8);
  font-weight: 500;
}

.itemForm {
  padding: 24px;
  border: 1px solid rgba(0, 6, 14, 0.08);
  margin-bottom: 16px;

  .functionTitle {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
  }

  .functionClass {
    padding: 24px;
    border: 1px solid rgba(0, 6, 14, 0.08);
    margin-bottom: 16px;
  }
}

.file-list {
  width: 40%;
  cursor: pointer;

  p {
    position: relative;
    padding: 2px 24px 2px 24px;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 8px;
    background: rgba(12, 112, 235, 0.08);
    border-radius: 4px;
  }

  .iconSize {
    width: 15px;
    height: 15px;
  }

  .close {
    position: absolute;
    right: 8px;
    top: 7px;
  }

  .icon-annex {
    width: 13px;
    height: 13px;
    color: #0c70eb;
    position: absolute;
    top: 2px;
    left: 8px;
  }
}

#addAbilit {
  background: #fff;
  // overflow: hidden;
  box-shadow: 4px 4px 8px 0px #f4f5f5;
  border-radius: 8px 8px 8px 8px;
  // overflow-y: auto;
  // height: calc(100% - 100px);
  margin-bottom: 32px;

  .line {
    width: 100%;
    height: 1px;
    background-image: linear-gradient(to right,
        rgba(0, 6, 14, 0.08) 0%,
        rgba(0, 6, 14, 0.08) 50%,
        transparent 50%);
    background-size: 10px 1px;
    background-repeat: repeat-x;
  }

  .m-img {
    max-width: 78px;
    max-height: 78px;
  }

  .icon {
    display: inline-block;
    width: 4px;
    height: 13px;
    background: #0c70eb;
    box-shadow: 2px 1px 6px 0px rgba(12, 112, 235, 0.5),
      inset 0px 0px 3px 0px rgba(255, 255, 255, 0.8);
    border-radius: 2px 2px 2px 2px;
    margin-right: 8px;
  }

  .font_F51D0F {
    cursor: pointer;
  }

  .cancel {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    border: 1px solid transparent;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    height: 32px;
    padding: 4px 15px;
    font-size: 14px;
    border-radius: 2px;
    color: rgba(0, 0, 0, 0.85);
    border-color: #d9d9d9;
    background: #fff;
  }

  .cancel:hover {
    color: #40a9ff;
    border-color: #40a9ff;
  }

  .notPublish {
    background: #f51d0f;
    color: #fff;
  }

  .reason {
    background: rgba(245, 29, 15, 0.1);
    padding: 16px 24px;
    border-radius: 8px 8px 8px 8px;
    min-height: 80px;
    max-height: 180px;
    border: 1px solid #f51d0f;
    margin-bottom: 24px;
    color: #000000;

    p {
      margin-bottom: 0;
    }

    .tip {
      font-weight: 500;
    }

    .content {
      font-weight: 400;
      max-height: 160px;
      word-wrap: break-word;
    }
  }

  .required :deep(.ant-form-item-label::before) {
    content: "*";
    color: red;
  }
}

.select_pro {
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 340px;
  }
}

.provinceBtn {
  display: flex;

  p {
    display: inline-block;
    margin-right: 10px;
  }
}

.el-dialog__header.show-close {
  text-align: center;
}

.el-dialog__body {
  text-align: center;

  .ant-form {
    .ant-row:last-child {
      padding-left: 0px;
    }
  }
}

:deep(.ant-radio-group) {
  text-align: left;
}

.ant-checkbox-wrapper {
  display: block;
  text-align: left;
}

.ant-radio-group {
  display: flex;
  // justify-content: center;
  align-items: center;
}

.ant-checkbox {
  display: inline-block;
}

.ant-checkbox-wrapper span:last-child {
  display: inline-block;
}

.company_left {
  margin-right: 16px;
}

.ant-checkbox-wrapper+.ant-checkbox-wrapper {
  margin-left: 0;
}

.custom-tooltip {
  color: #fff !important;
  max-width: 600px;
  /* 限制最大宽度 */
  background: rgba(96, 98, 102, 0.9) !important;
}

.el-tooltip__arrow::before {
  background: rgba(96, 98, 102, 0.9) !important;
  border-color: rgba(96, 98, 102, 0.9) !important;
}

.content_control {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  /* 显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-with-help {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}

.scrollable-table {
  overflow-x: auto;
  /* 启用横向滚动 */
  width: 100%;
  /* 确保宽度占满父容器 */
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}

.company_right {
  display: flex;
  justify-content: center;
  margin-right: 8px;
  gap: 8px;
  min-width: 156px;
  max-width: 176px;
  height: 28px;
  font-size: 15px;
  background: #FFFFFF;
  box-shadow: 0px 4px 8px 0px rgba(77, 120, 170, 0.1);
  border-radius: 20px 20px 20px 20px;
}

.company_underline {
  text-decoration: underline;
  cursor: pointer;
}

.module_group {
  padding: 24px;
  margin: 0 24px 0 32px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #D1D0D8;
}

.module_group:not(:first-child) {
  margin-top: 20px;
}

.module_title {
  display: flex;
  margin: 10px 0 10px 32px;
}

.no-margin-left-right {
  margin-left: 0;
  margin-right: 0;
}

.custom_btn {
  color: #FFFFFF;
  border-radius: 4px 4px 4px 4px;
  border: none;
}

.cancel_btn {
  background: rgba(12, 112, 235, 0.08);
  color: #0C70EB;
}

.active_btn {
  background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
}

.custom_btn:hover,
.custom_btn:focus {
  opacity: 0.6;
}

.font_0c70eb {
  width: 80px; // 解决附件操作文本不正常换行问题
  color: #0c70eb;
}

.person-wrap {
  width: max-content;
  min-width: 100%;
  padding: 0 38px;
  text-align: left;
  display: flex;
  align-items: center;
  gap: 5px;
  margin-bottom: 26px;
  white-space: nowrap;
  justify-content: space-between;
}

.person-wrap:last-of-type {
  margin-bottom: 0;
}

.margin_top_12 {
  margin-top: 12px;
}

.margin_bottom_2 {
  margin-bottom: 2px;
}

.base_info {
  margin: 10px 24px 10px 32px;
  padding: 24px;
  border-radius: 8px 8px 8px 8px;
  border: 1px solid #D1D0D8;
  gap: 24px;
  display: grid;
}

.operation {
  .ant-form-item {
    margin-bottom: 0px;
  }
}

.company-name {
  display: flex;
  flex-direction: column;
}

.font-weight-500 {
  font-weight: 500;
}

.contactName {
  display: inline-block;
  margin-bottom: 0;
  width: 119px;
  color: rgba(0, 0, 0, 0.85);
  // background: #f5f5f5;
  border: 1px solid #d9d9d9;
  border-radius: 2px;
  transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
  height: 32px;
  padding: 0 11px;
  line-height: 30px;
}

.contactPhone {
  display: inline-block;
  margin-bottom: 0;
}

.button_btn {
  margin-right: 10px;
}

.button_btn:last-of-type {
  margin-right: 0;
}

:deep(.ant-descriptions-item-label) {
  width: 146px !important;
  min-width: 146px !important;
}