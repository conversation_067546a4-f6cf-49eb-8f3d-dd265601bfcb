<template>
  <div style="display: flex;justify-content: center;margin-top: 20px;">
    <div id="addAbilit" class="background_fff" style="width:1200px">
      <div>
        <div class="loading-overlay" v-if="viewLoading">
          <a-spin :spinning="viewLoading" tip="附件加载中"></a-spin>
        </div>
        <div class="loading-overlay" v-if="formLoading">
          <a-spin :spinning="formLoading" tip="数据加载中..." />
        </div>
        <a-form :model="formData" labelAlign="right" :rules="rules" class="operation padding_l_24 padding_t_24">
          <p class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>售中工单信息
          </p>
          <div>
            <a-row>
              <a-col :span="8">
                <a-form-item label="工单标题">
                  {{ formData.title }}
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <div></div>
          <p class="group_title weight500 font_14 font_00060e">
            <span class="icon"></span>基础信息
          </p>
          <p class="abTitle">{{ formName }}信息：</p>
          <div class="abInfo">
            <div class="top">
              <div style="margin-bottom: 0">
                {{ formName }}名称：
                <p class="tit" style="cursor: pointer" @click="toDetail">
                  {{ baseData.name }}
                </p>
              </div>
            </div>
            <div class="center">
              {{ formName }}简介：
              <p style="margin-left: 4px; width: 94%">{{ baseData.intro }}</p>
            </div>
          </div>
          <p class="abTitle" v-if="action !== 'selectApply' && isSelectApply == false">
            项目信息：
          </p>
          <div class="abInfo" v-if="action !== 'selectApply' && isSelectApply == false">
            <a-row>
              <a-col :span="8">
                <a-form-item label="项目编码">
                  {{ dealContent(formData.projectcode) }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="8">
                <a-form-item label="项目名称">
                  {{ formData.projectName }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-item label="项目描述">
                  {{ dealContent(formData.projectContent) }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="8">
                <a-form-item label="项目金额">
                  {{ dealContent(formData.projectAmount) }}万元
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="8">
                <a-form-item label="合同编号">
                  {{ dealContent(formData.contractcode) }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="8">
                <a-form-item label="计划验收日期">
                  {{ dealContent(formData.acceptanceDate) }}
                </a-form-item>
              </a-col>
            </a-row>
            <a-row>
              <a-col :span="24">
                <a-form-item label="附件">
                  <div class="file-list" v-if="formData.fileList.length != 0">
                    <div class="flex">
                      <p>
                        <span>
                          <i class="iconfont icon-annex"></i>
                          <span>
                            &nbsp;{{ formData.fileList[0]?.name }} &nbsp;</span>
                        </span>
                      </p>
                      <div class="font_0c70eb">
                        <span @click="view(formData.fileList[0])">
                          &nbsp;预览</span>
                        <span @click="download(formData.fileList[0])">
                          &nbsp;下载</span>
                      </div>
                    </div>
                  </div>
                  <div v-else>暂无附件</div>
                </a-form-item>
              </a-col>
            </a-row>
          </div>
          <p class="group_title weight500 font_14 font_00060e" v-if="action == 'toSelect'">
            <span class="icon"></span>售中调度阶段
          </p>
          <div style="padding: 0 25px">
            <el-table :data="dataCompany" class="resize-table-header-line"
              v-if="action == 'toSelect' && needProvince == false" :empty-text="'暂无数据'"
              :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }" border
              style="width: 100%; margin-top: 20px; margin-bottom: 20px">
              <!-- <el-table-column prop="name" label="相关模块" width="380" /> -->
              <el-table-column prop="company" label="生态厂商">
                <template #default="scope">
                  <div v-for="(item, index) in scope.row.company" :key="index" class="box" style="margin-bottom: 26px">
                    <a-radio-group :value="selectId" style="display: inline-block">
                      <a-radio :value="item.contactPhone" @change="(e) => onCheckChange(e, item, scope.row)" :disabled="!(item.auth == 1 && item.sync == 1 && item.approve == 1)
                        ">
                      </a-radio>
                    </a-radio-group>
                    <div style="display: inline-block; width: 93%">
                      <div style="
                        width: 100%;
                        text-align: left;
                        display: flex;
                        align-items: center;
                      ">
                        <span class="company_left" v-if="item.ecopartnerName"
                          style="min-width: 38%; max-width: 38%; display: flex">
                          {{ item.ecopartnerName }}
                        </span>
                        <span style="display: flex; align-items: center">
                          <a-select v-model:value="item.contactName" @change="(value) => selectUserCom(value, item)"
                            :disabled="!(
                              item.auth == 1 &&
                              item.sync == 1 &&
                              item.approve == 1
                            )
                              ">
                            <template v-for="(opt, index) in item.contactList" :key="index">
                              <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                                {{ opt.contactName }}
                              </a-select-option>
                            </template>
                          </a-select>
                          <span style="margin-left: 12px" :style="{
                            color:
                              item.auth == 1 &&
                                item.sync == 1 &&
                                item.approve == 1
                                ? ''
                                : '#999',
                          }">
                            {{ item.contactPhone }}
                          </span>
                        </span>
                      </div>
                    </div>
                    <div style="text-align: left; padding-left: 6px; margin-top: 9px">
                      <!-- 已同步已认证 -->
                      <span v-if="item.auth == 1 && item.sync == 1 && item.approve">
                        <span v-if="item.approve != 1" style="color: red; margin-bottom: 0; margin-left: 12px">
                          生态厂商暂无该生态联系人！
                        </span>
                      </span>
                      <!-- 已同步未认证 -->
                      <span v-else-if="item.auth == 0">
                        <p style="color: red; margin-bottom: 0; margin-left: 12px">
                          该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
                        </p>
                      </span>
                      <span v-else>
                        <p style="color: red; margin-bottom: 0; margin-left: 12px">
                          生态厂商暂无该生态联系人！
                        </p>
                      </span>
                    </div>
                  </div>
                  <!-- <div style="width: 100%;height: 2px;background-color: red;"></div> -->
                  <a-button type="primary" @click="addCooperate(scope.row)">新增生态厂商</a-button>
                </template>
              </el-table-column>
              <el-table-column prop="ownPerson" label="自有人员" width="500">
                <template #default="scope">
                  <div v-for="(item, index) in scope.row.ownPerson" :key="index" class="box"
                    style="display: flex; margin-bottom: 16px">
                    <a-radio-group :value="selectIdOwn">
                      <a-radio :value="item.contactPhone" @change="(e) => onCheckChange(e, item, scope.row)">
                      </a-radio>
                    </a-radio-group>
                    <div style="
                      width: 100%;
                      text-align: left;
                      display: flex;
                      align-items: center;
                    ">
                      <span style="display: flex; align-items: center">
                        <span v-if="item.contactList" style="display: flex; align-items: center">
                          <a-select v-model:value="item.contactName" @change="(value) => selectUserCom(value, item)">
                            <template v-for="(opt, index) in item.contactList" :key="index">
                              <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                                {{ opt.contactName }}
                              </a-select-option>
                            </template>
                          </a-select>
                          <span style="
                            margin-right: 8px;
                            display: inline-block;
                            margin-left: 8px;
                          ">
                            {{ item.contactPhone }}
                          </span>
                        </span>
                        <span v-else>
                          <p style="
                            display: inline-block;
                            margin-bottom: 0;
                            width: 141px;
                            color: rgba(0, 0, 0, 0.25);
                            background: #f5f5f5;
                            border: 1px solid #d9d9d9;
                            border-radius: 2px;
                            transition: all 0.3s
                              cubic-bezier(0.645, 0.045, 0.355, 1);
                            height: 32px;
                            padding: 0 11px;
                            line-height: 30px;
                          ">
                            {{ item.contactName }}
                          </p>
                          <p style="
                            display: inline-block;
                            margin-bottom: 0;
                            margin-right: 8px;
                            margin-left: 8px;
                          ">
                            {{ item.contactPhone }}
                          </p>
                        </span>
                        {{ item.belong }}
                      </span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <a-row style="padding-left: 29px" v-if="action == 'toSelect' && !selectOwn">
            <a-col :span="8">
              <a-form ref="deliverFrom" :model="formDataNew">
                <a-form-item label="交付经理" name="deliveryManager" :rules="[{ required: true, message: '请选择交付经理' }]">
                  <a-select placeholder="请选择交付经理" v-model:value="formDataNew.deliveryManager" allowClear show-search
                    :filter-option="filterScheme">
                    <template v-for="(opt, index) in personList" :key="index">
                      <a-select-option :value="opt.id" :label="opt.realName">
                        {{ opt.realName }}
                      </a-select-option>
                    </template>
                  </a-select>
                </a-form-item>
              </a-form>
            </a-col>
          </a-row>
          <div style="padding: 0 24px">
            <p v-if="action == 'reSelectPage'">拒绝支撑能力方</p>
            <el-table :data="reSelectData" class="resize-table-header-line" v-if="action == 'reSelectPage'"
              :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }"
              border style="width: 100%; margin-top: 20px; margin-bottom: 20px">
              <el-table-column prop="info" label="生态厂商" width="480">
                <template #default="scope">
                  {{ scope.row.info.ecopartnerName }}
                  {{ scope.row.info.contactName }}
                  {{ scope.row.info.contactPhone }}
                </template>
              </el-table-column>
              <!-- <el-table-column prop="module" label="相关模块" /> -->
              <el-table-column prop="dealContent" :show-overflow-tooltip="true" label="是否支撑">
                <template #default="scope">
                  <el-tooltip :append-to="targetElement" trigger="hover" :content="scope.row.dealContent"
                    placement="top" popper-class="custom-tooltip" v-if="
                      scope.row.dealContent && scope.row.dealContent.length > 60
                    ">
                    <p class="content_control">
                      {{ scope.row.dealContent }}
                    </p>
                  </el-tooltip>
                </template>
              </el-table-column>
              <el-table-column prop="dealTime" label="回复时间" />
            </el-table>
          </div>
          <div style="padding: 0 24px">
            <p v-if="action == 'reSelectPage'">可重新选择能力方</p>
            <el-table :data="dataCompanyNew" v-if="action == 'reSelectPage'" class="resize-table-header-line"
              :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }"
              border style="width: 100%; margin-top: 20px; margin-bottom: 20px">
              <!-- <el-table-column prop="name" label="相关模块" width="380" /> -->
              <el-table-column prop="company" label="生态厂商">
                <template #default="scope">
                  <div v-for="(item, index) in scope.row.company" :key="index" class="box" style="margin-bottom: 26px">
                    <a-radio-group :value="selectId" style="display: inline-block">
                      <a-radio :value="item.contactPhone" @change="(e) => onCheckChange(e, item, scope.row)" :disabled="!(item.auth == 1 && item.sync == 1 && item.approve == 1)
                        ">
                      </a-radio>
                    </a-radio-group>
                    <div style="display: inline-block; width: 93%">
                      <div style="
                        width: 100%;
                        text-align: left;
                        display: flex;
                        align-items: center;
                      ">
                        <span class="company_left" v-if="item.ecopartnerName"
                          style="min-width: 38%; max-width: 38%; display: flex">
                          {{ item.ecopartnerName }}
                        </span>
                        <span style="display: flex; align-items: center">
                          <a-select v-model:value="item.contactName" @change="(value) => selectUserCom(value, item)"
                            :disabled="!(
                              item.auth == 1 &&
                              item.sync == 1 &&
                              item.approve == 1
                            ) || rejectCompanyIdlist.includes(item.userId)
                              ">
                            <template v-for="(opt, index) in item.contactList" :key="index">
                              <a-select-option :value="opt.contactName" :disabled="opt.approve != 1 ||
                                rejectCompanyIdlist.includes(opt.userId)
                                ">
                                {{ opt.contactName }}
                              </a-select-option>
                            </template>
                          </a-select>
                          <span style="margin-left: 12px" :style="{
                            color:
                              item.auth == 1 &&
                                item.sync == 1 &&
                                item.approve == 1
                                ? ''
                                : '#999',
                          }">
                            {{ item.contactPhone }}
                          </span>
                        </span>
                      </div>
                    </div>
                    <div style="text-align: left; padding-left: 6px; margin-top: 9px">
                      <!-- 已同步已认证 -->
                      <span v-if="item.auth == 1 && item.sync == 1 && item.approve">
                        <span v-if="item.approve != 1" style="color: red; margin-bottom: 0; margin-left: 12px">
                          生态厂商暂无该生态联系人！
                        </span>
                      </span>
                      <!-- 已同步未认证 -->
                      <span v-else-if="item.auth == 0">
                        <p style="color: red; margin-bottom: 0; margin-left: 12px">
                          该生态厂商尚未认证，请联系相关负责人前往iPartner平台认证！
                        </p>
                      </span>
                      <span v-else>
                        <p style="color: red; margin-bottom: 0; margin-left: 12px">
                          生态厂商暂无该生态联系人！
                        </p>
                      </span>
                    </div>
                  </div>
                  <!-- <div style="width: 100%;height: 2px;background-color: red;"></div> -->
                  <a-button type="primary" @click="addCooperate(scope.row)">新增生态厂商</a-button>
                </template>
              </el-table-column>
              <el-table-column prop="ownPerson" label="自有人员" width="500">
                <template #default="scope">
                  <div v-for="(item, index) in scope.row.ownPerson" :key="index" class="box"
                    style="display: flex; margin-bottom: 16px">
                    <a-radio-group :value="selectIdOwn">
                      <a-radio :value="item.contactPhone" @change="(e) => onCheckChange(e, item, scope.row)"
                        :disabled="rejectCompanyIdlist.includes(item.userId)">
                      </a-radio>
                    </a-radio-group>
                    <div style="
                      width: 100%;
                      text-align: left;
                      display: flex;
                      align-items: center;
                    ">
                      <span style="display: flex; align-items: center">
                        <span v-if="item.contactList" style="display: flex; align-items: center">
                          <a-select v-model:value="item.contactName" @change="(value) => selectUserCom(value, item)">
                            <template v-for="(opt, index) in item.contactList" :key="index">
                              <a-select-option :value="opt.contactName" :disabled="opt.approve != 1">
                                {{ opt.contactName }}
                              </a-select-option>
                            </template>
                          </a-select>
                          <span style="
                            margin-right: 8px;
                            display: inline-block;
                            margin-left: 8px;
                          ">
                            {{ item.contactPhone }}
                          </span>
                        </span>
                        <span v-else>
                          {{ item.contactName }}
                          {{ item.contactPhone }}
                        </span>
                        {{ item.belong }}
                      </span>
                    </div>
                  </div>
                </template>
              </el-table-column>
            </el-table>
          </div>
          <p class="group_title weight500 font_14 font_00060e" v-if="
            action == 'editDetailFinish' ||
            action == 'writeScore' ||
            action == 'dealedAll'
          ">
            <span class="icon"></span>售中阶段
          </p>
          <div style="
            padding: 24px;
            padding-top: 0;
            padding-bottom: 0;
            width: 100%;
            overflow-x: auto;
          ">
            <el-table :data="editFinishData" v-if="action == 'writeScore'" class="resize-table-header-line"
              :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }"
              border style="width: 100%; margin-top: 20px; margin-bottom: 20px">
              <el-table-column prop="info" label="生态厂商" width="380">
                <template #default="scope">
                  {{ scope.row.info.ecopartnerName }}
                  {{ scope.row.info.contactName }}
                  {{ scope.row.info.contactPhone }}
                </template>
              </el-table-column>
              <!-- <el-table-column prop="module" label="相关模块" width="280" /> -->
              <!-- <el-table-column prop="dealContent" label="是否支撑" width="180">
              <template #default="scope">
                <el-tooltip
                  :append-to="targetElement"
                  trigger="hover"
                  popper-class="custom-tooltip"
                  :content="scope.row.dealContent"
                  placement="top"
                  v-if="
                    scope.row.dealContent && scope.row.dealContent.length > 60
                  "
                >
                  <p class="content_control">
                    {{ dealMessage(scope.row.dealContent) }}
                  </p>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="dealTime" label="回复时间" width="180" /> -->
              <el-table-column prop="midResponseScore" label="售中响应及时率" width="180">
                <template #header="{ column }">
                  <div class="header-with-help" style="display: flex; text-align: center">
                    <span>{{ column.label }}</span>
                    <el-tooltip effect="dark" content="请对售中响应及时率的满意度情况打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                      placement="top">
                      <div class="header-help-icon">
                        <el-icon :size="12">
                          <QuestionFilled />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <el-input-number v-if="scope.row.contactPhone != ownPersonPhone" v-model="scope.row.midResponseScore"
                    :min="0" :max="10" :precision="1" @change="handleChange" />
                  <p v-if="scope.row.contactPhone == ownPersonPhone">-</p>
                </template>
              </el-table-column>
              <el-table-column prop="safeNormativeScore" label="安全保障与规范性" width="180">
                <template #header="{ column }">
                  <div class="header-with-help" style="display: flex; text-align: center">
                    <span>{{ column.label }}</span>
                    <el-tooltip effect="dark" content="请对实施安全规范的满意度情况打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                      placement="top">
                      <div class="header-help-icon">
                        <el-icon :size="12">
                          <QuestionFilled />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <el-input-number v-if="scope.row.contactPhone != ownPersonPhone"
                    v-model="scope.row.safeNormativeScore" :min="0" :max="10" :precision="1" @change="handleChange" />
                  <p v-if="scope.row.contactPhone == ownPersonPhone">-</p>
                </template>
              </el-table-column>
              <el-table-column prop="projectSafeguardScore" label="工期保障" width="180">
                <template #header="{ column }">
                  <div class="header-with-help" style="display: flex; text-align: center">
                    <span>{{ column.label }}</span>
                    <el-tooltip effect="dark" content="请确认本项目是否如期交付：A、如期交付 B 延期但获得客户认可  C 延期且带来客户满意度下降" placement="top">
                      <div class="header-help-icon">
                        <el-icon :size="12">
                          <QuestionFilled />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <div class="score-control" v-if="Route.query.action !== 'detail'">
                    <a-select placeholder="请选择工期保障" v-model:value="scope.row.projectSafeguardScore" allowClear>
                      <template v-for="(opt, index) in scoreList" :key="index">
                        <a-select-option :value="opt.id">
                          {{ opt.value }}
                        </a-select-option>
                      </template>
                    </a-select>
                  </div>
                  <p v-if="scope.row.contactPhone == ownPersonPhone">-</p>
                </template>
              </el-table-column>
              <el-table-column prop="deliveryScore" label="交付满意度" width="180">
                <template #header="{ column }">
                  <div class="header-with-help" style="display: flex; text-align: center">
                    <span>{{ column.label }}</span>
                    <el-tooltip effect="dark" content="请对项目售中交付整体满意度打分，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                      placement="top">
                      <div class="header-help-icon">
                        <el-icon :size="12">
                          <QuestionFilled />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <el-input-number v-if="scope.row.contactPhone != ownPersonPhone" v-model="scope.row.deliveryScore"
                    :min="0" :max="10" :precision="1" @change="handleChange" />
                  <p v-if="scope.row.contactPhone == ownPersonPhone">-</p>
                </template>
              </el-table-column>
              <el-table-column prop="cooperateScore" label="能力协同满意度" width="180">
                <template #header="{ column }">
                  <div class="header-with-help" style="display: flex; text-align: center">
                    <span>{{ column.label }}</span>
                    <el-tooltip effect="dark" content="请填写本次交付实施中融自有能力配合度情况，非常满意(8-10]分，满意(6-8]分，一般(4-6]，不满意(0,4]分）"
                      placement="top">
                      <div class="header-help-icon">
                        <el-icon :size="12">
                          <QuestionFilled />
                        </el-icon>
                      </div>
                    </el-tooltip>
                  </div>
                </template>
                <template #default="scope">
                  <el-input-number v-if="scope.row.contactPhone != ownPersonPhone" v-model="scope.row.cooperateScore"
                    :min="0" :max="10" :precision="1" @change="handleChange" />
                  <p v-if="scope.row.contactPhone == ownPersonPhone">-</p>
                </template>
              </el-table-column>
            </el-table>
            <el-table :data="editFinishData" v-if="action == 'dealedAll'" class="resize-table-header-line"
              :empty-text="'暂无数据'" :cell-style="{ textAlign: 'center' }" :header-cell-style="{ textAlign: 'center' }"
              border style="width: 100%; margin-top: 20px; margin-bottom: 20px">
              <el-table-column prop="info" label="生态厂商" width="480">
                <template #default="scope">
                  {{ scope.row.info.ecopartnerName }}
                  {{ scope.row.info.contactName }}
                  {{ scope.row.info.contactPhone }}
                </template>
              </el-table-column>
              <!-- <el-table-column prop="module" label="相关模块" width="280" /> -->
              <!-- <el-table-column prop="dealContent" label="是否支撑" width="180">
              <template #default="scope">
                <el-tooltip
                  :append-to="targetElement"
                  trigger="hover"
                  popper-class="custom-tooltip"
                  :content="scope.row.dealContent"
                  placement="top"
                  v-if="
                    scope.row.dealContent && scope.row.dealContent.length > 60
                  "
                >
                  <p class="content_control">
                    {{ dealMessage(scope.row.dealContent) }}
                  </p>
                </el-tooltip>
              </template>
            </el-table-column>
            <el-table-column prop="dealTime" label="回复时间" width="180" /> -->
              <el-table-column prop="midResponseScore" label="售中响应及时率" width="180">
                <template #default="scope">
                  {{ dealNumNew(scope.row) }}
                </template>
              </el-table-column>
              <el-table-column prop="safeNormativeScore" label="安全保障与规范性" width="180">
                <template #default="scope">
                  {{ dealNumNew(scope.row) }}
                </template>
              </el-table-column>
              <el-table-column prop="projectSafeguardScore" label="工期保障" width="180">
                <template #default="scope">
                  {{ dealNumNew(scope.row) }}
                </template>
              </el-table-column>
              <el-table-column prop="deliveryScore" label="交付满意度" width="180">
                <template #default="scope">
                  {{ dealNum(scope.row) }}
                </template>
              </el-table-column>
              <el-table-column prop="cooperateScore" label="能力协同满意度" width="180">
                <template #default="scope">
                  {{ dealNumNew(scope.row) }}
                </template>
              </el-table-column>
            </el-table>
          </div>
          <p class="group_title weight500 font_14 font_00060e" v-if="action != 'edit'">
            <span class="icon"></span>工单流程
          </p>
          <div style="padding: 0 24px">
            <el-table v-if="action != 'edit'" class="resize-table-header-line" :data="tableDataWork"
              :empty-text="'暂无数据'" border :header-cell-style="{ textAlign: 'center' }"
              style="width: 100%; margin-top: 20px">
              <el-table-column align="center" prop="activityName" label="工单流程" width="180" />
              <el-table-column align="center" prop="assigneeName" label="处理人" />
              <el-table-column align="left" prop="dealContent" label="处理内容">
                <template #default="scope">
                  <el-tooltip :append-to="targetElement" trigger="hover" popper-class="custom-tooltip"
                    :content="scope.row.dealContent" placement="top" v-if="
                      scope.row.dealContent && scope.row.dealContent.length > 60
                    ">
                    <p v-if="scope.row.dealContent !== ''" class="content_control">
                      {{ scope.row.dealContent }}
                    </p>
                  </el-tooltip>
                  <p v-if="scope.row.dealContent == ''">-</p>
                </template>
              </el-table-column>
              <el-table-column align="center" prop="endTime" label="处理时间" />
            </el-table>
          </div>
        </a-form>
      </div>
      <div class="flex just-center margin_t_32 margin_b_24" v-if="action != 'selectApply' && action != 'dealedAll'">
        <a-button class="margin_r_10" @click="backLast" v-if="action == 'toSelect'">驳回</a-button>
        <a-button class="margin_r_10" @click="cancel" v-if="action !== 'submitPage'">取消</a-button>
        <a-button type="primary" v-if="action == 'submitPage'" @click="submitBtn" :loading="addLoading">调度完成</a-button>
        <a-button v-else type="primary" @click="submit" :loading="addLoading">确定</a-button>
      </div>
      <div class="flex just-center margin_t_32 margin_b_24" v-if="action == 'dealedAll'">
        <a-button class="margin_r_10" @click="cancel">关闭</a-button>
      </div>
    </div>
    <a-modal v-model:visible="showAdd" title="生态厂商新增" @ok="handleOk" :footer="null" @close="closeAdd" width="50%">
      <a-form ref="addFormRef" :model="CooperateData" labelAlign="center" :rules="addRules">
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态合作方" name="company" style="padding-left: 3px">
              <a-select placeholder="请选择生态合作方" v-model:value="CooperateData.company" allowClear show-search
                @select="ecologyChangeOld" :not-found-content="fetching ? undefined : null" :filter-option="false"
                :virtual-scroll="{
                  itemHeight: 32,
                  height: 400,
                  remain: 8,
                }">
                <template v-for="opt in displayOptions" :key="opt.name">
                  <a-select-option :value="opt.name" :disabled="(opt.auth == 1 && opt.sync != 1) ||
                    (opt.auth != 1 && opt.sync == 1) ||
                    companyIdList?.includes(opt.enterpriseId)
                    ">
                    {{ opt.name }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态联系人" name="contanct" style="padding-left: 3px">
              <a-select placeholder="请选择生态联系人" v-model:value="CooperateData.contanct" allowClear
                @change="(value) => selectUser(value, item)">
                <template v-for="(opt, index) in contanctList" :key="index">
                  <a-select-option :value="opt.contactName" :disabled="opt.approve != 1 || rejectCompanyIdlist.includes(opt.userId)
                    ">
                    {{ opt.contactName }}
                  </a-select-option>
                </template>
              </a-select>
            </a-form-item></a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="生态联系方式：">
              <a-input disabled :value="CooperateData.phone" placeholder="请输入生态联系方式">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item label="负责区域：" style="padding-left: 28px">
              <a-input disabled :value="CooperateData.area" placeholder="请选择负责区域">
              </a-input>
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
      <div class="flex just-center margin_t_32">
        <a-button class="margin_r_10" @click="closeAdd">取消</a-button>
        <a-button type="primary" @click="submitAdd" :loading="addLoading">提交</a-button>
      </div>
    </a-modal>
    <a-modal v-model:visible="showSuggest" title="处理意见" @ok="handleOkSugget" :footer="null" @close="handleCloseSugget"
      width="50%">
      <a-textarea :rows="7" :showCount="true" :maxlength="500" placeholder="请输入处理意见，限制500个字符"
        v-model:value="suggest"></a-textarea>
      <div style="width: 100%; text-align: center; margin-top: 24px">
        <a-button class="margin_r_10" @click="handleCloseSugget"> 取消 </a-button>
        <a-button type="primary" @click="handleOkSugget"> 确认 </a-button>
      </div>
    </a-modal>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import { message } from "ant-design-vue";
import {
  submitWithdrawProcess,
  cityBack,
} from "@/api/processManage/backlog&completed/index.js";
import {
  getProcessInfo,
  isReselect,
  getProcessInfoNew,
} from "@/api/processManage/index.js";
import { selectTree } from "@/api/system/team";
import { QuestionFilled } from "@element-plus/icons-vue"; // 添加引入QuestionFilled图标
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { getPerson, beginSend } from "@/api/ticket/ticket.js";
export default defineComponent({
  components: { QuestionFilled },
  setup() {
    const Router = useRouter();
    const Route = useRoute();
    const getUserInfo = JSON.parse(window.localStorage.getItem("userInfo"));
    const data = reactive({
      formData: {
        fileList: [],
        projectName: "",
        title: "",
        projectContent: "",
        dispatchUser: undefined,
        needBefore: "2",
      },
      viewLoading: false,
      formLoading: false,
      formDataNew: {
        deliveryManager: undefined,
      },
      selectIdOwn: undefined,
      selectOwn: false,
      support_type: "",
      isFirst: true,
      sorceCompany: [],
      needProvince: false,
      userInfo: getUserInfo,
      changeName: "",
      selectCompanyId: undefined,
      ecopartnerId: undefined,
      CooperateData: {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        area: undefined,
        enterpriseId: undefined,
        projectcode: undefined,
      },
      enterpriseId: undefined,
      selectCompanyList: [],
      formName: "",
      ipartnerId: undefined, // 能力方id
      ownPersonPhone: "",
      num: 0,
      contanctList: [],
      selectCompany: [],
      baseData: {
        name: "",
        intro: "",
      },
      scoreList: [
        {
          id: "1",
          value: "A",
        },
        {
          id: "0.5",
          value: "B",
        },
        {
          id: "-1",
          value: "C",
        },
      ],
      selectId: null,
      teamOldList: [],
      action: Route.query.action,
      personList: [],
      addLoading: false,
      rules: {
        title: [{ required: true, message: "请输入工单标题", trigger: "blur" }],
        projectCode: [{ required: true, message: "项目编码不能为空", trigger: "blur" }],
        projectName: [
          { required: true, message: "请输入项目名称", trigger: "blur" },
        ],
        projectContent: [
          { required: true, message: "请输入项目描述", trigger: "blur" },
        ],
        supportType: [
          { required: true, message: "请选择支撑类型", trigger: "blur" },
        ],
        dispatchUser: [
          { required: true, message: "请选择调度人", trigger: "blur" },
        ],
        needBefore: [
          {
            required: true,
            message: "请选择是否关联售前工单",
            trigger: "blur",
          },
        ],
        projectAmount: [
          { type: 'number', required: true, message: "请输入项目金额", trigger: ['blur', 'change'] },
        ],
        contractcode: [
          {
            required: true,
            message: "请输入合同编号或者订单号",
            trigger: "blur",
          },
        ],
        acceptanceDate: [
          { required: true, message: "请选择计划验证日期", trigger: "blur" },
        ],
        deliveryManager: [
          { required: true, message: "请选择交付经理", trigger: "blur" },
        ],
      },
      addRules: {
        company: [
          {
            required: true,
            message: "请选择生态合作方",
            trigger: "change",
          },
        ],
        contanct: [
          {
            required: true,
            message: "请选择联系人",
            trigger: "change",
          },
        ],
      },
      // tableData1: [],
      tableDataWork: [],
      // nextId: "",
      showAdd: false,
      dataCompany: [],
      dataCompanyNew: [],
      companyId: "",
      editFinishData: [],
      reSelectData: [], //厂商拒绝后调度人页面重新选择厂商数据
      comId: undefined,
      linkId: "", //方案场景能力id
      type: "", //1方案2场景3能力
      supportType: "",
      dispatchUser: "", //调度人id
      isProvinceUser: false, //是否省级用户
      suggest: "", //处理意见
      showSuggest: false, //是否显示处理意见弹框
      provinceUser: undefined, //申请省级支援
      // backId: "", //驳回节点id
      companyAload: true,
      // rejectCompanyId: "", //拒绝支撑公司id
      module: "",
      rejectCompanyIdlist: [], //厂商拒绝后调度人页面重新选择厂商数据
      companyIdList: [], //页面内能力方公司id列表
      editDataCompany: [], //工单发起页面调度公司信息
      selectPhone: "", //工单发起页面选择人
      selectCompanyId: "", //工单发起页面选择公司
      refuseApplySecond: false, //厂商拒绝后调度人页面重新选择厂商数据
      cityPass: false, //市级通过省级退回市级
      isSelectApply: false, //是否为厂商查看详情
    });
    const addFormRef = ref(null);
    const deliverFrom = ref(null);
    const allOptions = ref([]); // 所有数据
    const displayOptions = ref([]); // 显示的数据
    const selectedValues = ref([]);
    const fetching = ref(false);
    const submit = async () => {
      try {
        data.addLoading = true;
        if (data.action == "writeScore") {
          // 评分
          data.editFinishData.forEach((item) => {
            item.abilityScore = 0;
          });
          console.log(data.editFinishData, `data.editFinishData`);
          const hasEmptyScore = data.editFinishData.some((item) => {
            if (!item.enterpriseId) return false;
            const scores = [
              item.safeNormativeScore,
              item.midResponseScore,
              item.projectSafeguardScore,
              item.deliveryScore,
              item.cooperateScore,
            ];
            return scores.some(
              (score) =>
                score === undefined ||
                score === null ||
                score === "" ||
                isNaN(score) // 如果不是数字
            );
          });
          if (hasEmptyScore) {
            message.warning("评分不能为空");
            data.addLoading = false;
            return; // 直接结束流程
          }

          console.log("评分数据", data.editFinishData);
          const addScoreInfo = data.editFinishData.filter(
            (item) => item.enterpriseId // 自动转换为布尔值
          );
          console.log("评分数据", addScoreInfo);
          submitWithdrawProcess({
            taskId: Route.query.taskId,
            procInsId: Route.query.procInsId,
            comment: "评分完毕",
            variables: {
              addScoreInfo: JSON.stringify({
                writeScore: addScoreInfo,
              }),
            },
          }).then((res) => {
            message.success("评分成功");
            window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
          }).catch((error) => {
            console.error("评分提交失败:", error);
            data.addLoading = false;
          });
        } else {
          let sendData = {
            ...data.formData,
            ...data.baseData,
            selectCompany: data.selectCompanyList,
            nextCompany: [],
            companyId: data.comId,
          };
          if (data.comId) {
            sendData.selectCompany.enterpriseId = data.comId;
          }
          sendData.selectCompany.obey = data.baseData.title;
          if (data.needProvince) {
            submitWithdrawProcess({
              taskId: Route.query.taskId,
              procInsId: Route.query.procInsId,
              comment: "申请省级调度支撑",
              nextUserIds: data.provinceUser,
              variables: {
                needHelp: true,
              },
            }).then((res) => {
              message.success(res.msg);
              window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
            });
          } else {
            let number = data.ipartnerId ? data.ipartnerId : data.selectIdOwn;
            if (number == "" || number == undefined || number == null) {
              message.warning("请选择生态厂商或自有联系人");
              data.addLoading = false;
            } else {
              let num = "";
              let ownId = undefined;
              let company = {};
              if (data.selectIdOwn != null && data.selectIdOwn != undefined) {
                ownId = data.dataCompany[0].ownPerson[0].userId;
                console.log("ownId", ownId);
                console.log(data.dataCompany[0]);
                company = {
                  module: data.baseData.name,
                  company: data.dataCompany[0].ownPerson[0].belong,
                  contactPhone: data.dataCompany[0].ownPerson[0].contactPhone,
                  contactUserName: data.dataCompany[0].ownPerson[0].contactName,
                  contactUserJob: "",
                  userId: ownId,
                  enterpriseId: "",
                };
              } else {
                company = {
                  module: data.baseData.name,
                  company: data.selectCompanyList.ecopartnerName,
                  contactPhone: data.selectCompanyList.contactPhone,
                  contactUserName: data.selectCompanyList.contactName,
                  contactUserJob: "",
                  userId: data.selectCompanyList.userId,
                  enterpriseId: data.selectCompanyList.enterpriseId,
                };
              }
              num = data.ipartnerId ? data.ipartnerId : ownId;
              console.log("提交数据", company);
              console.log("厂商", num);
              console.log(data.formDataNew.deliveryManager, `交付经理`);
              if (data.selectIdOwn) {
                submitWithdrawProcess({
                  taskId: Route.query.taskId,
                  procInsId: Route.query.procInsId,
                  comment: "选择自有人员",
                  copyUserIds: num,
                  variables: {
                    selfUser: true,
                  },
                }).then((res) => {
                  message.success(res.msg);
                  data.selectIdOwn = undefined;
                  window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
                });
              } else {
                await deliverFrom.value?.validate();
                submitWithdrawProcess({
                  taskId: Route.query.taskId,
                  procInsId: Route.query.procInsId,
                  comment: "选择能力方",
                  copyUserIds: num,
                  nextUserIds: data.formDataNew.deliveryManager,
                  variables: {
                    addCompanyInfo: JSON.stringify(company),
                    selfUser: false,
                  },
                }).then((res) => {
                  message.success(res.msg);
                  data.selectIdOwn = undefined;
                  window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
                });
              }
            }
          }
        }
      } catch (error) {
        console.log(error);
        data.addLoading = false;
      }
    };
    const cancel = () => {
      data.addLoading = false;
      if (data.action == "dealed") {
        window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=completed"
      }
      if (data.action == "writeScore") {
        window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      } else {
        Router.go(-1);
      }
      data.addLoading = false;
    };
    const getData = () => {
      if (
        Route.query.type == "reSelect" ||
        Route.query.type == "dealedAll" ||
        Route.query.type == "editDetailFinish" ||
        Route.query.type == "writeScore" ||// 评价打分
        Route.query.type == "toSelect" ||// 去选择
        Route.query.type == "dealed" ||
        Route.query.type == "editDetailStar"
      ) {
        data.formLoading = true;
        getProcessInfo(Route.query.id).then((res) => {
          const parsedData = JSON.parse(JSON.parse(res.data.customDataForm));
          data.ownPersonPhone = parsedData.ownPerson ? parsedData.ownPerson[0].contactPhone : "";
          data.formDataNew.deliveryManager = parsedData.deliveryManager;
          data.selectId = parsedData.selectPhone;
          data.cityPass = parsedData.cityPass;
          data.ipartnerId = parsedData.ipartnerId;
          data.selectIdOwn = parsedData.selectIdOwn;
          // data.rejectCompanyId = parsedData.rejectCompany;
          data.selectCompanyList = parsedData.selectCompanyList;
          // data.backId = parsedData.taskDefinitionKey;
          data.module = parsedData.name;
          if (Route.query.type == "toSelect") {
            data.action = "toSelect";
            if (parsedData.selectIdOwn) {
              data.selectOwn = true;
            } else {
              data.selectOwn = false;
            }
          }
          getProcessInfoNew(Route.query.procInsId).then((res) => {
            let allData = [...res.data];
            let num = allData.length;
            res.data.forEach((item) => {
              if (item.dealType == "2") {
                data.rejectCompanyIdlist.push(item.userId);
                let ar = [
                  {
                    name: parsedData.name,
                    company: parsedData.company,
                    ownPerson: parsedData.ownPerson,
                  },
                ];
                data.dataCompanyNew = ar;
                data.dataCompanyNew[0].company.forEach((item) => {
                  if (item.contactList) {
                    const availableContact = item.contactList.find(
                      (contact) =>
                        contact.approve === 1 &&
                        !data.rejectCompanyIdlist.includes(contact.userId)
                    );
                    if (availableContact) {
                      item.contactName = availableContact.contactName;
                      item.contactPhone = availableContact.contactPhone;
                      item.userId = availableContact.userId;
                    }
                  }
                });
              }
            });
            if (allData[num - 1].dealType == "2") {
              if (Route.query.type == "toSelect") {
                data.action = "reSelectPage";
                if (parsedData.selectIdOwn) {
                  data.selectIdOwn = undefined;
                }
                data.selectId = null;
              }
              data.reSelectData = allData;
              data.reSelectData.forEach((item) => {
                item.info = {
                  ecopartnerName: item.company,
                  contactName: item.contactUserName,
                  contactPhone: item.contactPhone,
                };
                item.dealTime = item.dealTime;
                if (
                  item.dealType == "2" &&
                  (item.dealContent == "" || item.dealContent == "-")
                ) {
                  item.dealContent = "拒绝";
                } else if (
                  item.dealType == "1" &&
                  (item.dealContent == "" || item.dealContent == "-")
                ) {
                  item.dealContent = "同意";
                } else {
                  item.dealContent = item.dealContent;
                }
              });
            }
            if (allData[num - 1].dealType == "1") {
              if (data.action !== "dealedAll" && Route.query.type !== "writeScore") {
                data.action = "submitPage";
              }
            }
            // 仅对 dealType == "0" || null 的项初始化评分（不影响 dealType == "2" 的原始值）
            // allData.forEach((item) => {
            //   if (item.dealType == "0" || item.dealType == null) {
            //     data.tableData1.push({
            //       companyData: {
            //         ecopartnerName: item.company,
            //         contactName: item.contactUserName,
            //         contactPhone: item.contactPhone,
            //       },
            //       module: parsedData.name,
            //     });
            //   }
            // });
            if (allData[num - 1].dealType == "1") {
              data.editFinishData = allData;

              data.editFinishData.forEach((item) => {
                item.info = {
                  ecopartnerName: item.company,
                  contactName: item.contactUserName,
                  contactPhone: item.contactPhone,
                };
                if (
                  item.dealType == "2" &&
                  (item.dealContent == "" || item.dealContent == "-")
                ) {
                  item.dealContent = "拒绝";
                } else if (
                  item.dealType == "1" &&
                  (item.dealContent == "" || item.dealContent == "-")
                ) {
                  item.dealContent = "同意";
                } else {
                  item.dealContent = item.dealContent;
                }
                item.dealTime = item.dealTime;
              });
            } else {
              data.editFinishData = allData;
              data.editFinishData.forEach((item) => {
                item.info = {
                  ecopartnerName: item.company,
                  contactName: item.contactUserName,
                  contactPhone: item.contactPhone,
                };
                if (
                  item.dealType == "2" &&
                  (item.dealContent == "" || item.dealContent == "-")
                ) {
                  item.dealContent = "拒绝";
                } else if (
                  item.dealType == "1" &&
                  (item.dealContent == "" || item.dealContent == "-")
                ) {
                  item.dealContent = "同意";
                } else {
                  item.dealContent = item.dealContent;
                }
                item.dealTime = item.dealTime;
              });
            }
            if (Route.query.type == "writeScore") {
              console.log(data.editFinishData, `data.editFinishData`);
              function processDuplicateCompanies(editFinishData) {
                if (!editFinishData || !Array.isArray(editFinishData)) {
                  return editFinishData; // 返回原始数据
                }

                // 创建一个Map来按company分组
                const companyMap = new Map();

                // 首先按company分组
                editFinishData.forEach((item) => {
                  if (!companyMap.has(item.enterpriseId)) {
                    companyMap.set(item.enterpriseId, []);
                  }
                  companyMap.get(item.enterpriseId).push(item);
                });

                // 处理每个company组
                const result = [];
                companyMap.forEach((items, enterpriseId) => {
                  if (items.length === 1) {
                    // 如果只有一条记录，直接保留
                    result.push(items[0]);
                  } else {
                    // 如果有多条记录
                    // 查找非拒绝的条目
                    const nonRejected = items.filter(
                      (item) => item.dealContent !== "拒绝"
                    );

                    if (nonRejected.length > 0) {
                      // 如果有非拒绝的条目，保留这些
                      result.push(...nonRejected);
                    } else {
                      // 如果全部都是拒绝的，保留第一条
                      result.push(items[0]);
                    }
                  }
                });

                return result; // 返回处理后的结果
              }

              // 使用方式1：直接修改data.editFinishData
              data.editFinishData = processDuplicateCompanies(
                data.editFinishData
              );
              console.log("处理后的editFinishData:", data.editFinishData);

              // 或者使用方式2：不修改原数据，只打印结果
              const processedData = processDuplicateCompanies(
                data.editFinishData
              );
              console.log("处理后的数据:", processedData);
            }
          }).catch((err) => {
            console.log(err);
          });
          if (parsedData.companyId) {
            data.comId = parsedData.companyId;
          } else {
            data.comId = undefined;
          }
          if (Route.query.type == "toSelect") {
            data.dispatchUser = parsedData.dispatchUser;
          }
          data.formName = parsedData.parsedData;
          let ar = [
            {
              name: parsedData.name,
              company: parsedData.company,
              ownPerson: parsedData.ownPerson,
            },
          ];
          data.dataCompany = ar;
          data.companyIdList = parsedData.companyIdList;
          data.formData = { ...data.formData, ...parsedData };
          data.baseData = { ...data.baseData, ...parsedData };
          data.formData.projectContent =
            parsedData.projectContent || parsedData.projectcontent;
          data.formName = parsedData.formName;
          if (parsedData.contentType == 1) {
            data.formName = "方案";
          } else if (parsedData.contentType == 2) {
            data.formName = "场景";
          } else if (parsedData.contentType == 3) {
            data.formName = "能力";
          }
          data.linkId = parsedData.linkId;
          if (res.data.historyProcNodeList[0].activityName == "生态反馈") {
            data.isSelectApply = true;
          }
          data.tableDataWork = res.data.historyProcNodeList.reverse();
          if (
            data.tableDataWork[data.tableDataWork.length - 1].activityName ==
            "已审核"
          ) {
            data.tableDataWork = data.tableDataWork.slice(1).slice(0, -1);
          } else {
            data.tableDataWork = res.data.historyProcNodeList.slice(1);
          }
          if (
            data.tableDataWork[data.tableDataWork.length - 1].activityName ==
            "结束"
          ) {
            data.tableDataWork = data.tableDataWork.slice(0, -1);
          }
          data.tableDataWork.forEach((item) => {
            // if (item.activityName == "调度中") {
            //   data.nextId = item.assigneeId;
            // }
            if (item.commentList && item.commentList.length > 0) {
              item.dealContent = item.commentList[0].fullMessage;
              if (
                item.activityName == "调度人确认" &&
                item.commentList[0].message == ""
              ) {
                item.dealContent = "请发起人评分";
              }
              if (item.commentList[0].type == "2") {
                item.dealContent = "驳回。" + item.commentList[0].message;
              }
              if (item.activityName == "生态反馈") {
                if (item.commentList[0].type == 1) {
                  item.dealContent = "同意。" + item.commentList[0].fullMessage;
                } else if (item.commentList[0].type == 2) {
                  item.dealContent = "拒绝。" + item.commentList[0].fullMessage;
                }
              }
            } else {
              item.dealContent = "";
            }
            if (item.endTime == null) {
              item.endTime = "-";
            }
          });
          data.editFinishData.map((item) => {
            item.name = parsedData.title;
          });
          isReselect(Route.query.procInsId).then((res) => {
            data.isFirst = res.data.isFirst;
            if (Route.query.type == "dealed") {
              data.action = "dealed";
            }
            if (Route.query.type == "writeScore") {
              data.action = "writeScore";
            }
            if (Route.query.type == "dealedAll") {
              data.action = "dealedAll";
            }
          });
          data.tableDataWork = data.tableDataWork.map(
            ({ activityName, assigneeName, dealContent, endTime }) => ({
              activityName,
              assigneeName,
              dealContent,
              endTime,
            })
          );
        }).finally(() => {
          data.formLoading = false;
        });
      }
      getPersonList();
    };
    // 获取当前用户所属省级还是市级
    const getPersonList = () => {
      let role = "";
      // let roleNew = "";
      let org = data.userInfo.orgId;
      // let roleId = data.userInfo.roleIds;
      if (
        org == "2" ||
        org == "3" ||
        org == "4" ||
        org == "5" ||
        org == "6" ||
        org == "7" ||
        org == "8" ||
        org == "9" ||
        org == "10" ||
        org == "11" ||
        org == "12" ||
        org == "13" ||
        org == "14"
      ) {
        role = "cityIndustryCharge";
        // roleNew = "citDeliveryCharge";
        data.support_type = "市级";
        data.isProvinceUser = false;
      } else {
        role = "provinceIndustryCharge";
        // roleNew = "provinceDeliveryCharge";
        data.isProvinceUser = true;
        data.support_type = "省级";
      }
      getPerson(role).then((res) => {
        data.personList = res.data;
      });
    };
    getData();
    // 厂商拒绝支撑
    const refuseApply = () => {
      submitWithdrawProcess({
        taskId: Route.query.taskId,
        procInsId: Route.query.procInsId,
        comment: data.suggest,
        nextUserIds: data.dispatchUser,
        variables: {
          chooseType: "2",
        },
        chooseType: "2",
      }).then((res) => {
        message.success(res.msg);
        data.suggest = "";
        window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
      });
    };
    const backLast = () => {
      data.showSuggest = true;
      data.refuseApplySecond = true;
    };
    const backLastComit = () => {
      if (data.support_type == "市级") {
        cityBack({
          taskId: Route.query.taskId,
          procInsId: Route.query.procInsId,
          comment: data.suggest,
        }).then((res) => {
          message.success(res.msg);
          data.suggest = "";
          data.showSuggest = false;
          window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
        });
      } else {
        if (data.cityPass) {
          submitWithdrawProcess({
            taskId: Route.query.taskId,
            procInsId: Route.query.procInsId,
            comment: data.suggest,
            nextUserIds: data.dispatchUser,
            variables: {
              chooseType: "2",
            },
            chooseType: "2",
          }).then((res) => {
            message.success(res.msg);
            data.suggest = "";
            window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
          });
        } else {
          cityBack({
            taskId: Route.query.taskId,
            procInsId: Route.query.procInsId,
            comment: data.suggest,
          }).then((res) => {
            message.success(res.msg);
            data.suggest = "";
            data.showSuggest = false;
            window.location.href = window.location.origin + "/#/dispatchCenter/workbench?tab=backlog"
          });
        }
      }
    };
    const addCooperate = (value) => {
      data.companyId = value.name;
      data.showAdd = true;
    };
    const closeAdd = () => {
      data.showAdd = false;
      data.contanctList = [];
      data.CooperateData = {
        company: undefined,
        contanct: undefined,
        phone: undefined,
        duty: undefined,
        area: undefined,
        enterpriseId: undefined,
      };
    };
    const ecologyChangeOld = (val) => {
      console.log(val, `val`);

      let list = [];
      data.contanctList = [];
      data.CooperateData.contanct = null;
      data.CooperateData.phone = null;
      data.CooperateData.duty = null;
      data.CooperateData.area = null;
      list = data.teamOldList.filter((item) => {
        return item.name == val;
      });
      console.log(list, `lliiii`);
      data.CooperateData.sync = list[0].sync;
      data.CooperateData.auth = list[0].auth;
      data.CooperateData.area =
        list[0].address != null ? list[0].address : "江苏省";
      console.log(data.CooperateData, `data.CooperateData`);
      data.contanctList = list[0].contactList;
      console.log(data.contanctList);
      if (data.action == "reSelectPage") {
        data.CooperateData.enterpriseId = list[0].enterpriseId;
      }
      data.comId = list[0].enterpriseId;
    };
    const selectUser = (val, item) => {
      let info = [];
      data.CooperateData.phone = null;
      data.CooperateData.duty = null;
      info = data.contanctList;
      const result = info.filter((item) => {
        return item.contactName === val;
      });
      data.CooperateData.phone = result[0].contactPhone;
      data.CooperateData.enterpriseId = result[0].enterpriseId;
      data.CooperateData.area = result[0].contactAddress
        ? result[0].contactAddress
        : data.CooperateData.area;
      data.CooperateData.userId = result[0].userId;
      data.CooperateData.approve = result[0].approve;
    };
    const selectUserCom = (value, item) => {
      const selectedCompany = item.contactList.find(
        (opt) => opt.contactName == value
      );
      if (selectedCompany) {
        item.contactPhone = selectedCompany.contactPhone;
      }
      item.userId = item.contactList.find(
        (opt) => opt.contactName == value
      ).userId;
      data.selectCompanyId = item.ecopartnerId;
      data.selectPhone = null;
      console.log(data.selectPhone, `data.selectPhone`);
      console.log(data.selectIdOwn, `data.selectIdOwn`);
    };
    // 新增生态厂商
    const submitAdd = async () => {
      try {
        await addFormRef.value?.validate();
        if (data.action == "reSelectPage") {
          let com = {
            ecopartnerName: data.CooperateData.company,
            contactPhone: data.CooperateData.phone,
            contactName: data.CooperateData.contanct,
            userId: data.CooperateData.userId,
            contactList: data.contanctList,
            enterpriseId: data.CooperateData.enterpriseId,
            sync: data.CooperateData.sync,
            auth: data.CooperateData.auth,
            approve: data.CooperateData.approve,
          };
          data.dataCompanyNew[0].company.push(com);
        }
        // else if (data.action == "edit") {
        //   let com = {
        //     ecopartnerName: data.CooperateData.company,
        //     contactPhone: data.CooperateData.phone,
        //     contactName: data.CooperateData.contanct,
        //     userId: data.CooperateData.userId,
        //     contactList: data.contanctList,
        //     enterpriseId: data.CooperateData.enterpriseId,
        //     sync: data.CooperateData.sync,
        //     auth: data.CooperateData.auth,
        //     approve: data.CooperateData.approve,
        //   };
        //   data.editDataCompany[0].company.push(com);
        //   data.companyIdList = data.editDataCompany[0].company.map((item) => {
        //     return item.enterpriseId;
        //   });
        //   data.CooperateData = {
        //     company: undefined,
        //     contanct: undefined,
        //     phone: undefined,
        //     area: undefined,
        //   };
        //   data.showAdd = false;
        // }
        else {
          data.dataCompany.forEach((item) => {
            if (item.name === data.companyId) {
              let com = {
                ecopartnerName: data.CooperateData.company,
                contactPhone: data.CooperateData.phone,
                contactName: data.CooperateData.contanct,
                userId: data.CooperateData.userId,
                contactList: data.contanctList,
                enterpriseId: data.CooperateData.enterpriseId,
                sync: data.CooperateData.sync,
                auth: data.CooperateData.auth,
                approve: data.CooperateData.approve,
              };
              item.company.push(com);
              console.log(item, `iiiii`);
            }
          });
        }
        message.success("添加成功");
        console.log(data.dataCompany, `data.dataCompany`);
        data.companyIdList = data.dataCompany[0].company.map((item) => {
          return item.enterpriseId;
        });
        data.showAdd = false;
        data.CooperateData = {
          company: undefined,
          contanct: undefined,
          phone: undefined,
          duty: undefined,
          area: undefined,
        };
      } catch (error) { }
    };
    const dealNumNew = (v) => {
      console.log(v, `v`);
      if (v) {
        return v;
      } else {
        return "-";
      }
    };
    // 选择的生态厂商
    const onCheckChange = (e, item, row) => {
      const { value } = e.target;
      console.log(item, `iijijij`, value);
      data.selectCompanyList = item;
      data.ipartnerId = item.userId;
      data.selectId = item.ecopartnerName ? value : null;
      data.selectIdOwn = item.ecopartnerName ? null : value;
      data.selectPhone = item.ecopartnerName ? value : null;
      if (data.selectIdOwn) {
        data.selectOwn = true;
        data.formData.deliveryManager = undefined;
      } else {
        data.selectOwn = false;
      }
      console.log(data.selectIdOwn, `data.selectIdOwn`);
      console.log(data.selectPhone, `data.selectPhone`);
    };
    const toDetail = () => {
      if (data.formName == "方案") {
        window.open(
          window.location.origin + "/backend/#/solveNew/detailNew?id=" + data.linkId
        );
      } else if (data.formName == "场景") {
        window.open(
          window.location.origin +
          "/backend/#/solveNew/applyNew?id=" +
          data.linkId +
          "&activeBtn=2"
        );
      } else {
        window.open(
          window.location.origin + "/backend/#/module/modulelNew?id=" + data.linkId
        );
      }
    };
    const handleCloseSugget = () => {
      data.showSuggest = false;
      data.companyAload = !data.companyAload;
    };
    const handleOkSugget = () => {
      if (data.refuseApplySecond) {
        backLastComit();
      } else {
        data.showSuggest = false;
        if (data.companyAload) {
          submit();
        } else {
          refuseApply();
        }
      }
    };
    const submitBtn = () => {
      data.showSuggest = true;
    };
    // const dealMessage = (v) => {
    //   console.log(v, `koppokpokop`);
    // };
    // 模拟获取数据
    const fetchData = async () => {
      fetching.value = true;
      try {
        const response = await selectTree();
        const mockData = response.data.filter((item) => {
          return item.id != "1189";
        });
        data.teamOldList = mockData || [];
        allOptions.value = mockData;
        displayOptions.value = mockData;
      } catch (error) {
        console.error("Failed to fetch options:", error);
        // 可以在这里设置错误状态或显示错误消息
        allOptions.value = [];
        displayOptions.value = [];
      } finally {
        fetching.value = false;
      }
    };
    const dealContent = (v) => {
      if (v) {
        return v;
      } else {
        return "-";
      }
    };
    const filterScheme = (input, option) => {
      return option.label.toLowerCase().includes(input.toLowerCase());
    };
    const handleOk = () => { };
    const handleChange = () => { };
    const download = (file) => {
      const href = file.url || file.fileUrl;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      let newHref = href;
      if (href.includes(windowOrigin)) {
        newHref = "/portal" + href.split(windowOrigin)[1];
      }
      window.open(windowOrigin + newHref + "?token=" + token);
      return false;
    };
    const view = (file) => {
      data.viewLoading = true;
      let windowOrigin = window.location.origin;
      let token = localStorage.getItem("token");
      pptTopdf({
        filePath: file.path,
      }).then((res) => {
        if (res.code == 200) {
          data.viewLoading = false;
          let newHref = res.data;
          if (res.data.includes(windowOrigin)) {
            newHref = "/portal" + res.data.split(windowOrigin)[1];
          }
          const newpage = Router.resolve({
            name: "lookPdf",
            query: {
              urlMsg: encodeURIComponent(
                windowOrigin + newHref + "?token=" + token
              ),
              urlName: file.name,
            },
          });
          window.open(newpage.href, "_blank");
        }
      });
      return false;
    };
    onMounted(fetchData);
    return {
      ...toRefs(data),
      selectedValues,
      displayOptions,
      deliverFrom,
      fetching,
      Router,
      Route,
      addFormRef,
      handleChange,
      view,
      download,
      handleOk,
      filterScheme,
      dealContent,
      dealNumNew,
      // dealMessage,
      submitBtn,
      handleCloseSugget,
      handleOkSugget,
      cancel,
      toDetail,
      submitAdd,
      selectUserCom,
      backLast,
      onCheckChange,
      selectUser,
      ecologyChangeOld,
      addCooperate,
      closeAdd,
      submit,
    };
  },
});
</script>
<style lang="scss" scoped>
.abTitle {
  margin-left: 24px;
}

.box {
  :deep(.ant-select-selector) {
    width: 140px !important;
    // margin-right: 24px;
  }
}

.abInfo {
  margin: 10px 24px;
  padding: 24px;
  border: 1px solid rgba(0, 6, 14, 0.08);

  .top {
    p {
      display: inline-block;
    }

    .tit {
      font-weight: 700;
      // margin-left: 20px;
    }
  }

  .center {
    display: flex;
    align-items: start;
    flex-wrap: wrap;

    p {
      display: inline-block;
      width: 24%;
    }
  }
}

:deep(.ant-form-item-label) {
  width: fit-content !important;
}

:deep(.file-list) {
  width: 100%;
}

:deep(.ant-select-arrow) {
  display: none;
}

.ant-input:focus {
  box-shadow: none !important;
}

:deep(textarea:focus) {
  box-shadow: none;
}

:deep(.ant-form-item-label) {
  width: 91px;
  font-weight: 500;
  color: rgba(0, 6, 14, 0.6);
  text-align: right;
}

:deep(.ant-upload) {
  border-radius: 4px;
}

:deep(.ant-upload-list) {
  width: 25%;
}

:deep(.ant-input) {
  color: rgba(0, 6, 14, 0.8);
  font-weight: 500;
}

:deep(textarea) {
  color: rgba(0, 6, 14, 0.8);
  font-weight: 500;
}

.itemForm {
  padding: 24px;
  border: 1px solid rgba(0, 6, 14, 0.08);
  margin-bottom: 16px;

  .functionTitle {
    font-size: 16px;
    font-weight: 500;
    margin-bottom: 20px;
  }

  .functionClass {
    padding: 24px;
    border: 1px solid rgba(0, 6, 14, 0.08);
    margin-bottom: 16px;
  }
}

.file-list {
  width: 40%;
  margin-top: 16px;
  cursor: pointer;

  p {
    position: relative;
    padding: 2px 24px 2px 24px;
    width: 80%;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    margin-bottom: 8px;
    background: rgba(12, 112, 235, 0.08);
    border-radius: 4px;
  }

  .iconSize {
    width: 15px;
    height: 15px;
  }

  .close {
    position: absolute;
    right: 8px;
    top: 7px;
  }

  .icon-annex {
    width: 13px;
    height: 13px;
    color: #0c70eb;
    position: absolute;
    top: 2px;
    left: 8px;
  }
}

#addAbilit {
  background: #fff;
  overflow: hidden;
  box-shadow: 4px 4px 8px 0px #f4f5f5;
  border-radius: 8px 8px 8px 8px;
  overflow-y: auto;
  height: calc(100vh - 100px);

  .line {
    width: 100%;
    height: 1px;
    background-image: linear-gradient(to right,
        rgba(0, 6, 14, 0.08) 0%,
        rgba(0, 6, 14, 0.08) 50%,
        transparent 50%);
    background-size: 10px 1px;
    background-repeat: repeat-x;
  }

  .m-img {
    max-width: 78px;
    max-height: 78px;
  }

  .icon {
    display: inline-block;
    width: 4px;
    height: 13px;
    background: #0c70eb;
    box-shadow: 2px 1px 6px 0px rgba(12, 112, 235, 0.5),
      inset 0px 0px 3px 0px rgba(255, 255, 255, 0.8);
    border-radius: 2px 2px 2px 2px;
    margin-right: 8px;
  }

  .font_F51D0F {
    cursor: pointer;
  }

  .cancel {
    display: inline-block;
    font-weight: 400;
    text-align: center;
    border: 1px solid transparent;
    box-shadow: 0 2px 0 rgba(0, 0, 0, 0.015);
    cursor: pointer;
    transition: all 0.3s cubic-bezier(0.645, 0.045, 0.355, 1);
    height: 32px;
    padding: 4px 15px;
    font-size: 14px;
    border-radius: 2px;
    color: rgba(0, 0, 0, 0.85);
    border-color: #d9d9d9;
    background: #fff;
  }

  .cancel:hover {
    color: #40a9ff;
    border-color: #40a9ff;
  }

  .notPublish {
    background: #f51d0f;
    color: #fff;
  }

  .reason {
    background: rgba(245, 29, 15, 0.1);
    padding: 16px 24px;
    border-radius: 8px 8px 8px 8px;
    min-height: 80px;
    max-height: 180px;
    border: 1px solid #f51d0f;
    margin-bottom: 24px;
    color: #000000;

    p {
      margin-bottom: 0;
    }

    .tip {
      font-weight: 500;
    }

    .content {
      font-weight: 400;
      max-height: 160px;
      word-wrap: break-word;
    }
  }

  .required :deep(.ant-form-item-label::before) {
    content: "*";
    color: red;
  }
}

.select_pro {
  justify-content: center;
  align-items: center;

  .ant-select {
    width: 340px;
  }
}

.el-dialog__header.show-close {
  text-align: center;
}

.el-dialog__body {
  text-align: center;

  .ant-form {
    .ant-row:last-child {
      padding-left: 0px;
    }
  }
}

.ant-checkbox-wrapper {
  display: block;
  text-align: left;
}

:deep(.ant-radio-group) {
  text-align: left !important;
  justify-content: start !important;
}

.ant-radio-group {
  display: flex;
  justify-content: center;
  align-items: center;
}

.ant-checkbox {
  display: inline-block;
}

.ant-checkbox-wrapper span:last-child {
  display: inline-block;
}

.company_left {
  margin-right: 16px;
}

.ant-checkbox-wrapper+.ant-checkbox-wrapper {
  margin-left: 0;
}

.custom-tooltip {
  color: #fff !important;
  max-width: 600px;
  /* 限制最大宽度 */
  background: rgba(96, 98, 102, 0.9) !important;
}

.el-tooltip__arrow::before {
  background: rgba(96, 98, 102, 0.9) !important;
  border-color: rgba(96, 98, 102, 0.9) !important;
}

.content_control {
  display: -webkit-box;
  -webkit-line-clamp: 2;
  line-clamp: 2;
  /* 显示行数 */
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.header-with-help {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  justify-content: center;
}

.scrollable-table {
  overflow-x: auto;
  /* 启用横向滚动 */
  width: 100%;
  /* 确保宽度占满父容器 */
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}

/* 新增评分相关样式 */
.score-control {
  display: flex;
  align-items: center;
  justify-content: center;
}
</style>
<style lang="scss">
.resize-table-header-line.el-table {

  // 默认表头和单元格使用默认光标
  th.el-table__cell,
  td>.cell {
    cursor: default !important;
  }

  // 只在表头分隔线位置显示调整列宽光标
  th.el-table__cell {
    position: relative;

    &::after {
      content: '';
      position: absolute;
      top: 0;
      right: 0;
      width: 8px; // 分隔线热区宽度
      height: 100%;
      cursor: default;
      transform: translateX(50%); // 居中显示
    }

    &:hover::after {
      cursor: col-resize !important;
    }
  }
}
</style>