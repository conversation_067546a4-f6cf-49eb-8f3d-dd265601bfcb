<template>
  <div class="bottom_say">
    <span class="title">发表评论</span>
    <div class="area" style="padding-bottom: 66px">
      <div class="area_tit">
        <span class="area_title">评分：</span>
        <el-rate
          v-model="starValue"
          @change="changeValue"
          allow-half
          size="large"
        />
      </div>
      <a-textarea
        v-model:value="inputValue"
        placeholder="请输入你想评论的内容，内容不超过100字"
        :rows="4"
        showCount
        :maxlength="100"
      />
      <div class="flex just-end">
        <div class="margin_t_20">
          <a-checkbox v-model:checked="checked">匿名</a-checkbox>
        </div>

        <button class="push" @click="pushReview" style="cursor: pointer">
          发表
        </button>
      </div>
    </div>
    <div class="say_list area" v-if="speakList.length > 0">
      <ul class="listBox">
        <li class="con" v-for="(item, index) in speakList" :key="index">
          <div class="con_left">
            <img src="@/assets/images/layout/person.png" alt="" />
          </div>
          <div class="con_right">
            <div class="con_title">
              <span class="name">{{
                item.anonymous == 1 ? "匿名用户" : item.creatorName
              }}</span
              ><el-rate v-model="item.score" allow-half disabled size="large" />
            </div>
            <div class="con_con">
              {{ item.review }}
            </div>
            <div class="con_boot">
              <span class="time">{{ item.createTime }}</span>
              <span
                class="delete"
                v-if="item.show"
                @click="deleteRiview(item.id)"
                style="cursor: pointer"
                >删除</span
              >
            </div>
          </div>
        </li>
      </ul>
      <div class="showMore" v-if="showMore && totalItemCount > 3">
        <span @click="showBtn"
          >查看全部 <span class="num">{{ totalItemCount }}</span> 条评论</span
        >
      </div>
      <div class="layPage" v-if="!showMore">
        <pagination
          :totalItemCount="totalItemCount"
          @size-change="sizeChange"
          @page-change="pageChange"
          :currentPage="pageNo"
          :pageItemSize="pageItemSize"
        />
      </div>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs, watch } from "vue";
import pagination from "@/components/pagination/index.vue";
import {
  getReview,
  getReviewList,
  getReviewDelete,
} from "@/api/moduleList/detail";
import { message } from "ant-design-vue";
export default defineComponent({
  name: "viewList",
  components: {
    pagination,
  },
  props: {
    id: {
      type: String,
      default: "",
    },
    type: {
      type: String,
      default: "",
    },
  },
  setup(props, { emit }) {
    const data = reactive({
      checked: false,
      starValue: "",
      inputValue: "",
      speakList: [],
      showMore: true,
      totalItemCount: 0,
      pageSizeOptions: [10, 20, 30],
      currentPage: 1,
      pageItemSize: 3,
      id: props.id,
      type: props.type,
      word: "",
    });
    const showBtn = () => {
      data.showMore = false;
      data.pageItemSize = "10";
      reviewList();
    };
    const reviewList = () => {
      let params = {
        sourceId: data.id,
        pageSize: data.pageItemSize,
        pageNo: data.currentPage,
        sourceType: data.type,
        delStatus: 1,
      };
      getReviewList(params).then((res) => {
        if (res.code == 200) {
          data.totalItemCount = res.data.totalRows;
          data.speakList = res.data.rows.map((item) => ({
            ...item,
            show: item.creatorName === userInfo.realName,
          }));
        }
      });
    };
    const pageChange = (page) => {
      data.currentPage = page;
      reviewList();
    };
    const sizeChange = (current) => {
      data.pageItemSize = current;
      reviewList();
    };
    watch(
      () => data.speakList,
      (newVal) => {
        if (data.currentPage != 1 && newVal.length == 0) {
          data.currentPage = 1;
          reviewList();
        }
      }
    );
    reviewList();
    if (data.type == 1) {
      data.word = "方案";
    } else if (data.type == 2) {
      data.word = "能力";
    } else if (data.type == 3) {
      data.word = "场景";
    } else if (data.type == 4) {
      data.word = "产品";
    } else if (data.type == 5) {
      data.word = "方案场景";
    }
    const pushReview = () => {
      let params = {
        sourceId: data.id,
        score: data.starValue,
        review: data.inputValue,
        sourceType: data.type,
        anonymous: data.checked ? 1 : 0,
      };
      if (data.starValue == "") {
        message.warn("评分不能为空");
      } else if (data.inputValue == "") {
        message.warn("评论不能为空");
      } else if (data.inputValue.length > 100) {
        message.warn("评论不能超过100个字");
      } else {
        getReview(params).then((res) => {
          data.checked = false;
          message.success("评论发表成功");
          reviewList();
          data.starValue = "";
          data.inputValue = "";
        });
      }
    };
    const deleteRiview = (item) => {
      getReviewDelete(item).then((res) => {
        message.success("评论删除成功");
        data.currentPage = 1;
        reviewList();
      });
    };
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));

    const changeValue = (e) => {
      let inputVal = data.inputValue; // 存储为局部变量
      let presetComments = [
        "",
        "这个" + data.word + "不太行！",
        "这个" + data.word + "仍需优化！",
        "这个" + data.word + "感觉一般！",
        "这个" + data.word + "很不错哦！",
        "这个" + data.word + "真的太棒了！",
      ];
      if (presetComments.includes(inputVal)) {
        if (0 < e && e <= 1) {
          data.inputValue = "这个" + data.word + "不太行！";
        } else if (1 < e && e <= 2) {
          data.inputValue = "这个" + data.word + "仍需优化！";
        } else if (2 < e && e <= 3) {
          data.inputValue = "这个" + data.word + "感觉一般！";
        } else if (3 < e && e <= 4) {
          data.inputValue = "这个" + data.word + "很不错哦！";
        } else if (4 < e && e <= 5) {
          data.inputValue = "这个" + data.word + "真的太棒了！";
        } else if (e == 0) {
          data.inputValue = "";
        }
      }
    };
    return {
      ...toRefs(data),
      deleteRiview,
      changeValue,
      showBtn,
      reviewList,
      pageChange,
      sizeChange,
      pushReview,
      userInfo,
    };
  },
});
</script>
<style lang="scss" scoped>
.bottom_say {
  padding-top: 40px;
  background-color: #f5f7fc;
  text-align: center;
  overflow-y: auto;

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #24456a;
    line-height: 28px;
  }

  .area {
    width: 1200px;
    margin: 32px auto;
    padding: 24px 40px;

    background-color: #ffffff;

    .area_tit {
      text-align: left;
      margin-bottom: 16px;

      .area_title {
        font-weight: 500;
        font-size: 14px;
        color: #2e3852;
        line-height: 28px;
      }
    }

    button {
      background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
      border-radius: 2px 2px 2px 2px;
      font-weight: 500;
      font-size: 14px;
      color: #ffffff;
      line-height: 20px;
      padding: 6px 16px;
      border: none;
      margin-top: 16px;
      float: right;
    }

    textarea {
      border: none;
      background-color: #f5f7fc;
    }
  }

  .say_list {
    .listBox {
      padding-left: 0;

      .con {
        display: flex;
        margin-bottom: 33px;
        width: 100%;

        .con_left {
          padding-top: 15px;

          img {
            width: 40px;
            height: 40px;
          }
        }

        .con_right {
          margin-left: 16px;
          text-align: left;
          font-weight: 400;
          font-size: 14px;
          color: rgba(0, 0, 0, 0.65);
          line-height: 24px;

          .con_title {
            margin-bottom: 8px;

            .name {
              font-weight: 500;
              color: rgba(0, 0, 0, 0.85);
              line-height: 22px;
              margin-right: 12px;
            }
          }

          .con_boot {
            display: flex;
            justify-content: space-between;

            .delete {
              color: #236cff;
            }
          }
        }
      }
    }
  }

  .showMore {
    width: 100%;
    text-align: center;

    span {
      font-weight: 400;
      font-size: 14px;
      color: #236cff;
      line-height: 24px;
      cursor: pointer;
    }
  }
}
</style>