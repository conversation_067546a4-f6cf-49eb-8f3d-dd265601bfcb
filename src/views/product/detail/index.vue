<template>
  <div class="box">
    <div class="loading-overlay" v-if="loadShow">
      <a-spin :spinning="loadShow" :tip="loadShowTitle"></a-spin>
    </div>
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">产品</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.name }}</span>
      </div>
      <div class="right_nav">
        <div @click="back" style="margin-right: 20px">返回</div>
        <!-- <div @click="collectById" :class="{ active: collectActive }">
          <img
            width="22px"
            height="22px"
            v-if="detailData.collect == 0"
            src="@/assets/images/solution/detail/notCollect.png"
          />
          <img
            v-else
            width="22px"
            height="22px"
            src="@/assets/images/solution/detail/isCollect.png"
          />
        </div> -->
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" id="bacPhoto">
        <div class="top_card">
          <div class="left">
            <div class="flex">
              <p class="title">{{ detailData.name }}</p>
            </div>
            <div class="left_middle">
              <div class="introduction">
                <a-tooltip overlayClassName="tooltip_class">
                  <template v-if="isShowToolTip(detailData.introduction, 140)" #title>
                    {{ detailData.introduction }}</template>
                  {{ detailData.introduction }}
                </a-tooltip>
              </div>
              <div class="flex">
                <div class="label flex" style="display: inline-block; margin-right: 12px">
                  <span v-if="false" style="margin-right: 6px" v-for="(item, key) in detailData.merchantMarketList[0].tagLabelList" :key="key">
                    {{ item.name }}
                  </span>
                  <span style="margin-right: 6px" v-for="(item, key) in detailData.demandLabelProductList" :key="key">
                    {{ item.name }}
                  </span>
                </div>
                <div class="flex info_bottom2 bottom1 tips">
                  <p v-if="detailData.phone != null" style="
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    ">
                    <img src="@/assets/images/solution/detail/eyes.png" alt="" />{{ detailData.viewCount }}
                  </p>
                  <p v-else style="
                      display: flex;
                      justify-content: center;
                      align-items: center;
                    ">
                    <img src="@/assets/images/solution/detail/eyes.png" alt="" />-
                  </p>
                  <!-- <p v-if="detailData.downloadCount != null">
                    <img
                      src="@/assets/images/solution/detail/left_down.png"
                      alt=""
                    />{{ detailData.downloadCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/left_down.png"
                      alt=""
                    />-
                  </p> -->
                  <!--<p v-if="detailData.collectCount != null">
                    <img
                      src="@/assets/images/solution/detail/star.png"
                      alt=""
                    />{{ detailData.collectCount }}
                  </p>
                  <p v-else>
                    <img src="@/assets/images/solution/detail/star.png" alt="" />-
                  </p>-->
                </div>
                <div class="apply_info" style="margin-right:6px;" v-if="showCommon">
                  {{ '通用产品' }}
                </div>
                <div class="apply_info" v-if="detailData.marketList?.length>0">
                  应用市场：{{ dataDeal(detailData.marketList) }}
                </div>
              </div>
              <div class="info_bottom" style="margin-top: 0px">
                <p>
                  <!-- 联系人：{{
                    detailData.provider == "产品运营中心"
                      ? "陈超"
                      : detailData.contact
                  }} -->
                  联系人：{{
                     detailData.contact || '-'
                  }}
                </p>
                <p>
                  <!-- 联系电话：{{
                    detailData.provider == "产品运营中心"
                      ? "13912197039"
                      : dataDeal(detailData.phone)
                  }} -->
                  联系电话：{{
                    dataDeal(detailData.phone)
                  }}
                </p>
                <p>
                  <!-- 联系邮箱：
                  {{
                    detailData.provider == "产品运营中心"
                      ? "<EMAIL>"
                      : dataDeal(detailData.email)
                  }} -->
                联系邮箱：
                  {{
                    dataDeal(detailData.email)
                  }}
                </p>
              </div>
              <div class="info_bottom">
                <!-- <p>产品分类：{{ detailData.classifyName }}</p> -->
                <!-- <p>产品分类：{{ dataDeal(detailData.classifyList) }}</p> -->
                <!-- <p>产品编码：{{ dataDeal(detailData.code) }}</p>
                <p>产品来源： {{ dataDeal(detailData.source) }}</p> -->
                <p>产品提供方：{{ detailData.provider }}</p>
                <p>基础产品分类：{{ dealClassIfy() }}</p>
                <!-- <p>上架时间：{{ detailData.createTime }}</p> -->
                <p>首次上架： {{ detailData.shelfTime ? shelfTimeWith(detailData.shelfTime) : detailData.createTime }}</p>
              </div>

              <div class="info_bottom">
                <p>最新更新： {{ detailData.editTime ? shelfTimeWith(detailData.editTime) : shelfTimeWith(detailData.shelfTime) }}</p>
                <!-- <p>产品所属：{{ dataDeal(detailData.productOwnership) }}</p> -->
                <!-- <p>基础产品分类：{{ dealClassIfy() }}</p>
                <p>上架时间：{{ detailData.createTime }}</p> -->
              </div>
              <div class="addCar" v-if="levelShow == 'prefecture'">
                <button v-if="detailData.addCart == 1 && detailData.existsPpt" class="disabled"
                  style="margin-right: 20px">
                  已加入预选
                </button>
                <button v-else-if="detailData.addCart == 0 && detailData.existsPpt" @click="toShop"
                  style="margin-right: 20px">
                  加入预选
                </button>

                <!-- <button
                  v-if="detailData.addOrder"
                  :disabled="true"
                  class="disabled"
                >
                  已加入定制
                </button>
                <button v-else @click="toBuy">加入定制</button> -->
              </div>
            </div>
            <!-- <div class="left_bottom" @click="toBuy()">加入预选</div> -->
          </div>
          <div class="right">
            <img v-if="detailData.image" :src="detailData.image" alt="" style="width: 100%; height: 100%" @click="imgShow(detailData.image)" />
          </div>
        </div>
      </div>

      <div class="anchors">
        <a-anchor direction="horizontal" :affix="false" v-for="(item, key) in anchorList" :key="key"
          @click="handleClick">
          <a-anchor-link :class="{ currentActive: isActive === key }" @click="change(key)" :href="item.href"
            :title="item.title" />
        </a-anchor>
      </div>
      <div class="content" id="anchorContent">
        <!-- 产品视频 -->
        <div class="card applyCard" id="#videoCard" v-if="detailData.video" style="padding-bottom: 0">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="flex function" style="justify-content: center; margin-top: 0">
              <div style="
                  margin-bottom: 56px;
                  display: flex;
                  justify-content: center;
                " :style="videoData.style">
                <video id="video" ref="videoRef" :style="videoData.style" controls :src="detailData.video" />
              </div>
            </div>
          </div>
        </div>
        <!-- 功能特点 -->
        <div class="card applyCard" id="#advantageList" v-if="detailData.advantageList.length > 0">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">功能特点</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>

            <div class="flex function">
              <div class="card_list" v-for="(val, key) in detailData.advantageList" :key="key">
                <div class="card_title">
                  <img src="@/assets/images/solution/detail/card_icon.png" />
                  {{ val.name }}
                </div>
                <div class="margin_t_12 desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(val.description, 100)" #title>{{ val.description }}</template>
                    {{ val.description }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 应用场景 -->
        <div class="card applyCard" id="#sceneList">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">应用场景</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>
            <div class="cards">
              <div class="item_card" v-for="(item, key) in detailData.sceneList" :key="key + 1">
                <img v-if="`${item.cover}`" v-lazy="`${item.cover}`" alt="" class="img" />
                <img v-else src="@/assets/images/ability/adlityDetail/apply.png" class="img" />
                <p class="title">{{ item.name }}</p>
                <p class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(item.description, 70)" #title>
                      {{ item.description }}</template>
                    {{ item.description }}
                  </a-tooltip>
                </p>
              </div>
            </div>
          </div>
        </div>

        <!-- 资费套餐 -->
        <!-- <div class="card applyCard" id="#tariffList">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">资费套餐</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div class="table" v-if="showTariff">
              <div class="tableBox" v-if="detailData.tariffList.length > 1">
                <a-image style="margin-bottom: 20px;" v-for="(item,index) in detailData.tariffList" :key="index" :src="item.cover" alt="" />
                <img style="visibility: hidden;height: 0;margin: 0;"/>
              </div>
              <div v-else  style="width: 100%;text-align: center;">
                <a-image style="width: 582px;height: 320px;" v-for="(item,index) in detailData.tariffList" :key="index" :src="item.cover" alt="" />
              </div>
            </div>
            <div v-else style="width: 100%">
              <div class="tariff_none">
                <span class="con">根据客户定制需求，按实际工作量报价收费</span>
              </div>
            </div>
          </div>
        </div> -->

        <!-- 交付形态 -->
        <!-- <div class="card applyCard" id="#deliveryList" v-if="detailData.deliveryList.length > 0">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">交付形态</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>
            <div class="cards">
              <div
                class="item_card"
                v-for="(item, key) in detailData.deliveryList"
                :key="key + 1"
              >
                <img
                  v-if="item.cover && item.cover != ''"
                  v-lazy="`${item.cover}`"
                  alt=""
                  class="img"
                />
                <img
                  v-else
                  src="@/assets/images/ability/adlityDetail/apply.png"
                  class="img"
                />
                <p class="title">{{ item.name }}</p>
                <p class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(item.description, 70)" #title>
                      {{ item.description }}</template
                    >
                    {{ item.description }}
                  </a-tooltip>
                </p>
              </div>
            </div>
          </div>
        </div> -->

        <!-- 落地案例 -->
        <div class="card applyCard" id="#caseList" v-if="detailData.market && !detailData.market.includes('Hdict市场')">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt="" />
              <div class="tit">落地案例</div>
              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt="" />
            </div>

            <div class="cards" v-for="(item, key) in detailData.caseList" :key="key + 1">
              <img v-if="item.cover == '' || item.cover == null" src="@/assets/images/ability/adlityDetail/bac.png"
                alt="" />
              <img v-else v-lazy="`${item.cover}`" alt="" />
              <div class="right">
                <div style="font-size: 18px; font-weight: 600; line-height: 40px">
                  {{ item.name }}
                </div>
                <div class="desc">
                  <a-tooltip overlayClassName="tooltip_class">
                    <template v-if="isShowToolTip(item.description, 250)" #title>
                      {{ item.description }}</template>
                    {{ item.description }}
                  </a-tooltip>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 话术 -->

        <!-- <div class="card applyCard" id="#marketingList" v-if="marketShow">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">营销话术</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div class="market">
              <div
                class="card_list"
                v-for="(val, key) in detailData.marketingList"
                style="padding: 0;"
                :key="key"
              >
                <div class="cardAll_list">
                <div class="top_key">
                  <img src="@/assets/images/scenario/label.png" alt="">
                  <span>营销话术</span>
                </div>
                <div class="flex">
                  <img src="@/assets/images/scenario/top.png" alt="">
                  <div class="desc lang">
                    <a-tooltip overlayClassName="tooltip_class">
                      <template
                        v-if="isShowToolTip(val.marketingRhetoric, 215)"
                        #title
                      >
                        {{ val.marketingRhetoric }}</template
                      >
                      {{ val.marketingRhetoric }}
                    </a-tooltip>
                  </div>
                </div>
              </div>
              </div>
            </div>
          </div>
        </div> -->

        <!-- 订购操作 -->
        <!-- <div class="card applyCard" id="#orderList">
          <div class="card_content" style="padding-top: 0px; display: block">
            <div class="tab_content">
              <img
                src="@/assets/images/solution/detail/leftIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
              <div class="tit">订购操作</div>
              <img
                src="@/assets/images/solution/detail/rightIcon.png"
                style="width: 33px; height: 22px"
                alt=""
              />
            </div>

            <div class="order">
              <el-carousel
                type="normal‌"
                :loop="false"
                height="156px"
                width="100%"
                :autoplay="false"
                arrow="always"
                indicator-position="none"
              >
                <el-carousel-item
                  v-for="(val, key) in detailData.orderList"
                  :key="key"
                >
                  <div class="flex card_list">
                    <div class="left_index">{{ key + 1 }}</div>
                    <img
                      style="width: 116px; height: 90px"
                      v-if="val.operationImage"
                      v-lazy="val.operationImage"
                      alt=""
                      @click="imgShow(val.operationImage)"
                    />
                    <img
                      style="width: 116px; height: 90px"
                      v-else
                      src="../../../assets/images/ability/adlityDetail/bac.png"
                      alt=""
                    />
                    <div class="order_right">
                      <div class="card_title">步骤 {{ key + 1 }}</div>
                      <div class="desc">
                        <a-tooltip>
                          <template
                            v-if="isShowToolTip(val.operationProcedure, 100)"
                            #title
                            >{{ val.operationProcedure }}</template
                          >
                          {{ val.operationProcedure }}
                        </a-tooltip>
                      </div>
                    </div>
                  </div>
                </el-carousel-item>
              </el-carousel>
            </div>
          </div>
        </div> -->

        <!-- 附件 -->
        <div class="tab_content" id="#download" v-if="detailData.fileList && detailData.fileList.length > 0">
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">产品附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
        </div>
        <ul class="list">
          <li v-for="(item, key) in detailData.fileList">
            <div class="li_box" @click="fileShow(item)">
              <div class="left_box">
                <img src="@/assets/images/solution/detail/word.png" alt="" style="width: 40px; height: 40px" />
                <p class="fileText">{{ item.name }}</p>
              </div>
              <div class="flex" style="align-items: center;">
                <!-- <div class="flex linkIcon"  @click.stop="shareLink(item)">
                  <LinkOutlined style="font-size: 22px;color: #2E7FFF;"/>
                </div> -->
                <img
                  src="@/assets/images/solution/detail/download.png"
                  alt=""
                  @click.stop="downloadBtn(item.url)"
                  style="cursor: pointer"
                />
              </div>
              <!-- <img src="@/assets/images/solution/detail/download.png" alt="" @click.stop="downloadBtn(item.url)"
                style="cursor: pointer" /> -->
            </div>
          </li>
        </ul>
        <div class="market_con" v-if="detailData.market">
          <!-- <div class="title">
            <span class="tit_line"></span>
            <span class="market_tit">产品应用市场信息</span>
            <span class="tit_line_right"></span>
          </div> -->
          <div class="tab_content" v-if="detailData.market?.length>0" id="#market">
            <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
            <div class="tit">应用市场</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          </div>
          <div class="market_box" v-if="detailData.market?.length>0" style="margin-top: 24px;">
            <!-- <div class="con" @click="toMarket('1')" v-if="detailData.market.includes('行业市场')">
              <img src="@/assets/images/product/hangye.png" alt="" />
              <span>行业市场</span>
            </div> -->
            <div class="con" @click="toMarket('2')" v-if="detailData.market.includes('商客市场')">
              <img src="@/assets/images/product/business.png" alt="" />
              <span>商客市场</span>
            </div>
            <div class="con" @click="toMarket('3')" v-if="detailData.market.includes('Hdict市场')">
              <img src="@/assets/images/product/dict.png" alt="" />
              <span>Hdict市场</span>
            </div>
            <!-- <div class="con" @click="toMarket('4')" v-if="detailData.market.includes('移动云市场')">
              <img src="@/assets/images/product/yun.png" alt="" />
              <span>移动云市场</span>
            </div> -->
          </div>
        </div>
      </div>
      <view-list :id="id" :type="type"></view-list>
      <img class="top" src="@/assets/images/solution/detail/toTap.png" alt="" @click="scrollUp" />
    </div>
  </div>
  <a-modal width="1200px" :visible="imgVisible" :footer="null" @cancel="imgCancel">
    <img alt="example" style="width: 100%" v-lazy="previewImage" />
  </a-modal>
  <a-modal v-model:visible="showDownloadModal" title="" :closable="false" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="450px">
    <promptBox @downloadModalCancel="downloadModalCancel" @downloadModalConfirm="downloadModalConfirm" :msgObj="msgObj"/>
  </a-modal>
  <a-modal v-model:visible="showDownloadForm" title="新增工单" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="600px">
    <reviewForm @downloadFormCancel="downloadFormCancel" @downloadFormConfirm="downloadFormConfirm" />
  </a-modal>
  <a-modal v-model:visible="shareVisable" title="分享链接" :mask-closable="false" :footer="null" :destroyOnClose="true"
    width="500px" @cancel="shareVisableClose">
    <div class="shareBody">
      <div class="shareContent">
        <LinkOutlined style="font-size: 26px;"/>
        <div class="shareContent-link">
          <div :title="`${shareTitle}方案`">{{shareTitle}}方案</div>
          <div :title="link">{{ link }}</div>
        </div>
      </div>
      <div class="copyLink">
        <div @click="copy">复制链接</div>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs, onMounted } from "vue";
import { isShowToolTip } from "../../../utils/index.js";
import { useRouter, useRoute } from "vue-router";
import {
  getDetail,
  cancelCollect,
  collect,
  getDownCount,
} from "@/api/product/detail.js";
import { pptTopdf } from "@/api/fileUpload/uploadFile.js";
import { message } from "ant-design-vue";
import viewList from "./viewList.vue";
import axios from "axios";
import eventBus from "../../../utils/eventBus";
import { addShop } from "@/api/buyList/index.js";
import { addShoppingCart } from "@/api/combine/shoppingCart.js";
import { getNewDownCount } from "../../../api/solutionNew/detail";
import promptBox from "@/components/promptBox/index.vue";
import reviewForm from "@/components/reviewForm/index.vue";
import { LinkOutlined } from '@ant-design/icons-vue';
import clipboard3 from 'vue-clipboard3'

export default defineComponent({
  name: "productDetail",
  components: {
    viewList,
    promptBox,
    reviewForm,
    LinkOutlined
  },

  setup(props, { emit }) {
    const route = useRoute();
    const Router = useRouter();
    const roleKey = JSON.parse(localStorage.getItem("userInfo")).roleKeyList;

    const data = reactive({
      msgObj:'',
      shareTitle:'',
      shareVisable:false,
      link:'',
      showCommon:false,//是否展示通用产品
      imgVisible: false,
      previewImage: null,
      loadShow: false,
      loadShowTitle:'附件加载中',
      functionKey: 1,
      caseKey: 1,
      isActive: 0,
      levelShow: route.query.type,
      marketShow:
        roleKey.includes("customerManager") ||
        roleKey.includes("industryManager") ||
        roleKey.includes("sysAdmin"),
      collectActive: false,
      detailData: {
        deliveryList: [],
        advantageList: [],
      },
      // 话术根据角色显示隐藏
      anchorList: [
        {
          key: "advantageList",
          href: "#advantageList",
          title: "功能特点",
        },
        {
          key: "sceneList",
          href: "#sceneList",
          title: "应用场景",
        },
        // {
        //   key: "tariffList",
        //   href: "#tariffList",
        //   title: "资费套餐",
        // },
        // {
        //   key: "deliveryList",
        //   href: "#deliveryList",
        //   title: "交付形态",
        // },
        {
          key: "caseList",
          href: "#caseList",
          title: "落地案例",
        },

        // {
        //   key: "marketingList",
        //   href: "#marketingList",
        //   title: "营销话术",
        // },
        // {
        //   key: "orderList",
        //   href: "#orderList",
        //   title: "订购操作",
        // },
        { key: "download", href: "#download", title: "产品附件", show: true },
      ],
      tabList: [],
      id: route.query.id,
      currentAnchor: "#desc",
      isShow: "#desc",
      showTariff: false,
      type: "4",
      showDownloadModal: false,
      showDownloadForm: false,
    });

    onMounted(() => {
      if (!data.marketShow) {
        data.anchorList = data.anchorList.filter(
          (item) => item.key !== "marketingList"
        );
      }
      setTimeout(() => {
        const videoDom = document.getElementById("video");
        if (videoDom.videoHeight > videoDom.videoWidth) {
          videoData.style = "height:400px;";
        } else {
          videoData.style = "width: 100%;";
        }
      }, 2000);
    });

    const shareLink = (item) => {
      // console.log('item',item);
      data.loadShow = true;
      data.loadShowTitle = '生成链接中'
      data.shareTitle = `【麒麟平台】${item.name}`
      let fileType = item.name.split('.')[1]
      // console.log('fileType',fileType);
      if(fileType == 'pdf'){
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref = item.url;
        if(item.url.includes(windowOrigin)){
          newHref = item.url.split(windowOrigin)[1]
        }else{
          let url = item.url.split('/portal')
          newHref = url[1]
        }
        data.link = `${windowOrigin}/#/sharePdf?urlMsg=${encodeURIComponent(windowOrigin + newHref + "?token=" + token)}&urlName=${item.name}`
        data.loadShow = false
        data.shareVisable = true
      }else{
        pptTopdf({
          filePath: item.path,
          fileUrl: item.url,
        }).then((res) => {
          if (res.code == 200) {
            let windowOrigin = window.location.origin;
            let token = localStorage.getItem("token");
            let newHref = res.data;
            if(res.data.includes(windowOrigin)){
      	      newHref = res.data.split(windowOrigin)[1]
            }else{
              let url = res.data.split('/portal')
              newHref = url[1]
            }
            // data.link = `${windowOrigin}/#/lookPdf?urlMsg=${encodeURIComponent(windowOrigin + newHref + "?token=" + token)}&urlName=${item.name}`
            data.link = `${windowOrigin}/#/sharePdf?urlMsg=${encodeURIComponent(windowOrigin + newHref + "?token=" + token)}&urlName=${item.name}`
            data.loadShow = false
            data.shareVisable = true
          }else{
            data.loadShow = false
          }
        });
        return false;
      }
    }
    const copy = async() => {
      const { toClipboard } = clipboard3()
      // console.log('navigator',navigator);
      await toClipboard(data.link)
      // navigator.clipboard.writeText(data.link)
      message.success('复制成功')
    }
    const shareVisableClose = () => {
      data.shareVisable = false
      data.link = ''
      data.shareTitle = ''
    }

    const back = () => {
      Router.back(-1);
    };
    const videoData = reactive({
      style: "position: fixed;right:200%;",
    });
    const change = (v) => {
      data.isActive = v;
    };

    const getData = async () => {
      if (route.query.id) {
        await getDetail(route.query.id)
          .then((res) => {
            let date = new Date(res.data.createTime);
            var Y = date.getFullYear();
            var M =
              date.getMonth() + 1 < 10
                ? "0" + (date.getMonth() + 1)
                : date.getMonth() + 1;
            var D =
              (date.getDate() < 10 ? "0" + date.getDate() : date.getDate()) +
              " ";
            let GMT = Y + "-" + M + "-" + D;
            res.data.createTime = GMT;
            if (
              res.data.tariffList &&
              res.data.tariffList.length > 0 &&
              res.data.tariffList[0].cover &&
              res.data.tariffList[0].cover != ""
            ) {
              data.showTariff = true;
            }
            // res.data.provider = res.data.provider.split("/")[0];
            let classifyList = [];
            if (res.data.classifyName) classifyList.push(res.data.classifyName);
            if (res.data.typeName) classifyList.push(res.data.typeName);
            if (res.data.labelName) classifyList.push(res.data.labelName);
            if (res.data.marketplaceList.length > 0) {
              res.data.market = [];
              if(res.data.marketplaceList.length == 1 && res.data.marketplaceList[0].applicationMarket == 0){

              }else{
                data.anchorList.push({
                  key: "market",
                  href: "#market",
                  title: "应用市场",
                });
              }
              res.data.marketplaceList.forEach((element) => {
                if (element.applicationMarket == 1) {
                  // res.data.market.push("行业市场");
                } else if (element.applicationMarket == 2) {
                  res.data.market.push("商客市场");
                } else if (element.applicationMarket == 3) {
                  res.data.market.push("Hdict市场");
                } else if (element.applicationMarket == 4) {
                  // res.data.market.push("移动云市场");
                }
                if(element.applicationMarket == 3) {
                   data.anchorList = data.anchorList.filter(
                    (item) => item.key !== "caseList"
                  );
                }
                if (element.applicationMarket == 0) {
                  data.showCommon = true
                }
              });
              res.data.marketList = res.data.market.join(",");
            }
            data.detailData = res.data;
            data.detailData.classifyList = classifyList.join("/");
            if (data.detailData.merchantMarketList && data.detailData.merchantMarketList[0].deliveryList.length < 1) {
              data.anchorList = data.anchorList.filter(
                (item) => item.key !== "deliveryList"
              );
            }
            data.detailData.fileList.forEach((item, index) => {
              if (item.type == 5) {
                data.detailData.video = window.location.origin + item.url + "?token=" + localStorage.getItem("token");
                data.detailData.fileList.splice(index, 1);
              }
            });
            
            // if (data.detailData.video) {
            //   data.anchorList.unshift({
            //     key: "videoCard",
            //     href: "#videoCard",
            //     title: "产品视频",
            //   });
            // }
          })
          .catch((err) => {
            console.log(err);
          });
      }
    };
    getData();

    const dataDeal = (value) => {
      if (value) return value;
      return "-";
    };
    const dealClassIfy = () => {
      return data.detailData.baseClassifyName ? data.detailData.baseClassifyName.replace(',','/') : '-'
    }
    const collectById = () => {
      if (data.detailData.collect == 1) {
        cancelCollect(route.query.id)
          .then(() => {
            message.success("取消收藏成功");
            getData();
          })
          .catch((err) => {
            console.log(err);
          });
      } else {
        collect(route.query.id)
          .then(() => {
            message.success("收藏成功");
            getData();
          })
          .catch((err) => {
            console.log(err);
          });
      }
      data.collectActive = !data.collectActive;
    };

    const getCurrentAnchor = () => {
      return data.currentAnchor;
    };

    const scrollUp = () => {
      data.currentAnchor = "#desc";
      getCurrentAnchor();
      data.isActive = 0;
      document.getElementById("layout_content").scrollTo({
        top: 0,
        behavior: "smooth",
      });
    };
    const handleClick = (e, link) => {
      const href = link.href.replace("#", "");
      e.preventDefault();
      data.currentAnchor = "#" + href;
      let srcolls = document.getElementById(link.href);
      srcolls &&
        srcolls.scrollIntoView({
          block: "center",
          behavior: "smooth",
        });
      data.isShow = href;
    };
    const downloadBtn = (e) => {
      // data.msgObj = {
      //   applyTimes:1,
      //   msg:'您今日下载附件量已超过上限，如果继续下载需提交工单审核。'
      // }
      // data.showDownloadModal = true;
      getNewDownCount({
        businessId: route.query.id,
        businessType: 4,
      }).then((res) => {
        if (res.data) {
          const href = e;
          const downName = e.name;
          let windowOrigin = window.location.origin;
					let token = localStorage.getItem("token");
					let newHref = href;
					if(href.includes(windowOrigin)){
						newHref = "/portal" + href.split(windowOrigin)[1]
					}
				  window.open(windowOrigin + newHref + "?token=" + token);
        } else {
          if(res.code == 200){
            if(res.msg.includes('5')){
              data.msgObj = {
                applyTimes:1,
                msg: res.msg,
                fullPath:route.fullPath
              }
            }else if(res.msg.includes('10')){
              data.msgObj = {
                applyTimes:1,
                msg: res.msg,
                fullPath:route.fullPath
              }
            }else if(res.msg.includes('额外下载次数已满') && !res.msg.includes('今日不可再提交')){
              data.msgObj = {
                applyTimes:2,
                msg:res.msg,
                fullPath:route.fullPath
              }
            }else if(res.msg.includes('今日不可再提交')){
              data.msgObj = {
                applyTimes:3,
                msg:res.msg,
                fullPath:route.fullPath
              }
            }else{}
            data.showDownloadModal = true;
          }
        }
      });
      //data.loadShow = true;
      // return false;
      // axios
      //   .get(href, { responseType: "blob" })
      //   .then((res) => {
      //     data.loadShow = false;
      //     const blob = new Blob([res.data]);
      //     const link = document.createElement("a");
      //     link.href = URL.createObjectURL(blob);
      //     link.download = downName;
      //     link.click();
      //     URL.revokeObjectURL(link.href);
      //   })
      //   .catch(console.error);
    };

    const fileShow = (val) => {
      let fileType = val.name.split('.')[1]
      // console.log('fileType',fileType);
      if(fileType == 'pdf'){
        let windowOrigin = window.location.origin;
        let token = localStorage.getItem("token");
        let newHref = val.url;
        if(val.url.includes(windowOrigin)){
          newHref = "/portal" + val.url.split(windowOrigin)[1]
        }
        const newpage = Router.resolve({
          name: "lookPdf",
          query: {
            urlMsg: encodeURIComponent(
              windowOrigin + newHref + "?token=" + token
            ),
            urlName: val.name,
          },
        });
        window.open(newpage.href, "_blank");
      }else{
        data.loadShowTitle = '附件加载中'
        data.loadShow = true;
        pptTopdf({
          filePath: val.path,
          fileUrl: val.url,
        }).then((res) => {
          data.loadShow = false;
          if (res.code == 200) {
            let windowOrigin = window.location.origin;
            let token = localStorage.getItem("token");
            let newHref = res.data;
            if(res.data.includes(windowOrigin)){
              newHref = "/portal" + res.data.split(windowOrigin)[1]
            }
            const newpage = Router.resolve({
              name: "lookPdf",
              query: {
                urlMsg: encodeURIComponent(
                  windowOrigin + newHref + "?token=" + token
                ),
                urlName: val.name,
              },
            });
            window.open(newpage.href, "_blank");
          }
        });
        return false;
      }
    };
    const toBuy = () => {
      addShop({ productId: route.query.id, type: "2" })
        .then((res) => {
          eventBus.emit("cartRefresh");
          getData();
        })
        .catch((err) => {
          console.log(err);
        });
    };

    const toShop = () => {
      addShoppingCart({
        schemeId: route.query.id,
        type: "4",
      }).then((res) => {
        eventBus.emit("cartRefresh");
        getData();
      });
    };

    const imgShow = (url) => {
      data.imgVisible = true;
      data.previewImage = url;
    };

    const imgCancel = () => {
      data.imgVisible = false;
    };
    const dealData = (e, type) => {
      if (e === undefined || e === null || e === "" || e >= 8888) {
        return "以具体业务定价为准";
      } else {
        return e;
      }
    };
    const toMarket = (i) => {
      console.log(i);
      Router.push({
        name: "precinctDetail",
        query: {
          id: route.query.id,
          appMarket: i,
        },
      });
    };
    eventBus.on("productDetailRefresh", getData);
    // 下载超限提示弹窗取消按钮
    const downloadModalCancel = () => {
      data.showDownloadModal = false;
    };
    // 下载超限提示弹窗确认按钮
    const downloadModalConfirm = () => {
      data.showDownloadModal = false;
      // data.showDownloadForm = true;
    };
    const downloadFormCancel = () => {
      data.showDownloadForm = false;
    };
    const downloadFormConfirm = () => {
      data.showDownloadForm = false;
    };
    const shelfTimeWith = (value) => {
      if (value) {
        return value.slice(0, 10);
      }
      return "-";
    };
    return {
      ...toRefs(data),
      shelfTimeWith,
      collectById,
      toMarket,
      downloadBtn,
      toBuy,
      toShop,
      fileShow,
      shareLink,
      copy,
      shareVisableClose,
      back,
      scrollUp,
      handleClick,
      dataDeal,
      dealClassIfy,
      dealData,
      change,
      isShowToolTip,
      imgShow,
      imgCancel,
      videoData,
      downloadModalCancel,
      downloadModalConfirm,
      downloadFormCancel,
      downloadFormConfirm,
    };
  },
});
</script>

<style lang="scss" scoped>
.linkIcon{
  background-color: #fff;
  width:37px;
  height:37px;
  border-radius: 18px;
  justify-content: center;
  align-items: center;
  margin-bottom:7px;
}
.shareBody{
  display: flex;
  justify-content: space-around;
  align-items: center;
  .shareContent{
    flex: 1;
    display: flex;
    align-items: center;
    >img{
      width: 32px;
      height: 32px;
    }
    >.shareContent-link{
      min-width: 160px;
      max-width: 320px;
      >div{
        width: 100%;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      >div:last-child{
        text-indent: 0.6em;
      }
    }
  }
  .copyLink{
    width: 74px;
    padding: 4px 9px;
    border-radius: 8px;
    background-color: #96cfed
  }
}
@import "./index.scss";
</style>

<style lang="scss">
::v-deep(.ant-modal-body) {
  width: 1000px;
}
.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }

  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }

  .ant-modal-content {
    height: 395px;
    padding: 0;
  }

  .ant-form {
    width: 100%;
  }

  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: #1a66fb;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  .ant-tabs-nav-wrap {
    margin-top: 16px;
    width: 236px;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(196deg,
        #eaeff7 0%,
        rgba(234, 239, 247, 0.41) 100%);
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(196deg,
        #eaeff7 0%,
        rgba(234, 239, 247, 0.41) 100%);
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }

  .ant-tabs-tab {
    width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
  }

  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}

.display {
  display: none !important;
}

.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  width: 100vw;
  height: 100vh;
  display: flex;
  justify-content: center;
  align-items: center;
  background-color: rgba(255, 255, 255, 0.7);
  /* 半透明遮罩 */
  z-index: 9999;
}

.label {
  span {
    display: inline-block;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #236cff;
    font-weight: 400;
    font-size: 14px;
    color: #236cff;
    line-height: 25px;
    padding: 0 10px;
  }
}
</style>
