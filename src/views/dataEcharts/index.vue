<template>
  <div class="contents">
    <div class="left">
      <div class="title">
        <img src="../../assets/images/echarts/Group <EMAIL>" alt="" />
        <p>江苏省揭榜能力 {{ totalNum }} 项</p>
      </div>
      <div class="bac">
        <div class="echarts">
          <div class="first">
            <p class="begin">
              省外复制项目<span class="num">{{ outData.num }}</span
              >个
            </p>
            <p>
              项目金额<span class="money">{{ outData.money }}</span
              >万元
            </p>
            <div id="outside"></div>
          </div>
          <div class="line"></div>
          <div class="first bottom">
            <p class="begin">
              省内复制项目<span class="num">{{ inData.num }}</span
              >个
            </p>
            <p>
              项目金额<span class="money">{{ inData.money }}</span
              >万元
            </p>
            <div id="Province"></div>
          </div>
        </div>
      </div>
    </div>
    <div class="right">
      <div class="title">
        <img src="../../assets/images/echarts/Group <EMAIL>" alt="" />
        <p>江苏省引入能力 {{ projectNum }} 项</p>
      </div>
      <ul class="list">
        <li class="card" v-for="(item, key) in rightData" :key="key">
          <div class="header">
            <div class="left">
              <span>{{ key + 1 }}. {{ item.province }}</span>
              <a-tag class="tag">揭榜省</a-tag>
            </div>
          </div>
          <div
            class="content"
            v-for="(value, index) in item.solutionList"
            :key="index"
          >
            <div class="footer">
              <p class="solution">{{ value.name }}</p>
              <div class="data" v-for="(v, k) in value.projectList" :key="k">
                <p class="tit">项目名称</p>
                <p class="cont">
                  {{ v.name }}
                </p>
                <div class="detail">
                  <p class="con">项目金额（万元）</p>
                  <div class="red box">
                    <p>{{ v.money }}</p>
                  </div>
                </div>
                <div class="detail">
                  <p class="con">项目落地地市</p>
                  <div class="box">
                    <p>{{ v.city }}</p>
                  </div>
                </div>
                <div class="detail">
                  <p class="con">项目签约时间</p>
                  <div class="box">
                    <p>{{ v.signDate }}</p>
                  </div>
                </div>
              </div>
            </div>
            <div class="line"></div>
          </div>
        </li>
      </ul>
      <div class="layPage">
        <pagination
          :totalItemCount="totalData"
          @size-change="sizeChange"
          @page-change="pageChange"
        />
      </div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import * as echarts from "echarts";
import pagination from "../../components/pagination/index.vue";
import { onMounted, onUnmounted, reactive, ref,onBeforeUnmount } from "vue";
import { getOutside, getInside, getRight } from "../../api/operate/echarts";
onMounted(() => {
  outside();
  createInside();
  getRightData();
});
const outData = reactive({
  num: 0,
  money: 0,
});
const inData = reactive({
  num: 0,
  money: 0,
});
const data = ref({
  params: {
    currentPage: "1",
    pageItemSize: "10",
  },
});
const leftCont = ref()
const totalData = ref();
const projectNum = ref();
const pageChange = (page) => {
  data.value.params.currentPage = page;
  getRightData();
};
const sizeChange = (size) => {
  data.value.params.pageItemSize = size;
};
const totalNum = ref();
const pageSizeOptions = reactive(["5", "10", "15", "20"]);
const rightData = ref([]);
/// 声明定义一下echart
let echart = echarts;
const outside = () => {
  getOutside().then((res) => {
    outData.num = res.data.count;
    outData.money = res.data.amount;
    totalNum.value = res.data.projectList.length;
    let outsideXMoney = [];
    let outsideXNum = [];
    let outsideXName = [];
    res.data.projectList.forEach((ele) => {
      outsideXMoney.push(Number(ele.value2));
      outsideXNum.push(Number(ele.value1));
      outsideXName.push(ele.name);
    });
    initChart(
      "outside",
      outsideXName,
      outsideXNum,
      outsideXMoney,
      Math.ceil(Math.max(...outsideXNum) / 9.5) * 10,
      Math.ceil(Math.max(...outsideXMoney) / 9.5) * 10
    );
  });
};
const createInside = () => {
  getInside().then((res) => {
    inData.num = res.data.count;
    inData.money = res.data.amount;
    let outsideXMoney = [];
    let outsideXNum = [];
    let outsideXName = [];
    leftCont.value = res.data.length - 1
    res.data.projectList.forEach((ele) => {
      outsideXMoney.push(Number(ele.value2));
      outsideXNum.push(Number(ele.value1));
      outsideXName.push(ele.name);
    });
    initChart(
      "Province",
      outsideXName,
      outsideXNum,
      outsideXMoney,
      Math.ceil(Math.max(...outsideXNum) / 9.5) * 10,
      Math.ceil(Math.max(...outsideXMoney) / 9.5) * 10
    );
  });
};
const getRightData = () => {
  getRight(data.value.params).then((res) => {
    rightData.value = res.data;
    totalData.value = res.data.length;
    let num = 0;
    res.data.forEach(ele=>{
      ele.solutionList.forEach(item=>{
        num += item.projectList.length;
      })
    })
    projectNum.value = num;
  });
};
function initChart(id, XName, YData1, YData2, max_YData1, max_YData2) {
  let chart = echarts.init(document.getElementById(id));
  chart.setOption({
    tooltip: {
      trigger: "none", // 设置触发方式为'none'，鼠标经过时不显示任何内容
    },
    legend: {
      x: "right",
      data: ["项目数量", "项目金额"],
      icon: "circle",
      itemGap: 40,
    },
    grid: {
      left: "3.5%",
      right: "3%",
      containLabel: true,
      bottom: "10%",
      top: "18%",
    },
    xAxis: [
      {
        type: "category",
        data: XName,
        axisLine: {
          lineStyle: {
            show: false, //是否显示坐标轴轴线，
            color: "#CBD2DA", //x轴轴线的颜色
            width: 2, //x轴粗细
          },
        },
        splitLine: {
          show: false,
        },
        axisTick: {
          show: false,
        },
        axisLabel: {
          formatter: function (params) {
            var newParamsName = ""; // 拼接后的新字符串
            var paramsNameNumber = params.length; // 实际标签数
            var provideNumber = 4; // 每行显示的字数
            var rowNumber = Math.ceil(paramsNameNumber / provideNumber); // 如需换回，算出要显示的行数
            if (paramsNameNumber > provideNumber) {
              /** 循环每一行,p表示行 */
              for (var i = 0; i < rowNumber; i++) {
                var tempStr = ""; // 每次截取的字符串
                var start = i * provideNumber; // 截取位置开始
                var end = start + provideNumber; // 截取位置结束
                if (end > provideNumber * 2) {
                  newParamsName += "...";
                  break;
                  // 最后一行的需要单独处理
                } else {
                  if (i === rowNumber - 1) {
                    tempStr = params.substring(start, paramsNameNumber);
                  } else {
                    tempStr = params.substring(start, end) + "\n";
                  }
                }
                newParamsName += tempStr;
              }
            } else {
              newParamsName = params;
            }
            return newParamsName;
          },
          textStyle: {
            color: "#24456A", // 文字颜色
            fontSize: 14, // 文字大小
          },
        },
      },
    ],
    yAxis: [
      {
        type: "value",
        name: "项目数量（个）",
        axisLabel: {
          formatter: "{value} ",
          algin: "left",
          overflow: "truncate",
          margin: 22,
        },
        splitLine: {
          lineStyle: {
            // 分隔线
            type: "dashed", // 线的类型
            color: "#D2DAE5", // 分隔线颜色
            width: 2,
          },
        },
        min: 0, //最小值
        max: max_YData1, //最大值
        splitNumber: 5, //坐标轴的分割段数
        interval: max_YData1 / 5, // 强制设置坐标轴分割间隔度(取本Y轴的最大值 max / 分割段数 splitNumber )
        nameTextStyle: {
          padding: 5, // 设置与坐标轴的距离，单位为像素
        },
      },
      {
        type: "value",
        name: "项目金额（万元）",
        axisLabel: {
          formatter: "{value}",
        },
        splitLine: {
          lineStyle: {
            type: "dashed", // 线的类型
            color: "#D2DAE5", // 分隔线颜色
            width: 2,
          },
        },
        min: 0, //最小值
        max: max_YData2, //最大值
        splitNumber: 5, //坐标轴的分割段数
        interval: max_YData2 / 5, // 强制设置坐标轴分割间隔度(取本Y轴的最大值 max / 分割段数 splitNumber )
        nameTextStyle: {
          padding: 5, // 设置与坐标轴的距离，单位为像素
        },
      },
    ],
    series: [
      {
        name: "项目数量",
        type: "bar",
        showBackground: true,
        data: YData1,
        barGap: "55%",
        barCategoryGap: "40%",
        barWidth: 17,
        yAxisIndex: 0,
        label: {
          show: true,
          position: "top", // 顶部显示
          formatter: "{c}", // 显示数据值
          textStyle: {
            color: "#00C5B9", // 数字颜色
            fontSize: 12, // 字体大小
            fontWeight: "bold", // 字体加粗
          },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [10, 10, 10, 10],
            color: "#216FED",
          },
        },
        backgroundStyle: {
          color: "#F2F6FB",
          borderRadius: [10, 10, 10, 10],
        },
      },
      {
        name: "项目金额",
        type: "bar",
        showBackground: true,
        data: YData2,
        yAxisIndex: 1,
        barWidth: 17,
        label: {
          show: true,
          position: "top", // 顶部显示
          formatter: "{c}", // 显示数据值
          textStyle: {
            color: "#15D0C5", // 数字颜色
            fontSize: 12, // 字体大小
            fontWeight: "bold", // 字体加粗
          },
        },
        itemStyle: {
          normal: {
            barBorderRadius: [10, 10, 10, 10],
            color: "#15D0C5",
          },
        },
        backgroundStyle: {
          color: "#F2F6FB",
          borderRadius: [10, 10, 10, 10],
        },
      },
    ],
  });
    setTimeout(() => {
    window.addEventListener("resize", resizeFn);
  }, 100);
  const resizeFn = () => {
    return chart.resize();
  }
}

//第一处
onBeforeUnmount(() => {
  // 离开页面必须进行移除，否则会造成内存泄漏，导致卡顿
  window.removeEventListener("resize", outside);
  window.removeEventListener("resize", createInside);
});

defineExpose({
  outside,
  createInside
});
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>
<style lang="scss">
#outside,#Province{
  width: 90%;
  height:40vh ;
}
</style>