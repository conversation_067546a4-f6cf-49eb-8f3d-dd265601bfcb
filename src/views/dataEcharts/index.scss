.contents {
    background-color: #F5F7FC;
    display: flex;
    margin-left: 120px;
    padding-bottom: 104px;

    .title {
        font-weight: 500;
        font-size: 22px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 26px;
        margin-top: 56px;
        margin-bottom: 7px;
        display: flex;
        position: relative;

        img {
            width: 27px;
            height: 18px;
            margin-right: 9px;
            position: absolute;
            bottom: 23px;
        }

        p {
            margin-left: 36px;
        }
    }

    .left {
        margin-right: 40px;
        width: calc(100vw - 960px);
        // width: 60%;

        .bac {
            background-image: url("../../assets/images/echarts/bac.png");
            background-size: 100% 100%;
            background-repeat: no-repeat;
            height: 100vh;
        }

        .echarts {
            padding: 56px 32px 76px 32px;
            // width: 711px;
            // height: 1110px;

            .first {

                p {
                    display: inline-block;
                    font-weight: 500;
                    font-size: 20px;
                    color: #24456A;
                    line-height: 22px;

                    .num {
                        color: #216FED;
                        margin-left: 15px;
                        margin-right: 15px;
                    }

                    .money {
                        color: #00C5B9;
                        margin-left: 15px;
                        margin-right: 15px;
                    }
                }

                .begin {
                    margin-right: 24px;
                    margin-bottom: 18px;
                }
            }

            .line {
                width: 96%;
                height: 2px;
                background: #CAD5EA;
            }

            .bottom {
                margin-top: 48px;
            }
        }
    }

    .right {
        
        flex: auto;

        margin-right: 120px;

        .list {
            padding: 24px;
            background-color: #FFFFFF;
            height: 91vh;
            overflow-y: scroll;
            width: 680px;
            margin-bottom: 0;
            .card {
                padding: 0;
                margin-bottom: 24px;

                .header {
                    background-color: #EBEFF6;
                    height: 48px;
                    display: flex;
                    justify-content: space-between;
                    font-weight: bold;
                    font-size: 16px;
                    line-height: 22px;
                    padding: 13px 24px 13px 31px;
                    position: relative;

                    span {
                        color: #24456A;
                        margin-right: 6px;
                    }

                    p {
                        font-weight: 500;
                        color: #216FED;
                    }

                    .tag {
                        border-radius: 4px 4px 4px 4px;
                        border: 1px solid #216FED;
                        font-weight: 400;
                        font-size: 14px;
                        color: #216FED;
                        position: absolute;
                        bottom: 13px
                    }
                }


                .footer {
                    // height: 168px;

                    background: linear-gradient(159deg, #FFFFFF 0%, #F6F7F9 62%, #F1F3F6 100%);
                    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(36, 69, 106, 0.65);
                    line-height: 22px;
                    padding: 20px 0 20px 33px;

                    .data {
                        padding-bottom: 16px;
                        width: 92%;
                        // border-bottom: 2px solid #E8EDF5;


                        .tit {
                            margin-bottom: 6px;
                        }
                    }

                    .solution {
                        font-weight: 500;
                        font-size: 16px;
                        color: #216FED;
                        line-height: 22px;
                        margin-bottom: 16px;
                    }

                    .cont {
                        font-weight: 500;
                        font-size: 16px;
                        color: #24456A;
                        margin-bottom: 12px;
                    }

                    div {
                        display: inline-block;
                    }

                    .box {
                        background: #FFFFFF;
                        box-shadow: 8px 8px 20px 0px rgba(4, 58, 107, 0.06);
                        font-weight: 500;
                        font-size: 16px;
                        color: #24456A;
                        line-height: 22px;
                        width: 100%;
                        height: 40px;
                        margin-top: 4px;
                        padding-top: 9px;
                        padding-left: 12px;
                    }

                    .detail {
                        width: 120px;
                        margin-right: 60px;
                    }

                    .red {
                        color: #FF2B00;
                    }
                }

                .line {
                    height: 2px;
                    background: #E8EDF5;
                    // margin: 16px 32px;
                    margin-left: 0;
                    margin-left: 24px;
                    margin-right: 24px;
                    display: block !important;
                }
            }
        }
    }
}
ul{
    list-style: none;
}
.list::-webkit-scrollbar {
    display: block;
    position: absolute;
    right: 5px;
}

/* 滚动槽 */
.list::-webkit-scrollbar-track {
    -webkit-box-shadow: inset006pxrgba(221, 165, 165, 0) !important;
    background: #FFFFFF !important;
    border-radius: 0 !important;
}

/* 滚动条滑块 */
.list::-webkit-scrollbar-thumb {
    border-radius: 10px;
    background: #D7E1F2;
    -webkit-box-shadow: inset006pxrgb(0, 0, 0);
}

.layPage {
    width: 100%;
    text-align: right;
    // margin: 15px 0;
    background-color: #FFFFFF;
    padding-top: 3vh;
    padding-bottom: 2.5vh;
    padding-right: 24px;
    height: 9vh;
}

::v-deep(.text-center) {
    text-align: right;
}

::-webkit-scrollbar {
    width: 4px;
    /*  设置纵轴（y轴）轴滚动条 */
    height: 4px;
    /*  设置横轴（x轴）轴滚动条 */
}

/* 滚动条滑块（里面小方块） */
::-webkit-scrollbar-thumb {
    border-radius: 10px;
    box-shadow: inset 0 0 5px rgba(0, 0, 0, 0.2);
    background: rgba(0, 0, 0, 0.9);
}