<template>
  <div class="box">
    <div class="top_nav">
      <div class="left_nav">
        <span class="title" @click="back">行业方案</span>
        <span class="title"> / </span>
        <span class="current">{{ detailData.name }}</span>
      </div>
      <div class="right_nav">
        <div @click="back" style="margin-right: 20px">返回</div>
        <div @click="collectById" :class="{ active: collectActive }">
          <img
            width="22px"
            height="22px"
            v-if="detailData.collect == 0"
            src="@/assets/images/solution/detail/notCollect.png"
          />
          <img
            v-else
            width="22px"
            height="22px"
            src="@/assets/images/solution/detail/isCollect.png"
          />
        </div>
      </div>
    </div>
    <div style="margin-top: 50px">
      <div class="banner" id="bacPhoto">
        <div class="top_card">
          <div class="left">
            <div class="left_tit">
              <p>{{ detailData.name }}</p>
            </div>
            <div class="left_middle">
              <p class="info">
                {{ detailData.description }}
              </p>
              <div class="info_bottom">
                <div
                  class="label flex"
                  style="display: inline-block; margin-right: 24px"
                >
                  <span
                    style="margin-right: 6px"
                    v-for="(item, key) in detailData.labelName"
                    :key="key"
                    >{{ item }}</span
                  >
                </div>
                <div style="margin-left: 0; display: inline-block" class="tips">
                  <p v-if="detailData.phone != null">
                    <img
                      src="@/assets/images/solution/detail/eyes.png"
                      alt=""
                    />
                    {{ detailData.viewCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/eyes.png"
                      alt=""
                    />
                    -
                  </p>
                </div>
                <div style="display: inline-block" class="tips">
                  <p v-if="detailData.downloadCount != null">
                    <img
                      src="@/assets/images/solution/detail/left_down.png"
                      alt=""
                    />
                    {{ detailData.downloadCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/left_down.png"
                      alt=""
                    />-
                  </p>
                </div>
                <div style="display: inline-block" class="tips">
                  <p v-if="detailData.collectCount != null">
                    <img
                      src="@/assets/images/solution/detail/star.png"
                      alt=""
                    />
                    {{ detailData.collectCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/star.png"
                      alt=""
                    />
                    -
                  </p>
                </div>
                <!--<div style="display: inline-block" class="tips">
                  <p v-if="detailData.combineCount != null">
                    <img
                      src="@/assets/images/solution/detail/yinru.png"
                      alt=""
                    />{{ detailData.combineCount }}
                  </p>
                  <p v-else>
                    <img
                      src="@/assets/images/solution/detail/yinru.png"
                      alt=""
                    />-
                  </p>
                </div>-->
                <div class="bottom_tip" style="display: block; margin-left: 0">
                  <p v-if="detailData.contact != null">
                    联系人：{{ detailData.contact }}
                  </p>
                  <p v-else>联系人：-</p>
                  <p v-if="detailData.phone != null">
                    联系电话：{{ detailData.phone }}
                  </p>
                  <p v-else>联系电话：-</p>
                  <p v-if="detailData.mail != null">
                    联系邮箱：{{ detailData.mail }}
                  </p>
                  <p v-else>联系邮箱：-</p>
                </div>
                <div class="white_bac">
                  <span
                    v-if="detailData.provider != null"
                    style="margin-left: 0"
                  >
                    {{ detailData.provider }}
                  </span>
                  <span
                    v-if="detailData.categoryName != null"
                    style="
                      border-left: 1px solid #dbe2ed;
                      border-right: 1px solid #dbe2ed;
                      padding: 0 16px;
                    "
                  >
                    {{ detailData.categoryName }}
                  </span>
                  <span v-if="detailData.createTime != null">
                    {{ detailData.createTime }}
                  </span>
                </div>

                <div class="addCar">
                  <button v-if="detailData.addCart">已加入</button>
                  <button v-else @click="add">加入预选</button>
                </div>
              </div>
              <div
                style="display: block; margin-left: 0; margin-bottom: 8px"
              ></div>
            </div>
            <div class="left_bottom"></div>
          </div>
        </div>
      </div>

      <div class="anchors">
        <a-anchor
          direction="horizontal"
          :affix="false"
          v-for="(item, key) in anchorList"
          @click="handleClick"
        >
          <a-anchor-link
            :class="{ currentActive: isActive === key }"
            @click="change(key)"
            :href="item.href"
            :title="item.title"
          />
        </a-anchor>
      </div>
      <div class="content" id="anchorContent">
	      	<div class="card applyCard" :id="cardItem.href" v-for="(cardItem) in anchorList">
	      		<div v-for="(item, index) in valueData" :key="index">
	      			<div class="card_content" style="padding-top: 0px; display: block" v-if="item.type == cardItem.type">
	            <div class="tab_content">
	              <img src="@/assets/images/solution/detail/leftIcon.png" style="width: 33px; height: 22px" alt=""/>
	              <div class="tit">{{cardItem.title}}</div>
	              <img src="@/assets/images/solution/detail/rightIcon.png" style="width: 33px; height: 22px" alt=""/>
	            </div>
	            <div class="cards">
	            	<div class="item_card" v-if="item.type == 5" v-for="(moduleItem,moduleIndex) in item.moduleList">
	                <img v-if="`${moduleItem.abilityPicture}`" :src="`${moduleItem.abilityPicture}`" alt="" class="img"/>
	                <img v-else src="@/assets/images/ability/adlityDetail/apply.png" class="img"/>
	                <p class="title">{{ moduleItem.name }}</p>
	                <p class="desc">{{ moduleItem.abilityIntro }}</p>
	              </div>
	              <div class="oneCards" v-else>
			              <img v-if="item.moduleList[0].abilityPicture == '' || item.moduleList[0].abilityPicture == null"
			                src="../../../assets/images/ability/adlityDetail/bac.png"
			                alt=""
			              />
			              <img v-else :src="`${item.moduleList[0].abilityPicture}`" alt="" />
			            <div class="right">
			              <div>
			                {{ item.moduleList[0].abilityIntro }}
			              </div>
			              <div class="file" v-if="item.moduleList[0].fileInfo != null" style="margin-top: 32px">
			                <img src="@/assets/images/solution/detail/text.png" alt="" style="width: 40px; height: 40px"/>
			                <a :href="`${item.moduleList[0].fileInfo.path}`">
			                	{{item.moduleList[0].fileInfo.name}}
			                </a>
			              </div>
			            </div>
	              </div>
	            </div>
	          </div>
	      		</div>
	          
	        </div>
        <div v-for="(item, index) in valueData" :key="index">
          <div class="tab_content" id="#market" v-if="item.type == 2">
            <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
            <div class="tit">市场分析</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          </div>

          <div class="tab_content" id="#need" v-if="item.type == 3">
            <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
            <div class="tit">市场需求</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          </div>

          <div class="tab_content" id="#desc" v-if="item.type == 4">
            <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
            <div class="tit">方案概述</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          </div>

          <div class="tab_content" id="#apply" v-if="item.type == 5">
            <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
            <div class="tit">应用场景</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          </div>

          <div class="tab_content" id="#ability" v-if="item.type == 6">
            <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
            <div class="tit">部署方案</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          </div>

          <div class="tab_content" id="#team" v-if="item.type == 7">
            <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
            <div class="tit">合作模式</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          </div>

          <div class="tab_content" id="#example" v-if="item.type == 8">
            <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
            <div class="tit">应用案例</div>
            <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          </div>
          <div
            v-if="
              item.type == 5 ||
              item.type == 6 ||
              item.type == 7 ||
              item.type == 8
            "
            class="card applyCard"
          >
            <div
              class="tab_card"
              style="padding-top: 0px; display: block; width: 100%"
            >
              <a-tabs
                v-model:activeKey="functionKey"
                class="apply"
                type="'card'"
              >
                <a-tab-pane
                  :tab="val.name"
                  v-for="(val, key) in item.moduleList"
                  :key="key + 1"
                >
                  <div v-if="item.type == 5">
                    <img
                      v-if="val.abilityPicture == '' || val.abilityPicture == null"
                      src="../../../assets/images/ability/adlityDetail/apply.png"
                      alt=""
                    />
                    <img
                      v-else
                      :src="`${val.abilityPicture}`"
                      alt=""
                      class="img"
                    />
                  </div>
                  <div v-if="item.type == 6">
                    <img
                      v-if="val.abilityPicture == '' || val.abilityPicture == null"
                      src="../../../assets/images/ability/adlityDetail/ability.png"
                      alt=""
                    />
                    <img
                      v-else
                      :src="`${val.abilityPicture}`"
                      alt=""
                      class="img"
                    />
                  </div>
                  <div v-if="item.type == 7">
                    <img
                      v-if="val.abilityPicture == '' || val.abilityPicture == null"
                      src="../../../assets/images/ability/adlityDetail/team.png"
                      alt=""
                    />
                    <img
                      v-else
                      :src="`${val.abilityPicture}`"
                      alt=""
                      class="img"
                    />
                  </div>
                  <div v-if="item.type == 8">
                    <img
                      v-if="val.abilityPicture == '' || val.abilityPicture == null"
                      src="../../../assets/images/ability/adlityDetail/example.png"
                      alt=""
                    />
                    <img
                      v-else
                      :src="`${val.abilityPicture}`"
                      alt=""
                      class="img"
                    />
                  </div>

                  <div class="right" style="flex: 1">
                    <p class="desc" style="line-height: 28px">
                      {{ val.abilityIntro }}
                    </p>
                    <div class="file" v-if="val.fileInfo != null">
                      <img
                        src="@/assets/images/solution/detail/text.png"
                        alt=""
                        style="width: 40px; height: 40px"
                      />
                      <a :href="`${val.fileInfo.url}`">{{
                        val.fileInfo.name
                      }}</a>
                    </div>
                  </div>
                </a-tab-pane>
              </a-tabs>
            </div>
          </div>
          <div class="card" v-else>
            <!-- <div class="card_content"> -->
            
            <div v-if="item.type == 2">
              <img
                v-if="
                  item.moduleList[0].abilityPicture == '' ||
                  item.moduleList[0].abilityPicture == null
                "
                src="../../../assets/images/ability/adlityDetail/market.png"
                alt=""
              />
              <img v-else :src="`${item.moduleList[0].abilityPicture}`" alt="" />
            </div>
            <div v-if="item.type == 3">
              <img
                v-if="
                  item.moduleList[0].abilityPicture == '' ||
                  item.moduleList[0].abilityPicture == null
                "
                src="../../../assets/images/ability/adlityDetail/need.png"
                alt=""
              />
              <img v-else :src="`${item.moduleList[0].abilityPicture}`" alt="" />
            </div>
            <div v-if="item.type == 4">
              <img
                v-if="
                  item.moduleList[0].abilityPicture == '' ||
                  item.moduleList[0].abilityPicture == null
                "
                src="../../../assets/images/ability/adlityDetail/desc.png"
                alt=""
              />
              <img v-else :src="`${item.moduleList[0].abilityPicture}`" alt="" />
            </div>

            <div class="right">
              <div>
                {{ item.moduleList[0].abilityIntro }}
              </div>
              <div
                class="file"
                v-if="item.moduleList[0].fileInfo != null"
                style="margin-top: 32px"
              >
                <img
                  src="@/assets/images/solution/detail/text.png"
                  alt=""
                  style="width: 40px; height: 40px"
                />
                <a :href="`${item.moduleList[0].fileInfo.path}`">{{
                  item.moduleList[0].fileInfo.name
                }}</a>
              </div>
            </div>
            <!-- </div> -->
          </div>
        </div>

        <div
          class="tab_content"
          id="#download"
          v-if="detailData.fileList && detailData.fileList.length > 0"
        >
          <!-- <div class="left" v-if="isShow == 'download'"></div> -->
          <img src="@/assets/images/solution/detail/leftIcon.png" alt="" />
          <div class="tit">方案附件</div>
          <img src="@/assets/images/solution/detail/rightIcon.png" alt="" />
          <!-- <div class="right" v-if="isShow == 'download'"></div> -->
        </div>
        <ul class="list" v-if="detailData.solutionFileList && detailData.solutionFileList.length > 0">
          <li v-for="(item, key) in detailData.solutionFileList">
            <div class="li_box" @click="fileShow(item)">
              <div class="left_box">
                <img
                  src="../../../assets/images/solution/detail/word.png"
                  alt=""
                  @click="doladFile"
                  style="width: 40px; height: 40px"
                />
                <p class="fileText">{{ item.name }}</p>
              </div>
              <img
                src="../../../assets/images/solution/detail/download.png"
                alt=""
                @click.stop="downloadBtn(item)"
                style="cursor: pointer"
              />
            </div>
          </li>
        </ul>
      </div>
      <view-list :id="viewId" :type="viewType"></view-list>
      <img
        class="top"
        src="../../../assets/images/solution/detail/toTap.png"
        alt=""
        @click="scrollUp"
      />
      <div class="bottom"></div>
    </div>
  </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, UnwrapRef, reactive } from "vue";
import { useRouter, useRoute } from "vue-router";
import {
  getDetail,
  getDownCount,
  cancelCollect,
  collect,
} from "../../../api/solutionNew/detail";
import { pptTopdf } from "../../../api/fileUpload/uploadFile.js"
import viewList from "../../product/detail/viewList.vue";
import { getTradeList } from "../../../api/solutionNew/home";
import { addShoppingCart } from "../../../api/combine/shoppingCart.js";
import axios from "axios";
import { message } from "ant-design-vue";
import eventBus from "../../../utils/eventBus";
interface FormState {
  name: string;
  code: string;
  categoryId: string | undefined;
  estimatedAmount: string;
  solutionId: string;
  status: number;
}
const route = useRoute();
const viewId = ref(route.query.id)
const viewType = ref('1')
onMounted(() => {
  getData();
});
const functionKey = ref(1);
const isActive = ref(0);
const collectActive = ref(false);
function change(v) {
  isActive.value = v;
}
const downloadBtn = (e) => {
  getDownCount(route.query.id)
    .then((res) => {
      if (res.code == 200) {
        const href = e.url;
        const downName = e.name;
        let windowOrigin = window.location.origin;
				let token = localStorage.getItem("token");
				let newHref = href;
	      if(href.includes(windowOrigin)){
	      	newHref = "/portal" + href.split(windowOrigin)[1]
	      }
				window.open(windowOrigin + newHref + "?token=" + token);
    		return false;
        axios
          .get(href, { responseType: "blob" })
          .then((res) => {
            const blob = new Blob([res.data]);
            const link = document.createElement("a");
            link.href = URL.createObjectURL(blob);
            link.download = downName;
            link.click();
            URL.revokeObjectURL(link.href);
          })
          .catch(console.error);
      }
    })
    .catch((err) => {
      console.log(err);
    });
};
const fileShow = (val) => {
	pptTopdf({
		filePath : val.path,
		fileUrl: val.url
	}).then(res=>{
		if(res.code == 200){
			let windowOrigin = window.location.origin;
			let token = localStorage.getItem("token");
			let newHref = res.data;
	    if(res.data.includes(windowOrigin)){
	     	newHref = "/portal" + res.data.split(windowOrigin)[1]
	    }
			window.open(windowOrigin + newHref + "?token=" + token);
		}
	})
};
const tabList = ref([]);
const getTarde = () => {
  let tradeParams = {};
  getTradeList(tradeParams).then((result) => {
    result.data.map((item) => {
      tabList.value.push({
        name: item.name,
        id: item.id.toString(),
      });
    });
  });
};
getTarde();
const previewVisible = ref(false);
const formState: UnwrapRef<FormState> = reactive({
  name: "",
  code: "",
  categoryId: undefined,
  estimatedAmount: "",
  solutionId: undefined,
  status: undefined,
});

const collectById = () => {
  if (detailData.value.collect == 1) {
    cancelCollect(route.query.id)
      .then(() => {
        message.success("取消收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  } else {
    collect(route.query.id)
      .then(() => {
        message.success("收藏成功");
        getData();
      })
      .catch((err) => {
        console.log(err);
      });
  }
  collectActive.value = !collectActive.value;
};
const getCurrentAnchor = () => {
  return currentAnchor.value;
};
const currentAnchor = ref("#desc");
const back = () => {
  Router.back(-1);
};
// 滚动函数
const scrollUp = () => {
  currentAnchor.value = "#desc";
  getCurrentAnchor();
  isActive.value = 0;
  document.getElementById("layout_content").scrollTo({
    top: 0,
    behavior: "smooth",
  });
};
const detailData = ref({});
const valueData = ref([]);
const add = () => {
  addShoppingCart({
    schemeId: route.query.id,
    type: "1",
  }).then((res) => {
    getData();
    eventBus.emit("cartRefresh");
  });
};

const anchorPoints = ref([
        {
          key: "bac",
          href: "#bac",
          title: "政策背景",
          type: 1,
        },
        {
          key: "market",
          href: "#market",
          title: "市场分析",
          type: 2,
        },
        {
          key: "need",
          href: "#need",
          title: "市场需求",
          type: 3,
        },
        {
          key: "desc",
          href: "#desc",
          title: "方案概述",
          type: 4,
        },

        {
          key: "apply",
          href: "#apply",
          title: "应用场景",
          type: 5,
        },
        {
          key: "ability",
          href: "#ability",
          title: "部署方案",
          type: 6,
        },
        {
          key: "team",
          href: "#team",
          title: "合作模式",
          type: 7,
        },
        {
          key: "example",
          href: "#example",
          title: "应用案例",
          type: 8,
        },
      ]);

const getData = () => {
  getDetail(route.query.id)
    .then((res) => {
      anchorList.value = [];
      res.data.createTime = res.data.createTime.slice(0, 10);
      res.data.moduleBody.sort((a, b) => a.type - b.type);
      
      res.data.moduleBody.forEach((item) => {
        const anchor = anchorPoints.value.find((a) => a.type === item.type);
        if (anchor) {
          anchorList.value.push(anchor);
        }
      });
      if (res.data.solutionFileList.length > 0) {
        anchorList.value.push({
          key: "download",
          href: "#download",
          title: "方案附件",
        });
      } else {
        res.data.fileList = false;
      }
      res.data.labelName = res.data.labelName.split(",");
      detailData.value = res.data;
      valueData.value = res.data.moduleBody;
    })
    .catch((err) => {
      console.log(err);
    });
};
const isShow = ref("desc");
const anchorList = ref([]);
const Router = useRouter();
const handleClick = (e, link) => {
  const href = link.href.replace("#", "");
  e.preventDefault();
  currentAnchor.value = "#" + href;
  let srcolls = document.getElementById(link.href);
  srcolls &&
    srcolls.scrollIntoView({
      block: "center",
      behavior: "smooth",
    });
  isShow.value = href;
};
</script>
<style lang="scss" scoped>
@import "./index.scss";
</style>

<style lang="scss">
.label {
  span {
    display: inline-block;
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #236cff;
    font-weight: 400;
    font-size: 14px;
    color: #236cff;
    line-height: 25px;
    padding: 0 10px;
  }
}
.dialogModal {
  .dia_box {
    background-image: url("@/assets/images/solution/detail/downBgc.png");
    height: 150px;
    padding: 20px 24px;
  }
  .ant-modal .ant-modal-title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    text-align: center;
  }
  .ant-modal-content {
    height: 395px;
    padding: 0;
  }
  .ant-form {
    width: 100%;
  }
  .title {
    font-weight: bold;
    font-size: 24px;
    color: #122c6c;
    line-height: 28px;
    margin-bottom: 8px;
  }

  .ant-tabs-tab-active {
    background: rgba(214, 228, 255, 0.6);
    border-radius: 2px 2px 2px 2px;

    .ant-tabs-tab-btn {
      color: #ffffff !important;
    }
  }

  :deep(.ant-tabs-nav-wrap) {
    margin-top: 16px;
    width: 236px !important;
    // overflow-x: auto;
    height: 48px;
    background: #ffffff;
    border-radius: 10px 10px 10px 10px;
    border: 1px solid #ffffff;
  }

  .ant-input {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
  }

  .ant-input-affix-wrapper {
    background: linear-gradient(
      196deg,
      #eaeff7 0%,
      rgba(234, 239, 247, 0.41) 100%
    );
    box-shadow: 0px -8px 32px 0px #ffffff, inset 0px 8px 24px 0px #dfe4ed;
    border-radius: 4px 4px 4px 4px;

    button {
      font-weight: 500;
      font-size: 16px;
      color: #1a66fb;
      line-height: 28px;
    }
  }

  .ant-tabs-nav::before {
    display: none;
  }

  .ant-tabs-tabpane {
    background-color: #ffffff !important;
    font-weight: 500;
    font-size: 16px;
    color: #2e3852;
    line-height: 28px;
    height: 150px;
  }

  .ant-tabs-ink-bar {
    display: none;
  }

  .ant-tabs-content {
    padding-left: 10px;
  }
  .ant-tabs-tab {
    // width: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: #4d6886 !important;
  }
  .key {
    font-weight: 400;
    font-size: 16px;
    color: #2b3f66;
    line-height: 28px;
  }
}
:deep(.ant-tabs-nav-wrap) {
  margin-top: 16px;
  width: 236px !important;
  overflow-x: auto !important;
  height: 48px;
  background: #ffffff;
  border-radius: 10px 10px 10px 10px;
  border: 1px solid #ffffff;
}
</style>
