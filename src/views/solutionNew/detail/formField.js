const formField = [
  {
    title: "方案概述",
    type: 1,
    moduleList: [{
      picLabel: "封面图片",
      image: "",
      summary: "",
      descPlaceholder: "请输入方案概述，限制500字以内",
      moduleFileList: [],
      loading: false
    }]

  },
  // {
  //   title: "市场分析",
  //   type: 2,
  //   moduleList: [{
  //     image: "",
  //     summary: "",
  //     picLabel: "封面图片",
  //     descPlaceholder: "请输入市场分析，限制500字以内",
  //     moduleFileList: [],
  //     loading: false
  //   }]

  // },
  // {
  //   title: "需求分析",
  //   type: 3,
  //   moduleList: [{
  //     image: "",
  //     summary: "",
  //     picLabel: "封面图片",
  //     descPlaceholder: "请输入需求分析，限制500字以内",
  //     moduleFileList: [],
  //     loading: false
  //   }]

  // },
  // {
  //   title: "方案概述",
  //   type: 4,
  //   moduleList: [{
  //     image: "",
  //     picLabel: "封面图片",
  //     summary: "",
  //     descPlaceholder: "请输入能力概述，限制500字以内",
  //     moduleFileList: [],
  //     loading: false
  //   }]

  // },
  {
    title: "应用场景",
    type: 5,
    moduleList: [{
      name: "",
      nameLabel: "应用场景名称",
      namePlaceholder: "请输入应用场景名称，限制200字",
      image: "",
      picLabel: "场景配图",
      summary: "",
      descPlaceholder: "请输入场景描述，限制500字以内",
      functionList: [],
      selectLabelId: [],
      caseList: [],
      moduleFileList: [],
      fileInfo: {},
      loading: false,
      sceneType: 1,
      id:null
    }],

  },

  // {
  //   title: "部署方案",
  //   type: 6,
  //   moduleList: [{
  //     name: "",
  //     nameLabel: "部署方案名称",
  //     namePlaceholder: "请输入部署方案名称，限制200字",
  //     image: "",
  //     picLabel: "封面图片",
  //     summary: "",
  //     descPlaceholder: "请输入部署方案，限制500字以内",
  //     moduleFileList: [],
  //   }]
  // },
  // {
  //   title: "合作模式",
  //   type: 7,
  //   moduleList: [{
  //     name: "",
  //     nameLabel: "合作模式名称",
  //     namePlaceholder: "请输入合作模式名称，限制200字",
  //     image: "",
  //     picLabel: "封面图片",
  //     summary: "",
  //     descPlaceholder: "请输入合作模式，限制500字以内",
  //     moduleFileList: [],
  //   }]

  // },
  {
    title: "应用案例",
    type: 8,
    moduleList: [{
      picLabel: "封面图片",
      name: "",
      nameLabel: "应用案例名称",
      namePlaceholder: "请输入应用案例，限制200字",
      image: "",
      summary: "",
      descPlaceholder: "请输入应用案例，限制500字以内",
      projectName: "",
      scale: "",
      time: "",
      projectIntroduction: "",
      moduleFileList: [],
      logoCase:"",
      logoOther:"",
      fileWays: "1",
      caseType: 1,
      id:null
    }]
  },
]
export default formField
