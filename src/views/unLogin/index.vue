<template>
  <div class="content">
    <div class="banner">
      <img src="../../assets/images/IfLogin/unLogin.png" alt="" />
    </div>
    <div class="list" v-if="dataSource.length != 0">
      <ul class="cardList">
        <li
          class="card"
          v-for="(value, key) in dataSource"
          @click="getActiveDetail(value.id)"
        >
          <div class="left">
            <span class="mounth">{{ value.updateTime.slice(5, 10) }}</span>
            <span class="year">{{ value.updateTime.slice(0, 4) }}</span>
            <div class="line"></div>
          </div>
          <div class="center">
            <div class="tit">{{ value.title }}</div>
            <div class="con">
              {{ value.articleSummary }}
            </div>
            <div class="con_bot">
              <img src="../../assets/images/IfLogin/listEye.png" alt="" />
              <span class="num">{{ value.readingVolume }}</span>
            </div>
          </div>
          <div class="right">
            <img :src="`${value.articleCover}`" alt="" />
          </div>
        </li>
      </ul>
      <div class="layPage">
        <pagination
          :totalItemCount="totalItemCount"
          :currentPage="pageNo"
          :pageItemSize="pageSize"
          @size-change="sizeChange"
          @page-change="pageChange"
        />
      </div>
    </div>
    <div
      class="flex just-center"
      style="margin-top: 26px"
      v-if="dataSource.length == 0"
    >
      <img
        src="@/assets/images/make/noData.png"
        alt=""
        width="240"
        height="244"
      />
    </div>
  </div>
</template>
<script >
import { defineComponent, reactive, toRefs, ref, onMounted } from "vue";
import pagination from "../../components/pagination/index.vue";
import { getList } from "../../api/IfLogin/unLogin";
import { useRouter } from "vue-router";
import { getMakeUrl } from "@/utils/getUrl.js";
export default defineComponent({
  components: {
    pagination,
  },
  setup() {
    const data = reactive({
      totalItemCount: 0,
      pageNo: 1,
      pageSize: 10,
      dataSource: [],
    });
    const router = useRouter();
    const baseURL = getMakeUrl();
    const getDataList = () => {
      let params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        status: 2,
      };
      getList(params)
        .then((res) => {
          data.dataSource = res.data.rows;
          data.totalItemCount = res.data.totalRows;
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };
    const getActiveDetail = (e) => {
      data.isActive = e;
      router.push({
        query: {
          id: e,
          type: 2,
        },
        name: "detail",
      });
    };
    onMounted(() => {});
    getDataList();
    const pageChange = (page) => {
      data.pageNo = page;
      getDataList();
    };
    const sizeChange = (size) => {
      data.pageSize = size;
      getDataList();
    };
    return {
      ...toRefs(data),
      pageChange,
      sizeChange,
      getDataList,
      baseURL,
      getActiveDetail,
      router,
    };
  },
});
</script>
<style lang="scss" scoped>
ul {
  list-style: none;
}
.content {
  width: 100%;
  height: 100%;
  background-color: #f7f8fe;
  .banner {
    img {
      width: 100%;
    }
    height: 245px;
  }
  .list {
    margin: 30px 120px 70px 120px;
    background-color: #ffffff;
    .cardList {
      padding: 0px 50px;
      .card {
        display: flex;
        padding-top: 33px;
        padding-left: 9px;
        padding-bottom: 30px;
        border-bottom: 1px solid #dddddd;
        .left {
          position: relative;
          width: 142px;
          span {
            display: block;
          }
          .mounth {
            font-weight: 600;
            font-size: 24px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 40px;
            position: absolute;
            top: -10px;
          }
          .line {
            width: 24px;
            height: 0px;
            border: 1px solid #cccccc;
            position: absolute;
            top: 9px;
            right: 25px;
          }
          .year {
            width: 40px;
            height: 18px;
            border-radius: 0px 0px 0px 0px;
            border: 1px solid #cccccc;
            font-weight: 400;
            font-size: 14px;
            color: #cccccc;
            line-height: 18px;
            text-align: center;
            margin-left: 15px;
            margin-top: 10px;
            position: absolute;
            top: 26px;
          }
        }
        .center {
          width: 75%;
          .tit {
            font-weight: 500;
            font-size: 18px;
            color: rgba(0, 0, 0, 0.85);
            line-height: 23px;
            margin-bottom: 20px;
          }
          .con {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            line-height: 26px;
            margin-bottom: 16px;
          }
          .con_bot {
            width: 56px;
            height: 20px;
            background: rgba(30, 99, 255, 0.06);
            text-align: center;
            position: relative;
            img {
              width: 16px;
              height: 16px;
              position: absolute;
              bottom: 2px;
              left: 7px;
            }
            .num {
              font-weight: 400;
              font-size: 12px;
              position: absolute;
              bottom: 3px;
              left: 26px;
              color: rgba(0, 0, 0, 0.65);
              line-height: 14px;
            }
          }
        }
        .right {
          img {
            width: 200px;
          }
          // border: 1px solid red;
        }
      }
    }
  }
  .layPage {
    width: 100%;
    text-align: right;
    // margin: 15px 0;
    background-color: #ffffff;
    padding-top: 3vh;
    padding-bottom: 2.5vh;
    padding-right: 24px;
    height: 9vh;
  }
}
</style>