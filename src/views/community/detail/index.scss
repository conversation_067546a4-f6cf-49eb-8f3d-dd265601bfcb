.contentCard {
    margin: 70px auto 20px;
    width: 1200px;
}

.top_card {

    background: #FFFFFF;
    border-radius: 8px;

    .card_content {
        padding: 25px 40px;

        .text {
            font-weight: 500;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
        }
    }

    .card_table {
        display: flex;

        .table_left {
            width: 160px;
            min-height: 46px;
            background: #F5F7FC;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.65);
            text-align: right;
            border: 1px solid #E8EAED;
            border-right: none;
            border-bottom: none;
        }

        .table_right {
            flex: 1;
            min-height: 46px;
            color: rgba(0, 0, 0, 0.85);
            border: 1px solid #E8EAED;
            padding: 0 16px;
            border-left: none;
            border-bottom: none;
            word-break: break-all;
        }

        .table_line {
            border-bottom: 1px solid #E8EAED;
        }

        .tableFile {
            color: #0C70EB;
            border-bottom: 1px solid #0C70EB;
            width: fit-content;
            cursor: pointer;
        }
    }
}

.top_nav {
    background-color: #f5f7fc;
    padding-left: 120px;
    height: 60px;
    width: 100%;
    margin-top: 8px;
    padding-right: 120px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    top: 50px;
    z-index: 21;
    padding-top: 20px;

    div {
        display: inline-block;
    }

    .left_nav {
        padding-bottom: 8px;

        .title {
            font-weight: 400;
            font-size: 12px;
            color: #84899a;
            line-height: 20px;
            margin-right: 8px;
            cursor: pointer;
        }

        .current {
            font-weight: 400;
            font-size: 12px;
            color: #2e3852;
            line-height: 20px;
        }
    }
}

.leaveWord {
    background: #FFFFFF;
    border-radius: 8px;
    margin-top: 24px;

    .leaveContent {
        padding: 25px 40px;

        .textWord {
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
        }
    }


}

.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(255, 255, 255, 0.7);
    /* 半透明遮罩 */
    z-index: 9999;
}
.form-row,
.form-row * {
  box-sizing: border-box;
}
.form-row {
  display: flex;
  min-height: 48px;
  border-bottom: 1px solid #e8e8e8;
  align-items: stretch;
  /* 关键属性：让子元素撑满高度 */
}

.form-row:last-child {
  border-bottom: none;
}

.label-col {
  background-color: #f5f7fc;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding: 0 16px;
  /* 上下 padding 改为 0 */
  margin: 0;
  /* 确保没有 margin */
  height: auto;
  /* 确保高度自适应 */
}

.input-col {
  padding: 8px 16px;
  display: flex;
  /* 新增 */
  align-items: center;
  /* 确保内容垂直居中 */
}
.custom-upload-item {
  display: flex;
  align-items: center;
  padding: 8px 12px;
  border-radius: 4px;
  background: #fafafa;
  width: 100%;
}

.custom-upload-item .anticon {
  margin-right: 8px;
  font-size: 16px;
  color: #1890ff;
}

.file-name {
  flex: 1;
  padding-right: 8px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 14px;
}

.delete-btn {
  flex-shrink: 0;
  color: #ff4d4f;
}

.delete-btn:hover {
  background: rgba(255, 77, 79, 0.1);
}
.label-text {
  font-weight: 400;
  font-size: 14px;
  color: rgba(0, 0, 0, 0.65);
  padding-right: 16px;
}

.ant-input,
.ant-select-selector,
.ant-input-affix-wrapper {
  border: none !important;
  box-shadow: none !important;
}

.ant-input:focus,
.ant-select-focused .ant-select-selector {
  box-shadow: none !important;
}

.form-row,
.form-row * {
  box-sizing: border-box;
}

.ant-col {
  margin: 0;
  padding: 0;
}