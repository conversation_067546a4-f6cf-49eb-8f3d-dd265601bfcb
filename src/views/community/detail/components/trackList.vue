<template>
  <div class="modelContent">
    <div class="title">
      <img
        src="@/assets/images/community/arrowRight.png"
        width="14px"
        height="8px"
        style="margin-right: 10px"
        alt=""
      />
      <img
        src="@/assets/images/community/track.png"
        width="72px"
        height="18px"
        alt=""
      />
    </div>
    <div class="text"></div>
    <el-timeline>
      <el-timeline-item
        v-for="(item, key) in history"
        :timestamp="getProcessText(item.process)"
        placement="top"
        :key="key"
      >
        <div class="card-content">
          <div class="flex align-center just-sb">
            <div class="flex align-center">
              <div class="word">{{ item.dealName }}</div>
              <div class="time">{{ item.createTime }}</div>
            </div>
            <div class="textName" v-if="item.process == 1">
              送审核管理员审核
            </div>
            <div
              class="textName"
              v-if="item.process == 2 && item.auditResult == 0"
            >
              审核管理员驳回
            </div>
            <div
              class="textName"
              v-if="item.process == 2 && item.auditResult == 1"
            >
              送给主办/协办部门处理
            </div>
            <div
              class="textName"
              v-if="item.process == 3 && item.auditResult == 0"
            >
              主办部门驳回
            </div>
            <div
              class="textName"
              v-if="item.process == 3 && item.auditResult == 1"
            >
              主办部门已处理
            </div>
            <div
              class="textName"
              v-if="item.process == 4 && item.auditResult == 1"
            >
              协办部门已处理
            </div>
            <div
              class="textName"
              v-if="item.process == 5 && item.auditResult == 1"
            >
              发帖人已确认
            </div>
          </div>
          <div
            style="margin-top: 12px"
            v-if="
              item.process == 2 || (item.process == 3 && item.auditResult == 0)
            "
          >
            {{ item.auditReason }}
          </div>
        </div>
      </el-timeline-item>
    </el-timeline>
  </div>
</template>

<script>
import { defineComponent, toRefs, reactive } from "vue";

export default defineComponent({
  props: {
    history: {
      type: Array,
      default() {
        return [];
      },
    },
  },
  setup(props) {
    const data = reactive({
      history: props.history,
    });
    console.log(data.history, `historyhistory`);
    const getProcessText = (v) => {
      if (v == 1 || v == 5) {
        return "发布人";
      }
      if (v == 2) {
        return "审核管理员";
      }
      if (v == 3) {
        return "主办部门";
      }
      if (v == 4) {
        return "协办部门";
      }
    };
    return {
      ...toRefs(data),
      getProcessText,
    };
  },
});
</script>

<style lang="scss" scoped>
::v-deep(.el-timeline-item__timestamp) {
  font-weight: 500;
  font-size: 16px;
  color: rgba(0, 0, 0, 0.85);
}
::v-deep(.el-timeline-item__node) {
  width: 10px;
  height: 10px;
  left: 0;
  top: 5px;
  background: #0c70eb;
}
::v-deep(.el-timeline-item__wrapper) {
  padding-left: 20px;
}
::v-deep(.el-timeline-item__tail) {
  border-left: 2px dashed rgba(0, 0, 0, 0.08);
  margin-top: 12px;
}
.modelContent {
  margin-right: 16px;
}
.title {
  margin-top: -8px;
}
.text {
  font-size: 16px;
  font-weight: bold;
  text-align: center;
  color: rgba(0, 0, 0, 0.85);
  margin: 32px 0;
}
.card-content {
  background: #f5f7fc;
  border-radius: 4px;
  padding: 12px 16px;

  .word {
    color: rgba(0, 0, 0, 0.85);
    font-size: 14px;
  }
  .time {
    color: rgba(0, 0, 0, 0.25);
    font-size: 14px;
    margin-left: 16px;
  }
  .textName {
    font-weight: 500;
    font-size: 14px;
    color: #0c70eb;
  }
  .wordName {
    font-weight: 500;
    font-size: 14px;
    color: #00bd62;
  }
}
</style>

