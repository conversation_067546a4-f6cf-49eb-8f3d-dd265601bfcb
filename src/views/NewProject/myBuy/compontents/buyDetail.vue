<template>
  <div class="buyDetail" ref="buyRef">
    <div class="canvasBox">
      <div
        class="flex align-center just-center"
        :style="`width:${
          tableList.length > 2
            ? '3200px'
            : tableList.length == 2
            ? '1402px'
            : '1100px'
        }`"
        style="position: relative"
      >
        <div class="list_tit">产品列表</div>
      </div>
      <div class="list">
        <div
          class="buyList"
          :style="`width:${
            tableList.length > 2
              ? '3200px'
              : tableList.length == 2
              ? '1362px'
              : '1000px'
          }`"
        >
          <div class="flex">
            <a-table
              :dataSource="tableList[0]"
              :columns="columns"
              :pagination="{ pageSize: 15 }"
              bordered
            >
              <template #index="{ record, text, index }">
                <span class="name">{{ index + 1 }}</span>
              </template>
              <template #productName="{ record, text }">
                <span class="productType">{{ text }}</span>
              </template>
              <template #productType_specification="{ record, text }">
                <span class="productType">{{ dealData(text, "1") }}</span>
              </template>
              <template #price="{ record, text }">
                <span class="name">{{ dealData(text, "2") }}</span>
              </template>
            </a-table>
            <a-table
              v-if="tableList.length > 1"
              :dataSource="tableList[1]"
              :columns="columns"
              :pagination="{ pageSize: 15 }"
              bordered
            >
              <template #index="{ record, text, index }">
                <span class="name">{{ index + 1 + 15 }}</span>
              </template>
              <template #productName="{ record, text }">
                <span class="productType">{{ text }}</span>
              </template>
              <template #productType_specification="{ record, text }">
                <span class="productType">{{ dealData(text, "1") }}</span>
              </template>
              <template #price="{ record, text }">
                <span class="name">{{ dealData(text, "2") }}</span>
              </template>
            </a-table>
            <a-table
              v-if="tableList.length > 2"
              :dataSource="tableList[2]"
              :columns="columns"
              :pagination="{ pageSize: 15 }"
              bordered
            >
              <template #index="{ record, text, index }">
                <span class="name">{{ index + 1 + 30 }}</span>
              </template>
              <template #productName="{ record, text }">
                <span class="productType">{{ text }}</span>
              </template>
              <template #productType_specification="{ record, text }">
                <span class="productType">{{ dealData(text, "1") }}</span>
              </template>
              <template #price="{ record, text }">
                <span class="name">{{ dealData(text, "2") }}</span>
              </template>
            </a-table>
          </div>
        </div>
      </div>
      <!-- <div class="boot">
        <div class="box">
          <span class="left_tit">客户经理</span>
          <span class="left_con" v-if="buyDetail.customerManager != ''">{{
              buyDetail.customerManager
            }}</span>
          <span class="left_con" v-else>-</span>
        </div>
        <div class="box">
          <span class="left_tit">联系电话</span>
          <span class="left_con" v-if="buyDetail.managerNumber != ''">{{
              buyDetail.managerNumber
            }}</span>
          <span class="left_con" v-else>-</span>
        </div>
        <div class="box">
          <span class="left_tit">生成日期</span>
          <span class="left_con" v-if="buyDetail.contractDate != ''">{{
              buyDetail.contractDate
            }}</span>
          <span class="left_con" v-else>-</span>
        </div>
      </div> -->
    </div>
  </div>
</template>
<script>
import {
  defineComponent,
  reactive,
  toRefs,
  ref,
  watch,
  defineExpose,
} from "vue";
import { useHomeStore } from "@/store";
import html2canvas from "html2canvas";
import { productMerge } from "@/api/AI/ai.js";
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
export default defineComponent({
  props: {
    propObj: Object,
  },
  emits: ["dataSent"],
  setup(props, context) {
    const homeStore = useHomeStore();
    const buyRef = ref(null);
    const data = reactive({
      tableList: [[]],
      buyDetail: homeStore.aiBuyListStroe,
      columns: [
        {
          title: "序号",
          dataIndex: "index",
          key: "index",
          align: "center",
          slots: { customRender: "index" },
          width: "60px",
        },
        {
          title: "产品名称",
          dataIndex: "productName",
          key: "productName",
          align: "center",
          slots: { customRender: "productName" },
          width: "160px",
        },
        // {
        //   title: "产品介绍",
        //   dataIndex: "productDesc",
        //   key: "productDesc",
        //   align: "left",
        //   ellipsis: false,
        //   slots: { customRender: "productDesc" },
        // },
        // {
        //   title: "产品规格",
        //   dataIndex: "productType_specification",
        //   key: "productType_specification",
        //   // align: "left",
        //   ellipsis: false,
        //   slots: { customRender: "productType_specification" },
        //   width: "300px",
        // },
        // {
        //   title: "标准价格",
        //   dataIndex: "price",
        //   key: "price",
        //   align: "center",
        //   ellipsis: false,
        //   slots: { customRender: "price" },
        //   width: "20%",
        // },
        {
          title: "产品数量",
          dataIndex: "productQuantity",
          key: "productQuantity",
          align: "center",
          ellipsis: true,
          slots: { customRender: "productQuantity" },
          width: "120px",
        },
      ],
      str: "",
    });
    watch(
      () => homeStore.aiBuyListStroe,
      (newVal) => {
        data.buyDetail = newVal;
      }
    );
    const sliceBuy = (arr, size) => {
      let result = [];
      let m = 0;
      for (let i = 0; i < arr.length; i += size) {
        m++;
        result.push(arr.slice(i, m * size));
      }
      return result;
    };
    const createImg = (obj) => {
      data.buyDetail = obj;
      data.tableList = sliceBuy(data.buyDetail.productPackageLists, 15);
      setTimeout(() => {
        createCanvas();
      }, 300);
    };
    const createCanvas = () => {
      let num = 0.5;
      if (data.tableList.length > 1) {
        num = 0.4;
      }
      html2canvas(buyRef.value, {
        allowTaint: true,
        useCORS: true,
        width: buyRef.value.scrollWidth,
        height: buyRef.value.offsetHeight,
        windowWidth: buyRef.value.scrollWidth,
        backgroundColor: null,
        scale: 1,
      }).then(function (canvas) {
        let img = new Image();
        img.src = canvas.toDataURL("image/png");
        data.str = img.src;
        // console.log("img", img.src);
        // return
        // console.log(data.buyDetail, `oooooo`);
        const parts = data.str.split(";base64,");
        const contentType = parts[0].split(":")[1];
        let strArr = contentType.split("/");
        // console.log("strArr", strArr);
        const raw = window.atob(parts[1]);
        const rawLength = raw.length;
        const uInt8Array = new Uint8Array(rawLength);
        for (let i = 0; i < rawLength; ++i) {
          uInt8Array[i] = raw.charCodeAt(i);
        }
        const blob = new Blob([uInt8Array], { type: contentType });
        const blobUrl = URL.createObjectURL(blob);
        const newFilename = "图片" + "." + strArr[1];
        const file = new File([blob], newFilename, { type: contentType });
        let strArr1 = file.type.split("/");
        var fileForm = new FormData();
        fileForm.append("file", file);
        var newPicName = "首页" + "." + strArr1[1];
        fileForm.append("picName", newPicName);
        // uploadFileList(fileForm).then((res) => {
        let dataList = homeStore.aiBuyListStroe.productPackageLists;
        let arr = [];
        dataList.forEach((item) => {
          arr.push({
            id: item.productId,
            name: item.productName,
            tariff: item.productType_specification,
            count: item.productQuantity,
          });
        });
        let params = {
          productList: arr,
          title: homeStore.aiBuyListStroe.title,
          conclusion: homeStore.aiBuyListStroe.conclusion,
        };
        productMerge(params).then((res) => {
          if(res.code == 200){
          	let windowOrigin = window.location.origin;
						let token = localStorage.getItem("token");
						let newHref = res.data.pptFile;
			      if(res.data.pptFile.includes(windowOrigin)){
			      	newHref = "/portal" + res.data.pptFile.split(windowOrigin)[1]
			      }
					  window.open(windowOrigin + newHref + "?token=" + token);
            data.loadingShow = false;
            let show = false;
            context.emit("dataSent", show);
          }else{
            data.loadingShow = false;
            let show = false;
            context.emit("dataSent", show);
          }
        });
        // });
        return;
      });
    };
    function resizeBase64Image(base64, maxWidth, maxHeight, callback) {
      let img = new Image();
      img.src = base64;
      img.onload = function () {
        let width = img.width;
        let height = img.height;
        let ratio = width / height;

        if (width > maxWidth) {
          width = maxWidth;
          height = width / ratio;
        }

        if (height > maxHeight) {
          height = maxHeight;
          width = height * ratio;
        }

        let canvas = document.createElement("canvas");
        canvas.width = width;
        canvas.height = height;
        let ctx = canvas.getContext("2d");
        ctx.drawImage(img, 0, 0, width, height);
        let resizedBase64 = canvas.toDataURL("image/png", 1);
        callback(resizedBase64);
      };
      // img.src = base64;
    }

    const dealData = (e, type) => {
      if (e === undefined || e === null || e === "" || e === "-" || e >= 8888) {
        if (type == 1) return "以具体业务规格为准";
        if (type == 2) return "以具体业务定价为准";
        if (type == 3) return "-";
      } else {
        return e;
      }
    };
    defineExpose({ createImg });
    const base64ToBlob = (base) => {
      const parts = base.split(";base64,");
      const contentType = parts[0].split(":")[1];
      const raw = window.atob(parts[1]);
      const rawLength = raw.length;
      const uInt8Array = new Uint8Array(rawLength);

      for (let i = 0; i < rawLength; ++i) {
        uInt8Array[i] = raw.charCodeAt(i);
      }

      return new Blob([uInt8Array], { type: contentType });
    };
    const createObjectURL = (blod) => {
      return window.URL.createObjectURL(blob);
    };

    return {
      ...toRefs(data),
      homeStore,
      dealData,
      createObjectURL,
      base64ToBlob,
      createImg,
      createCanvas,
      buyRef,
    };
  },
});
</script>
<style lang="scss" scoped>
:deep(.ant-table-pagination.ant-pagination) {
  display: none !important;
}
.ant-table-thead > tr > th {
  text-align: center;
}
@import "./buyDetail.scss";
</style>