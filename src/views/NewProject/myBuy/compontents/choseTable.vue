<template>
  <a-tabs v-model:value="tabsActiveKey" @change="tabsChange">
    <a-tab-pane key="1" tab="添加产品"></a-tab-pane>
    <a-tab-pane key="2" tab="添加场景" force-render>
      <!-- <div class="searchInfo">
        <img style="width: 54px;height: 54px; position: absolute;z-index: 100;top: -8px;left: 18px;"
          src="../../../../assets/images/AI/ai.png" alt="">
        <div class="vocationPull">
          <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
            <a-input-search v-model:value="name" placeholder="请输入产品名称进行检索" @search="seekContent">
              <template #enterButton>
                <div class="flex just-center align-center">
                  <img style="width: 20px;height: 20px;" class="margin_r_16" src="../../../../assets/images/AI/search.png"
                    alt="">
                  <div>
                    搜索
                  </div>
                </div>
              </template>
            </a-input-search>
          </a-config-provider>
        </div>
      </div>
    
      <div class="tabContent">
        <div v-if="tableList && tableList.length > 0" style="width: 100%">
          <div class="cardContent">
            <div class="card_total">
              <template v-for="(item, index) in tableList" :key="index">
                <div :class="[
                  'card_content',
                  {
                    cardActive: cardActive == index,
                    rightActive: index % 2 != 0,
                    cardObvious: index < 2 && tableList.length < 3,
                    bottomLine:
                      (index == tableList.length - 1 ||
                        index == tableList.length - 2) &&
                      index > 1,
                    selectBorder: sceneSelectIds.includes(item.id),
                  },
                ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave">
                  <div style="display: flex; margin: 24px">
                    <a-checkbox-group :value="sceneSelectIds">
                      <a-checkbox :value="item.id" @change="proDetail(item)">
                      </a-checkbox>
                    </a-checkbox-group>
                    <div style="display: flex;margin-left: 12px;">
                  <div>
                    <div class="imgTitle">
                      <div>{{ item.name }}</div>
                      <img v-if="item.isKey == 1" class="mainScene" src="@/assets/images/scenario/mainSceneLeft.png"/>  
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div class="card_tag" style="display: flex;align-items: center;">
                        <a-tag color="#D7E6FF">{{ item.classifyName }}</a-tag>
                        <div class="card_title">{{ item.name }}</div>
                      </div>
                      <a-tag :bordered="false" class="cityStyle">{{
                        item.provider
                      }}</a-tag>
                    </div>
                    <div class="card_des">

                      <div>目标客户：{{ item.customers }}</div>
                      <div>需求方案组合：{{ item.list }}</div>
                    </div>
                    <div
                      class="flex"
                      style="justify-content: space-between"
                      v-if="item.typeName"
                    >
                      <div class="flex">
                        <a-tag
                          color="#D7E6FF"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ item.typeName }}</a-tag
                        >
                      </div>
                    </div>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-right: 5px;
                        "
                      >
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            margin-right: 18px;
                          "
                        >
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.viewCount"
                            >{{ item.viewCount }}</span
                          >
                          <span v-else>-</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
                  </div>
                </div>
              </template>
            </div>
          </div>
          <div class="layPage">
            <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage" :pageSizeOptions="pageSizeOptions"
              show-quick-jumper show-size-changer :total="totalItemCount" @change="pageChange" @showSizeChange="sizeChange"
              class="mypage" />
          </div>
          <div class="btn_box">
            <span class="refuse" @click="refuse">取消</span>
            <button :class="{
              submit: sceneSelectIds.length > 0 || selectIds.length > 0,
              disabled: sceneSelectIds.length == 0 && selectIds.length == 0,
            }" @click="submit" :disabled="sceneSelectIds.length == 0 && selectIds.length == 0">
              {{`确认添加（已选择${ sceneSelectIds.length }个场景,${selectIds.length }个产品）`}}
            </button>
          </div>
        </div>
        <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
          <img src="@/assets/images/home/<USER>" />
        </div>
        <div class="loading" v-show="loadingShow">
          <a-spin />
        </div>
      </div> -->
    </a-tab-pane>
  </a-tabs>
  <div class="searchInfo">
    <img style="width: 54px;height: 54px; position: absolute;z-index: 100;top: -8px;left: 18px;"
      src="../../../../assets/images/AI/ai.png" alt="">
    <div class="vocationPull">
      <a-config-provider :locale="zhCN" :getPopupContainer="(triggerNode) => triggerNode.parentNode">
        <a-input-search v-model:value="name" placeholder="请输入产品名称进行检索" @search="seekContent">
          <template #enterButton>
            <div class="flex just-center align-center">
              <img style="width: 20px;height: 20px;" class="margin_r_16" src="../../../../assets/images/AI/search.png"
                alt="">
              <div>
                搜索
              </div>
            </div>
          </template>
        </a-input-search>
      </a-config-provider>
    </div>
  <div class="selectData" ref="selectData" v-if="tabsActiveKey == 1">
    <div
      :class="[
        'selcet_box',
        { showMore: showScense == 'label' && showIndex === index },
      ]"
      v-for="(val, index) in vocationList"
      :key="index"
    >
      <div class="left_select">{{ val.label }}：</div>
      <div
        :class="[
          'right_select',
          { showHidden: showScense == 'label' && showIndex === index },
        ]"
      >
        <span
          ref="box"
          v-for="(value, key1) in val.children"
          :key="key1"
          :class="{ activeBtn: activeKey === value.value }"
          style="height: 49px"
        >
          <div
            class="title"
            @click="providerBtn(value, 'default', index)"
            @mouseenter="providerEnter(value, index)"
          >
            {{ value.label }}
          </div>
        </span>
      </div>
      <span
        class="more flex"
        v-if="val.children && val.children.length > 8 && showIndex !== index"
        @click="showMore('label', index)"
        >更多<img src="@/assets/images/solution/home/<USER>" alt=""
      /></span>
      <span
        class="more flex"
        v-if="val.children && val.children.length >= 8 && showIndex === index"
        @click="showless('label_less', index)"
        >收起<img src="@/assets/images/solution/home/<USER>" alt=""
      /></span>
    </div>
  </div>
  </div>

  <div class="tabContent">
    <div v-if="tableList && tableList.length > 0" style="width: 100%">
      <div class="cardContent">
        <div class="card_total">
          <template v-for="(item, index) in tableList" :key="index">
            <div :class="[
              'card_content',
              {
                cardActive: cardActive == index,
                rightActive: index % 2 != 0,
                cardObvious: index < 2 && tableList.length < 3,
                bottomLine:
                  (index == tableList.length - 1 ||
                    index == tableList.length - 2) &&
                  index > 1,
                selectBorder: selectIds.includes(item.id),
              },
            ]" @mouseenter="contentColor(index)" @mouseleave="contentLeave">
              <div v-if="tabsActiveKey == 1" style="display: flex; margin: 24px">
                <a-checkbox-group :value="selectIds">
                  <a-checkbox :value="item.id" @change="proDetail(item)">
                  </a-checkbox>
                </a-checkbox-group>
                <div>
                  <a-image :width="168" :height="105" :preview="false" v-if="item.image" :src="`${item.image}`" />
                  <img src="@/assets/images/home/<USER>" alt="" style="width: 168px; height: 105px" v-else />
                </div>
                <div class="card_center">
                  <div class="card_text">
                    <div class="card_tag">
                      <div class="card_title" @click="goDetail(item)">{{ item.name }}</div>

                      <span class="cardTag" style="background-color: #d7e6ff; color: #2e7fff">{{ item.industryName
                      }}</span>
                      <span class="cityStyle" v-if="item.provider">{{
                        item.provider
                      }}</span>
                    </div>
                  </div>
                  <div class="card_des">
                    {{ item.introduction }}
                  </div>
                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    ">
                    <div style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "></div>
                  </div>
                  <div style="
                      display: flex;
                      align-items: center;
                      justify-content: space-between;
                    ">
                    <div>
                      <img style="width: 112px; height: 22px" src="@/assets/images/home/<USER>" alt="" />
                    </div>
                    <div style="display: flex; align-items: center">
                      <img src="@/assets/images/home/<USER>" style="width: 16px; height: 16px" alt="" />
                      <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)" v-if="item.viewCount">{{ item.viewCount
                      }}</span>
                      <span v-else>-</span>
                    </div>
                  </div>
                </div>
              </div>
              <div v-else style="display: flex; margin: 24px">
                    <a-checkbox-group :value="sceneSelectIds">
                      <a-checkbox :value="item.id" @change="proDetail(item)">
                      </a-checkbox>
                    </a-checkbox-group>
                    <div style="display: flex;margin-left: 12px;">
                  <div>
                    <div class="imgTitle">
                      <div>{{ item.name }}</div>
                      <img v-if="item.isKey == 1" alt="" class="mainScene" src="@/assets/images/scenario/mainSceneLeft.png"/>  
                    </div>
                  </div>
                  <div class="card_center">
                    <div class="card_text">
                      <div class="card_tag" style="display: flex;align-items: center;">
                        <a-tag color="#D7E6FF">{{ item.classifyName }}</a-tag>
                        <div class="card_title">{{ item.name }}</div>
                      </div>
                      <a-tag :bordered="false" class="cityStyle">{{
                        item.provider
                      }}</a-tag>
                    </div>
                    <div class="card_des">

                      <div>目标客户：{{ item.customers }}</div>
                      <div>需求方案组合：{{ item.list }}</div>
                    </div>
                    <div
                      class="flex"
                      style="justify-content: space-between"
                      v-if="item.typeName"
                    >
                      <div class="flex">
                        <a-tag
                          color="#D7E6FF"
                          style="
                            display: block;
                            color: rgba(0, 0, 0, 0.45);
                            background-color: transparent;
                            border: 1px solid #d9d9d9;
                            line-height: 17px;
                          "
                          >{{ item.typeName }}</a-tag
                        >
                      </div>
                    </div>
                    <div
                      style="
                        display: flex;
                        align-items: center;
                        justify-content: space-between;
                      "
                    >
                      <div
                        style="
                          display: flex;
                          align-items: center;
                          justify-content: space-between;
                          margin-right: 5px;
                        "
                      >
                        <div
                          style="
                            display: flex;
                            align-items: center;
                            margin-right: 18px;
                          "
                        >
                          <img
                            src="@/assets/images/home/<USER>"
                            style="width: 16px; height: 16px"
                            alt=""
                          />
                          <span
                            style="font-size: 12px; color: rgba(0, 0, 0, 0.45)"
                            v-if="item.viewCount"
                            >{{ item.viewCount }}</span
                          >
                          <span v-else>-</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </template>
        </div>
      </div>
      <div class="layPage">
        <a-pagination v-model:pageSize="pageItemSize" v-model:current="currentPage" :pageSizeOptions="pageSizeOptions"
          show-quick-jumper show-size-changer :total="totalItemCount" @change="pageChange" @showSizeChange="sizeChange"
          class="mypage" />
      </div>
      <div class="btn_box">
        <span class="refuse" @click="refuse">取消</span>
        <button :class="{
          submit: sceneSelectIds.length > 0 || selectIds.length > 0,
          disabled: sceneSelectIds.length == 0 && selectIds.length == 0,
        }" @click="submit" :disabled="sceneSelectIds.length == 0 && selectIds.length == 0">
          {{`确认添加（已选择${ sceneSelectIds.length }个场景,${selectIds.length }个产品）`}}
        </button>
      </div>
    </div>
    <div v-if="tableList && tableList.length == 0" class="emptyPhoto">
      <img src="@/assets/images/home/<USER>" alt="" />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs, ref, watch, onMounted } from "vue";
import { useRouter } from "vue-router";
import { getProjectList } from "@/api/moduleList/home";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { addShopPro,toShopList,queryProduct } from "@/api/buyList/index";
import { getProductList } from "@/api/product/home";
import eventBus from "@/utils/eventBus";
import { getSeparateAIList } from "@/api/AI/ai.js"
import { fi } from "element-plus/es/locale/index.mjs";
import {
  merchantGetLabelList,
} from "@/api/product/merchant";
import {
  getSceneList,
} from "@/api/scenario/home";

export default defineComponent({
  components: {},
  props: {
    parentId: {
      type: Number,
      default: null,
    },
    proList: {
      type: Array,
      default: [],
    },
    id: {
      type: Number,
      default: null,
    },
    // nowCustomizeProductBagId: {
    //   type: Number,
    //   default: null,
    // }
  },
  setup(props, { emit }) {
    const box = ref(null)
    const selectData = ref(null)
    const vocation = ref("");
    const region = ref("");
    const data = reactive({
      tabsActiveKey:1,
      localStorageGetProductBagId: null,
      // nowCustomizeProductBagId: props.nowCustomizeProductBagId,
      name: "",
      moment: "",
      loadingShow: true,
      activeKey: "",
      cardActive: "-1",
      pageSizeOptions: ["10", "20", "30", "50"],
      totalItemCount: 0,
      tableList: [],
      parentId: props.parentId,
      totalNum: 0,
      totalItemCount1: 0,
      currentPage: 1,
      pageItemSize: 10,
      selectIds: [],
      sceneSelectIds:[],
      AISearchList: [],
      isAISearch: false,
      vocationList: [
        { label: "产品分类", value: 1, type: 1, length: 0, children: [] },
      ],
      showId:undefined,
      showLast:false,
      showMore: false,
      providerSelect: [],
      selectListOld: [],
      labelIdlist:[],
      selectListNew:[],
      showScense: "",
      showIndex: "",
    });

    const getLabel = () => {
      let params = {
        type: 1,
        pageNo: 1,
        pageSize: 100,
      };
      merchantGetLabelList(params).then((res) => {
          if (res.code === 200) {
            data.vocationList[0].length = res.data.rows.length;
            let list = res.data.rows.splice(0,7)
            data.vocationList[0].children = list.map((item,index) => ({
              label: item.name,
              value: item.id,
              length: item.children ? item.children.length : 0,
              children: item.children
                ? item.children.map((child) => ({
                    label: child.name,
                    value: child.id,
                    children: child.children
                      ? child.children.map((ele) => ({
                          label: ele.name,
                          value: ele.id,
                        }))
                      : undefined,
                  }))
                : undefined,
            }));
            // data.vocationList[0].children.push({
            //   label: '通用产品',
            //   value: -1,
            // })
          }
        });
    }
    getLabel()
    const getList = () => {
      // console.log('data.nowCustomizeProductBagId111111111', data.nowCustomizeProductBagId)
      console.log('data.localStorageGetProductBagId222222', data.localStorageGetProductBagId)
      if (data.isAISearch) {
        data.isAISearch = true
        // 调AI单召回接口 产品type=6
        data.loadingShow = true;
        let params = {
          question: data.name,
          type: 6,
          // busiId: data.localStorageGetProductBagId,
          resultType: 7,
        }
        getSeparateAIList(params).then((res) => {
          console.log('res', res)
          data.loadingShow = false;
          data.AISearchList = [];
          data.AISearchList = res.data;
          data.totalItemCount = res.data.length;
          data.tableList = data.AISearchList.slice(0, 10)
          // data.tableList.map((item) => {
          //   item.label = item.label.split(",");
          // });
        })
      } else {
        data.isAISearch = false
        let pageParams = {
          pageNo: data.currentPage,
          pageSize: data.pageItemSize,
          keyword: data.name,
          merchantClassify:data.activeKey,
          shelfStatus:1,
          applicationMarket:2
        };
        data.loadingShow = true;
        // queryProduct getProductList
        getProductList(pageParams)
          .then((res) => {
            data.loadingShow = false;
            data.tableList = [];
            data.tableList = res.data.rows;
            data.totalItemCount = res.data.totalRows;
            data.tableList.map((item) => {
              item.label = item.label.split(",");
            });
            if (data.activeKey == "") {
              data.totalItemCount1 = res.data.totalRows;
            }
          })
          .catch((error) => {
            data.loadingShow = false;
          });
      }

    };
    eventBus.on("moduleRefresh", getList);
    const seekContent = () => {
      data.currentPage = 1;
      if(data.tabsActiveKey == 1){
         getList();
      }else{
        getMerchantSceneList()
      }
    };

    const contentColor = (index) => {
      data.cardActive = index;
    };
    const router = useRouter();
    const proDetail = (item) => {
      if(data.tabsActiveKey == 1){
        let isHave = false;
        for (let i in data.selectIds) {
          if (data.selectIds[i] == item.id) {
            isHave = true;
            break;
          }
        }
        if (!isHave) {
          data.selectIds.push(item.id);
        } else {
          data.selectIds = data.selectIds.filter((item1) => item1 != item.id);
        }
      }else{
        let isHave = false;
        for (let i in data.sceneSelectIds) {
          if (data.sceneSelectIds[i] == item.id) {
            isHave = true;
            break;
          }
        }
        if (!isHave) {
          data.sceneSelectIds.push(item.id);
        } else {
          data.sceneSelectIds = data.sceneSelectIds.filter((item1) => item1 != item.id);
        }
      }
    };
    const contentLeave = () => {
      data.cardActive = "-1";
    };

    const add = (id) => {
      data.selectIds.push(id);
      //getList();
    };
    const refuse = () => {
      data.selectIds = [];
      data.sceneSelectIds = []
      data.name = "";
      if(data.tabsActiveKey == 1){
        getList();
      }else{
        getMerchantSceneList()
      }
      emit("close");
    };
    const submit = () => {
      const schemes = data.selectIds.map((item) => {
        return {
          productId: item, //产品ID
          // productQuantity: 1, //产品数量
          // checkedState: 1,
          // demandId: null,
          type:2
        };
      });
      
      const sceneSchemes = data.sceneSelectIds.map(item=>{
        return {
          productId: item, //产品ID
          // productQuantity: 1, //产品数量
          // checkedState: 1,
          // demandId: null,
          type:1
        }
      })
      const mergeArr = schemes.concat(sceneSchemes)
      data.selectIds = [];
      data.sceneSelectIds = [];
      const mergedList = []
      mergeArr.forEach(item=>{
        mergedList.push(item)
      })
      // const mergedList = [...props.proList];
      // for (const scheme of mergeArr) {
      //   const existingProductIndex = mergedList.findIndex(
      //     (p) => p.productId === scheme.productId && p.demandId === scheme.demandId
      //   );
      //   if (existingProductIndex !== -1) {
      //     mergedList[existingProductIndex].productQuantity += 1;
      //   } else {
      //     mergedList.push(scheme);
      //   }
      // };
      let setData = {
        // id:props.id,
        productShoppingCarts: mergedList,
        // id:data.parentId,
      };
      addShopPro(setData).then((res) => {
        emit("onSubmit");
        data.name = "";
        if(data.tabsActiveKey == 1){
          getList();
        }else{
          getMerchantSceneList()
        }
      });
      // return
      // toShopList(setData).then((res) => {
      //   emit("onSubmit");
      //   data.name = "";
      //   if(data.tabsActiveKey == 1){
      //     getList();
      //   }else{
      //     getMerchantSceneList()
      //   }
      // });
    };
    const pageChange = (page, pageSize) => {
      data.currentPage = page;
      // console.log('111111111111111222', page)
      if (!data.isAISearch) {
        if(data.tabsActiveKey == 1){
          getList();
        }else{
          getMerchantSceneList()
        }
      } else {
        // 前端手动分页
        data.tableList = data.AISearchList.slice((data.currentPage - 1) * data.pageItemSize, data.currentPage * data.pageItemSize)
      }
    };
    const sizeChange = (current, size) => {
      data.pageItemSize = size;
      if (!data.isAISearch) {
        if(data.tabsActiveKey == 1){
          getList();
        }else{
          getMerchantSceneList()
        }
      } else {
        // 前端手动分页
        data.tableList = data.AISearchList.slice((data.currentPage - 1) * data.pageItemSize, data.currentPage * data.pageItemSize)
      }
    };
    const deleteBtn = (id) => {
      if(data.tabsActiveKey == 1){
        const index = data.selectIds.indexOf(id);
        if (index > -1) {
          data.selectIds.splice(index, 1);
        }
      }else{
        const index = data.sceneSelectIds.indexOf(id);
        if (index > -1) {
          data.sceneSelectIds.splice(index, 1);
        }
      }
    };
    const goDetail = (val) => {
      window.open(window.location.origin + "/#/product/productDetail?id=" + val.id);
    };
    const getlocalStorageId = () => {
      data.localStorageGetProductBagId = localStorage.getItem("AInowCustomizeProductBagId")
      console.log('data.localStorageGetProductBagId', data.localStorageGetProductBagId)

    }
    getlocalStorageId()

    const providerEnter = (val, index) => {
      data.showId = val.value;
      data.showLast = true;
    };

    const providerLeave = (val, index) => {
      data.showId = "";
      data.showLast = false;
    };
    const getBoxTitle = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: rect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + "px",
      };
    };

    const getBoxLeft = (key) => {
      const rect = box.value[key].getBoundingClientRect();
      const firstRect = box.value[0].getBoundingClientRect();
      const selectHtml = selectData.value.getBoundingClientRect();
      return {
        left: firstRect.x - selectHtml.x + "px",
        top: rect.y - selectHtml.y + 49 + "px",
      };
    };
    const showMore = (type, index) => {
      if (type == "provider") {
        data.showScense = type;
        data.morePro = false;
      } else {
        data.showIndex = index;
        data.showScense = type;
        data.showLast = true;
      }
    };
    const showless = (type, index) => {
      if (type == "provider_less") {
        data.showScense = type;
        data.morePro = true;
      } else {
        data.showIndex = "";
        data.showScense = type;
        data.showLast = false;
      }
    };
    const providerBtn = (value, type = "default", index, parvalue) => {
      if (type == "last") data.showLast = false;
      if (value.children && type !== "last") {
        data.showId = value.value;
        data.showScense = "label";
        //data.showIndex = index;
      }
      console.log('vale',value);
      
      if (type != "last") {
        data.activeKey = vocation.value = value.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [];
          data.selectListOld = [];
          data.activeKey = "";
          data.showLast = false;
        } else {
          if (value.children) {
            data.showLast = true;
          } else {
            data.showLast = false;
          }
          data.providerSelect = [];
          data.selectListOld = [];
          data.providerSelect.push(value.label);
          data.selectListOld.push(value.value);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      } else {
        data.activeKey = parvalue.value;
        if (data.providerSelect.includes(value.label)) {
          data.providerSelect = [parvalue.label];
          data.selectListOld = [parvalue.value];
        } else {
          data.providerSelect = [parvalue.label];
          data.providerSelect.push(value.label);
          data.providerSelect = data.providerSelect.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
          data.selectListOld = [parvalue.value];
          data.selectListOld.push(value.value);
          data.selectListOld = data.selectListOld.filter(
            (value, index, self) => {
              return self.indexOf(value) === index;
            }
          );
        }
      }
      data.labelIdlist = data.selectListNew.join(",");
      data.currentPage = 1;
      getList();
    };

    // 获取商客场景列表
    const getMerchantSceneList = () => {
      let pageParams = {
        pageNo: data.currentPage,
        pageSize: data.pageItemSize,
        keyword: data.name, //关键字
        applicationMarkets: 2,
      };
      data.loadingShow = true;
      getSceneList(pageParams).then((res) => {
        data.showPagination = true;
        data.loadingShow = false;
        data.tableList = [];
        data.tableList = res.data.rows;
        data.totalItemCount = res.data.totalRows;
        data.tableList.forEach((item) => {
          // item.labelName = item.labelName ? item.labelName.split(",") : [];
          item.list = item.demandSchemeList.map((item) => item.name).join(",");
        });
        if (data.activeKey == "") {
          data.totalItemCount1 = res.data.totalRows;
        }
      });
    }
    eventBus.on("moduleRefresh", getMerchantSceneList);
    const tabsChange = (val) => {
      console.log('val',val);
      data.tabsActiveKey = val
      data.tableList = []
      if(val == 1){
        getList()
      }else{
        getMerchantSceneList()
      }
    }
    watch(
      () => props.parentId,
      (newV) => {
        data.parentId = newV;
      }
    );
    onMounted(()=>{
      if(data.tabsActiveKey == 1){
        getList()
      }else{
        getMerchantSceneList()
      }
    })
    // watch(() => props.nowCustomizeProductBagId,
    //   (newV) => {
    //     data.nowCustomizeProductBagId = newV
    //   })
    return {
      ...toRefs(data),
      getMerchantSceneList,
      tabsChange,
      providerBtn,
      showMore,
      showless,
      getBoxTitle,
      getBoxLeft,
      providerEnter,
      providerLeave,
      getLabel,
      vocation,
      region,
      add,
      contentColor,
      sizeChange,
      contentLeave,
      proDetail,
      router,
      pageChange,
      zhCN,
      deleteBtn,
      refuse,
      seekContent,
      submit,
      goDetail
    };
  },
});
</script>

<style lang="scss" scoped src="./choseTable.scss"></style>

<style lang="scss">
.ant-modal-body{
  padding-top: 0;
}
.mypage {
  .ant-pagination {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-select-selector {
    color: rgba(0, 0, 0, 0.65);
  }

  .ant-pagination-item-active {
    background: #007eff;
  }

  .ant-pagination-item-active a {
    color: #ffffff;
  }

  .ant-pagination-item-active:focus a,
  .ant-pagination-item-active:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-active a:focus,
  .ant-pagination-item-active a:hover {
    color: #ffffff;
  }

  .ant-pagination-item:focus,
  .ant-pagination-item:hover {
    background: #007eff;
  }

  .ant-pagination-item:focus a,
  .ant-pagination-item:hover a {
    color: #ffffff;
  }

  .ant-pagination-item-disabled:hover,
  .ant-pagination-item-disabled:focus {
    background-color: #ffffff;
    border-color: #d9d9d9;

    a {
      color: rgba(0, 0, 0, 0.25);
    }
  }

  .ant-pagination-options-size-changer.ant-select {
    margin-right: 0;
  }

  .ant-pagination-options-quick-jumper {
    margin-left: 8px;
  }
}

/*.shopping {
  position: absolute;
  right: 4px;
  bottom: 12px;
}*/
</style>