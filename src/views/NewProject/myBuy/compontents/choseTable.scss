.searchInfo {
    padding: 24px 0;
    margin-left: -20px;
    position: relative;
    padding-top: 0;

    .vocationPull {
        background: #FFFFFF;
        margin: 0 40px 0 60px;
        display: flex;
        align-items: center;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;

        ::v-deep(.ant-btn) {
            width: 120px;
            height: 40px;
            font-size: 16px;
            border-radius: 0px 6px 6px 0px;
            background: linear-gradient(108deg, #4446FF 0%, #4173FF 50%, #3EB8FF 100%);
        }

        ::v-deep(.ant-input) {
            height: 40px;
        }
    }

    .inputClass {
        width: 100%;
        height: 100%;
    }

    .line {
        width: 1px;
        height: 26px;
        background: #EFF0F4;
    }

    .lines {
        width: 1px;
        height: 56px;
        background: #EFF0F4;
    }

    .seekInfo {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 160px;
        height: 56px;
        cursor: pointer;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;

        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .stage {
        margin-left: 44px;
        margin-top: 16px;
    }

}
.selectData {
    // width: 1200px;
    margin: 16px auto 26px;
    position: relative;
    margin-bottom: 0;
    margin-left: 19px;
    .showMore {
        max-height: 400px !important;
        overflow-y: auto;
    }
    .showHidden {
        max-height: 400px !important;
    }
    .selcet_box {
        border-top: 1px solid #DAE2F5;
        border-bottom: 1px solid #DAE2F5;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 16px;
        display: flex;
        max-height: 49px;
        /* 设置最大高度 */
        overflow: hidden;
        /* 超出部分隐藏 */
        position: relative;
        /* 伪元素的定位需要 */
        line-height: 1.5em;
        /* 行高用于计算总高度 */
        transition: max-height 0.3s ease-out;
        /* 动画效果 */
        .left_select {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            background: #F5F7FC;
            padding: 11px 16px;
            min-width: 103px;
        }
        .right_select {
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            background-color: #FFFFFF;
            padding-left: 12px;
            width: 100%;
            .title {
                width: 121px;
                line-height: 49px;
                cursor: pointer;
                text-align: center;
                overflow: hidden; /* 确保超出容器的内容被裁剪 */
              	white-space: nowrap; /* 确保文本在一行内显示 */
              	text-overflow: ellipsis;
            }
            span {
                cursor: pointer;
                text-align: center;
            }
            .activeBtn {
                position: relative;
                background: #F3F8FF;
                font-weight: 500;
                font-size: 14px;
                color: #2E7FFF;
            }
        }
    }
    .more {
        background: #ECF4FE;
        padding: 5px 12px;
        font-weight: 400;
        font-size: 14px;
        color: #0C70EB;
        line-height: 22px;
        width: 73px;
        align-items: center;
        cursor: pointer;
        img {
            width: 8px;
            height: 4px;
            margin-left: 4px;
        }
    }
    .select_boot {
        margin: 11px 0;
        padding-left: 12px;
        justify-content: space-between;
        padding: 5px 20px;
        background-color: #FFFFFF;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        .right_con {
            color: #9A9A9A;
            span {
                color: #333333;
                margin: 0 6px;
                font-weight: 500;
            }
        }
    }
    .label {
        margin-left: 24px;
    }
    .right_btn {
        width: 70px;
        height: 32px;
        justify-content: center;
        align-items: center;
        background: rgba(12, 112, 235, 0.08);
        font-weight: 500;
        font-size: 16px;
        color: #0C70EB;
        line-height: 22px;
        margin-top: 6px;
        cursor: pointer;
    }
}
.last_data_top {
    width: 135px;
    text-align: center;
    height: 50px;
    line-height: 49px;
    border: 1px solid #00000032;
    border-bottom: none;
    color: #2E7FFF;
    background-color: #F3F8FF;
    position: absolute;
    top: 0;
    left: 0;
    z-index: 3;
    cursor: pointer;
}

.last_data {
    display: flex;
    flex-wrap: wrap;
    width: 1000px;
    border: 1px solid #00000032;
    color: rgba(0, 0, 0, 0.65);
    background-color: #F3F8FF;
    position: absolute;
    z-index: 2;
    .activeBtn {
        font-weight: 500;
        font-size: 14px;
        color: #2E7FFF;
    }
}

.tabContent {
    margin: 20px 32px 0 20px;
    // background: #F5F7FC;
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    justify-content: space-around;
    position: relative;


    .loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .tabModel {
        border-right: 1px solid #DAE2F5;
        height: 850px;
        overflow: hidden auto;
        flex: none
    }

    .tabModel::-webkit-scrollbar {
        display: none;
    }

    .cardContent {
        // height: 780px;
        overflow: hidden auto;
        position: relative;

    }

    .cardContent::-webkit-scrollbar {
        display: none;
    }

    .tab_text {
        font-size: 18px;
        color: #262626;
        cursor: pointer;
        height: 40px;
    }

    .activeTab {
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
        padding-top: 32px;
    }

    .topTab {
        margin-top: 0;
        background-image: url("@/assets/images/home/<USER>");
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
    }

    ::v-deep(.ant-tag) {
        font-weight: 500;
        font-size: 12px;
        color: #2E7FFF;
        height: 20px;
        display: flex;
        align-items: center;
    }

    .card_total {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        // min-height: 500px;
        // max-height: 700px;
        max-height: 500px;
        // border-bottom: 1px solid #DAE2F5;
    }

    .emptyPhoto {
        margin: auto;

        img {
            width: 240px;
            height: 248px;
        }
    }

    .card_content {
        // display: inline-block;
        position: relative;
        background: #F5F7FC;
        width: 49%;
        height: 163px;
        margin-bottom: 16px;
        // border-right: 1px solid #DAE2F5;
        // border-bottom: 1px solid #DAE2F5;
        cursor: pointer;
        border-radius: 10px;
    }

    .selectBorder {
        border: 2px solid #0C70EB !important;
    }

    .cart-button {
        position: absolute;
        right: 20px;
        top: 24px;
        border: none;
        background-color: #f5f7fc;
        color: #40a9ff;
    }

    .cart-button[disabled] {
        position: absolute;
        cursor: not-allowed;
        opacity: 0.5;
    }

    .rightActive {
        border-right: none;
        margin-left: 16px;
    }

    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }

    .bottomLine {
        border-bottom: none;
    }

    .cardObvious {
        border-bottom: none;
    }

    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 117px;
        margin-left: 12px;
        width: 80%
    }
    .imgTitle {
        width: 168px;
        height: 105px;
        display: flex;
        justify-content: center;
        align-items: center;
        color: #005693;
        border-radius: 3px;
        background-image: url("@/assets/images/scenario/listbg.png");
        position: relative;

        >div {
            width: 130px;
            text-align: center; // 使段落中的文本居中
            word-wrap: break-word; // 允许在长单词或URL中间断换行
            margin: 0 auto; // 自动外边距使段落居中
        }
        .mainScene{
            position: absolute;
            top: -4px;
            left: -5px;
            width: 64px;
            height: 64px;
        }
    }
    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card_tag {
            // display: flex;
            // align-items: center;
        }

        .cardTag {
            display: inline-block;
            padding-left: 8px;
            padding-right: 8px;
            margin-right: 8px;
        }

        .card_title {
            width: 200px;
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            margin-bottom: 4px;

        }

        ::v-deep(.cityStyle) {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;
            border: none;
        }
    }

    .card_des {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
    }

    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }
}

.add-icon {
    width: 16px;
    margin-bottom: 3px;
}

::v-deep(.ant-image-img) {
    height: 117px !important;
}
.ant-tabs{
    text-align: center;
}
.btn_box {
    margin: 26px auto;
    font-weight: 500;
    font-size: 16px;
    line-height: 22px;
    text-align: center;

    .refuse {
        background: rgba(12, 112, 235, 0.08);
        border-radius: 4px 4px 4px 4px;
        color: #0C70EB;
        padding: 9px 24px;
        margin-right: 24px;
        cursor: pointer;
    }

    .disabled {
        border: 1px solid #d3d3d3;
        padding: 9px 24px;
        border-radius: 4px 4px 4px 4px;
        pointer-events: none;
    }

    .submit {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        border-radius: 4px 4px 4px 4px;
        color: #FFFFFF;
        padding: 9px 24px;
        cursor: pointer;
        border: none;
    }
}

::v-deep(.ant-checkbox-group) {
    display: flex !important;
    justify-content: center !important;
    margin-right: 6px !important;
    align-items: center !important;
}