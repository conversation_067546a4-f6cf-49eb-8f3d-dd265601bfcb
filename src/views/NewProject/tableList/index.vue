<template>
  <div style="
      padding-top: 20px;
      padding-bottom: 40px;
      width: 1200px;
      margin: 0 auto;
    ">
    <div v-if="showFirst">
      <!--<div class="titleAll">
  			<img src="@/assets/images/newProject/titleTop.png" alt="" />
  			<div>行业市场</div>
  		</div>-->
      <div class="textWord">
        基础方案
        <span class="number">
          （{{ titleTip }}
          <span class="num">{{ proList.length }}</span>
          条基础方案）
        </span>
      </div>
      <div class="noLength" v-if="proList.length == 0">暂无相关的基础方案</div>
      <scheme-list :list="proList" />

      <div class="textWord">
        场景方案
        <span class="number">
          （{{ titleTip }}<span class="num">{{ sceListClass1.length }}</span>条场景方案）
        </span>
      </div>
      <div class="noLength" v-if="sceListClass1.length == 0">暂无相关的场景方案</div>
      <ablity-list :list="sceListClass1" />

      <div class="textWord">
        原子能力
        <span class="number">
          （{{ titleTip }}<span class="num">{{ sceListClass2.length }}</span>条原子能力）
        </span>
      </div>
      <div class="noLength" v-if="sceListClass2.length == 0">暂无相关的原子能力</div>
      <ablity-list :list="sceListClass2" />

      <div class="textWord">
        标准产品
        <span class="number">
          （{{ titleTip }}<span class="num">{{ productList.length }}</span>条标准产品）
        </span>
      </div>
      <div class="noLength" v-if="productList.length == 0">暂无相关的标准产品</div>
      <product-list :list="productList" />

      <!--<div class="titleAll" style="margin-top: 60px;">
  			<img src="@/assets/images/newProject/titleTop2.png" alt="" />
  			<div>商客市场</div>
  		</div>-->
      <div class="textWord">
        商客场景
        <span class="number">
          （{{ titleTip }}<span class="num">{{ policyList.length }}</span>条商客场景）
        </span>
      </div>
      <div class="noLength" v-if="policyList.length == 0">暂无相关的商客场景</div>
      <policy-list :list="policyList" />
    </div>

    <div v-if="!showFirst">
      <div class="textWord">
        标准产品
        <span class="number">
          （{{ titleTip }}
          <span class="num">{{ productList.length }}</span>
          条标准产品）
        </span>
      </div>
      <div class="noLength" v-if="productList.length == 0">
        暂无相关的标准产品
      </div>
      <product-list :list="productList" />

      <div class="textWord">
        商客场景
        <span class="number">
          （{{ titleTip }}
          <span class="num">{{ policyList.length }}</span>
          条商客场景）
        </span>
      </div>
      <div class="noLength" v-if="policyList.length == 0">暂无相关的商客场景</div>
      <policy-list :list="policyList" />

      <div class="textWord">
        标准方案
        <span class="number">
          （{{ titleTip }}
          <span class="num">{{ proList.length }}</span>
          条标准方案）
        </span>
      </div>
      <div class="noLength" v-if="proList.length == 0">暂无相关的标准方案</div>
      <scheme-list :list="proList" />

      <div class="textWord">
        原子能力
        <span class="number">
          （{{ titleTip }}
          <span class="num">{{ sceList.length }}</span>
          条原子能力）
        </span>
      </div>
      <div class="noLength" v-if="sceList.length == 0">暂无相关的原子能力</div>
      <ablity-list ref="abilityRef" :list="sceList" />
    </div>

  </div>
</template>

<script>
import { defineComponent, onMounted, reactive, toRefs, ref, watch } from "vue";
import { useRouter, useRoute } from "vue-router";
import schemeList from "./components/scheme.vue";
import ablityList from "./components/ablity.vue";
import productList from "./components/product.vue";
import policyList from "./components/policy.vue";

export default defineComponent({
  name: "topContent",
  components: {
    schemeList,
    ablityList,
    productList,
    policyList,
  },
  props: {
    AIgetInfo: {
      type: Object,
      default: {},
    },
    isAIpush: {
      type: Boolean,
      default: false,
    },
    name: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    const data = reactive({
      showFirst: true,
      name: props.name,
      sourceType: "1",
      AIgetInfo: props.AIgetInfo,
      isAIpush: props.isAIpush,
      proList: [],
      productList: [],
      sceList: [],
      policyList: [],
      sceListClass1: [],
      sceListClass2: [],
      titleTip: "",
      titleTips: [
        "系统已为您找到",
        "我们已经为您搜索到",
        "平台已经为您发现",
        "我们已为您检索到",
        "系统已成功为您定位到",
      ],
    });
    const abilityRef = ref(null);
    const router = useRouter();
    const route = useRoute();

    const tabChange = (type) => {
      data.sourceType = type;
    };

    watch(
      () => props.isAIpush,
      (newV) => {
        data.isAIpush = newV;
        if (newV) {
          //数据处理
          getData();
        }
      }
    );
    onMounted(() => {
      if (props.AIgetInfo) {
        getData();
      }
    });
    const mergeArr = (arr1, arr2) => {
      let merged = [...arr1, ...arr2];
      let result = [];
      merged.forEach((item) => {
        let res = result.find((el) => el.id == item.id);
        if (res) {
          Object.assign(res, item);
        } else {
          result.push(item);
        }
      });
      return result;
    };
    const getData = () => {
      data.name = props.name;
      data.proList = [];
      data.sceList = [];
      data.sceListClass1 = [];
      data.sceListClass2 = [];
      data.productList = [];
      data.policyList = [];
      data.titleTip = data.titleTips[Math.floor(Math.random() * 5)];
      if (Object.keys(props.AIgetInfo).length === 0) {
        return false;
      }
      console.log('props.AIgetInfo', props.AIgetInfo)
      //方案
      if (props.AIgetInfo.solutionList.length !== 0) {
        for (let i of props.AIgetInfo.solutionList) {
          for (let x of i.priority) {
            x.showOrder = 1;
            for (let j of i.solution) {
              if (j.id == x.id) {
                data.proList.push(j);
              }
            }
          }
        }
      }

      //方案能力
      for (let i of props.AIgetInfo.sceneList) {
        for (let x of i.priority) {
          x.showOrder = 2;
          for (let j of i.scene) {
            if (j.id == x.id) {
            	j.classify = 1;
              data.sceListClass1.push(j);
            }
          }
        }
      }
      //自有能力
      for (let i of props.AIgetInfo.abilityList) {
        for (let x of i.priority) {
          x.showOrder = 3;
          for (let j of i.ability) {
            if (j.id == x.id) {
            	if(j.isEcologyOrAbility == 1){
            		j.classify = 6;
            	} else {
            		j.classify = 7;
            	}
              data.sceListClass2.push(j);
            }
          }
        }
      }
      console.log(data.sceListClass1);
      console.log(data.sceListClass2);
      //场景+能力
      // for (let i of props.AIgetInfo.moduleList) {
      // 	for (let x of i.priority) {
      //   	for (let j of i.ability) {
      // 			if (j.id == x.id) {
      // 				data.sceList.push({
      // 	        ...j,
      // 	        classify:2
      // 	      })
      // 			}
      // 		}
      // 	}
      // }
      // for (let i of props.AIgetInfo.solutionModelList) {
      // 	for (let x of i.priority) {
      // 		for (let j of i.scene) {
      // 			if (j.id == x.id) {
      // 				data.sceList.push({
      //           ...j,
      //           classify:1
      //         })
      // 			}
      // 		}
      // 	}
      // }
      // 标准产品
      // data.productList = props.AIgetInfo.productList.flatMap(
      //   (item) => item.product
      // );
      // 商客场景
      // data.policyList = props.AIgetInfo.productBagList.flatMap(
      //   (item) => item.productBag
      // );
      if (props.AIgetInfo.productList.length !== 0) {
        for (let i of props.AIgetInfo.productList) {
          for (let x of i.priority) {
            x.showOrder = 4;
            for (let j of i.product) {
              if (j.id == x.id) {
                data.productList.push(j);
              }
            }
          }
        }
      }
      if (props.AIgetInfo.productPackageList.length !== 0) {
        for (let i of props.AIgetInfo.productPackageList) {
          for (let x of i.priority) {
            x.showOrder = 5;
            for (let j of i.productBag) {
              if (j.id == x.id) {
                data.policyList.push(j);
              }
            }
          }
        }
      }


      let priority = [
        ...props.AIgetInfo.solutionList[0] ? props.AIgetInfo.solutionList[0].priority : [],
        ...props.AIgetInfo.abilityList[0] ? props.AIgetInfo.abilityList[0].priority : [],
        ...props.AIgetInfo.sceneList[0] ? props.AIgetInfo.sceneList[0].priority : [],
        ...props.AIgetInfo.productList[0] ? props.AIgetInfo.productList[0].priority : [],
        ...props.AIgetInfo.productPackageList[0] ? props.AIgetInfo.productPackageList[0].priority : [],
      ];
      priority.sort(compare('similarity'))
      console.log(priority);
      if (priority[0].showOrder <= 3) {
        //data.showFirst = true ;
      } else {
        //data.showFirst = false ;
      }
    };

    const compare = (value) => {
      return (a, b) => {
        let value1 = a[value];
        let value2 = b[value];
        return value2 - value1;
      }
    }

    return {
      ...toRefs(data),
      tabChange,
      abilityRef,
      router,
      route,
    };
  },
});
</script>

<style lang="scss" scoped src="./index.scss"></style>