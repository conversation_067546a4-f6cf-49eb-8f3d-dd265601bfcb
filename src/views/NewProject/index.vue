<template>
  <div style="position: relative">
    <Transition>
      <customized :isAIpush="isAIpush" :nowCustomizeSolutionId="nowCustomizeSolutionId"
        :abilityListPush="abilityListPush" :titleName="titleName" @event="changeShow" v-if="resultType == '2'">
      </customized>
      <table-list :name="name" :AIgetInfo="AIgetInfo" :isAIpush="isAIpush" v-else-if="resultType == '3'"></table-list>
      <div style="height: calc(100vh - 274px)" v-else-if="resultType == '4'"></div>
      <mybuy :aiBuy="aiBuy" :addNew="addNew" :nowCustomizeProductBagId="nowCustomizeProductBagId" :isBuy="isBuy"
        v-else-if="resultType == '7'"></mybuy>
    </Transition>
  </div>
</template>

<script>
import {
  defineComponent,
  reactive,
  toRefs,
  watch,
  onBeforeMount,
  ref,
  onMounted,
} from "vue";
import axios from 'axios';
import { useHomeStore } from "@/store";
import projectInfo from "./projectOrAbility/index.vue";
import customized from "./customized/index.vue";
import mybuy from "./myBuy/index.vue";
import tableList from "./tableList/index.vue";
import { useRouter, useRoute } from "vue-router";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import voiceRecorder from "@/components/voiceRecorder/voiceRecorder.vue";
import newVoiceRecorder from "@/components/voiceRecorder/newVoiceRecorder.vue";
import { getNewAssociate } from "@/api/AI/ai.js";
import { getReplay, getModelList, getAssociate, AIvoice, getMoreChat, clearSession } from "@/api/AI/ai.js";
import { toCombinePage } from "@/api/combine/shoppingCart.js";
import { addCombine } from "@/api/combine/combine.js";
import writingLoadingSmall from "@/components/superLoadingSmall/writingLoadingSmall.vue";
import bac from "@/assets/images/noDataBac.png";
import { message, notification } from "ant-design-vue";
import { isShowToolTip } from "@/utils/index.js";
import eventBus from "@/utils/eventBus";
import { toShopList, addShopPro } from "@/api/buyList/index.js";
import { ElMessage } from "element-plus";
import chatMessage from "./home/<USER>/chatMessage.vue";
import searchKeywordList from "./searchKeyword.js"
export default defineComponent({
  name: "newProject",
  components: {
    voiceRecorder,
    projectInfo,
    customized,
    tableList,
    writingLoadingSmall,
    newVoiceRecorder,
    mybuy,
    chatMessage,
  },
  setup() {
    const contRef = ref(null);
    const recorderCom = ref(null);
    const cancelTokenSource = ref(null); // 存储取消令牌源
    const data = reactive({
      newData: false,
      historyRec1: ["定制一个XXX方案，包含XXX、XXX功能"],
      historyRec2: ["定制一个XXX方案，包含XXX、XXX功能", "补充XX功能"],
      name: "",
      resultType: "4",
      commenTile: "XX客户产品包",
      loadingAnswer: false,
      switchOnOff: true,
      isTranslating: false,
      canBtnUse: false,
      AIgetInfo: {},
      isAIpush: false,
      aiBuy: false,
      array: [],
      isBuy: false,
      backgroundImage: bac,
      search: false,
      historyShow: false,
      searchHistory: [],
      historyName: localStorage.getItem("historyHome") ? JSON.parse(localStorage.getItem("historyHome")) : [],
      canSendMessage: true,
      showSuggest: false,
      keyName: "",
      suggestList: [],
      showList: [],
      isshowList: [],
      pageS: 3,
      pageN: 1,
      isLess: false,
      abilityListPush: [],
      titleName: "",
      showTui: true,
      topOneCombineList: [],
      proList: [],
      addNew: false,
      WxStatus: "CLOSED",
      nowCustomizeSize: null,
      chooseDesignType: false,
      allDesginData: null,
      showSorry: false,
      searchQuestion: '',
      customizeType: null,
      nowCustomizeSolutionId: null,
      nowCustomizeProductBagId: null,
      messages: [],
      AIAnswerLoading: false,
      isWritingOver: true,
      isAISearch: false,
      sessionId: localStorage.getItem('goCustomizedSessionId'),
      // sessionId: null,
      micWords: null,//以下是用于语音输入的
      micphoneVisible: false,
      isSpeaking: false,
    });
    //data.resultType == "3" ? [...historyRec1] : (data.resultType == "2" ? [...historyRec2]: [])

    const router = useRouter();
    const route = useRoute();
    const useStroe = useHomeStore();
    watch(
      () => useStroe.coverName,
      (newV) => {
        if (newV) {
          data.keyName = newV;
        }
      }
    );
    data.keyName = useStroe.coverName;
    const getNewYear = () => {
      if (new Date().getTime() < 1737993600000) data.newData = false
      if (new Date().getTime() > 1737993600000 && new Date().getTime() < 1738684800000) data.newData = false
      if (new Date().getTime() > 1738684800000) data.newData = false
    }
    getNewYear();
    //ai搜索跳转详情
    const routeDetail = (item) => {
      // console.log('item',item);
      router.push({
        query: {
          id: item.id,
        },
        name: "solveDetailNew",
      });
    };
    const getHistory = () => {
      data.searchHistory = [...data.historyName.slice(0, 3)];
    };
    getHistory();
    const backgroundStyles = () => {
      return {
        backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
        backgroundRepeat: "no-repeat",
        backgroundSize: "cover",
      };
    };
    const handleData = (result) => {
      console.log("result", result);
      let abilityList = []; //这里面是获取的根据语义获取的（能力+方案）场景的集合
      let customizeArr = []; //这里面是把方案、能力、场景根据不同的type分别处理好以后去调go接口的集合
      let newArr = []; //把定制的数组去重
      let usedSolutionId = null;
      let newAbilityList = []; //根据语义把每一种语义下的能力和场景组成一个数组，多个语义的数组集
      let topOneAbilityList = []; //取每一个语义下的top1
      let customizeSolution = [];
      if (result.solutionList) {
        for (let i of result.solutionList) {
          // 方案
          for (let j of i.solution) {
            j.specialTitle = "方案";
          }
        }
      }
      if (result.sceneList) {
        for (let i of result.sceneList) {
          // 方案场景
          for (let j of i.scene) {
            j.specialTitle = "方案场景";
          }
        }
      }
      if (result.moduleList) {
        for (let i of result.moduleList) {
          // 能力
          for (let j of i.ability) {
            j.specialTitle = "能力";
          }
        }
      }
      if (result.productList) {
        for (let i of result.productList) {
          // 能力
          for (let j of i.product) {
            j.specialTitle = "产品";
          }
        }
      }
      usedSolutionId = result.solutionList.find(
        (item) => item.solution.length !== 0
      );
      // console.log("usedSolutionId", usedSolutionId);
      customizeSolution = usedSolutionId.solution.find(
        (item) => (item.id = usedSolutionId.priority[0].id)
      );
      // console.log("customizeSolution", customizeSolution);
      data.nowCustomizeSolutionId = customizeSolution.id
      // console.log(" data.nowCustomizeSolutionId", data.nowCustomizeSolutionId);
      localStorage.setItem("AInowCustomizeSolutionId", data.nowCustomizeSolutionId);
      // 方案加入定制type=1
      customizeArr.push({
        schemeId: customizeSolution.id,
        type: 1,
      });
      //存定制的标签id
      // let industryIdReq = customizeSolution.industryIdReq
      //   ? JSON.parse(customizeSolution.industryIdReq)
      //   : [customizeSolution.industryId];
      //localStorage.setItem("industryId", industryIdReq[0]);

      for (let i of result.moduleList) {
        for (let j of i.ability) {
          for (let h of i.priority) {
            if (h.id == j.id) {
              j.similarity = h.similarity;
            }
          }
        }
      }
      for (let i of result.sceneList) {
        for (let j of i.scene) {
          for (let h of i.priority) {
            if (h.id == j.id) {
              j.similarity = h.similarity;
            }
          }
        }
      }
      for (let i of result.productList) {
        for (let j of i.product) {
          for (let h of i.priority) {
            if (h.id == j.id) {
              j.similarity = h.similarity;
            }
          }
        }
      }

      for (let i = 0; i < result.moduleList.length; i++) {
        newAbilityList[i] = result.moduleList[i].ability.concat(
          result.sceneList[i].scene, result.productList[i].product
        );
      }
      let filterProductList = [] //方案定制里筛选出行业市场的产品
      if (newAbilityList.length !== 0) {
        filterProductList = newAbilityList.map((subArray) =>
          subArray.filter((item) => {
            if (item.specialTitle === "方案场景") return true;
            if (item.specialTitle === "能力") return true;
            if (item.specialTitle === "产品") {
              return item.marketplaceList.some(
                (market) => market.applicationMarket === 1
              );
            }
            return false;
          })
        )
      }
      if (filterProductList.length !== 0) {
        for (let i of filterProductList) {
          topOneAbilityList.push(
            i.reduce(
              (max, item) => (item.similarity > max.similarity ? item : max),
              i[0]
            )
          );
        }
      }
      data.topOneCombineList = [];
      if (topOneAbilityList.length !== 0) {
        for (let i of topOneAbilityList) {
          if (i.specialTitle == "方案场景") {
            customizeArr.push({
              schemeId: i.id,
              type: 3,
            });
            data.topOneCombineList.push({
              schemeId: i.id,
              type: 3,
            });
          }
          if (i.specialTitle == "能力") {
            customizeArr.push({
              schemeId: i.id,
              type: 2,
            });
            data.topOneCombineList.push({
              schemeId: i.id,
              type: 2,
            });
          }
          if (i.specialTitle == "产品") {
            customizeArr.push({
              schemeId: i.id,
              type: 4,
            });
            data.topOneCombineList.push({
              schemeId: i.id,
              type: 4,
            });
          }
        }
      }

      // // 产品加入定制
      // if (result.productList.length !== 0) {
      //   for (let i of result.productList) {
      //     for (let j of i.product) {
      //       if (j.id == i.priority[0].id) {
      //         customizeArr.push({
      //           schemeId: j.id,
      //           type: 4,
      //         });
      //       }
      //     }
      //   }
      // }
      newArr = Array.from(
        new Map(
          customizeArr.map((item) => [item.schemeId + "_" + item.type, item])
        ).values()
      );
      toCombinePage({ list: newArr, source: "2" }).then((res) => {
        console.log(res);
        changeResultType("2");
        data.loadingAnswer = false;
        data.isAIpush = true;
        setTimeout(() => {
          data.isAIpush = false;
        }, 2000);
        getHistory();
      });
    };

    const changeShow = () => {
      console.log(111);
      data.showSuggest = false;
      data.showTui = true;
    }

    //定制方案的推荐处理
    const recommend = (resData) => {
      data.showSuggest = false;
      data.showTui = false;
      data.suggestList = [];
      let dataSolution = resData.moduleCombine.solutionList[0].solution;
      let dataPriority = resData.moduleCombine.solutionList[0].priority;
      for (let i of dataPriority) {
        for (let j of dataSolution) {
          if (i.id == j.id) {
            data.suggestList.push(j);
          }
        }
      }
      // data.suggestList = res.data.moduleCombine.solutionList[0].solution;
      data.suggestList.forEach((item, index) => {
        item.provider = item.provider.split("/").pop();
        // item.labelName = item.labelName.split(",");
        item.index = index;
      });
      let newList = JSON.parse(JSON.stringify(data.suggestList));
      newList.splice(0, 1);
      if (newList.length > 3) {
        data.showList = [...newList, ...newList, ...newList];
        data.isLess = false;
      } else if (newList.length == 0) {
        data.showSuggest = false;
        data.showTui = true;
      } else {
        data.showList = newList;
        data.isLess = true;
      }
      data.isshowList = data.showList.slice(0, 3);
    };

    const getCustomizeType = (type) => {
      // type==1：默认去定制方案
      // type==2：让用户选择定制方案还是产品包
      // type==3：默认去定制产品包
      data.customizeType = type
      console.log('customizeType', data.customizeType)
    }
    const seekContent = () => {
      data.searchQuestion = data.name
      //隐藏推荐方案栏
      data.showSuggest = false;
      data.showTui = true;
      if (data.name == "") {
        localStorage.removeItem("seekName");
        message.error("请输入搜索字");
        return false;
      }
      data.chooseDesignType = false;
      data.showSorry = false;
      // 点击搜索关闭webscoket连接
      if (recorderCom.value) {
        console.log("关闭webscoket连接");
        recorderCom.value.closeConnection();
      }
      // data.keyName = localStorage.getItem('coverName')
      // localStorage.removeItem("coverName");
      data.search = true;
      data.historyShow = false;
      localStorage.setItem("seekName", data.name);
      data.historyName.reverse();
      data.historyName.push(data.name);
      data.historyName.reverse();
      data.historyName = [...new Set(data.historyName)];
      localStorage.setItem("historyHome", JSON.stringify(data.historyName));
      let isWrong = false;
      if (data.name == "") {
        changeResultType("4");
        data.search = false;
        return false;
      }
      data.loadingAnswer = true;
      // getAIData()
      // zdl改的，AI多轮对话
      if (searchKeywordList.some(keyword => data.name.includes(keyword))) {
        let isWrong = false;
        // 此时是新增
        if (data.isWritingOver) {
          // 假装走了多轮对话接口新增
          let searchName = data.name
          handleUserMessage();
          data.AIAnswerLoading = true
          setTimeout(() => {
            data.AIAnswerLoading = false
            const aiMessage = {
              message: '',
              isUser: false,
              AIData: {
                answer: '正在为您添加内容，请耐心等待...'
              }
            };
            data.messages.push(aiMessage)
            setTimeout(() => {
              let scrollElem = contRef.value;
              scrollElem.scrollTo({
                top: scrollElem.scrollHeight,
                behavior: "smooth",
              });
            }, 10)
          }, 1000);
          // 
          data.isWritingOver = false
          getNewAssociate({
            question: searchName,
          }).then((res) => {
            if (res.code == 200) {
              data.nowCustomizeSize = res.data.resultType;
              localStorage.setItem("nowCustomizeSize", data.nowCustomizeSize);
              if (
                res.data.moduleCombine.solutionList.length +
                res.data.moduleCombine.moduleList.length +
                res.data.moduleCombine.productList.length +
                res.data.moduleCombine.sceneList.length ==
                0
              ) {
                // 方案和能力、场景、产品都没查到东西
                message.warning(
                  "很抱歉，未能为您搜索到相关内容，无法为您添加"
                );
                isWrong = true;
              } else {
                let AllLength = 0;
                if (res.data.moduleCombine.solutionList.length !== 0) {
                  for (let i of res.data.moduleCombine.solutionList) {
                    AllLength += i.solution.length;
                  }
                }
                if (res.data.moduleCombine.moduleList.length !== 0) {
                  for (let i of res.data.moduleCombine.moduleList) {
                    AllLength += i.ability.length;
                  }
                }
                if (res.data.moduleCombine.sceneList.length !== 0) {
                  for (let i of res.data.moduleCombine.sceneList) {
                    AllLength += i.scene.length;
                  }
                }
                if (res.data.moduleCombine.productList.length !== 0) {
                  for (let i of res.data.moduleCombine.productList) {
                    AllLength += i.product.length;
                  }
                }
                if (AllLength == 0) {
                  isWrong = true;
                  message.warning(
                    "很抱歉，未能为您搜索到相关内容，无法为您添加"
                  );
                } else {
                  isWrong = false;
                }
              }
              console.log("data.resultType", data.resultType);
              if (!isWrong) {
                const aiMessage = {
                  message: '',
                  isUser: false,
                  AIData: {
                    answer: '已为您添加完成，请查看'
                  }
                };
                data.messages.push(aiMessage)
                if (data.resultType == "2") {
                  // 增加方案
                  //addFlag=="1"，为用户新增情况
                  let abilityList = []; //把每个语义搜出来的能力和场景合并，组成一个按语义搜出来的数组，里面的每一项能力在前，方案场景在后
                  let customizeArr = []; //取每一个按语义搜出来的数组的第一条加入定制数组，即优先添加能力
                  let lastArr = []; //把筛选后的customizeArr，组合成后端接口需要的格式用于传参数
                  // 给每一个小项加上他所属的模块名称
                  if (res.data.moduleCombine.sceneList) {
                    for (let i of res.data.moduleCombine.sceneList) {
                      // 方案场景
                      if (i.scene.length !== 0) {
                        for (let j of i.scene) {
                          j.specialTitle = "方案场景";
                        }
                      }
                    }
                  }
                  if (res.data.moduleCombine.moduleList) {
                    for (let i of res.data.moduleCombine.moduleList) {
                      // 能力
                      if (i.ability.length !== 0) {
                        for (let j of i.ability) {
                          j.specialTitle = "能力";
                        }
                      }

                    }
                  }
                  if (res.data.moduleCombine.productList) {
                    for (let i of res.data.moduleCombine.productList) {
                      // 能力
                      if (i.product.length !== 0) {
                        for (let j of i.product) {
                          j.specialTitle = "产品";
                        }
                      }
                    }
                  }
                  // 给每一个小项加上他的匹配度，把能力、场景、产品放一起时方便比较匹配度
                  for (let i of res.data.moduleCombine.moduleList) {
                    for (let j of i.ability) {
                      for (let h of i.priority) {
                        if (h.id == j.id) {
                          j.similarity = h.similarity;
                        }
                      }
                    }
                  }
                  for (let i of res.data.moduleCombine.sceneList) {
                    for (let j of i.scene) {
                      for (let h of i.priority) {
                        if (h.id == j.id) {
                          j.similarity = h.similarity;
                        }
                      }
                    }
                  }
                  for (let i of res.data.moduleCombine.productList) {
                    for (let j of i.product) {
                      for (let h of i.priority) {
                        if (h.id == j.id) {
                          j.similarity = h.similarity;
                        }
                      }
                    }
                  }
                  // 把能力、场景、产品按语义分别放到一个数组里，匹配度相同能力优先
                  for (let i = 0; i < res.data.moduleCombine.moduleList.length; i++) {
                    abilityList[i] = res.data.moduleCombine.moduleList[i].ability.concat(
                      res.data.moduleCombine.sceneList[i].scene, res.data.moduleCombine.productList[i].product
                    );
                  }
                  console.log('arr1xxxxx', abilityList)

                  abilityList = abilityList.filter(item => item != null && item !== '')
                  console.log('arr1', abilityList)
                  // 获取每个语义里匹配度最高的项
                  for (let i of abilityList) {
                    i.sort((a, b) => b.similarity - a.similarity);
                    customizeArr.push(
                      i.reduce(
                        (max, item) => (item.similarity > max.similarity ? item : max),
                        i[0]
                      )
                    );
                  }
                  console.log('arr2', customizeArr)
                  // 辨别匹配度最高那一项属于哪个模块，添加到最终定制的数组里
                  for (let i of customizeArr) {
                    if (i.specialTitle == "能力") {
                      lastArr.push({
                        schemeId: i.id,
                        classify: 1,
                      });
                    }
                    if (i.specialTitle == "方案场景") {
                      lastArr.push({
                        schemeId: i.id,
                        classify: 2,
                      });
                    }
                    if (i.specialTitle == "产品") {
                      lastArr.push({
                        schemeId: i.id,
                        classify: 0,
                      });
                    }
                  }
                  console.log('lastArr', lastArr)
                  if (lastArr[0].classify != 0) {
                    let arr = [];
                    for (let i of abilityList) {
                      i.forEach(item => {
                        if (item.specialTitle == "能力") {
                          item.classify = 1;
                          console.log(item);
                          arr.push(item);
                        }
                        if (item.specialTitle == "方案场景") {
                          item.classify = 2;
                          item.abilityPicture = item.image;
                          item.abilityIntro = item.summary;
                          console.log(item);
                          arr.push(item);
                        }
                      })
                    }

                    arr.forEach((item, index) => {
                      item.index = index;
                    });
                    data.abilityListPush = arr;
                    console.log("arr", arr);

                    data.showSuggest = false;
                    data.showTui = true;
                    data.titleName = customizeArr[0].name;
                  }

                  console.log("lastArr", lastArr);
                  addCombine(lastArr).then((res) => {
                    changeResultType("2");
                    getHistory();
                    data.loadingAnswer = false;
                    data.isAIpush = true;
                    localStorage.setItem(
                      "lastArr",
                      JSON.stringify(lastArr)
                    );
                    setTimeout(() => {
                      data.isAIpush = false;
                    }, 2000);
                  });

                } else if (data.resultType == "7") {
                  // 增加产品
                  let productList = []; //把每个场景下的产品都掏出来和单独搜的产品一起放到一个数组
                  let noRepeatList = []; //去重后的数组
                  let addProductArr = []; //用于去定制的数组
                  if (res.data.moduleCombine.productBagList.length !== 0) {
                    for (let i of res.data.moduleCombine.productBagList) {
                      if (i.productBag.length !== 0) {
                        data.commenTile = i.key
                          ? i.key + '产品包'
                          : "XX客户产品包";
                        for (let j of i.productBag[0].demandSchemeList) {
                          for (let k of j.demandProductList) {
                            productList.push(k);
                          }
                        }
                      }
                    }
                  }
                  if (res.data.moduleCombine.productList.length !== 0) {
                    for (let i of res.data.moduleCombine.productList) {
                      if (i.product.length !== 0) {
                        productList.push(i.product[0]);
                      }
                    }
                  }
                  console.log("sssssssss", productList);
                  const map = new Map();
                  productList.forEach((item) => map.set(item.id, item));
                  noRepeatList = Array.from(map.values());
                  data.addNew = !data.addNew;
                  // 新增场景/产品
                  for (let i of noRepeatList) {
                    addProductArr.push({
                      productId: i.id,
                      tariffId: i.tariffId,
                      productQuantity: 1,
                    });
                  }
                  setTimeout(() => {
                    data.addNew = !data.addNew;
                    let productListOld = [];
                    useStroe.productList.forEach(item => {
                      productListOld.push({
                        productQuantity: item.productQuantity,
                        productId: item.productId,
                        tariffId: item.tariffId,
                        demandId: item.demandId
                      });
                    })
                    const mergedList = [...productListOld];
                    for (const scheme of addProductArr) {
                      const existingProductIndex = mergedList.length == 0 ? -1 : mergedList.findIndex(
                        (p) => p.productId === scheme.productId && p.demandId === scheme.demandId
                      );
                      if (existingProductIndex !== -1) {
                        mergedList[existingProductIndex].productQuantity += 1;
                      } else {
                        mergedList.push(scheme);
                      }
                    }
                    let params = {
                      productPackageLists: [...mergedList],
                      id: useStroe.parentId,
                    };
                    addShopPro(params).then((res) => {
                      data.aiBuy = true;
                      setTimeout(() => {
                        data.aiBuy = false;
                      }, 1000);
                    });
                  }, 1000);
                  console.log("addProductArr", addProductArr);
                  // 新增调接口/跳转页面
                }
              }







              data.isWritingOver = true
            }
          })
        }
      } else {
        // 此时是新的定制
        let question = JSON.parse(JSON.stringify(data.name.trim()))
        if (data.name.trim() !== '' && data.isWritingOver) {
          // 发送消息
          getMoreChat({
            question: data.name,
            sessionId: data.sessionId,
          }).then((res) => {
            console.log('res', res)
            data.AIAnswerLoading = false
            handleAiReplay(question, res.data)
          })
          handleUserMessage();
          data.AIAnswerLoading = true
        }
      }


    };
    // 调AI接口
    const getAIData = async () => {
      let isWrong = false;
      cancelTokenSource.value = axios.CancelToken.source();
      let timer = setTimeout(() => {
        cancelRequest()
        getAIData()
      }, 50000)//50秒未调通则关闭请求重新请求
      try {
        const response = await axios.get('/portal/view/ai/changAiList?question=' + data.name, {
          headers: {
            'Authorization': localStorage.getItem("token")
          },
          cancelToken: cancelTokenSource.value.token // 绑定令牌‌:ml-citation{ref="1,2" data="citationList"}
        });


        console.log('response.data', response.data)
        if (response.data.code == 200) {
          clearTimeout(timer)

          data.nowCustomizeSize = response.data.data.resultType;
          localStorage.setItem("nowCustomizeSize", data.nowCustomizeSize);

          if (response.data.data.resultType == "1") {
            // resultType == 1 闲聊
            message.warning("很抱歉，未能为您搜索到相关内容");
            isWrong = true;
          } else if (response.data.data.resultType == "0") {
            // resultType == 0 检索
            if (
              response.data.data.moduleCombine &&
              response.data.data.moduleCombine.solutionList.length +
              response.data.data.moduleCombine.moduleList.length +
              response.data.data.moduleCombine.sceneList.length ==
              0
            ) {
              // 所有数组都为空
              message.warning("很抱歉，未能为您搜索到相关内容");
              isWrong = true;
            } else {
              isWrong = false;
            }
          } else if (response.data.data.resultType == "4") {
            // resultType == 4 定制
            if (response.data.data.addFlag == "0") {
              // addFlag == 0 （非新增时）
              let solutionListLength = 0;
              if (response.data.data.moduleCombine.solutionList.length !== 0) {
                for (let i of response.data.data.moduleCombine.solutionList) {
                  solutionListLength += i.solution.length;
                }
                if (solutionListLength == 0) {
                  // 方案列表为空，即没查到方案
                  message.warning(
                    "很抱歉，未能为您搜索到相关方案，无法进行定制"
                  );
                  isWrong = true;
                } else {
                  isWrong = false;
                }
              } else {
                message.warning(
                  "很抱歉，未能为您搜索到相关方案，无法进行定制"
                );
                isWrong = true;
              }
            } else if (response.data.data.addFlag == "1") {
              // addFlag == 1 （新增时）
              if (
                response.data.data.moduleCombine.solutionList.length +
                response.data.data.moduleCombine.moduleList.length +
                response.data.data.moduleCombine.productList.length +
                response.data.data.moduleCombine.sceneList.length ==
                0
              ) {
                // 方案和能力、场景、产品都没查到东西
                message.warning(
                  "很抱歉，未能为您搜索到相关内容，无法为您添加"
                );
                isWrong = true;
              } else {
                let AllLength = 0;
                if (response.data.data.moduleCombine.solutionList.length !== 0) {
                  for (let i of response.data.data.moduleCombine.solutionList) {
                    AllLength += i.solution.length;
                  }
                }
                if (response.data.data.moduleCombine.moduleList.length !== 0) {
                  for (let i of response.data.data.moduleCombine.moduleList) {
                    AllLength += i.ability.length;
                  }
                }
                if (response.data.data.moduleCombine.sceneList.length !== 0) {
                  for (let i of response.data.data.moduleCombine.sceneList) {
                    AllLength += i.scene.length;
                  }
                }
                if (response.data.data.moduleCombine.productList.length !== 0) {
                  for (let i of response.data.data.moduleCombine.productList) {
                    AllLength += i.product.length;
                  }
                }
                if (AllLength == 0) {
                  isWrong = true;
                  message.warning(
                    "很抱歉，未能为您搜索到相关内容，无法为您添加"
                  );
                } else {
                  isWrong = false;
                }
              }
            }
          } else if (response.data.data.resultType == "7") {
            //data.resultType = "7";
            let allProductLength = 0;
            if (response.data.data.moduleCombine.productBagList.length !== 0) {
              for (let i of response.data.data.moduleCombine.productBagList) {
                for (let j of i.productBag) {
                  allProductLength += j.demandSchemeList.length;
                }
              }
            }
            if (response.data.data.moduleCombine.productList.length !== 0) {
              for (let i of response.data.data.moduleCombine.productList) {
                allProductLength += i.product.length;
              }
            }
            if (allProductLength == 0) {
              isWrong = true;
              changeResultType("4");
              message.warning("很抱歉，未能为您搜索到相关内容，无法为您添加");
            } else {
              isWrong = false;
            }
          }

          if (!isWrong) {
            if (response.data.data.resultType == "0") {
              // 查询
              changeResultType("3");
              getHistory();
              data.AIgetInfo = response.data.data;
              data.loadingAnswer = false;
              data.isAIpush = true;
              setTimeout(() => {
                data.isAIpush = false;
              }, 2000);
            } else {
              if (response.data.data.addFlag == "1") {
                // 当前是增加
                console.log("data.resultType", data.resultType);

                if (data.resultType == "2") {
                  // 增加方案
                  //addFlag=="1"，为用户新增情况

                  if (response.data.data.moduleCombine.solutionList.length !== 0) {
                    // AI语义认为提到了方案
                    let solutionLength = 0;
                    for (let i of response.data.data.moduleCombine.solutionList) {
                      solutionLength += i.solution.length;
                    }
                    if (solutionLength !== 0) {
                      // AI语义认为提到了方案且搜到了方案——>走go接口
                      handleData(response.data.data.moduleCombine);
                    } else {
                      // AI语义认为提到了方案但是没搜到方案——>走新增add接口
                      let abilityList = []; //把每个语义搜出来的能力和场景合并，组成一个按语义搜出来的数组，里面的每一项,能力在前，方案场景在后
                      let customizeArr = []; //取每一个按语义搜出来的数组的第一条加入定制数组，即优先添加能力
                      let lastArr = []; //把筛选后的customizeArr，组合成后端接口需要的格式用于传参数
                      // 给每一个小项加上他所属的模块名称
                      if (response.data.data.moduleCombine.sceneList) {
                        for (let i of response.data.data.moduleCombine.sceneList) {
                          // 方案场景
                          for (let j of i.scene) {
                            j.specialTitle = "方案场景";
                          }
                        }
                      }
                      if (response.data.data.moduleCombine.moduleList) {
                        for (let i of response.data.data.moduleCombine.moduleList) {
                          // 能力
                          for (let j of i.ability) {
                            j.specialTitle = "能力";
                          }
                        }
                      }
                      if (response.data.data.moduleCombine.productList) {
                        for (let i of response.data.data.moduleCombine.productList) {
                          // 能力
                          for (let j of i.product) {
                            j.specialTitle = "产品";
                          }
                        }
                      }
                      // 给每一个小项加上他的匹配度，把能力、场景、产品放一起时方便比较匹配度
                      for (let i of response.data.data.moduleCombine.moduleList) {
                        for (let j of i.ability) {
                          for (let h of i.priority) {
                            if (h.id == j.id) {
                              j.similarity = h.similarity;
                            }
                          }
                        }
                      }
                      for (let i of response.data.data.moduleCombine.sceneList) {
                        for (let j of i.scene) {
                          for (let h of i.priority) {
                            if (h.id == j.id) {
                              j.similarity = h.similarity;
                            }
                          }
                        }
                      }
                      for (let i of response.data.data.moduleCombine.productList) {
                        for (let j of i.product) {
                          for (let h of i.priority) {
                            if (h.id == j.id) {
                              j.similarity = h.similarity;
                            }
                          }
                        }
                      }
                      // 把能力、场景、产品按语义分别放到一个数组里，匹配度相同能力优先
                      for (let i = 0; i < response.data.data.moduleCombine.moduleList.length; i++) {
                        abilityList[i] = response.data.data.moduleCombine.moduleList[i].ability.concat(
                          response.data.data.moduleCombine.sceneList[i].scene, response.data.data.moduleCombine.productList[i].product
                        );
                      }
                      console.log('arr1', abilityList)

                      // 获取每个语义里匹配度最高的项
                      if (abilityList.length !== 0) {
                        for (let i of abilityList) {
                          i.sort((a, b) => b.similarity - a.similarity);
                          customizeArr.push(
                            i.reduce(
                              (max, item) => (item.similarity > max.similarity ? item : max),
                              i[0]
                            )
                          );
                        }
                      }

                      // 辨别匹配度最高那一项属于哪个模块，添加到最终定制的数组里
                      if (customizeArr.length !== 0) {
                        for (let i of customizeArr) {
                          if (i.specialTitle == "能力") {
                            lastArr.push({
                              schemeId: i.id,
                              classify: 1,
                            });
                          }
                          if (i.specialTitle == "方案场景") {
                            lastArr.push({
                              schemeId: i.id,
                              classify: 2,
                            });
                          }
                          if (i.specialTitle == "产品") {
                            lastArr.push({
                              schemeId: i.id,
                              classify: 0,
                            });
                          }
                        }
                      }

                      console.log('lastArr', lastArr)
                      if (lastArr[0].classify != 0) {
                        let arr = [];
                        for (let i of abilityList) {
                          i.forEach(item => {
                            if (item.specialTitle == "能力") {
                              item.classify = 1;
                              console.log(item);
                              arr.push(item);
                            }
                            if (item.specialTitle == "方案场景") {
                              item.classify = 2;
                              item.abilityPicture = item.image;
                              item.abilityIntro = item.summary;
                              console.log(item);
                              arr.push(item);
                            }
                          })
                        }

                        arr.forEach((item, index) => {
                          item.index = index;
                        });
                        data.abilityListPush = arr;
                        console.log("arr", arr);

                        data.showSuggest = false;
                        data.showTui = true;
                        data.titleName = customizeArr[0].name;
                      }

                      addCombine(lastArr).then((res) => {
                        changeResultType("2");
                        getHistory();
                        data.loadingAnswer = false;
                        data.isAIpush = true;
                        localStorage.setItem(
                          "lastArr",
                          JSON.stringify(lastArr)
                        );
                        setTimeout(() => {
                          data.isAIpush = false;
                        }, 2000);
                      });
                    }
                  } else {
                    // AI语义认为没有提到方案——>走新增add接口
                    let abilityList = []; //把每个语义搜出来的能力和场景合并，组成一个按语义搜出来的数组，里面的每一项能力在前，方案场景在后
                    let customizeArr = []; //取每一个按语义搜出来的数组的第一条加入定制数组，即优先添加能力
                    let lastArr = []; //把筛选后的customizeArr，组合成后端接口需要的格式用于传参数
                    // 给每一个小项加上他所属的模块名称
                    if (response.data.data.moduleCombine.sceneList) {
                      for (let i of response.data.data.moduleCombine.sceneList) {
                        // 方案场景
                        if (i.scene.length !== 0) {
                          for (let j of i.scene) {
                            j.specialTitle = "方案场景";
                          }
                        }
                      }
                    }
                    if (response.data.data.moduleCombine.moduleList) {
                      for (let i of response.data.data.moduleCombine.moduleList) {
                        // 能力
                        if (i.ability.length !== 0) {
                          for (let j of i.ability) {
                            j.specialTitle = "能力";
                          }
                        }

                      }
                    }
                    if (response.data.data.moduleCombine.productList) {
                      for (let i of response.data.data.moduleCombine.productList) {
                        // 能力
                        if (i.product.length !== 0) {
                          for (let j of i.product) {
                            j.specialTitle = "产品";
                          }
                        }
                      }
                    }
                    // 给每一个小项加上他的匹配度，把能力、场景、产品放一起时方便比较匹配度
                    for (let i of response.data.data.moduleCombine.moduleList) {
                      for (let j of i.ability) {
                        for (let h of i.priority) {
                          if (h.id == j.id) {
                            j.similarity = h.similarity;
                          }
                        }
                      }
                    }
                    for (let i of response.data.data.moduleCombine.sceneList) {
                      for (let j of i.scene) {
                        for (let h of i.priority) {
                          if (h.id == j.id) {
                            j.similarity = h.similarity;
                          }
                        }
                      }
                    }
                    for (let i of response.data.data.moduleCombine.productList) {
                      for (let j of i.product) {
                        for (let h of i.priority) {
                          if (h.id == j.id) {
                            j.similarity = h.similarity;
                          }
                        }
                      }
                    }
                    // 把能力、场景、产品按语义分别放到一个数组里，匹配度相同能力优先
                    for (let i = 0; i < response.data.data.moduleCombine.moduleList.length; i++) {
                      abilityList[i] = response.data.data.moduleCombine.moduleList[i].ability.concat(
                        response.data.data.moduleCombine.sceneList[i].scene, response.data.data.moduleCombine.productList[i].product
                      );
                    }
                    console.log('arr1xxxxx', abilityList)

                    abilityList = abilityList.filter(item => item != null && item !== '')
                    console.log('arr1', abilityList)
                    // 获取每个语义里匹配度最高的项
                    for (let i of abilityList) {
                      i.sort((a, b) => b.similarity - a.similarity);
                      customizeArr.push(
                        i.reduce(
                          (max, item) => (item.similarity > max.similarity ? item : max),
                          i[0]
                        )
                      );
                    }
                    console.log('arr2', customizeArr)
                    // 辨别匹配度最高那一项属于哪个模块，添加到最终定制的数组里
                    for (let i of customizeArr) {
                      if (i.specialTitle == "能力") {
                        lastArr.push({
                          schemeId: i.id,
                          classify: 1,
                        });
                      }
                      if (i.specialTitle == "方案场景") {
                        lastArr.push({
                          schemeId: i.id,
                          classify: 2,
                        });
                      }
                      if (i.specialTitle == "产品") {
                        lastArr.push({
                          schemeId: i.id,
                          classify: 0,
                        });
                      }
                    }
                    console.log('lastArr', lastArr)
                    if (lastArr[0].classify != 0) {
                      let arr = [];
                      for (let i of abilityList) {
                        i.forEach(item => {
                          if (item.specialTitle == "能力") {
                            item.classify = 1;
                            console.log(item);
                            arr.push(item);
                          }
                          if (item.specialTitle == "方案场景") {
                            item.classify = 2;
                            item.abilityPicture = item.image;
                            item.abilityIntro = item.summary;
                            console.log(item);
                            arr.push(item);
                          }
                        })
                      }

                      arr.forEach((item, index) => {
                        item.index = index;
                      });
                      data.abilityListPush = arr;
                      console.log("arr", arr);

                      data.showSuggest = false;
                      data.showTui = true;
                      data.titleName = customizeArr[0].name;
                    }

                    console.log("lastArr", lastArr);
                    addCombine(lastArr).then((res) => {
                      changeResultType("2");
                      getHistory();
                      data.loadingAnswer = false;
                      data.isAIpush = true;
                      localStorage.setItem(
                        "lastArr",
                        JSON.stringify(lastArr)
                      );
                      setTimeout(() => {
                        data.isAIpush = false;
                      }, 2000);
                    });
                  }
                } else if (data.resultType == "7") {
                  // 增加产品
                  let productList = []; //把每个场景下的产品都掏出来和单独搜的产品一起放到一个数组
                  let noRepeatList = []; //去重后的数组
                  let addProductArr = []; //用于去定制的数组
                  if (response.data.data.moduleCombine.productBagList.length !== 0) {
                    for (let i of response.data.data.moduleCombine.productBagList) {
                      if (i.productBag.length !== 0) {
                        data.commenTile = i.key
                          ? i.key + '产品包'
                          : "XX客户产品包";
                        for (let j of i.productBag[0].demandSchemeList) {
                          for (let k of j.demandProductList) {
                            productList.push(k);
                          }
                        }
                      }
                    }
                  }
                  if (response.data.data.moduleCombine.productList.length !== 0) {
                    for (let i of response.data.data.moduleCombine.productList) {
                      if (i.product.length !== 0) {
                        productList.push(i.product[0]);
                      }
                    }
                  }
                  console.log("sssssssss", productList);
                  const map = new Map();
                  productList.forEach((item) => map.set(item.id, item));
                  noRepeatList = Array.from(map.values());
                  data.addNew = !data.addNew;
                  // 新增场景/产品
                  for (let i of noRepeatList) {
                    addProductArr.push({
                      productId: i.id,
                      tariffId: i.tariffId,
                      productQuantity: 1,
                    });
                  }
                  setTimeout(() => {
                    data.addNew = !data.addNew;
                    let productListOld = [];
                    useStroe.productList.forEach(item => {
                      productListOld.push({
                        productQuantity: item.productQuantity,
                        productId: item.productId,
                        tariffId: item.tariffId,
                        demandId: item.demandId
                      });
                    })
                    const mergedList = [...productListOld];
                    for (const scheme of addProductArr) {
                      const existingProductIndex = mergedList.length == 0 ? -1 : mergedList.findIndex(
                        (p) => p.productId === scheme.productId && p.demandId === scheme.demandId
                      );
                      if (existingProductIndex !== -1) {
                        mergedList[existingProductIndex].productQuantity += 1;
                      } else {
                        mergedList.push(scheme);
                      }
                    }
                    let params = {
                      productPackageLists: [...mergedList],
                      id: useStroe.parentId,
                    };
                    addShopPro(params).then((res) => {
                      data.aiBuy = true;
                      setTimeout(() => {
                        data.aiBuy = false;
                      }, 1000);
                    });
                  }, 1000);
                  console.log("addProductArr", addProductArr);
                  // 新增调接口/跳转页面
                }
              } else if (response.data.data.addFlag == "0") {
                // 当前是第一次定制
                if (response.data.data.designType !== "2") {
                  // 不是方案和场景都搜到东西
                  if (response.data.data.designType == "0") {
                    data.showSorry = true;
                  }
                  // 方案和场景只有一样搜到东西_直接新定制
                  if (response.data.data.resultType == "4") {
                    if (
                      response.data.data.moduleCombine.solutionList[0].solution
                        .length !== 0
                    ) {
                      recommend(response.data.data); //定制方案推荐
                      handleData(response.data.data.moduleCombine);
                    } else {
                      message.warning("很抱歉，未能为您搜索到相关内容");
                    }
                  } else if (response.data.data.resultType == "7") {
                    let productList = []; //把每个场景下的产品都掏出来和单独搜的产品一起放到一个数组
                    let noRepeatList = []; //去重后的数组
                    let addProductArr = []; //用于去定制的数组
                    // if (response.data.data.moduleCombine.productBagList.length !== 0) {
                    //   for (let i of response.data.data.moduleCombine.productBagList) {
                    //     console.log('11111111111111111', i.key)
                    //     data.commenTile = i.key
                    //       ? i.key + '产品包'
                    //       : "XX客户产品包";
                    //     if (i.productBag.length !== 0) {
                    //       for (let j of i.productBag[0].demandSchemeList) {
                    //         for (let k of j.demandProductList) {
                    //           productList.push(k);
                    //         }
                    //       }
                    //     }
                    //   }
                    // }
                    console.log('response.data.data.moduleCombine.productBagList[0].key', response.data.data.moduleCombine.productBagList[0].key)
                    data.commenTile = response.data.data.moduleCombine.productBagList[0].key
                      ? response.data.data.moduleCombine.productBagList[0].key + '产品包'
                      : "XX客户产品包";
                    data.nowCustomizeProductBagId = response.data.data.moduleCombine.productBagList[0].productBag[0].id
                    console.log(' data.nowCustomizeProductBagId', data.nowCustomizeProductBagId)
                    localStorage.setItem("AInowCustomizeProductBagId", data.nowCustomizeProductBagId);
                    if (response.data.data.moduleCombine.productList.length !== 0) {
                      for (let i of response.data.data.moduleCombine.productList) {
                        if (i.product.length !== 0) {
                          // i.product[0].productId = i.product[0].id
                          for (let j of i.product) {
                            j.productId = j.id
                          }
                          productList.push(i.product[0]);
                        }
                      }
                    }
                    console.log('addProductArr2222222', productList)
                    const map = new Map();
                    productList.forEach((item) =>
                      map.set(item.productId, item)
                    );
                    noRepeatList = Array.from(map.values());
                    console.log("addProductArr333333", noRepeatList);
                    let filterProductBagList = []//筛选出用于定制商客市场的产品
                    filterProductBagList = noRepeatList.filter((item) => {
                      return item.marketplaceList.some(
                        (market) => market.applicationMarket === 1
                      );
                    })
                    console.log('筛选出用于定制商客市场的产品', filterProductBagList)
                    // 第一次定制场景
                    for (let i of filterProductBagList) {
                      addProductArr.push({
                        productId: i.productId,
                        type: 2,
                      });
                    }
                    if (response.data.data.moduleCombine.productBagList.length !== 0) {
                      for (let i of response.data.data.moduleCombine.productBagList) {
                        if (i.productBag.length !== 0) {
                          addProductArr.push({
                            productId: i.productBag[0].id,
                            type: 1,
                          })
                        }
                      }
                    }
                    console.log("addProductArr11111111", addProductArr);

                    // 定制调接口/跳转页面
                    toShopList({
                      productShoppingCarts: addProductArr,
                      source: "2",
                      title: data.commenTile,
                    }).then((res) => {
                      data.aiBuy = true;
                      setTimeout(() => {
                        data.aiBuy = false;
                      }, 2000);
                      changeResultType("7");
                    });
                  }
                } else {
                  console.log("212112");

                  // 方案和场景都搜到了东西,让用户选择
                  data.allDesginData = response.data.data;
                  data.chooseDesignType = true;
                }
              }
            }
          }
        } else {
          changeResultType("4");
        }
        data.search = false;
        data.canSendMessage = true;
      } catch (error) {
      }
    }
    // 取消请求
    const cancelRequest = () => {
      if (cancelTokenSource.value) {
        cancelTokenSource.value.cancel('用户主动取消'); // 触发取消‌:ml-citation{ref="2,4" data="citationList"}
        cancelTokenSource.value = null;
      }
    };
    const handleFocus = (value) => {
      data.historyShow = value;
    };

    const setName = (name) => {
      data.name = name;
      //seekContent(name);
    };
    const clear = () => {
      data.historyName = [];
      getHistory();
      localStorage.removeItem("historyHome");
    };

    const changeResultType = (index) => {
      data.resultType = index;
      localStorage.setItem("resultType", index);
    };
    eventBus.on("changeResultType", changeResultType);

    watch(
      () => route,
      (newVal, oldVal) => {
        console.log("newVal", newVal);
        if (newVal.name != "newAllProject") {
          eventBus.emit("closeMo");
        }
        if (newVal.query.type == "2") {
          sessionStorage.setItem('type',2)
          changeResultType(newVal.query.type);
          data.showSuggest = false; //推荐隐藏
          data.showTui = true; //推荐隐藏
          data.isAIpush = true;
          setTimeout(() => {
            data.isAIpush = false;
          }, 2000);
          router.replace({
            name: "newAllProject",
          });
          localStorage.removeItem("seekName");
        } else if (newVal.query.type == "10") {
          sessionStorage.setItem('type',10)
          console.log("ppppp");
          data.showSuggest = false; //推荐隐藏
          data.showTui = true; //推荐隐藏
          data.aiBuy = true;
          changeResultType("7");
          // data.resultType = "7";
          router.replace({
            name: "newAllProject",
          });
          setTimeout(() => {
            data.aiBuy = false;
          }, 2000);
          localStorage.removeItem("seekName");
        } else if (newVal.query.type == "6") {
          sessionStorage.setItem('type',6)
          changeResultType("3");
          console.log('本地获取检索数据', JSON.parse(localStorage.getItem("goSearchPageList")))
          data.AIgetInfo = JSON.parse(localStorage.getItem("goSearchPageList"));//本地取数据
          data.loadingAnswer = false;
          data.isAIpush = true;
          setTimeout(() => {
            data.isAIpush = false;
          }, 2000);
        } else {
          console.log('xxxxxxxxxxxxssssssssssssssssss')
          if (newVal.name == "newAllProject") {
            if (localStorage.getItem("nowCustomizeSize")) {
              data.nowCustomizeSize = localStorage.getItem("nowCustomizeSize");
            }
            if (localStorage.getItem("seekName")) {
              data.name = localStorage.getItem("seekName");
              if (localStorage.getItem("isFromHome")) {
                console.log("456");

                changeResultType("4");
                seekContent();
                localStorage.removeItem("isFromHome");
                // localStorage.removeItem("seekName");
              } else {
                if (
                  localStorage.getItem("resultType") == "2" ||
                  localStorage.getItem("resultType") == "7"
                ) {
                  data.resultType = localStorage.getItem("resultType");
                } else if (localStorage.getItem("resultType") == "3") {
                  data.resultType = localStorage.getItem("resultType");
                  seekContent();
                }
              }
            } else {
              console.log("9999999");
              data.name = "";
              data.resultType = localStorage.getItem("resultType");
            }
          }
        }
      },
      { deep: true, immediate: true }
    );
    const changeData = () => {
      data.pageN++;
      let num = data.pageN * data.pageS;
      data.isshowList = data.showList.slice(data.pageN * 3 - 3, num);
      if (num >= data.showList.length) {
        data.pageN = 0;
      }
    };
    const reChange = (item) => {
      toCombinePage({
        list: [{ schemeId: item.id, type: 1 }, ...data.topOneCombineList],
        source: "2",
      }).then(() => {
        let newList = JSON.parse(JSON.stringify(data.suggestList));
        newList.splice(item.index, 1);
        if (newList.length > 3) {
          data.showList = [...newList, ...newList, ...newList];
          data.isLess = false;
        } else {
          data.showList = newList;
          data.isLess = true;
        }
        data.isshowList = data.showList.slice(0, 3);

        changeResultType("2");
        data.loadingAnswer = false;
        data.isAIpush = true;
        data.showSuggest = false;
        setTimeout(() => {
          data.isAIpush = false;
        }, 2000);
        getHistory();
      });
    };

    watch(
      () => data.micphoneVisible,
      (newV) => {
        console.log("22222", newV);
        if (newV === false) {
          data.micWords = null;
        }
      }
    );
    // 切换定制维度
    const changeCustomize = () => {
      console.log("data.nowCustomizeSize", data.nowCustomizeSize);
      if (data.nowCustomizeSize == 4) {
        // 初始定制为方案定制，则切换成场景定制
        data.nowCustomizeSize = 7;
      } else if (data.nowCustomizeSize == 7) {
        // 初始定制为场景定制，则切换成方案定制
        data.nowCustomizeSize = 4;
      }
    };
    // 选择去定制方案还是定制场景
    const chooseType = (type) => {
      if (type == "1") {
        //去定制方案
        data.chooseDesignType = false;
        recommend(data.allDesginData); //定制方案推荐
        handleData(data.allDesginData.moduleCombine);
      } else if (type == "2") {
        //去定制场景
        data.chooseDesignType = false;

        changeResultType("7");
        // data.resultType = "7";
        let productList = []; //把每个场景下的产品都掏出来和单独搜的产品一起放到一个数组
        let noRepeatList = []; //去重后的数组
        let addProductArr = []; //用于去定制的数组
        console.log("222222", data.allDesginData);
        // if (data.allDesginData.moduleCombine.productBagList.length !== 0) {
        //   for (let i of data.allDesginData.moduleCombine.productBagList) {
        //     if (i.productBag.length !== 0) {
        //       data.commenTile = i.key
        //         ? i.key + '产品包'
        //         : "XX客户产品包";
        //       for (let j of i.productBag[0].demandSchemeList) {
        //         for (let k of j.demandProductList) {
        //           productList.push(k);
        //         }
        //       }
        //     }
        //   }
        // }
        console.log('res.data.moduleCombine.productBagList[0].key', data.allDesginData.moduleCombine.productBagList[0].key)
        data.commenTile = data.allDesginData.moduleCombine.productBagList[0].key
          ? data.allDesginData.moduleCombine.productBagList[0].key + '产品包'
          : "XX客户产品包";
        data.nowCustomizeProductBagId = data.allDesginData.moduleCombine.productBagList[0].productBag[0].id
        console.log(' data.nowCustomizeProductBagId', data.nowCustomizeProductBagId)
        localStorage.setItem("AInowCustomizeProductBagId", data.nowCustomizeProductBagId);
        if (data.allDesginData.moduleCombine.productList.length !== 0) {
          for (let i of data.allDesginData.moduleCombine.productList) {
            if (i.product.length !== 0) {
              for (let j of i.product) {
                j.productId = j.id
              }
              productList.push(i.product[0]);
            }
          }
        }
        console.log("sxss", productList);
        const map = new Map();
        productList.forEach((item) => map.set(item.productId, item));
        noRepeatList = Array.from(map.values());
        console.log("noRepeatList", noRepeatList);
        let filterProductBagList = []//筛选出用于定制商客市场的产品
        filterProductBagList = noRepeatList.filter((item) => {
          return item.marketplaceList.some(
            (market) => market.applicationMarket === 1
          );
        })
        console.log('筛选出用于定制商客市场的产品', filterProductBagList)
        // 第一次定制场景
        for (let i of filterProductBagList) {
          addProductArr.push({
            productId: i.productId,
            type: 2,
          });
        }
        if (data.allDesginData.moduleCombine.productBagList.length !== 0) {
          for (let i of data.allDesginData.moduleCombine.productBagList) {
            if (i.productBag.length !== 0) {
              addProductArr.push({
                productId: i.productBag[0].id,
                type: 1,
              })
            }
          }
        }
        console.log("addProductArr", addProductArr);

        // 定制调接口/跳转页面
        toShopList({
          productShoppingCarts: addProductArr,
          source: "2",
          title: data.commenTile,
        }).then((res) => {
          data.aiBuy = true;
          setTimeout(() => {
            data.aiBuy = false;
          }, 2000);
        });
      }
    };
    // 处理用户发送消息
    const handleUserMessage = (message = "") => {
      data.isWritingOver = false
      data.isAISearch = true
      const inputMessage = message !== "" ? message : data.name.trim();
      if (inputMessage != "") {
        // 添加用户的消息
        data.messages.push({
          message: inputMessage,
          isUser: true,
        });
        // 清空输入框
        data.name = "";
        setTimeout(() => {
          let scrollElem = contRef.value;
          scrollElem.scrollTo({
            top: scrollElem.scrollHeight,
            behavior: "smooth",
          });
        }, 10)

      }
    };
    // 处理AI回复的消息
    const handleAiReplay = (AImessage = "", AIData) => {
      const inputMessage = AImessage !== "" ? AImessage : data.userInput.trim();
      if (inputMessage != "") {
        const aiMessage = {
          message: '',
          isUser: false,
          AIData: AIData
        };
        data.messages.push(aiMessage)
        data.scrollTimeout = setInterval(() => {
          console.log('xxxxxx')
          let scrollElem = contRef.value;
          // 触发鼠标滚轮时间时，清除定时器，使之在打字的过程中不会一直往下滚动导致不能向上翻阅
          scrollElem.addEventListener("wheel", onUserScroll);
          scrollElem.scrollTo({
            top: scrollElem.scrollHeight,
            behavior: "smooth",
          });
        }, 100)
      }
    }
    // 监听滚轮事件
    const onUserScroll = () => {
      // console.log('ssss')
      data.isUserScrolling = true
      // 清除自动滚动的计时器
      clearInterval(data.scrollTimeout);
      console.log('data.isWritingOver', data.isWritingOver)
    };
    // AI回答完毕
    const writingOver = () => {
      data.isWritingOver = true
      clearInterval(data.scrollTimeout);
    }
    // 清除AI记忆
    const clearAISession = () => {
      clearSession().then((res) => {
        console.log('已清除', res)
      })
    }
    // 每收到方案的时候点击继续生成
    const continueScroll = () => {
      console.log('又继续啦')
      data.AIAnswerLoading = true
      data.scrollTimeout = setInterval(() => {
        console.log('xxxxxx')
        let scrollElem = contRef.value;
        // 触发鼠标滚轮时间时，清除定时器，使之在打字的过程中不会一直往下滚动导致不能向上翻阅
        scrollElem.addEventListener("wheel", onUserScroll);
        scrollElem.scrollTo({
          top: scrollElem.scrollHeight,
          behavior: "smooth",
        });
      }, 100)
    }
    const openSpeak = () => {
      data.isAISearch = !data.isAISearch
    }
    const closeSpeakBox = () => {
      console.log('11111111111111')
      data.isAISearch = false
    }
    // 新语音输入
    const openMicphoneModal = () => {
      data.micphoneVisible = true;
      data.micWords = null;
    };
    const closeMicModal = () => {
      console.log("1");
      // data.micWords = null
      data.isSpeaking = false;
      data.micphoneVisible = false;
      // console.log('connectWebSocket')
      recorderCom.value.closeConnection();
    };
    const startSpeaking = () => {
      data.isSpeaking = true
    }
    const handleAudio = (text) => {
      console.log("text", text);
      data.micWords = text;
    };
    const getVoiceWord = (words) => {
      data.micphoneVisible = false
      data.name = data.micWords
      data.isSpeaking = false
    }
    onMounted(() => {
      data.isAISearch = false
      data.messages = []
    })
    return {
      ...toRefs(data),
      zhCN,
      router,
      route,
      getNewYear,
      seekContent,
      isShowToolTip,
      reChange,
      changeData,
      handleAudio,
      backgroundStyles,
      handleFocus,
      setName,
      clear,
      getHistory,
      changeResultType,
      useStroe,
      routeDetail,
      recorderCom,
      openMicphoneModal,
      closeMicModal,
      changeCustomize,
      chooseType,
      changeShow,
      getCustomizeType,
      handleAiReplay,
      writingOver,
      clearAISession,
      contRef,
      continueScroll,
      openSpeak,
      closeSpeakBox,
      startSpeaking,
      handleAudio,
      getVoiceWord,
    };
  },
});
</script>

<style lang="scss" scoped src="./index.scss"></style>
<style type="text/css" lang="scss">
.history {
  color: rgba(0, 0, 0, 0.85);
  position: absolute;
  width: 1120px;
  background: #fff;
  top: 78px;
  left: 40px;
  padding-left: 40px;
  border-radius: 0 0 5px 5px;
  // z-index: 999;
  box-shadow: 0px 4px 8px 0px rgba(116, 157, 219, 0.3);

  p {
    cursor: pointer;
  }

  :hover {
    color: #40a9ff;
  }

  .clear {
    color: #40a9ff;
  }
}

.newProject {
  width: 100%;
  height: 400px;
  position: absolute;
  top: 0;
  background-image: url("@/assets/images/newProject/customBg.png");
  background-size: cover;
  z-index: -1;
}

.v-enter-active {
  transition: all 0 ease-out;
}

.v-leave-active {
  transition: all 0 cubic-bezier(1, 0.5, 0.8, 1);
}

.v-enter-from,
.v-leave-to {
  transform: translateX(20px);
  opacity: 0;
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #007bff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.AIChat {
  width: 1200px;
  // height: 300px;
  transition: 0.5s;
  background: linear-gradient(180deg, #d3e4f6, #fff);
  border-radius: 0px 0px 8px 8px;
  padding: 0px 20px;
  margin: auto;
  // margin-top: 20px;
  overflow-y: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;

  .user-message {
    text-align: right;
  }

  .ai-message {
    text-align: left;
  }
}

.AIChat::-webkit-scrollbar {
  display: none;
}

.height1 {
  height: 300px;
  transition: 0.5s;

}

.height2 {
  height: 0px;
  transition: 0.5s;
}

.boxBg1 {
  background-color: #fff;
}

.boxBg2 {
  background: linear-gradient(180deg, #d3e4f6, #fff);
}

.loading-spinner {
  border: 4px solid rgba(0, 0, 0, 0.1);
  border-left-color: #007bff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
}
</style>