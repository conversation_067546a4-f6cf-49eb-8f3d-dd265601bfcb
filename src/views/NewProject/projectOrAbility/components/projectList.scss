.AIBg {
    background-image: url("@/assets/images/AI/newAIbg.png");
}

.commonBg {
    background-image: url("@/assets/images/home/<USER>");
}

.tabContent {
		width: 1200px;
    margin: 40px auto;
    background: #F5F7FC;
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    position: relative;

    .AITips {
        width: 100%;
        height: 40px;
        margin-top: 0;
        border-radius: 40px;
        background: linear-gradient(90deg, rgba(145, 141, 254, 0.3) 0%, rgba(73, 151, 254, 0.3) 54%, rgba(73, 194, 254, 0.3) 100%);

        .words {
            font-size: 16px;
            font-weight: 500;
            background: linear-gradient(90deg, #4740FF 0%, #247EFD 55%, #40A2FF 100%);
            -webkit-background-clip: text;
            /* Safari */
            -webkit-text-fill-color: transparent;
            /* 让文字透明 */
        }
    }

    .loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .tabModel {
        border-right: 1px solid #DAE2F5;
        height: 850px;
        overflow: hidden auto;
        flex: none
    }

    .tabModel::-webkit-scrollbar {
        display: none;
    }

    .cardContent {
        // height: 780px;
        overflow: hidden auto;
    }

    .cart-button {
        // position: absolute;
        // right: 20px;
        // bottom: 6px;
        border: none;
        background-color: #f5f7fc;
        color: #40a9ff;
    }

    .cardContent::-webkit-scrollbar {
        display: none;
    }

    .tab_text {
        font-size: 18px;
        color: #262626;
        cursor: pointer;
        height: 40px;
    }

    .cart-button[disabled] {
        cursor: not-allowed;
        opacity: 0.5;
    }

    .activeTab {
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
        padding-top: 32px;
    }

    .topTab {
        margin-top: 0;
        background-image: url("@/assets/images/home/<USER>");
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
    }

    :deep(.ant-tag) {
        font-weight: 500;
        font-size: 12px;
        color: #2E7FFF;
        height: 20px;
        display: flex;
        align-items: center;
    }

    .card_total {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        border-bottom: 1px solid #DAE2F5;
    }

    .emptyPhoto {
        margin: auto;

        img {
            width: 240px;
            height: 248px;
        }
    }

    .card_content {
        position: relative;
        width: 50%;
        height: 163px;
        border-right: 1px solid #DAE2F5;
        border-bottom: 1px solid #DAE2F5;
        cursor: pointer;
    }


    .rightActive {
        border-right: none;
    }

    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }

    .bottomLine {
        border-bottom: none;
    }

    .cardObvious {
        border-bottom: none;
    }

    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        // height: 105px;
        margin-left: 12px;
        // min-width: 66%;
        flex: 1;
    }

    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card_tag {
            display: flex;
            align-items: center;
        }

        .card_title {
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        :deep(.cityStyle) {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;
            // border: none;
            // margin-right: -6px;
        }
    }

    .card_des {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        margin-right: 4px;
        word-break: break-all;
    }

    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }
}

.add-icon {
    width: 16px;
}

.selectData {
		width: 1200px;
    margin: 16px auto 26px;

    .showMore {
        max-height: 400px !important;
        overflow-y: auto;
    }

    .showHidden {
        // overflow: auto;
        max-height: 400px !important;
    }

    .selcet_box {
        border-top: 1px solid #DAE2F5;
        border-bottom: 1px solid #DAE2F5;
        font-weight: 400;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 16px;
        display: flex;
        // align-items: center;
        max-height: 49px;
        /* 设置最大高度 */
        overflow: hidden;
        /* 超出部分隐藏 */
        position: relative;
        /* 伪元素的定位需要 */
        line-height: 1.5em;
        /* 行高用于计算总高度 */
        transition: max-height 0.3s ease-out;

        /* 动画效果 */
        .left_select {
            font-weight: 500;
            color: rgba(0, 0, 0, 0.85);
            background: #F5F7FC;
            padding: 11px 16px;
            min-width: 110px;
        }

        .right_select {
            //position: relative;

            // padding: 11px 16px;
            display: flex;
            flex-wrap: wrap;
            align-items: flex-start;
            // justify-content: left;
            background-color: #FFFFFF;
            padding-left: 12px;
            width: 100%;

            span {
                width: 135px;
                padding: 11px 2px;
                // margin-right: 44px;
                cursor: pointer;
                text-align: center;
            }

            .activeBtn {
                position: relative;
                background: #F3F8FF;
                font-weight: 500;
                font-size: 14px;
                color: #2E7FFF;
                //height: 49px;
            }
        }
    }

    .more {
        background: #ECF4FE;
        //border-radius: 4px 4px 4px 4px;
        padding: 5px 12px;
        font-weight: 500;
        font-size: 16px;
        color: #0C70EB;
        line-height: 22px;
        width: 85px;
        //height: 32px;
        align-items: center;
        //margin-top: 9px;
        cursor: pointer;

        img {
            width: 8px;
            height: 4px;
            margin-left: 4px;
        }
    }

    .select_boot {
        margin: 11px 0;
        padding-left: 12px;
        justify-content: space-between;
        padding: 5px;
        background-color: #FFFFFF;

        .right_con {
            color: #9A9A9A;

            span {
                color: #333333;
                margin: 0 6px;
                font-weight: 500;
            }
        }
    }

    .label {
        margin-left: 24px;
    }

    .right_btn {
        width: 70px;
        height: 32px;
        justify-content: center;
        align-items: center;
        background: rgba(12, 112, 235, 0.08);
        font-weight: 500;
        font-size: 16px;
        color: #0C70EB;
        line-height: 22px;
        margin-top: 6px;
        cursor: pointer;
    }

    .last_data {
        display: flex;
        // max-width: 1085px;
        // min-width: 645px;
        width: 75vw;
        color: rgba(0, 0, 0, 0.65);
        margin-top: 14px;
        background-color: #F3F8FF;
        position: absolute;
        bottom: 0;
        left: 0;
    }
}

.second_line {
    text-align: center;
    font-weight: 500;
    font-size: 16px;
    color: #0C70EB;
    line-height: 22px;
    margin-top: 11px;
    display: flex;
    justify-content: center;

    span {
        cursor: pointer;
    }

    .img_box {
        padding-top: 8px;
        margin-left: 4px;

        img {
            width: 8px;
            height: 4px;
            display: block;
        }
    }
}