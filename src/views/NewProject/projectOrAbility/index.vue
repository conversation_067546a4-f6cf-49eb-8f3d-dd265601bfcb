<template>
	<div style="padding-top: 20px;">
    <div class="totaoText">
	    <span @click="tabChange('1')" :class="{ activeBtn: sourceType === '1' }">标准方案</span>
	    <span @click="tabChange('2')" :class="{ activeBtn: sourceType === '2' }">原子能力</span>
	  </div>
	  <project-list v-show="sourceType === '1'"></project-list>
	  <scene-list v-show="sourceType === '2'"></scene-list>
	</div>
</template>

<script>
import { defineComponent, reactive, toRefs,watch } from "vue";
import projectList from "./components/projectList.vue";
import sceneList from "./components/sceneList.vue";
import { useRouter, useRoute } from "vue-router";
export default defineComponent({
  name: "topContent",
  components: {
    projectList,
    sceneList,
  },
  props: {},
  setup(props) {
    const data = reactive({
      sourceType: "1",
      proList: [],
      sceList: []
    });
    
    const router = useRouter();
    const route = useRoute();
    
    const tabChange = (type) => {
      data.sourceType = type;
    };
    
    return {
      ...toRefs(data),
      tabChange,
      router,
      route,
    };
  },
});
</script>

<style lang="scss" scoped src="./index.scss"></style>