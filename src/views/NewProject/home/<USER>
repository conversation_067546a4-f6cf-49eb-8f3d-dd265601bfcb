<template>
    <div class="home flex">
        <div class="AISearch flex maxHeight" style="flex-direction: column; padding-bottom: 55px;">
            <div class="chatArea padding_t_20 " ref="contRef" @scroll="handleScroll">
                <div v-if="showHistoryLoading" class="maxWidth maxHeight flex just-center align-center">
                    <img style="width: 120px;height: 120px;" src="../../../assets/images/loading3.gif" alt="">
                </div>
                <div v-else class="homeAIChat" v-if="isAISearch">
                    <div v-for="(item, index) in messages" :key="index"
                        :class="item.isUser ? 'user-message' : 'ai-message'">
                        <chat-message ref="chatMessageRef" :message="item.message" :isUser="item.isUser"
                            :thinkingStatus="thinkingStatus" :sessionId="sessionId" @writingOver="writingOver"
                            @clearAISession="clearAISession" @continueScroll="continueScroll" @noGood="noGood"
                            @showProductPackageDetailBox="showProductPackageDetailBox"
                            @changeRightModuleType="(type) => changeRightModuleType(type)" @addPPT="addPPT"
                            @contorlAddingPic="contorlAddingPic" @openPPTPreview="openPPTPreview"
                            @closePPTPreview="closePPTPreview" @clearPPTList="clearPPTList"
                            @closeProductPackageDetailBox="closeProductPackageDetailBox"
                            @addPPTBigTitle="addPPTBigTitle" @addPPTSmallTitle="addPPTSmallTitle" @updatePPT="updatePPT"
                            :AIData="item.AIData" @showHDICTBox="showHDICTBox" @contorlAddingHDICT="contorlAddingHDICT"
                            @addHDICTPics="addHDICTPics" @getChooseSolutionList="getChooseSolutionList" />
                    </div>
                    <div v-if="AIAnswerLoading" class="loading-spinner"></div>
                    <div v-else style="height:20px;margin-top:20px"> </div>
                </div>

            </div>
            <div class="newSearchInfo">
                <div class="vocationPull maxWidth maxHeight">
                    <a-textarea ref="textareaRef" v-model:value="name" class="inputClass"
                        style="min-height: 60px; font-size: 14px" allow-clear auto-size
                        @keydown.enter.prevent="seekContent1" placeholder="您好！我是麒麟AI助手小麟，请问有什么可以帮您的吗？" />
                    <div class="btns flex align-center">
                        <!-- <new-voiceRecorder ref="recorderCom" style="
                height: 44px;
                background-color: #ffffff;
                margin-right: 20px;
              " :isTranslating="isTranslating" :newYear="newData" :canBtnUse="canBtnUse" @goSearch="seekContent1"
                            @audioReady="handleAudio"></new-voiceRecorder> -->
                        <img class="pointer" style="width: 24px;height: 24px;margin-right: 20px;"
                            src="../../../assets/images/AI/microphone.png" @click="openMicphoneModal" alt="">
                        <img v-if="!showStopBtn" class="sendbtn pointer" src="../../../assets/images/AI/goSearchBtn.png"
                            @click="seekContent1()" alt="">
                        <img v-else style="width: 32px;height: 32px;" class="pointer"
                            src="../../../assets/images/AI/stopTalking.png" @click="stopTalking()" alt="">
                        <!-- <i class="sendbtn iconfont icon-fasong pointer"
                            :style="newData ? 'color: #FF5900FF' : 'color: #0c70eb'" @click="seekContent1()"></i> -->
                    </div>
                </div>
                <div v-if="showThinkingSwitch" class="thinking flex margin_t_10">
                    <div class="thinkingBtnOpen">定制深度思考</div>
                    <a-tooltip placement="top">
                        <template #title>
                            <span>利用深度思考,为行业方案扩展内容,但这通常会消耗更长的时间。</span>
                        </template>
                        <svg t="1750293864135" class="icon margin_r_10" viewBox="0 0 1024 1024" version="1.1"
                            xmlns="http://www.w3.org/2000/svg" p-id="3229" width="24" height="24">
                            <path
                                d="M544 581.696v15.488a32 32 0 1 1-64 0V560c0-14.848 10.304-26.752 24-30.4 72.032-30.08 72.064-74.688 72-76.608l-0.032-49.184c0-28.544-28.704-51.84-64-51.84-35.264 0-63.968 23.296-63.968 51.84v15.584a32 32 0 0 1-64 0v-15.584c0-63.84 57.408-115.84 127.968-115.84 70.592 0 128 52 128 115.84v47.584c0.16 1.28 4.672 80.768-95.968 130.304M512 736a32 32 0 1 1 0-64 32 32 0 0 1 0 64m0-608C300.256 128 128 300.288 128 512c0 211.744 172.256 384 384 384s384-172.256 384-384c0-211.712-172.256-384-384-384"
                                fill="#24456A" p-id="3230"></path>
                        </svg>
                    </a-tooltip>
                    <a-switch v-model:checked="thinkingStatus" :disabled="!isWritingOver" />
                </div>
                <!-- <img v-if="data.isAIAnswer && data.hasOverflowed" @click="goBottom()" class="goBottom pointer"
          src="../../../assets/images/AI/goBottom.png" alt=""> -->
            </div>
        </div>
        <div class="showPPT" :class="[isShowPPTOpen ? 'showPPT-open' : 'showPPT-close']">
            <div v-if="isShowPPTOpen" class="margin_l_10 padding_t_40">
                <div class="flex align-center">
                    <div style="width: 24px; height: 24px;" class="pointer" @click="contorlPPT('close')">
                        <img class="maxWidth maxHeight" src="../../../assets/images/AI/openHistory.png" alt="">
                    </div>
                    <div v-if="moduleType == 'common' && showPPTPic"
                        style="width: 88%;text-align: center;font-weight: bold;font-size: 24px;">
                        智能展示橱窗
                    </div>
                    <div v-if="moduleType == 'productPackage' && showProductPackageDetail && showPPTPic"
                        style="width: 88%;text-align: center;font-weight: bold;font-size: 24px;">
                        推荐场景包
                    </div>
                    <div v-if="moduleType == 'PPT' && showPPTPic"
                        style="width: 88%;text-align: center;font-weight: bold;font-size: 24px;">
                        PPT展示
                    </div>
                    <div v-if="moduleType == 'HDICT' && showPPTPic"
                        style="width: 88%;text-align: center;font-weight: bold;font-size: 24px;">
                        HDICT定制内容
                    </div>
                    <div v-if="moduleType == 'chooseSolution' && showContent"
                        style="width: 88%;text-align: center;font-weight: bold;font-size: 24px;">
                        方案推荐
                    </div>
                </div>
                <!-- 右侧用于展示选择方案 -->
                <div class="chooseSolutionDetail" v-if="moduleType == 'chooseSolution'">
                    <div v-if="showContent">
                        <div class="chooseSolutionItem margin_b_16" v-for="(item, index) in chooseSolutionList"
                            :key="index">
                            <div class="imgBox">
                                <div class="genericImgBox" :style="backgroundStyles()">
                                    <p class="genericImgTitle">
                                        {{ item.name }}
                                    </p>
                                </div>
                            </div>
                            <div class="chooseSolutionItemTitle margin_t_10">
                                {{ item.name }}
                            </div>
                            <div class="chooseSolutionItemDescription margin_t_10">
                                <a-tooltip :title="item.description">
                                    <template placement="top">
                                        <div>
                                            {{ item.description }}
                                        </div>
                                    </template>
                                    <div class="textEllipsis3">
                                        {{ item.description }}
                                    </div>
                                </a-tooltip>
                            </div>
                            <div class="btnBox flex just-center align-center margin_t_10">
                                <div class="btnItemDetail pointer margin_r_20" @click="goChooseSolutionDetail(item)">
                                    查看详情
                                </div>
                                <div class="btnItemChoose pointer" @click="chooseThisSolution(item)">
                                    选择
                                </div>
                            </div>
                        </div>
                    </div>

                </div>
                <!-- 右侧用于展示PPT模板 -->
                <div class="PPTDetail" v-if="moduleType == 'PPT'">
                    <div v-if="PPTList.length == 0"
                        style=" height: calc(100vh - 60px - 40px - 32px - 40px); width: 100%;"
                        class="flex just-center align-center">
                        <div v-if="showPPTPic">
                            <div class="flex align-center just-center">
                                <img src="../../../assets/images/AI/PPTworking.png" alt="">
                            </div>
                            <div class="text-center" style="color: rgba(0,0,0,0.45);font-size: 16px;">
                                暂无PPT展示
                            </div>
                        </div>
                    </div>
                    <div v-else style="overflow-y: auto;height: calc(100vh - 60px - 40px - 32px - 20px);"
                        class="flex just-center" ref="pptScrollRef">
                        <div v-if="showPPTPic" class="PPTs">
                            <div v-for="(item, index) in PPTList" class="margin_b_20 " :key="index"
                                :class="{ 'selected-ppt': selectedPPTIndex === index }">
                                <div v-if="item.bigTitle" class="PPTBigTitle flex align-center">
                                    <div class="PPTBigTitleIcon"></div>
                                    <div style="margin-left: 10px;">
                                        {{ item.bigTitle }}
                                    </div>
                                </div>
                                <div v-if="item.smallTitle"
                                    class="PPTSmallTitle margin_b_10 flex just-center align-center">
                                    <div class="smallBorder"></div>
                                    <div>{{ item.smallTitle }}</div>
                                    <div class="smallBorder"></div>
                                </div>
                                <img @click="showPreview(index)" v-if="item.img" class="PPTimg pointer" :src="item.img"
                                    alt="">
                            </div>
                            <div class="maxWidth flex just-center">
                                <div v-if="isAddingPic == 'true'" class="loading-spinner"></div>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- 右侧用于展示产品包详情 -->
                <div class="productPackageDetail" v-if="moduleType == 'productPackage'">
                    <div v-if="showProductPackageDetail">
                        <div class="eachProductPackageDetail margin_b_16" v-for="(item, index) in productPackageList"
                            :key="index">
                            <div class="imgBox">
                                <img class="maxWidth maxHeight" :src="item.mainImg" alt="">
                            </div>
                            <div class="productPackageTitle margin_t_6">
                                {{ item.name }}
                            </div>
                            <div class="productPackageIntroduction margin_t_4">
                                {{ item.introduce }}
                            </div>
                            <div v-if="!item.showSolutionList"
                                class="productPackageBtn flex just-center align-center pointer"
                                @click="openProductPackageDetail(item)">
                                <div class="productPackageBtnIcon flex just-center align-center margin_r_8">
                                    <img class="maxWidth maxHeight" src="../../../assets/images/AI/open.png" alt="">
                                </div>
                                <div class="productPackageBtnItem flex just-center align-center">
                                    展开详情
                                </div>
                            </div>
                            <div v-if="item.showSolutionList" class="margin_t_40">
                                <div class="tab_content">
                                    <img src="@/assets/images/solution/detail/leftIcon.png"
                                        style="width: 33px; height: 22px" alt="" />
                                    <div class="tit">需求方案</div>
                                    <img src="@/assets/images/solution/detail/rightIcon.png"
                                        style="width: 33px; height: 22px" alt="" />
                                </div>
                                <div class="eachSolution margin_b_10"
                                    v-for="(solutionItem, solutionIndex) in item.demandSchemeList" :key="solutionIndex">
                                    <div class="eachSolutionTitle margin_b_8">
                                        {{ solutionItem.name }}
                                    </div>
                                    <div class="eachSolutionContent">
                                        <div class="eachSolutionContentText margin_b_16">
                                            {{ solutionItem.description }}
                                        </div>
                                        <div class="line margin_b_12"></div>
                                        <div class="productRecommend">
                                            <div class="productRecommendTitle margin_b_16">
                                                产品推荐
                                            </div>
                                            <div class="productList">
                                                <div class="eachProduct flex align-center margin_b_16"
                                                    v-for="(productItem, productIndex) in solutionItem.demandProductList"
                                                    :key="productIndex">
                                                    <div class="productImgBox">
                                                        <img class="maxWidth maxHeight" :src="productItem.image" alt="">
                                                    </div>
                                                    <div class="productTitle margin_l_16">
                                                        {{ productItem.name }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div v-if="productPackageList.length == 0"
                            style=" height: calc(100vh - 60px - 40px - 32px - 40px); width: 100%;"
                            class="flex just-center align-center">
                            <div>
                                <div class="flex align-center just-center">
                                    <img src="../../../assets/images/AI/PPTworking.png" alt="">
                                </div>
                                <div class="text-center" style="color: rgba(0,0,0,0.45);font-size: 16px;">
                                    暂无推荐场景包
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
                <!-- 右侧用于默认H图片 -->
                <div class="commonDetail" v-if="moduleType == 'common'">
                    <div style=" height: calc(100vh - 60px - 40px - 32px - 250px); width: 100%;"
                        class="flex just-center align-center">
                        <div v-if="showPPTPic">
                            <img style="width: 300px;height: 300px;" src="../../../assets/images/AI/AIRobot.png" alt="">
                            <div style="width: 300px;font-size: 18px;font-weight: 400;color: #aaa;">
                                这里是小麟的智能展示橱窗，小麟将在这里向您展示推荐给您的内容哦~
                            </div>
                        </div>

                    </div>
                </div>
                <!-- 右侧用于HDICT内容 -->
                <div class="HDICTDetail" v-if="moduleType == 'HDICT'">
                    <div class="flex just-center align-center"
                        style="height: calc(100vh - 60px - 40px - 32px - 60px);width: 100%;"
                        v-if="HDICTList.length == 0 && isAddingHDICT == 'false'">
                        <div v-if="showPPTPic">
                            <div class="flex align-center just-center">
                                <img src="../../../assets/images/AI/PPTworking.png" alt="">
                            </div>
                            <div class="text-center" style="color: rgba(0,0,0,0.45);font-size: 16px;">
                                暂无HDICT内容展示
                            </div>
                        </div>
                    </div>
                    <div v-else style="overflow-y: auto;height: calc(100vh - 60px - 40px - 32px - 20px);" class="flex"
                        ref="HDICTScrollRef">
                        <div v-if="showPPTPic" class="HDICTPics">
                            <div class="eachHDICTDetail" v-for="(item, index) in HDICTList" :key="index">
                                <div v-if="item.type == 'houseType'">
                                    <div class="PPTBigTitle flex align-center margin_b_16">
                                        <div class="PPTBigTitleIcon"></div>
                                        <div style="margin-left: 10px;">
                                            定制户型
                                        </div>
                                    </div>
                                    <div @click="showPreview(imgIndex)" v-for="(imgItem, imgIndex) in item.data.image"
                                        :key="imgIndex">
                                        <div class="imgBox margin_b_16">
                                            <img class="maxWidth maxHeight pointer" :src="imgItem" alt="">
                                        </div>
                                    </div>
                                    <div class="productPackageTitle margin_t_6">
                                        {{ item.data.name }}
                                    </div>
                                    <div class="productPackageIntroduction margin_t_4">
                                        {{ item.data.description }}
                                    </div>
                                    <div class="tab_content margin_t_16">
                                        <img src="@/assets/images/solution/detail/leftIcon.png"
                                            style="width: 33px; height: 22px" alt="" />
                                        <div class="tit">户型场景</div>
                                        <img src="@/assets/images/solution/detail/rightIcon.png"
                                            style="width: 33px; height: 22px" alt="" />
                                    </div>
                                    <div class="eachSolution margin_b_10"
                                        v-for="(solutionItem, solutionIndex) in item.data.scenes" :key="solutionIndex">
                                        <div class="eachSolutionTitle margin_b_8">
                                            {{ solutionItem.name }}
                                        </div>
                                        <div class="eachSolutionContent">
                                            <div class="eachSolutionContentText margin_b_16">
                                                {{ solutionItem.introduction }}
                                            </div>
                                            <div class="line margin_b_12"></div>
                                            <div class="productRecommend">
                                                <div class="productRecommendTitle margin_b_16">
                                                    产品推荐
                                                </div>
                                                <div class="productList">
                                                    <div class="eachProduct flex align-center margin_b_16"
                                                        v-for="(productItem, productIndex) in solutionItem.products"
                                                        :key="productIndex">
                                                        <div class="productImgBox">
                                                            <img class="maxWidth maxHeight" :src="productItem.image"
                                                                alt="">
                                                        </div>
                                                        <div class="productTitle margin_l_16">
                                                            {{ productItem.name }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div v-if="item.type == 'scene'">
                                    <div class="PPTBigTitle flex align-center margin_b_16">
                                        <div class="PPTBigTitleIcon"></div>
                                        <div style="margin-left: 10px;">
                                            定制场景
                                        </div>
                                    </div>
                                    <div class="eachSolution margin_b_10">
                                        <div class="eachSolutionTitle margin_b_8">
                                            {{ item.data.name }}
                                        </div>
                                        <div class="eachSolutionContent">
                                            <div class="eachSolutionContentText margin_b_16">
                                                {{ item.data.introduction }}
                                            </div>
                                            <div class="line margin_b_12"></div>
                                            <div class="productRecommend">
                                                <div class="productRecommendTitle margin_b_16">
                                                    产品推荐
                                                </div>
                                                <div class="productList">
                                                    <div class="eachProduct flex align-center margin_b_16"
                                                        v-for="(productItem, productIndex) in item.data.products"
                                                        :key="productIndex">
                                                        <div class="productImgBox">
                                                            <img class="maxWidth maxHeight" :src="productItem.image"
                                                                alt="">
                                                        </div>
                                                        <div class="productTitle margin_l_16">
                                                            {{ productItem.name }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <div class="HDICTProduct" v-if="item.type == 'product'">
                                    <div class="PPTBigTitle flex align-center margin_b_16">
                                        <div class="PPTBigTitleIcon"></div>
                                        <div style="margin-left: 10px;">
                                            定制产品
                                        </div>
                                    </div>
                                    <div class="eachProduct flex align-center margin_b_16" style="">
                                        <div class="productImgBox">
                                            <img class="maxWidth maxHeight" :src="item.data.image" alt="">
                                        </div>
                                        <div class="productTitle margin_l_16">
                                            {{ item.data.name }}
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="maxWidth flex just-center maxWidth" style="width: 380px;">
                                <div v-if="isAddingHDICT == 'true'" class="loading-spinner"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div v-else class="flex just-center padding_t_40">
                <div style="width: 24px; height: 24px;" class="pointer" @click="contorlPPT('open')">
                    <img class="maxWidth maxHeight" src="../../../assets/images/AI/closeHistory.png" alt="">
                </div>
            </div>
        </div>
        <a-modal class="micModal" v-if="micphoneVisible" v-model:visible="micphoneVisible" :width="1056" :footer="null"
            :maskClosable="false" @cancel="closeMicModal()" :style="{ top: '30%' }">
            <div class="content maxWidth" style="padding: 8px 40px 8px 40px">
                <div class="flex">
                    <div class="margin_b_32 margin_r_40">
                        <img style="width: 88px;height: 88px;" src="../../../assets/images/AI/voiceMic.png" alt="">
                    </div>
                    <div>
                        <img v-if="!isSpeaking" style="width: 532px;height: 88px;"
                            src="../../../assets/images/AI/blackSpeaking.png" alt="">
                        <img v-else style="width: 532px;height: 88px;" src="../../../assets/images/AI/speaking.gif"
                            alt="">
                    </div>
                </div>

                <div class="micTextWords flex align-center just-center margin_b_32">
                    <a-textarea v-model:value="micWords" :disabled="false" class="inputClass" :resize="'none'" auto-size
                        placeholder="语音实时转文字内容..." style="
                font-size: 16px;
                width: 928px;
                min-height: 185px;
                border: none;
                border-radius: 10px;
                background: rgba(255,255,255,0.3);
                color: #000;
                border: 2px solid #FFFFFF;
              " />
                </div>
                <div class="micLogo flex just-center align-center maxWidth">
                    <new-voiceRecorder ref="recorderCom" @startSpeaking="startSpeaking" @audioReady="handleAudio"
                        @getVoiceWord="getVoiceWord"></new-voiceRecorder>
                </div>
            </div>
        </a-modal>
        <!-- 添加图片预览弹窗 -->
        <a-modal v-model:visible="previewVisible" :footer="null" :width="1500" :closable="false" class="preview-modal">
            <div class="preview-container">
                <div class="preview-arrow left" @click="prevImage">
                    <img style="width: 48px;height: 48px;" src="../../../assets/images/AI/goLeft.png" alt="上一张">
                </div>
                <div class="preview-content maxWidth maxHeight">
                    <img style="width: 100%;height: 100%;" :src="currentPreviewImage" alt="预览图片" class="preview-image">
                    <!-- <div class="preview-title">{{ currentPreviewTitle }}</div> -->
                </div>
                <div class="preview-arrow right" @click="nextImage">
                    <img style="width: 48px;height: 48px;" src="../../../assets/images/AI/goRight.png" alt="下一张">
                </div>
            </div>
        </a-modal>
    </div>

</template>

<script>
import { defineComponent, onMounted, onUnmounted, reactive, toRefs, watch, onBeforeUnmount } from "vue";
import { useRouter, useRoute } from "vue-router";
import chatMessage from "./components/chatMessage.vue";
import newVoiceRecorder from "@/components/voiceRecorder/newVoiceRecorder.vue";
import { getMoreChat, clearSession, getAISearchHistoryList, deleteAISearchHistory, getChatContent } from "@/api/AI/ai.js";
import eventBus from "@/utils/eventBus";
import { fa, tr } from "element-plus/es/locale/index.mjs";
import productPackageImg from "@/assets/images/AI/Group 11090.png";
import { treeEmits } from "element-plus/es/components/tree-v2/src/virtual-tree.mjs";
import bac from "@/assets/images/noDataBac.png";
const contRef = ref(null);
const recorderCom = ref(null);
const chatMessageRef = ref()
const pptScrollRef = ref(null);
const HDICTScrollRef = ref(null);
const textareaRef = ref(null);
export default defineComponent({
    name: "AISearch",
    components: {
        chatMessage,
        newVoiceRecorder
    },
    props: {

    },
    setup(props) {
        const router = useRouter();
        const route = useRoute();
        const data = reactive({
            showThinkingSwitch: false,//要展示的思考展示开关
            backgroundImage: bac,
            thinkingStatus: true,//是否开启要素思考
            selectedIndex: null,
            isAISearch: true,
            messages: [],
            scrollTimeout: null,
            isUserScrolling: false,
            isWritingOver: true,
            AIAnswerLoading: false,
            name: '',
            newData: false,
            micWords: '',
            scrollTimeout: null,
            onSearchPage: true,
            isNavigationOpen: true,
            isShowPPTOpen: false,
            showNewChat: true,
            historyList: [
                {
                    time: '今天',
                    chatList: []
                },
                {
                    time: '昨天',
                    chatList: []
                },
                {
                    time: '7天内',
                    chatList: []
                },
                {
                    time: '更早',
                    chatList: []
                },
            ],
            sessionId: null,
            dialogType: 0,
            showStopBtn: false,
            showPPTPic: false,
            nowHoverIndex: null,
            nowHoverListIndex: null,
            historyDataList: [],
            micWords: null,//以下是用于语音输入的
            micphoneVisible: false,
            isSpeaking: false,
            PPTList: [],
            selectedPPTIndex: null,
            previewVisible: false,
            currentPreviewIndex: 0,
            showHistoryLoading: false,
            moduleType: 'common',//PPT、productPackage、common
            showProductPackageDetail: false,
            productPackageList: [],
            useContrastArr: [],
            isAddingPic: 'false',
            isAddingHDICT: 'false',
            HDICTList: [],//用来展示HDICT乱七八糟的东西
            chooseSolutionList: [],//用来展示可供用户选择的方案列表
            showContent: false,
        });
        // 语音输入
        // const handleAudio = (text) => {
        //     console.log("text", text);
        //     data.name = text;
        // };
        // 新语音输入
        const openMicphoneModal = () => {
            data.micphoneVisible = true;
            data.micWords = null;
        };
        const closeMicModal = () => {
            console.log("1");
            // data.micWords = null
            data.isSpeaking = false;
            data.micphoneVisible = false;
            // console.log('connectWebSocket')
            recorderCom.value.closeConnection();
        };
        const startSpeaking = () => {
            data.isSpeaking = true
        }
        const handleAudio = (text) => {
            console.log("text", text);
            data.micWords = text;
        };
        const getVoiceWord = (words) => {
            data.micphoneVisible = false
            data.name = data.micWords
            data.isSpeaking = false
            handleTextareaChange()
        }
        // 处理用户发送消息
        const handleUserMessage = (message = "") => {
            data.isWritingOver = false
            data.isAISearch = true
            const inputMessage = message !== "" ? message : data.name.trim();
            if (inputMessage != "") {
                // 添加用户的消息
                data.messages.push({
                    message: inputMessage,
                    isUser: true,
                });
                // 清空输入框
                data.name = "";
                setTimeout(() => {
                    let scrollElem = contRef.value;
                    scrollElem.scrollTo({
                        top: scrollElem.scrollHeight,
                        behavior: "smooth",
                    });
                }, 100)

            }
        };
        // 处理AI回复的消息
        const handleAiReplay = (AImessage = "", AIData, isError = false) => {
            if (isError == '否') {
                data.showStopBtn = true
                const inputMessage = AImessage !== "" ? AImessage : data.userInput.trim();
                if (inputMessage != "") {
                    const aiMessage = {
                        message: '',
                        isUser: false,
                        AIData: AIData
                    };
                    console.log('zjjzjj', aiMessage)
                    data.messages.push(aiMessage)

                    // 更新localStorage中的历史数据
                    const historyData = JSON.parse(localStorage.getItem("homeHisData") || "[]");
                    const lastMessage = historyData[historyData.length - 1];
                    if (lastMessage) {
                        lastMessage.answer = JSON.stringify({ answer: AIData.answer });
                        localStorage.setItem("homeHisData", JSON.stringify(historyData));
                    }

                    data.scrollTimeout = setInterval(() => {
                        console.log('xxxxxx')
                        let scrollElem = contRef.value;
                        // 触发鼠标滚轮时间时，清除定时器，使之在打字的过程中不会一直往下滚动导致不能向上翻阅
                        scrollElem.addEventListener("wheel", onUserScroll);
                        scrollElem.scrollTo({
                            top: scrollElem.scrollHeight,
                            behavior: "smooth",
                        });
                    }, 100)
                }
            } else if (isError == '是') {
                data.showStopBtn = true
                const aiMessage = {
                    message: '',
                    isUser: false,
                    AIData: {
                        answer: 'AI正在训练中，请稍后再试',
                        dialogType: 0,
                        sessionId: null
                    }
                };
                // console.log('zjjzjj', aiMessage)
                data.messages.push(aiMessage)

                // 更新localStorage中的历史数据
                const historyData = JSON.parse(localStorage.getItem("homeHisData") || "[]");
                const lastMessage = historyData[historyData.length - 1];
                if (lastMessage) {
                    lastMessage.answer = JSON.stringify({ answer: AIData.answer });
                    localStorage.setItem("homeHisData", JSON.stringify(historyData));
                }

                data.scrollTimeout = setInterval(() => {
                    console.log('xxxxxx')
                    let scrollElem = contRef.value;
                    // 触发鼠标滚轮时间时，清除定时器，使之在打字的过程中不会一直往下滚动导致不能向上翻阅
                    scrollElem.addEventListener("wheel", onUserScroll);
                    scrollElem.scrollTo({
                        top: scrollElem.scrollHeight,
                        behavior: "smooth",
                    });
                }, 100)
            }

        }
        // 没收到方案的时候点击继续生成
        const continueScroll = () => {
            console.log('又继续啦')
            data.showStopBtn = true
            // data.AIAnswerLoading = true
            data.scrollTimeout = setInterval(() => {
                console.log('xxxxxx')
                let scrollElem = contRef.value;
                // 触发鼠标滚轮时间时，清除定时器，使之在打字的过程中不会一直往下滚动导致不能向上翻阅
                scrollElem.addEventListener("wheel", onUserScroll);
                scrollElem.scrollTo({
                    top: scrollElem.scrollHeight,
                    behavior: "smooth",
                });
            }, 100)
        }
        // 监听滚轮事件
        const onUserScroll = () => {
            // console.log('ssss')
            data.isAIAnswer = true
            data.isUserScrolling = true
            // 清除自动滚动的计时器
            clearInterval(data.scrollTimeout);
            console.log('data.isWritingOver', data.isWritingOver)
        };
        // AI回答完毕
        const writingOver = () => {
            console.log('writingOver')
            data.isWritingOver = true
            data.showStopBtn = false
            clearInterval(data.scrollTimeout);
        }
        eventBus.on('writingOver', () => {
            writingOver();
        });
        // 从历史记录进来的时候，设置对话类型为0
        const fromHistoryDialogType = () => {
            console.log('手动更改data.dialogType')
            data.dialogType = 0
        }
        eventBus.on('fromHistoryDialogType', () => {
            fromHistoryDialogType();
        });
        // 清除AI记忆
        const clearAISession = () => {
            if (data.sessionId) {
                clearSession({
                    sessionId: data.sessionId,
                }).then((res) => {
                    console.log('已清除', res)
                })
            }

        }
        // 滚动到底部的方法
        const goBottom = () => {
            clearInterval(data.scrollTimeout);
            data.scrollTimeout = null
            let scrollElem = contRef.value;
            scrollElem.scrollTo({
                top: scrollElem.scrollHeight,
                behavior: "smooth"
            });
            if (!data.isWritingOver) {
                data.scrollTimeout = setInterval(() => {
                    console.log('xxxxxx')
                    // 触发鼠标滚轮时间时，清除定时器，使之在打字的过程中不会一直往下滚动导致不能向上翻阅
                    scrollElem.addEventListener("wheel", onUserScroll);
                    scrollElem.scrollTo({
                        top: scrollElem.scrollHeight,
                        behavior: "smooth",
                    });
                }, 300)
            }
            setTimeout(() => {
                data.isAIAnswer = false
            }, 500);
        }
        // 溢出检测方法
        const checkOverflow = () => {
            const container = contRef.value
            if (container.scrollHeight > container.clientHeight) {
                console.log('超出了')
                data.hasOverflowed = true

                // data.overIndex += 1
                // console.log('data.overIndex', data.overIndex)
                // if (data.overIndex >= 2) {
                //   data.hasOverflowed = true
                // }
            }
        }
        const handleScroll = (e) => {
            if (!contRef.value) return

            // 首次滚动时判断溢出状态
            // if (!data.hasOverflowed) {
            //   checkOverflow()
            // }
        }
        const seekContent1 = () => {
            localStorage.setItem("isFromHistory", '不是')
            let question = JSON.parse(JSON.stringify(data.name.trim()))
            if (data.name.trim() !== '' && data.isWritingOver) {
                // 发送消息
                if (localStorage.getItem('isFromHome') == '是' && !localStorage.getItem('fromHomeSessionId')) {
                    eventBus.emit('setTemporaryHistoryInfo', JSON.stringify({ question: data.name, sessionId: null }))
                }
                getMoreChat({
                    question: data.name,
                    sessionId: data.sessionId,
                    dialogType: data.dialogType,
                }).then((res) => {
                    data.sessionId = res.data.sessionId
                    data.dialogType = res.data.dialogType
                    if (localStorage.getItem('isFromHome') == '是') {
                        if (!localStorage.getItem('fromHomeSessionId')) {
                            localStorage.setItem("fromHomeSessionId", res.data.sessionId)
                            eventBus.emit('setTemporaryHistoryInfo', JSON.stringify({ question: null, sessionId: res.data.sessionId }))
                        }
                    }
                    console.log('res', res)
                    data.AIAnswerLoading = false
                    if (data.onSearchPage) {
                        handleAiReplay(question, res.data, '否')
                    }
                }).catch((err) => {
                    console.log('err', err)
                    handleAiReplay('AI正在训练中，请稍后再试', [], '是')
                    data.AIAnswerLoading = false
                })
                handleUserMessage();
                data.AIAnswerLoading = true
            }
            // // 点击搜索关闭webscoket连接
            if (recorderCom.value) {
                console.log("关闭webscoket连接");
                recorderCom.value.closeConnection();
            }
        };
        const noGood = () => {
            let question = '我不满意'
            getMoreChat({
                question: question,
                sessionId: data.sessionId,
            }).then((res) => {
                data.sessionId = res.data.sessionId
                console.log('res', res)
                data.AIAnswerLoading = false
                handleAiReplay(question, res.data)
            })
            data.AIAnswerLoading = true
        }


        // 暂停对话
        const stopTalking = () => {
            if (chatMessageRef.value) {
                chatMessageRef.value.forEach(component => {
                    if (component.continueProcess) {
                        component.continueProcess = false
                        component.isAIWriting = false
                    }
                });
            }
            data.isAddingPic = 'false'
            writingOver();
        };
        onBeforeUnmount(() => {
            console.log('chatMessageRef.value', chatMessageRef.value)
            stopTalking()
        })
        // 加载历史数据的方法
        const loadHistoryData = (sessionId) => {
            if (!sessionId) return;

            const historyData = JSON.parse(localStorage.getItem("homeHisData"));
            console.log('xxxxxxxxxxxaaaa', historyData)
            if (historyData) {
                data.sessionId = sessionId;
                data.messages = [];

                const newMessages = [];
                historyData.forEach(item => {
                    newMessages.push({
                        message: item.question,
                        isUser: true,
                    });
                    if (item.answer) {
                        let answer = JSON.parse(item.answer)
                        let aimDescription = null
                        if (answer.aimDescription) {
                            aimDescription = JSON.parse(answer.aimDescription)
                            console.log('aimDescription', aimDescription)
                            // 把chatId赋值给具体的定制内容的对象aimDescription
                            aimDescription.chatId = item.id
                        }
                        // console.log('item.answer', item.answer)
                        newMessages.push({
                            isUser: false,
                            AIData: {
                                answer: answer.answer,
                                aimDescription: answer.aimDescription ? aimDescription : null
                            }
                        });
                    }
                });

                data.messages = newMessages;
                data.isAISearch = false;
                nextTick(() => {
                    data.isAISearch = true;
                    setTimeout(() => {
                        contRef.value?.scrollTo({
                            top: contRef.value.scrollHeight,
                            behavior: "smooth"
                        });
                    }, 100);
                });
            }
        };

        // 监听路由参数 sessionId 的变化
        watch(() => route.query.sessionId, (newSessionId) => {
            loadHistoryData(newSessionId);
        }, { immediate: true });

        onMounted(() => {
            handleTextareaChange()
            console.log('route', route)
            console.log('地址栏', route.query.isFromHomeHis)
            console.log('要素思考状态', route.query.thinkingStatus)
            if (route.query.thinkingStatus === 'true') {
                data.thinkingStatus = true
            } else if (route.query.thinkingStatus === 'false') {
                data.thinkingStatus = false
            }
            // 从localStorage中获取选中的历史记录索引
            const selectedHistoryIndex = JSON.parse(localStorage.getItem("selectedHistoryIndex") || "null");
            if (selectedHistoryIndex) {
                data.selectedIndex = selectedHistoryIndex;
            }
            if (localStorage.getItem('isFromHome') == '是' && localStorage.getItem('fromHomeSessionId')) {
                data.messages = []
                const targetSessionId = localStorage.getItem('fromHomeSessionId');
                if (targetSessionId) {
                    // 通过API获取最新的聊天记录
                    data.showHistoryLoading = true
                    getChatContent({
                        sessionId: targetSessionId
                    }).then((res) => {
                        localStorage.setItem("homeHisData", JSON.stringify(res.data))
                        localStorage.setItem("isFromHistory", '是')
                        data.showHistoryLoading = false
                        loadHistoryData(targetSessionId)
                    });
                }
            } else {
                if (route.query.isFromHomeHis === 'true' || route.query.sessionId) {
                    // 清空现有消息
                    data.messages = []
                    const targetSessionId = route.query.sessionId;
                    if (targetSessionId) {
                        // 通过API获取最新的聊天记录
                        data.showHistoryLoading = true
                        getChatContent({
                            sessionId: targetSessionId
                        }).then((res) => {
                            localStorage.setItem("homeHisData", JSON.stringify(res.data))
                            localStorage.setItem("isFromHistory", '是')
                            data.showHistoryLoading = false
                            loadHistoryData(targetSessionId)
                        });
                    }
                } else {
                    data.name = localStorage.getItem("AISearchQuestion")
                    if (localStorage.getItem("AISearchQuestion")) {
                        seekContent1()
                    }
                }
            }


            // 监听openChatLoading事件
            eventBus.on('openChatLoading', () => {
                openChatLoading();
            });
            // 监听closeChatLoading事件
            eventBus.on('closeChatLoading', () => {
                closeChatLoading();
            });

            // 添加localStorage变化监听
            window.addEventListener('storage', (e) => {
                if (e.key === 'homeHisData' && e.newValue) {
                    const newHistoryData = JSON.parse(e.newValue);
                    if (newHistoryData && newHistoryData.length > 0) {
                        loadHistoryData(route.query.sessionId);
                    }
                }
            });

            // 监听showThinkingSwitch事件
            eventBus.on('showThinkingSwitch', handleShowThinkingSwitch);
        })

        // 导航栏开关
        const contorlNavigation = (type) => {
            if (type == 'open') {
                data.isNavigationOpen = true
                setTimeout(() => {
                    data.showNewChat = true
                }, 100);
            } else if (type == 'close') {
                data.showNewChat = false
                data.isNavigationOpen = false
            }
        }
        // PPT栏开关
        const contorlPPT = (type) => {
            if (type == 'open') {
                data.isShowPPTOpen = true
                setTimeout(() => {
                    data.showPPTPic = true
                    data.showProductPackageDetail = true
                    data.showContent = true
                }, 500);
            } else if (type == 'close') {
                data.isShowPPTOpen = false
                data.showPPTPic = false
                data.showProductPackageDetail = false
                data.showContent = false
            }
        }
        // 开始新对话
        const startNewChat = () => {
            router.push({
                path: "/home",
            });
        }
        // 计算距离当前日子多少天
        const countDays = (date) => {
            const parts = date.split('-');
            const year = parseInt(parts[0], 10);
            const month = parseInt(parts[1], 10) - 1; // 月份从0开始
            const day = parseInt(parts[2], 10);

            // 创建输入日期的Date对象（本地时间0点）
            const userDate = new Date(year, month, day);
            userDate.setHours(0, 0, 0, 0);

            // 创建今天的Date对象（本地时间0点）
            const today = new Date();
            today.setHours(0, 0, 0, 0);

            // 计算时间差（毫秒）
            const diffTime = userDate.getTime() - today.getTime();

            // 转换为天数并返回
            return Math.floor(diffTime / (1000 * 60 * 60 * 24));
        }
        // 获取多轮对话历史记录
        const getHistoryChat = () => {
            data.historyList[0].chatList = []
            data.historyList[1].chatList = []
            data.historyList[2].chatList = []
            data.historyList[3].chatList = []
            getAISearchHistoryList({}).then((res) => {
                if (res.data.rows.length !== 0) {
                    res.data.rows.sort((a, b) => new Date(b.startTime) - new Date(a.startTime));
                }

                console.log('历史记录', res)
                data.historyDataList = res.data.rows
                getHistoryChatData()
            })
        }
        // getHistoryChat()
        const getHistoryChatData = () => {
            if (data.historyDataList.length !== 0) {
                for (let i of data.historyDataList) {
                    console.log('11111111111111', countDays(i.startTime))
                    if (countDays(i.startTime) == 0) {
                        // 说明是今天
                        data.historyList[0].chatList.push({
                            question: i.startQuestion,
                            sessionId: i.sessionId,
                            dataList: i.dataList
                        })
                    } else if (countDays(i.startTime) == -1) {
                        //说明是昨天
                        data.historyList[1].chatList.push({
                            question: i.startQuestion,
                            sessionId: i.sessionId,
                            dataList: i.dataList,
                        })
                    } else if (countDays(i.startTime) < -1 && countDays(i.startTime) > -7) {
                        //说明是七天内
                        data.historyList[2].chatList.push({
                            question: i.startQuestion,
                            sessionId: i.sessionId,
                            dataList: i.dataList,

                        })
                    } else if (countDays(i.startTime) < -7) {
                        // 说明过去了七天以上
                        data.historyList[3].chatList.push({
                            question: i.startQuestion,
                            sessionId: i.sessionId,
                            dataList: i.dataList,
                        })
                    }
                }
            }
        }
        const searchHistory = (info, index) => {
            // 保存选中的历史记录索引到localStorage
            localStorage.setItem("selectedHistoryIndex", JSON.stringify(index));

            // 重置所有历史记录的选中状态
            data.historyList.forEach((item, groupIndex) => {
                item.chatList.forEach((chat, chatIndex) => {
                    if (groupIndex === index.groupIndex && chatIndex === index.chatIndex) {
                        data.selectedIndex = { groupIndex, chatIndex };
                    }
                });
            });

            console.log('info', info)
            localStorage.setItem("isFromHistory", '是')
            data.sessionId = info[0].sessionId

            // 清空现有消息
            data.messages = []

            // 创建临时数组构建完整对话记录
            const newMessages = []

            // 遍历每条消息记录
            info.forEach(item => {
                // 添加用户问题
                newMessages.push({
                    message: item.question,
                    isUser: true,
                })

                // 添加AI回答
                if (item.answer) {
                    try {
                        const answerData = JSON.parse(item.answer)
                        newMessages.push({
                            isUser: false,
                            AIData: {
                                answer: answerData.answer
                            }
                        })
                    } catch (e) {
                        console.error('解析答案失败:', e)
                    }
                }
            })

            // 强制组件重新渲染
            data.isAISearch = false
            nextTick(() => {
                // 更新消息数组
                data.messages = newMessages
                data.isAISearch = true

                // 确保滚动到底部
                setTimeout(() => {
                    if (contRef.value) {
                        contRef.value.scrollTo({
                            top: contRef.value.scrollHeight,
                            behavior: "smooth"
                        })
                    }
                }, 100)
            })
        }
        // 删除历史记录
        const deleteChatHis = (item) => {
            console.log('item', item)
            deleteAISearchHistory({
                sessionId: item.sessionId
            }).then((res) => {
                console.log('删除历史记录', res)
                data.historyList.forEach(group => {
                    group.chatList = group.chatList.filter(chat => chat.sessionId !== item.sessionId)
                })
            })
        }
        onUnmounted(() => {
            console.log('离开')
            stopTalking()
            clearInterval(data.scrollTimeout);
            localStorage.removeItem("AISearchQuestion")
            data.onSearchPage = false
            data.scrollTimeout = null
            if (data.sessionId) {
                clearSession({
                    sessionId: data.sessionId,
                }).then((res) => {
                    console.log('清除记忆', res)
                })
            }
            // 移除事件监听
            eventBus.off('openChatLoading');
            eventBus.off('closeChatLoading');
            eventBus.off('writingOver');
            // 移除storage事件监听
            window.removeEventListener('storage', () => { });

            // 移除事件监听
            eventBus.off('showThinkingSwitch', handleShowThinkingSwitch);
        })

        // 添加预览相关的方法
        const showPreview = (index) => {
            if (data.moduleType == 'PPT') {
                data.currentPreviewIndex = index;
                data.selectedPPTIndex = index;
                data.previewVisible = true;
                // 同步滚动右侧抽屉
                nextTick(() => {
                    if (pptScrollRef.value) {
                        const selectedElement = pptScrollRef.value.querySelector(`.margin_b_20:nth-child(${index + 1})`);
                        if (selectedElement) {
                            selectedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                });
            } else if (data.moduleType == 'HDICT') {
                data.currentPreviewIndex = index;
                data.selectedPPTIndex = index;
                data.previewVisible = true;
                // 同步滚动右侧抽屉
                nextTick(() => {
                    if (HDICTScrollRef.value) {
                        const selectedElement = HDICTScrollRef.value.querySelector(`.margin_b_20:nth-child(${index + 1})`);
                        if (selectedElement) {
                            selectedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                        }
                    }
                });
            }

        };

        const prevImage = () => {
            if (data.moduleType == 'PPT') {
                let newIndex = data.currentPreviewIndex;
                do {
                    newIndex = newIndex > 0 ? newIndex - 1 : data.PPTList.length - 1;
                } while (!data.PPTList[newIndex]?.img && newIndex !== data.currentPreviewIndex);

                if (newIndex !== data.currentPreviewIndex) {
                    data.currentPreviewIndex = newIndex;
                    data.selectedPPTIndex = newIndex;
                    // 同步滚动右侧抽屉
                    nextTick(() => {
                        if (pptScrollRef.value) {
                            const selectedElement = pptScrollRef.value.querySelector(`.margin_b_20:nth-child(${newIndex + 1})`);
                            if (selectedElement) {
                                selectedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }
                    });
                }
            } else if (data.moduleType == 'HDICT') {
                let HDICTArr = []
                for (let i of data.HDICTList) {
                    if (i.type == 'houseType') {
                        HDICTArr = i.data.image
                    }
                }
                let newIndex = data.currentPreviewIndex;
                do {
                    newIndex = newIndex > 0 ? newIndex - 1 : HDICTArr.length - 1;
                } while (!HDICTArr[newIndex] && newIndex !== data.currentPreviewIndex);
                if (newIndex !== data.currentPreviewIndex) {
                    data.currentPreviewIndex = newIndex;
                    data.selectedPPTIndex = newIndex;
                    // 同步滚动右侧抽屉
                    nextTick(() => {
                        if (HDICTScrollRef.value) {
                            const selectedElement = HDICTScrollRef.value.querySelector(`.margin_b_20:nth-child(${newIndex + 1})`);
                            if (selectedElement) {
                                selectedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }
                    });
                }
            }

        };

        const nextImage = () => {
            if (data.moduleType == 'PPT') {
                let newIndex = data.currentPreviewIndex;
                do {
                    newIndex = newIndex < data.PPTList.length - 1 ? newIndex + 1 : 0;
                } while (!data.PPTList[newIndex]?.img && newIndex !== data.currentPreviewIndex);

                if (newIndex !== data.currentPreviewIndex) {
                    data.currentPreviewIndex = newIndex;
                    data.selectedPPTIndex = newIndex;
                    // 同步滚动右侧抽屉
                    nextTick(() => {
                        if (pptScrollRef.value) {
                            const selectedElement = pptScrollRef.value.querySelector(`.margin_b_20:nth-child(${newIndex + 1})`);
                            if (selectedElement) {
                                selectedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }
                    });
                }
            } else if (data.moduleType == 'HDICT') {
                let HDICTArr = []
                for (let i of data.HDICTList) {
                    if (i.type == 'houseType') {
                        HDICTArr = i.data.image
                    }
                }
                let newIndex = data.currentPreviewIndex;
                do {
                    newIndex = newIndex < HDICTArr.length - 1 ? newIndex + 1 : 0;
                } while (!HDICTArr[newIndex] && newIndex !== data.currentPreviewIndex);
                if (newIndex !== data.currentPreviewIndex) {
                    data.currentPreviewIndex = newIndex;
                    data.selectedPPTIndex = newIndex;
                    // 同步滚动右侧抽屉
                    nextTick(() => {
                        if (HDICTScrollRef.value) {
                            const selectedElement = HDICTScrollRef.value.querySelector(`.margin_b_20:nth-child(${newIndex + 1})`);
                            if (selectedElement) {
                                selectedElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
                            }
                        }
                    });
                }
            }
        }

        // 计算当前预览图片的URL
        const currentPreviewImage = computed(() => {
            console.log('data.HDICTList', data.HDICTList)
            if (data.moduleType == 'PPT') {
                return data.PPTList[data.currentPreviewIndex]?.img;
            } else if (data.moduleType == 'HDICT') {
                for (let i of data.HDICTList) {
                    if (i.type == 'houseType') {
                        return i.data.image[data.currentPreviewIndex]
                    }
                }
            }
        });

        // 计算当前预览图片的标题
        const currentPreviewTitle = computed(() => {
            return data.PPTList[data.currentPreviewIndex]?.title;
        });
        const addPPT = (arr) => {
            // console.log('添加图片', arr)
            for (let i of arr) {
                data.PPTList.push({
                    // title: '新图片',
                    bigTitle: i.bigTitle,
                    smallTitle: i.smallTitle,
                    img: i.img,
                });
            }
            setTimeout(() => {
                if (pptScrollRef.value) {
                    pptScrollRef.value.scrollTo({
                        top: pptScrollRef.value.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            }, 500)
        }
        const addPPTBigTitle = (bigTitle) => {
            data.PPTList.push({
                // title: '新图片',
                bigTitle: bigTitle,
            });
            setTimeout(() => {
                if (pptScrollRef.value) {
                    pptScrollRef.value.scrollTo({
                        top: pptScrollRef.value.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            }, 500)
        }
        const addPPTSmallTitle = (smallTitle) => {
            data.PPTList.push({
                // title: '新图片',
                smallTitle: smallTitle,
            });
            setTimeout(() => {
                if (pptScrollRef.value) {
                    pptScrollRef.value.scrollTo({
                        top: pptScrollRef.value.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            }, 500)
        }
        const openChatLoading = () => {
            // data.isChatLoading = true
            console.log('打开加载')
            data.showHistoryLoading = true
        }
        const closeChatLoading = () => {
            // data.isChatLoading = false
            console.log('关闭加载')
            data.showHistoryLoading = false
        }
        const openProductPackageDetail = (selectedItem) => {
            // 遍历所有产品包，关闭其他的详情
            data.productPackageList.forEach(item => {
                // 如果是当前选中的项目，切换其状态
                if (item === selectedItem) {
                    item.showSolutionList = !item.showSolutionList;
                } else {
                    // 其他项目都设置为关闭状态
                    item.showSolutionList = false;
                }
            });
        }
        // 展开右侧抽屉并展示产品包详情
        const showProductPackageDetailBox = (sceneList) => {
            data.moduleType = 'productPackage'
            console.log('data.isShowPPTOpen', data.isShowPPTOpen)
            let isDataChanged = false
            if (sceneList.length === data.productPackageList.length) {
                let allIdEqual = true
                for (let i = 0; i < sceneList.length; i++) {
                    if (sceneList[i].id !== data.productPackageList[i].id) {
                        allIdEqual = false
                        break
                    }
                }
                if (allIdEqual) {
                    isDataChanged = false
                } else {
                    isDataChanged = true
                }
            } else {
                isDataChanged = true
            }
            for (let i of sceneList) {
                i.showSolutionList = false
            }
            data.productPackageList = sceneList
            if (data.productPackageList.length == 1) {
                data.productPackageList[0].showSolutionList = true
            }
            if (data.isShowPPTOpen) {
                console.log('开着的')
                if (isDataChanged) {
                    // console.log('关闭')
                    contorlPPT('close')
                    setTimeout(() => {
                        contorlPPT('open')
                    }, 1000)
                }
            } else {
                console.log('关着的')
                contorlPPT('open')
            }
        }
        const closeProductPackageDetailBox = () => {
            if (data.isShowPPTOpen) {
                contorlPPT('close')
            }
            data.productPackageList = []
            data.showProductPackageDetail = false
        }
        // 切换右侧模块类型
        const changeRightModuleType = (type) => {
            if (type == data.moduleType) {
                console.log('相同', type)
            } else {
                console.log('不同', type)
                contorlPPT('close')
                console.log('data.isShowPPTOpen', data.isShowPPTOpen)
                setTimeout(() => {
                    data.moduleType = type
                    contorlPPT('open')
                }, 500);
                // if (data.isShowPPTOpen) {
                //     contorlPPT('close')
                //     setTimeout(() => {
                //         data.moduleType = type
                //         contorlPPT('open')
                //     }, 500);
                // } else {
                //     data.moduleType = type
                //     contorlPPT('open')
                // }
            }
        }
        // 控制是否正在添加图片
        const contorlAddingPic = (type) => {
            console.log('type', type)
            data.isAddingPic = type
            console.log('isAddingPicisAddingPicisAddingPictype', data.isAddingPic)

        }
        // 控制打开PPT预览抽屉
        const openPPTPreview = () => {
            contorlPPT('open')
        }
        // 控制关闭PPT预览抽屉
        const closePPTPreview = () => {
            contorlPPT('close')
        }
        const clearPPTList = () => {
            console.log('清除PPT', data.moduleType)
            if (data.moduleType == 'PPT') {
                data.PPTList = []
            } else if (data.moduleType == 'HDICT') {
                data.HDICTList = []
            }
            console.log('清除PPT', data.PPTList)

        }
        const updatePPT = (info) => {
            console.log('更新PPT', data.PPTList)
            // 找到匹配smallTitle的索引
            const startIndex = data.PPTList.findIndex(item => item.smallTitle === info.title);
            console.log('startIndex', startIndex)
            if (startIndex !== -1) {
                // 删除从startIndex开始的slideUrls.length个项
                data.PPTList.splice(startIndex, info.slideUrls.length + 1);
            }
        }
        const handleTextareaChange = () => {
            console.log("textareaRef.value", textareaRef.value);
            textareaRef.value.focus()
        }
        const showHDICTBox = (obj) => {
            console.log('★', obj)
            if (obj.type == 'houseType') {
                obj.data.image = obj.data.image.split(',')
            }
            console.log('obj', obj)
            data.HDICTList.push(obj)
            setTimeout(() => {
                if (HDICTScrollRef.value) {
                    HDICTScrollRef.value.scrollTo({
                        top: HDICTScrollRef.value.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            }, 200)
        }
        // 控制是否正在添加HDICT
        const contorlAddingHDICT = (type) => {
            console.log('type', type)
            data.isAddingHDICT = type
        }
        // 从历史记录来时候渲染HDICT的抽屉内容
        const addHDICTPics = (arr) => {
            data.HDICTList = []
            // console.log('添加图片', arr)
            for (let i of arr) {
                data.HDICTList.push(i);
            }
            setTimeout(() => {
                if (HDICTScrollRef.value) {
                    HDICTScrollRef.value.scrollTo({
                        top: HDICTScrollRef.value.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            }, 800)
        }
        const getChooseSolutionList = (arr) => {
            console.log('获取选择方案列表', arr)
            data.chooseSolutionList = arr
            setTimeout(() => {
                if (pptScrollRef.value) {
                    pptScrollRef.value.scrollTo({
                        top: pptScrollRef.value.scrollHeight,
                        behavior: 'smooth'
                    });
                }
            }, 500)
        }

        const backgroundStyles = () => {
            return {
                backgroundImage: `url(${data.backgroundImage})`, // 使用模板字符串来插入变量
                backgroundRepeat: "no-repeat",
                backgroundSize: "cover",
            };
        };
        const goChooseSolutionDetail = (item) => {
            console.log('查看详情', item)
            let href = window.location.origin + '/#/solveNew/detailNew?id=' + item.id
            window.open(href, '_blank')
        }
        const chooseThisSolution = (item) => {
            console.log('选择方案', chatMessageRef.value)
            for (let i of chatMessageRef.value) {
                if (i.solutionStatus == '选方案中') {
                    i.getPersonChooseSolutionData(item)
                }
            }
        }

        // 在methods中添加处理方法
        const handleShowThinkingSwitch = () => {
            // 将showThinkingSwitch设置为true
            data.showThinkingSwitch = true;
        }

        // 监听thinkingStatus的变化
        watch(() => data.thinkingStatus, (newVal, oldVal) => {
            // 每次切换时都将showThinkingSwitch设置为false
            data.showThinkingSwitch = false;
        })

        return {
            ...toRefs(data),
            router,
            route,
            contRef,
            chatMessageRef,
            textareaRef,
            handleTextareaChange,
            handleScroll,
            handleAudio,
            handleUserMessage,
            handleAiReplay,
            continueScroll,
            onUserScroll,
            writingOver,
            clearAISession,
            goBottom,
            checkOverflow,
            goBottom,
            checkOverflow,
            handleScroll,
            seekContent1,
            noGood,
            contorlNavigation,
            getHistoryChat,
            countDays,
            startNewChat,
            searchHistory,
            stopTalking,
            contorlPPT,
            deleteChatHis,
            openMicphoneModal,
            closeMicModal,
            startSpeaking,
            handleAudio,
            getVoiceWord,
            showPreview,
            prevImage,
            nextImage,
            currentPreviewImage,
            currentPreviewTitle,
            addPPT,
            pptScrollRef,
            openChatLoading,
            closeChatLoading,
            openProductPackageDetail,
            showProductPackageDetailBox,
            closeProductPackageDetailBox,
            changeRightModuleType,
            contorlAddingPic,
            openPPTPreview,
            closePPTPreview,
            clearPPTList,
            addPPTBigTitle,
            addPPTSmallTitle,
            updatePPT,
            showHDICTBox,
            HDICTScrollRef,
            contorlAddingHDICT,
            addHDICTPics,
            getChooseSolutionList,
            backgroundStyles,
            goChooseSolutionDetail,
            chooseThisSolution,
            handleShowThinkingSwitch,
        };
    },
});
</script>

<style lang="scss" scoped>
.home {
    // padding-top: 7vh;
    height: calc(100vh - 60px);
    background-image: url(../../../assets/images/AI/newHomeBg.png);
    background-size: cover;
    position: relative;
    z-index: -2;

    .showPPT {
        height: 100%;
        background-color: #F2F3FE;
        box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.24);
        // overflow: hidden;
        transition: 0.5s;

    }

    .showPPT-close {
        min-width: 64px;
    }

    .showPPT-open {
        min-width: 410px;
    }

    .PPTs {
        .PPTBigTitle {
            font-size: 18px;
            font-weight: bold;
            color: #24456A;

            .PPTBigTitleIcon {
                width: 10px;
                height: 10px;
                background-color: rgb(20, 94, 255);
            }
        }

        .PPTSmallTitle {
            font-size: 16px;
            font-weight: bold;
            color: #24456A;
            width: 320px;
            border-radius: 8px;

            .smallBorder {
                // width: 10px;
                flex: 1;
                height: 1px;
                border: 1px solid #24456A;
                margin: 4px;
            }
        }

        .PPTimg {
            width: 320px;
            height: 180px;
            border-radius: 10px;
            transition: all 0.3s ease;
            border: 2px solid transparent;
        }

        .margin_b_20 {
            &:hover {
                .PPTimg {
                    border: 3px solid rgba(20, 135, 230, 0.315);
                }
            }
        }

        .selected-ppt {
            .PPTimg {
                border: 3px solid rgba(20, 136, 230, 0.712);
            }
        }
    }

    .chooseSolutionDetail {
        padding: 20px 24px;
        height: calc(100vh - 60px - 40px - 32px - 40px);
        overflow-y: auto;

        .chooseSolutionItem {
            max-width: 350px;

            .imgBox {
                width: 100%;
                height: 182px;
                border-radius: 4px;
                overflow: hidden;

                .genericImgBox {
                    width: 100%;
                    height: 100%;
                    text-align: center;
                    position: relative;

                    .genericImgTitle {
                        font-weight: 700;
                        display: block;
                        color: #1f82c8;
                        position: absolute;
                        left: 50%;
                        top: 50%;
                        transform: translate(-50%, -50%);
                        font-size: 16px;
                    }
                }
            }

            .chooseSolutionItemTitle {
                font-size: 16px;
                font-weight: 500;
                color: #24456A;
                margin-top: 10px;
            }

            .chooseSolutionItemDescription {
                text-indent: 28px;
            }

            .btnBox {
                .btnItemDetail {
                    padding: 2px 12px;
                    border-radius: 4px;
                    background: #ECF4FD;
                    font-weight: 500;
                    font-size: 14px;
                    color: #0C70EB;
                    border: 1px solid #0C70EB;
                }

                .btnItemChoose {
                    padding: 2px 12px;
                    border-radius: 4px;
                    background: #ECF4FD;
                    font-weight: 500;
                    font-size: 14px;
                    background-color: #0C70EB;
                    color: #fff;
                }
            }
        }
    }

    .productPackageDetail {
        padding: 20px 24px;
        height: calc(100vh - 60px - 40px - 32px - 40px);
        overflow-y: auto;

        .eachProductPackageDetail {
            max-width: 350px;
            background-color: #fff;
            padding: 24px 24px 4px 24px;
            border-radius: 8px;
            box-shadow: 5px 8px 8px 0px rgba(131, 179, 223, 0.15);

            .imgBox {
                width: 100%;
                height: 182px;
                border-radius: 4px;
                overflow: hidden;
            }

            .productPackageTitle {
                font-size: 20px;
                font-weight: bold;
                color: #24456A;
            }

            .productPackageIntroduction {
                font-weight: 400;
                font-size: 14px;
                color: #0C213A;
            }

            .productPackageBtn {
                width: 100px;
                height: 32px;
                border-radius: 4px 4px 4px 4px;
                background: #ECF4FD;
                margin: 12px auto;

                .productPackageBtnIcon {
                    width: 12px;
                    height: 12px;
                }

                .productPackageBtnItem {
                    font-weight: 500;
                    font-size: 14px;
                    color: #0C70EB;
                }
            }

            .tab_content {
                text-align: center;
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 24px;

                .tit {
                    font-weight: bold;
                    font-size: 24px;
                    color: #24456A;
                    line-height: 28px;
                    display: inline-block;
                    margin-left: 24px;
                    margin-right: 24px;
                }

                img {
                    width: 33px;
                    height: 22px;
                }

                .left {
                    display: inline-block;
                    width: 24px;
                    height: 24px;
                    background-size: contain;
                    position: relative;
                    top: 3px;
                }

                .right {
                    display: inline-block;
                    width: 24px;
                    height: 24px;
                    background-size: contain;
                    position: relative;
                    top: 3px;
                }
            }

            .eachSolution {
                background: rgba(234, 239, 247, 0.2);
                box-shadow: 0px -8px 32px 0px #FFFFFF;
                border-radius: 4px 4px 4px 4px;
                padding: 16px;


                .eachSolutionTitle {
                    font-weight: bold;
                    font-size: 20px;
                    color: #2E3852;
                }

                .eachSolutionContent {
                    background: #FFFFFF;
                    border-radius: 8px 8px 8px 8px;
                    box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
                    padding: 16px;

                    .eachSolutionContentText {
                        text-indent: 32px;
                        font-weight: 400;
                        font-size: 16px;
                        color: #2E3852;
                    }

                    .line {
                        width: 242px;
                        height: 0px;
                        border: 1px solid #DAE2F5;
                    }

                    .productRecommend {
                        .productRecommendTitle {
                            font-weight: bold;
                            font-size: 18px;
                            color: #2E3852;
                        }

                        .productList {
                            .eachProduct {
                                background: linear-gradient(163deg, #F1F3F6 0%, #F6F7F9 38%, #FFFFFF 100%);
                                border: 2px solid #FFFFFF;
                                box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                                padding: 16px;

                                .productImgBox {
                                    width: 48px;
                                    height: 48px;
                                    border-radius: 4px;
                                }

                                .productTitle {
                                    font-weight: 500;
                                    font-size: 18px;
                                    color: #2E3852;
                                }
                            }
                        }
                    }
                }


            }
        }
    }

    .HDICTDetail {
        .eachHDICTDetail {
            .PPTBigTitle {
                font-size: 18px;
                font-weight: bold;
                color: #24456A;

                .PPTBigTitleIcon {
                    width: 10px;
                    height: 10px;
                    background-color: rgb(20, 94, 255);
                }
            }

            margin: 16px auto;
            max-width: 350px;
            background-color: #fff;
            padding: 24px 24px 4px 24px;
            border-radius: 8px;
            box-shadow: 5px 8px 8px 0px rgba(131, 179, 223, 0.15);

            .imgBox {
                width: 100%;
                height: 182px;
                border-radius: 4px;
                overflow: hidden;
            }

            .productPackageTitle {
                font-size: 20px;
                font-weight: bold;
                color: #24456A;
            }

            .productPackageIntroduction {
                font-weight: 400;
                font-size: 14px;
                color: #0C213A;
            }

            .tab_content {
                text-align: center;
                position: relative;
                display: flex;
                justify-content: center;
                align-items: center;
                margin-bottom: 24px;

                .tit {
                    font-weight: bold;
                    font-size: 24px;
                    color: #24456A;
                    line-height: 28px;
                    display: inline-block;
                    margin-left: 24px;
                    margin-right: 24px;
                }

                img {
                    width: 33px;
                    height: 22px;
                }

            }

            .eachSolution {
                background: rgba(234, 239, 247, 0.2);
                box-shadow: 0px -8px 32px 0px #FFFFFF;
                border-radius: 4px 4px 4px 4px;
                padding: 16px;


                .eachSolutionTitle {
                    font-weight: bold;
                    font-size: 20px;
                    color: #2E3852;
                }

                .eachSolutionContent {
                    background: #FFFFFF;
                    border-radius: 8px 8px 8px 8px;
                    box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
                    padding: 16px;

                    .eachSolutionContentText {
                        text-indent: 32px;
                        font-weight: 400;
                        font-size: 16px;
                        color: #2E3852;
                    }

                    .line {
                        width: 242px;
                        height: 0px;
                        border: 1px solid #DAE2F5;
                    }

                    .productRecommend {
                        .productRecommendTitle {
                            font-weight: bold;
                            font-size: 18px;
                            color: #2E3852;
                        }

                        .productList {
                            .eachProduct {
                                background: linear-gradient(163deg, #F1F3F6 0%, #F6F7F9 38%, #FFFFFF 100%);
                                border: 2px solid #FFFFFF;
                                box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                                padding: 16px;

                                .productImgBox {
                                    width: 48px;
                                    height: 48px;
                                    border-radius: 4px;
                                }

                                .productTitle {
                                    font-weight: 500;
                                    font-size: 18px;
                                    color: #2E3852;
                                }
                            }
                        }
                    }
                }


            }
        }
    }

    .navigation {
        height: 100%;
        background-color: #fff;
        box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.24);
        transition: min-width 0.3s ease;

        .newChat {
            height: 40px;
            background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
            border-radius: 4px 4px 4px 4px;
            font-weight: 500;
            font-size: 14px;
            color: #fff;
            transition: width 0.3s ease;
            overflow: hidden;
        }

        .width0 {
            width: 0px;
        }

        .width102 {
            width: 102px;
        }

        .allHistoryList {
            height: calc(100vh - 60px - 40px - 32px - 40px);
            padding: 0px 24px;
            overflow-y: auto;

            .title {
                font-weight: 500;
                color: rgba(0, 0, 0, 0.45);
                font-size: 14px;
                overflow: hidden;
            }

            .list {
                .eachChat {
                    font-weight: 400;
                    color: rgba(0, 0, 0, 0.85);
                    font-size: 14px;
                    font-family: Source Han Sans CN, Source Han Sans CN;
                    transition: all 0.3s ease;
                    width: 0;
                    position: relative;
                    border-radius: 4px;
                    padding: 4px 8px;

                    &:hover {
                        background-color: rgba(24, 144, 255, 0.1);

                        .delete-icon {
                            opacity: 1;
                        }
                    }

                    .delete-icon {
                        opacity: 0;
                        transition: opacity 0.3s ease;
                        cursor: pointer;
                        margin-right: 8px;
                    }
                }

                .width0 {
                    width: 0px;
                }

                .width220 {
                    width: 220px;
                }
            }
        }
    }

    .navigation-close {
        min-width: 64px;
    }

    .navigation-open {
        min-width: 220px;
    }


    .AISearch {
        flex: 1;
        min-width: 0;
        width: 100%;

        .chatArea {
            overflow-y: auto;
            width: 100%;
            flex: 1;
            /* 隐藏滚动条但保持滚动功能 */
            scrollbar-width: none;
            /* Firefox */
            -ms-overflow-style: none;
            /* IE and Edge */

            &::-webkit-scrollbar {
                display: none;
                /* Chrome, Safari and Opera */
            }

            .homeAIChat {
                max-width: 1200px;
                width: 100%;
                padding: 16px 20px;
                margin: auto;
                transition: none;

                .user-message {
                    text-align: right;
                }

                .ai-message {
                    text-align: left;
                }
            }
        }

        .thinking {
            ::v-deep(.ant-btn) {
                background: none;
                border: none;
            }
        }

    }


    .newSearchInfo {
        background-repeat: no-repeat;
        background-size: 100% 100%;
        max-width: 1050px;
        width: 100%;
        height: 150px;
        margin: 0 auto;
        position: relative;
        transition: none;

        .vocationPull {
            background: #ffffff;
            border-radius: 16px;
            overflow: hidden;
            padding-left: 10px;
            border: 1px solid #e5e5e5;
            box-shadow: 0px 5px 20px 1px #c9d6e4;
            width: 100%;
        }

        .inputClass {
            border: none;
            width: 98%;
            height: 70%;
            box-shadow: none !important;
        }

        .btns {
            width: 100%;
            height: 30%;
            justify-content: flex-end;
            padding-right: 20px;
            padding-bottom: 8px;
        }

        .goBottom {
            width: 40px;
            height: 40px;
            position: absolute;
            right: 40px;
            top: -140px;
        }

        .thinking {
            .thinkingBtnOpen {
                color: rgba(36, 69, 106, 0.65);
                font-weight: bold;
                font-size: 14px;
            }

            .thinkingBtnClose {
                color: #999;
                font-size: 14px;
            }
        }
    }

}



:deep(.ant-input-affix-wrapper .ant-input) {
    font-size: 18px;
    height: 100px !important;
}

:deep(.ant-input-textarea-clear-icon) {
    top: 15px;
}

.ant-modal-content {
    border-radius: 8px !important;
}

:deep(.ant-modal-content) {
    border-radius: 10px;
}

.loading-spinner {
    border: 4px solid rgba(0, 0, 0, 0.1);
    border-left-color: #007bff;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% {
        transform: rotate(0deg);
    }

    100% {
        transform: rotate(360deg);
    }
}

.custom-popover.ant-popover-inner {
    border-radius: 15px;
}

.selected-item {
    background-color: #e6f7ff;
    /* 浅蓝色背景 */
    cursor: pointer;
    /* 可选：改变鼠标指针为手型 */
}

/* 如果需要过渡效果 */
.margin_b_40.margin_t_32 {
    transition: background-color 0.3s ease;
}

.menu-icon {
    color: #999;
    font-size: 20px;
    padding: 0 8px;
    transition: all 0.3s ease;

    &:hover {
        color: #333;
    }
}

:deep(.ant-dropdown-menu) {
    padding: 8px;
    border-radius: 4px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

:deep(.ant-dropdown-menu-item) {
    display: flex;
    align-items: center;
    padding: 8px 16px;
    cursor: pointer;

    &:hover {
        background-color: #f5f5f5;
    }
}

.preview-modal {
    :deep(.ant-modal-content) {
        background: transparent;
        box-shadow: none;
    }
}

.preview-container {
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    height: 800px;
}

.preview-content {
    text-align: center;
}

.preview-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 8px;
}

.preview-title {
    margin-top: 16px;
    color: #fff;
    font-size: 16px;
}

.preview-arrow {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    width: 60px;
    height: 60px;
    cursor: pointer;
    display: flex;
    align-items: center;
    justify-content: center;
    background: rgba(255, 255, 255, 0.2);
    border-radius: 50%;
    transition: all 0.3s;

    &:hover {
        background: rgba(255, 255, 255, 0.4);
    }

    img {
        width: 24px;
        height: 24px;
    }
}

.left {
    left: 0;
}

.right {
    right: 0;
}

.HDICTProduct {

    .eachProduct {
        background: linear-gradient(163deg, #F1F3F6 0%, #F6F7F9 38%, #FFFFFF 100%);
        border: 2px solid #FFFFFF;
        box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
        padding: 16px;

        .productImgBox {
            width: 48px;
            height: 48px;
            border-radius: 4px;
        }

        .productTitle {
            font-weight: 500;
            font-size: 18px;
            color: #2E3852;
        }
    }



}
</style>