<template>
  <div class="demo-container">
    <div class="data-screen">
      <div class="item" v-for="(item, index) in items" :key="index">
        <p class="title">{{ item.title }}</p>
        <number-scroll :value="item.value" :duration="1000" />
      </div>
    </div>
    <a-button class="refresh-btn" @click="refreshNumbers">刷新数据</a-button>
  </div>
</template>

<script>
import { ref } from 'vue'
import NumberScroll from '@/components/NumberScroll.vue'

export default {
  components: {
    NumberScroll
  },
  setup() {
    const items = ref([
      { title: '总访问量', value: 1234 },
      { title: '当日访问', value: 423 },
      { title: '总用户数', value: 5678 },
      { title: '在线用户', value: 89 }
    ])
    
    const refreshNumbers = () => {
      items.value = items.value.map(item => ({
        ...item,
        value: Math.floor(Math.random() * 10000)
      }))
    }
    
    return {
      items,
      refreshNumbers
    }
  }
}
</script>

<style scoped>
.demo-container {
  padding: 20px;
}

.data-screen {
  display: flex;
  gap: 20px;
  margin-bottom: 20px;
}

.item {
  background-color: #f5f7fa;
  padding: 20px;
  border-radius: 8px;
  min-width: 200px;
  text-align: center;
}

.title {
  color: #666;
  margin-bottom: 10px;
}

.refresh-btn {
  background-color: #2D65F7;
  color: white;
}
</style> 