<template>
  <a-modal
    :visible="previewVisible"
    @cancel="closeModal"
    title="能力撰写申请"
    :width="700"
    :footer="null"
  >
    <div id="addAbility" class="background_fff">
      <div>
        <a-form
          :model="formData"
          labelAlign="right"
          :rules="rules"
          ref="computerInfoElem"
        >
          <a-row>
            <a-col :span="24">
              <a-form-item label="申请名称" name="applicationName">
                <a-input
                  v-model:value="formData.applicationName"
                  placeholder="请输入申请名称"
                >
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="商机名称" name="name">
                <a-input
                  v-model:value="formData.name"
                  placeholder="请输入商机名称"
                >
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="商机编号" name="code">
                <a-input
                  v-model:value="formData.code"
                  placeholder="请输入商机编号"
                >
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item
                label="所属行业"
                name="industrySelectedId"
                :rules="[{ required: true, message: '请选择所属行业' }]"
              >
                <a-cascader
                  :options="typeList"
                  :fieldNames="fieldNames"
                  v-model:value="formData.industrySelectedId"
                  placeholder="请选择所属行业"
                  @change="categoryChange"
                  show-search
                  tree-default-expand-all
                  change-on-select
                />
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="预估金额（万元)" name="estimatedAmount">
                <a-input
                  type="number"
                  v-model:value="formData.estimatedAmount"
                  placeholder="请输入预估金额（万元）"
                >
                </a-input>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item
                label="行业经理"
                name="industryManagerId"
                :rules="[{ required: true, message: '请选择行业经理' }]"
              >
                <a-select
                  placeholder="请选择行业经理"
                  v-model:value="formData.industryManagerId"
                >
                  <template v-for="(opt, index) in managersOption" :key="index">
                    <a-select-option :value="opt.id">
                      {{ opt.realName }}
                    </a-select-option>
                  </template>
                </a-select>
              </a-form-item>
            </a-col>
          </a-row>
          <a-row>
            <a-col :span="24">
              <a-form-item label="描述" name="remark">
                <a-textarea
                  :rows="4"
                  v-model:value="formData.remark"
                  placeholder="请输入描述(限300字)"
                >
                </a-textarea>
              </a-form-item>
            </a-col>
          </a-row>
        </a-form>
        <div class="flex just-center">
          <a-button style="margin-right: 20px" @click="closeModal"
            >取消</a-button
          >
          <a-button type="primary" @click="submit()" :loading="loading">
            <span>提交</span>
          </a-button>
        </div>
      </div>
    </div>
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { message } from "ant-design-vue";
import { useRouter, useRoute } from "vue-router";
import { userList, create } from "@/api/combine/combine.js";
import { industryList } from "@/api/solutionNew/home";

export default defineComponent({
  name: "addForm",
  components: {},
  emits: ["submit-ok", "submit-cancel"],
  props: {
    sourceIds: {
      type: Object,
      default: {},
    },
  },
  setup(props, { emit }) {
    const computerInfoElem = ref();
    const Router = useRouter();
    const Route = useRoute();
    const descriptionDesc = (rule, value) => {
      return new Promise((resolve, reject) => {
        if (value && value.replace(/\s/g, "").length > 300) {
          reject("能力撰写备注不得超过三百个字");
        } else {
          resolve();
        }
      });
    };

    const summaryDescMoney = (rule, value) => {
      return new Promise((resolve, reject) => {
        // 简化字符串处理逻辑
        const trimmedValue = value ? value.trim() : "";

        // 检查是否包含小数点，并据此判断是否可以以零开头
        const hasDecimalPoint = trimmedValue.includes(".");
        const startsWithZero = trimmedValue.startsWith("0");

        // 如果以零开头且不含小数点，则只允许输入为 "0"
        if (startsWithZero && !hasDecimalPoint && trimmedValue !== "0") {
          reject(new Error("金额不能以0开头"));
        } else {
          const numericValue = parseFloat(trimmedValue);
          if (numericValue <= 0 || numericValue > 100000000) {
            // 检查是否为非数字、小于等于0或大于100000000
            reject(new Error("请输入有效的金额（大于0且不超过100000000）"));
          } else {
            resolve(); // 所有验证通过
          }
        }
      });
    };
    const data = reactive({
      typeList: [],
      managersOption: [],
      previewVisible: true,
      fieldNames: { value: "id", label: "name" },
      formData: { industrySelectedId: [] },
      loading: false,
      rules: {
        applicationName: [
          { required: true, message: "请输入申请名称", trigger: "blur" },
          {
            pattern: /^.{1,100}$/,
            message: "申请名称不得超过一百个字",
            trigger: "blur",
          },
        ],

        estimatedAmount: [{ validator: summaryDescMoney, trigger: "blur" }],
        remark: [
          { required: true, message: "请输入描述", trigger: "blur" },
          {
            validator: descriptionDesc,
            trigger: "blur",
          },
        ],
      },
    });

    const getType = () => {
      industryList({ type: 1 })
        .then((res) => {
          data.typeList = res.data[0].children;
        })
        .catch((error) => {
          console.error("获取行业列表失败：", error);
        });
    };
    const getManagers = () => {
      userList({ roleKey: "industryManager" })
        .then((res) => {
          data.managersOption = res.data.rows;
        })
        .catch((error) => {
          console.error("获取行业经理列表失败：", error);
        });
    };
    getType();
    getManagers();

    const categoryChange = (val) => {
      data.formData.categoryId = val[val.length - 1];
    };

    const closeModal = () => {
      data.previewVisible = false;
      computerInfoElem.value.resetFields();
    };

    const submit = (val) => {
      computerInfoElem.value
        .validate()
        .then(() => {
          // 能力
          data.formData.abilityId = props.sourceIds
            .filter((item) => item.type === 1)
            .map((item) => item.sourceId).join(",")
          // 场景
          data.formData.solutionSceneId = props.sourceIds
            .filter((item) => item.type === 2)
            .map((item) => item.sourceId).join(",")
          data.loading = true;
          create(data.formData).then((res) => {
            data.loading = false;
            data.formData = {};
            closeModal();
            message.success("提交成功");
          }) .catch(() => {
          data.loading = false;
        });
        })
        .catch(() => {
          data.loading = false;
        });
    };

    return {
      ...toRefs(data),
      summaryDescMoney,
      categoryChange,
      descriptionDesc,
      Route,
      Router,
      computerInfoElem,
      closeModal,
      submit,
    };
  },
});
</script>

<style lang="scss" scoped>
:deep(.ant-form-item-label) {
  min-width: 120px;
}
</style>
