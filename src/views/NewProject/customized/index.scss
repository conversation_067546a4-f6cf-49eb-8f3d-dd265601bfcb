.AIBg {
    background-image: url("@/assets/images/AI/newAIbg.png");
}

.commonBg {
    background-image: url("@/assets/images/home/<USER>");
}

.totaoText {
    width: 1200px;
    margin: 20px auto 0;
    font-weight: bold;
    font-size: 28px;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    padding-bottom: 32px;

    // border-bottom: 1px solid #DAE2F5;
    span {
        cursor: pointer;
        margin-right: 96px;
        padding-bottom: 16px;
    }

    .activeBtn {
        color: #0C70EB;
        border-bottom: 3px solid #0C70EB;
        ;
    }
}


.ombine-btn {
    float: right;
}

.listContent {
    margin-top: 20px;
    padding: 0 24px;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
    border-radius: 8px 8px 8px 8px;

    .line {
        padding: 12px 0;
        border-bottom: 1px solid rgba(0, 6, 14, 0.08);
        :deep(.ant-btn) {
            height: 36px;
            border-radius: 4px;
            padding: 4px 24px;
            margin-left: 20px;
            border: none !important;
            font-weight: 500;
            font-size: 16px;
            color: #0C70EB;
            text-align: center;
            background: rgba(12, 112, 235, 0.08);
        }
    }

    .title {
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);

        .sub-count {
            display: inline-block;
            margin-left: 12px;
            font-weight: 400;
            font-size: 14px;
            color: #00060E;
        }
    }
}

.tabContent {
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    position: relative;

    .loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .cardContent {
        width: 100%;
        margin-bottom: 24px;
        position: relative;
    }

    .cardContent::-webkit-scrollbar {
        display: none;
    }

    .tab_text {
        font-size: 18px;
        color: #262626;
        cursor: pointer;
        height: 40px;
    }

    .activeTab {
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
        padding-top: 32px;
    }

    .topTab {
        margin-top: 0;
        background-image: url("@/assets/images/home/<USER>");
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
    }

    :deep(.ant-tag) {
        font-weight: 500;
        font-size: 12px;
        color: #2E7FFF;
        height: 20px;
        display: flex;
        align-items: center;
    }

    .card_total {
        // border-bottom: 1px solid #DAE2F5;
    }

    .card_content {
        // height: 163px;
        border-radius: 10px;
        margin-top: 24px;
        cursor: pointer;
        position: relative;
        background: #F5F7FC;
    }

    .newImg {
        position: absolute;
        top: 0px;
    }

    .cart-button {
        position: absolute;
        right: 10px;
        bottom: 10px;
        width: 80px;
        height: 26px;
        line-height: 26px;
        text-align: center;
        border: 1px solid red;
        color: red;
        ;
    }

    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }

    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 117px;
        margin-left: 12px;
        width: 80%
    }

    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;

        .card_tag {

            // display: flex;
            // align-items: center;
            .cardTag {
                display: inline-block;
                padding-left: 8px;
                padding-right: 8px;
                margin-right: 8px;
            }

            .author {
                margin-right: 40px;
            }
        }

        .card_title {
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            margin-bottom: 4px;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        :deep(.cityStyle) {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;

            .add-icon {
                width: 16px;
            }

            .add {
                font-weight: 500;
                font-size: 12px;
                color: #0C70EB;
            }
        }

        :deep(.viewStyle) {
            display: flex;
            align-items: center;
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;
            margin-right: 10px;

            .add-icon {
                width: 16px;
            }

            .add {
                font-weight: 500;
                font-size: 12px;
                color: #0C70EB;
            }
        }
    }

    .card_des {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
  			word-break: break-all;
    }

    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }
}

.buttons {
    padding: 24px;
    margin-top: 36px;
    background: #FFFFFF;
    box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
    border-radius: 8px 8px 8px 8px;

    .left {
        align-items: center;

        :deep(.ant-btn) {
            height: 36px;
            border-radius: 4px;
            padding: 4px 24px;
            margin-right: 20px;
            border: none !important;
            font-weight: 500;
            font-size: 16px;
            color: #0C70EB;
            text-align: center;
            background: rgba(12, 112, 235, 0.08);
        }

        .AIadvise {
            width: 226px;
            height: 59px;
            background-image: url(../../../assets/images/AIadvise.png);
            position: relative;
            cursor: pointer;

            .AItitle {
                font-weight: 600;
                font-size: 16px;
                color: #0C70EB;
                position: absolute;
                top: 18px;
                left: 66px;
            }
        }
    }

    .right {
        align-items: center;

        :deep(.ant-btn) {
            height: 36px;
            border-radius: 4px;
            padding: 4px 24px;
            margin-left: 20px;
            border: none !important;
            font-weight: 500;
            font-size: 16px;
            color: #0C70EB;
            text-align: center;
            background: rgba(12, 112, 235, 0.08);
        }

        .apply {
            background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
            color: #fff;
        }
    }
}

.emptyPhoto {
    margin: 0 auto;
    font-size: 16px;
    text-align: center;
    margin: 24px;

    .tip {
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
    }

    .select {
        color: #1E63FF;
        cursor: pointer;
    }
}

.btncontent {
    display: flex;
    width: 100%;
    justify-content: center;

    .btn {
        padding-left: 12px;
        padding-right: 12px;
        height: 32px;
        line-height: 32px;
        color: #0C70EB;
        background: #ECF4FD;
        margin-top: 24px;
        cursor: pointer;
    }
}
.line1{
    width: 100%;
    height: 2px;
    background-color: rgba(0,6,14,0.08);
    margin-top: -3px;
    margin-bottom: 12px;
}
.con_suggest {
    background: linear-gradient(180deg, #F5F7FC 0%, #F5F7FC 47%, #E1E8FD 100%);
    border-radius: 10px 10px 10px 10px;
    padding: 12px 33px 24px 76px;
    margin-top: -2px;
    border-top-left-radius: 0;
    border-top-right-radius: 0;
    .top_box {

        // margin-top: 12px;
        .left {
            font-weight: 400;
            font-size: 14px;
            color: rgba(0, 0, 0, 0.45);
            line-height: 16px;

            .key {
                font-weight: 500;
                font-size: 20px;
                color: #4249C5;
                line-height: 32px;
                margin-right: 16px;
            }
        }

        .right {
            display: flex;
            font-weight: 500;
            font-size: 12px;
            color: #0C70EB;
            line-height: 14px;

            img {
                width: 12px;
                height: 12px;
                margin-right: 6px;
            }

            .action {
                display: flex;
                align-items: center;
                cursor: pointer;
            }
        }
    }

    .cardList {
        display: flex;
        // justify-content: space-between;
        gap: 18px;
        width: 100%;
        padding-top: 10px;
        .card {
            // width: 319px;
            height: 102px;
            background: #FFFFFF;
            border-radius: 10px 10px 10px 10px;
            padding: 16px;
            display: flex;

            img {
                width: 112px;
                height: 70px;
                border-radius: 2px 2px 2px 2px;
                margin-right: 8px;
            }

            .card_right {
                font-weight: 400;
                font-size: 14px;
                color: rgba(0, 0, 0, 0.65);
                line-height: 16px;
                .name {
                    font-weight: 500;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.85);
                    line-height: 16px;
                    margin-bottom: 6px;
                }

                .desc {
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 184px;
                    display: inline-block;
                    margin-bottom: 8px;
                }

                .action {
                    display: flex;
                    align-items: center;
                    padding-left: 100px;

                    img {
                        width: 20px;
                        height: 20px;
                    }
                }
            }
        }
    }
}