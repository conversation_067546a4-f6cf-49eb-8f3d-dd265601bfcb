.AIBg {
  background-image: url("@/assets/images/AI/bac.png");
}
.newYearAIBG {
	background-image: url("@/assets/images/AI/newYearAIBG.png");
}

.commonBg {
    background-image: url("@/assets/images/home/<USER>");
}

.searchInfo {
    background-repeat: no-repeat;
    background-size: 100% 100%;
    width: 1200px;
    margin: 0 auto;
    padding: 24px 40px 24px 40px;
    transition: 1s;
    position: relative;

    .switch {
        margin-right: 16px;
        transition: 1s;
        position: absolute;
        left: 20px;
        top: 24px;
        z-index: 1000;

        .AIlogo {
            width: 60px;
            height: 60px;
            background-image: url(@/assets/images/AI/ai.png);
            background-size: 100% 100%;
        }
    }

    .vocationPull {
        background: #FFFFFF;
        // margin: 0 40px;
        display: flex;
        align-items: center;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }

    .inputClass {
        align-items: center;
        border: none;
        // min-width: 83%;
        height: 100%;
        box-shadow: none !important;
        padding-left: 60px;
        margin-left: 38px;
    }

    .sendbtn {
        position: absolute;
        top: 40px;
        right: 60px;
    }

    .line {
        width: 1px;
        height: 26px;
        background: #EFF0F4;
    }

    .lines {
        width: 1px;
        height: 56px;
        background: #EFF0F4;
    }

    .AIbtn {
        background: linear-gradient(108deg, #4446FF 0%, #4173FF 50%, #3EB8FF 100%);
    }

    .commonBtn {
        background-color: #0C70EB;
    }

    .changeContentBtn {
        background: linear-gradient(108deg, #8944FF 0%, #415AFF 50%, #5058FF 100%);
    }

    .commonChangeBtn {
        background-color: #6A4BF5;
    }

    .seekInfo {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 160px;
        height: 56px;
        cursor: pointer;

        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .customized {
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        width: 160px;
        height: 56px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-top-right-radius: 5px;
        border-bottom-right-radius: 5px;
    }

    .stage {
        margin-left: 44px;
        margin-top: 16px;
    }
}

.btn_box {
    width: 1200px;
    margin: 0 auto;
    display: flex;
    margin-top: 12px;

    .btn {
        display: flex;
        justify-content: center;
        align-items: center;
        background: #eef3fa;
        padding: 10px 21px;
        border-radius: 20px;
        font-weight: 500;
        font-size: 16px;
        color: #0c70eb;
        line-height: 16px;
        cursor: pointer;

        .pro {
            background-image: url("../../../assets/images/home/<USER>");
        }

        span {
            display: inline-block;
        }

        img {
            width: 26px;
            height: 26px;
            margin-right: 8px;
        }
    }

    // .pro {
    //   // background-image: url("../../../assets/images/home/<USER>");
    //   background-size: cover; 
    //   background-repeat: no-repeat;
    //   width: 185px;
    //   height: 90px;
    //   background-position: left top;

    // }
    // .solution {
    //   // background-image: url("../../../assets/images/home/<USER>");
    //   background-size: cover; 
    //   background-repeat: no-repeat;
    //   width: 185px;
    //   height: 90px;
    // }
}

.totaoText {
    width: 1200px;
    margin: 20px auto 0;
    font-weight: bold;
    font-size: 28px;
    color: rgba(0, 0, 0, 0.85);
    text-align: center;
    padding-bottom: 32px;

    // border-bottom: 1px solid #DAE2F5;
    span {
        cursor: pointer;
        margin-right: 96px;
        padding-bottom: 16px;
    }

    .activeBtn {
        color: #0C70EB;
        border-bottom: 3px solid #0C70EB;
        ;
    }
}

.loading {
    position: fixed;
    top: 0;
    left: 0;
    width: 100vw;
    height: 100vh;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: rgba(0, 0, 0, 0.5);
    /* 半透明遮罩 */
    z-index: 9999;

    #load {
        position: absolute;
        width: 600px;
        height: 36px;
        left: 50%;
        top: 40%;
        margin-left: -300px;
        overflow: visible;
        -webkit-user-select: none;
        -moz-user-select: none;
        -ms-user-select: none;
        user-select: none;
        cursor: default;
    }

    #load div {
        position: absolute;
        width: 20px;
        height: 36px;
        opacity: 0;
        font-family: Helvetica, Arial, sans-serif;
        animation: move 2s linear infinite;
        -o-animation: move 2s linear infinite;
        -moz-animation: move 2s linear infinite;
        -webkit-animation: move 2s linear infinite;
        transform: rotate(180deg);
        -o-transform: rotate(180deg);
        -moz-transform: rotate(180deg);
        -webkit-transform: rotate(180deg);
        color: #35C4F0;
    }

    #load div:nth-child(2) {
        animation-delay: 0.2s;
        -o-animation-delay: 0.2s;
        -moz-animation-delay: 0.2s;
        -webkit-animation-delay: 0.2s;
    }

    #load div:nth-child(3) {
        animation-delay: 0.4s;
        -o-animation-delay: 0.4s;
        -webkit-animation-delay: 0.4s;
        -webkit-animation-delay: 0.4s;
    }

    #load div:nth-child(4) {
        animation-delay: 0.6s;
        -o-animation-delay: 0.6s;
        -moz-animation-delay: 0.6s;
        -webkit-animation-delay: 0.6s;
    }

    #load div:nth-child(5) {
        animation-delay: 0.8s;
        -o-animation-delay: 0.8s;
        -moz-animation-delay: 0.8s;
        -webkit-animation-delay: 0.8s;
    }

    #load div:nth-child(6) {
        animation-delay: 1s;
        -o-animation-delay: 1s;
        -moz-animation-delay: 1s;
        -webkit-animation-delay: 1s;
    }

    #load div:nth-child(7) {
        animation-delay: 1.2s;
        -o-animation-delay: 1.2s;
        -moz-animation-delay: 1.2s;
        -webkit-animation-delay: 1.2s;
    }

    @keyframes move {
        0% {
            left: 0;
            opacity: 0;
        }

        35% {
            left: 41%;
            -moz-transform: rotate(0deg);
            -webkit-transform: rotate(0deg);
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
            opacity: 1;
        }

        65% {
            left: 59%;
            -moz-transform: rotate(0deg);
            -webkit-transform: rotate(0deg);
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
            opacity: 1;
        }

        100% {
            left: 100%;
            -moz-transform: rotate(-180deg);
            -webkit-transform: rotate(-180deg);
            -o-transform: rotate(-180deg);
            transform: rotate(-180deg);
            opacity: 0;
        }
    }

    @-moz-keyframes move {
        0% {
            left: 0;
            opacity: 0;
        }

        35% {
            left: 41%;
            -moz-transform: rotate(0deg);
            transform: rotate(0deg);
            opacity: 1;
        }

        65% {
            left: 59%;
            -moz-transform: rotate(0deg);
            transform: rotate(0deg);
            opacity: 1;
        }

        100% {
            left: 100%;
            -moz-transform: rotate(-180deg);
            transform: rotate(-180deg);
            opacity: 0;
        }
    }

    @-webkit-keyframes move {
        0% {
            left: 0;
            opacity: 0;
        }

        35% {
            left: 41%;
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            opacity: 1;
        }

        65% {
            left: 59%;
            -webkit-transform: rotate(0deg);
            transform: rotate(0deg);
            opacity: 1;
        }

        100% {
            left: 100%;
            -webkit-transform: rotate(-180deg);
            transform: rotate(-180deg);
            opacity: 0;
        }
    }

    @-o-keyframes move {
        0% {
            left: 0;
            opacity: 0;
        }

        35% {
            left: 41%;
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
            opacity: 1;
        }

        65% {
            left: 59%;
            -o-transform: rotate(0deg);
            transform: rotate(0deg);
            opacity: 1;
        }

        100% {
            left: 100%;
            -o-transform: rotate(-180deg);
            transform: rotate(-180deg);
            opacity: 0;
        }
    }
}

.newLoading {
    // margin-left: 348px;
    // height: 140px;
    margin-bottom: 16px;
    width: 1200px;
    margin: 0 auto;

    .chooseModal {
        width: 1200px;
        background: linear-gradient(180deg, #D3E4F6 0%, #FFFFFF 100%);
        margin-left: 8px;
        border-radius: 12px;
        padding-left: 24px;
        padding-top: 10px;
        padding-bottom: 14px;
        font-size: 16px;
        margin: 0 auto;
        .wordStyle{
            padding: 4px 24px 4px 24px;
            color: #4249C5;
            font-weight: 500;
            font-size: 16px;
            font-family: Source Han Sans CN, Source Han Sans CN;
            background-image: url(../../assets/images/AI/chooseBg.png);
            background-repeat: no-repeat;
            background-size: 100% 100%;
        }
    }
}

.suggest {
    margin-top: 24px;

    .title {
        font-weight: 500;
        font-size: 17px;
        color: #4249C5;
        line-height: 32px;
    }

    .card_list {
        margin-top: 20px;
        display: flex;
        gap: 28px;

        .card {
            background: linear-gradient(180deg, #E7F0FA 0%, #FFFFFF 100%);
            box-shadow: 0px 4px 16px 0px rgba(29, 86, 138, 0.15);
            border-radius: 8px 8px 8px 8px;
            padding: 24px 20px;
            display: flex;
            width: 355px;

            img {
                width: 120px;
                height: 80px;
                border-radius: 2px 2px 2px 2px;
                margin-right: 12px;
            }

            .right_con {
                .top {
                    display: flex;
                    justify-content: space-between;
                    margin-bottom: 11px;

                    .top_name {
                        font-weight: bold;
                        font-size: 16px;
                        color: rgba(0, 0, 0, 0.85);
                        line-height: 16px;
                        // width: 112px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        display: -webkit-box;
                        -webkit-box-orient: vertical;
                        -webkit-line-clamp: 1;
                    }

                }

                .top_pro {
                    font-weight: 400;
                    font-size: 12px;
                    color: rgba(0, 0, 0, 0.45);
                    line-height: 14px;
                    margin-bottom: 6px;
                    display: inline-block;
                    // width: 103px;
                }

                .desc {
                    font-weight: 400;
                    font-size: 14px;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 16px;
                    white-space: nowrap;
                    overflow: hidden;
                    text-overflow: ellipsis;
                    width: 184px;
                    margin-bottom: 20px;
                }

                .label {
                    font-weight: 400;
                    font-size: 12px;
                    color: rgba(0, 0, 0, 0.65);
                    line-height: 12px;
                    margin-bottom: 19px;

                    span {
                        padding: 3px 8px;
                        margin-right: 4px;
                        background: #FFFFFF;
                        border-radius: 2px 2px 2px 2px;
                        border: 1px solid rgba(0, 0, 0, 0.25);
                    }
                }

                .action {
                    font-weight: 500;
                    font-size: 12px;
                    color: #0C70EB;
                    line-height: 14px;
                    display: flex;
                    float: right;
                    align-items: center;
                    cursor: pointer;

                    img {
                        width: 20px;
                        height: 20px;
                    }
                }
            }
        }
    }

    .bootm {
        margin-top: 24px;
        width: 100%;
        display: flex;
        align-items: center;
        justify-content: center;

        .shou {
            background: #ECF4FD;
            border-radius: 4px 4px 4px 4px;
            padding: 5px 12px;
            margin-right: 16px;
            font-weight: 500;
            font-size: 14px;
            color: #0C70EB;
            line-height: 22px;
            display: flex;
            cursor: pointer;
            align-items: center;

            img {
                width: 12px;
                height: 10px;
                margin-right: 6px;
            }
        }
    }
}

.open {
    width: 100%;
    margin-top: 24px;
    //display: flex;
    //align-items: center;
    //justify-content: center;
    
    .title {
        font-weight: 500;
        font-size: 17px;
        color: #4249C5;
        line-height: 32px;
    }

    .con {
        width: 114px;
        margin: 0 auto;
        margin-top: 24px;
        padding: 5px 12px;
        background: #ECF4FD;
        border-radius: 4px 4px 4px 4px;
        font-weight: 500;
        font-size: 14px;
        color: #0C70EB;
        line-height: 22px;
        display: flex;
        align-items: center;
        cursor: pointer;

        img {
            width: 12px;
            height: 10px;
            margin-right: 8px;
        }

    }
}

:deep(.ant-input-affix-wrapper .ant-input) {
    font-size: 18px;
    min-height: 38px !important;
}

:deep(.ant-input-textarea-clear-icon) {
    top: 15px;
}