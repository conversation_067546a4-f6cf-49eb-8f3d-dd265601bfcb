.banner {
    background-image: url("@/assets/images/solution/detail/banner.png");
    background-size: cover;
    background-repeat: no-repeat;
    height: 340px;
    width: 100%;
    padding-top: 32px;
    padding-bottom: 32px;
    margin-bottom: 12px;
    display: flex;

    .top_card {
        width: 1200px;
        margin: 0px auto;
        height: 100%;
        display: flex;

        .left {
            width: 74%;
            position: relative;
            height: 260px;
            display: inline-block;

            .left_tit {
                position: absolute;

                p {
                    font-weight: bold;
                    font-size: 24px;
                    color: #2E3852;
                    line-height: 28px;
                    text-align: left;
                    display: inline-block;
                    margin-right: 8px;

                }

                .tag {
                    position: relative;
                    bottom: 3px;
                    background-color: #D7E4FB;
                    font-weight: 500;
                    font-size: 14px;
                    color: #2E7FFF;
                    line-height: 22px;
                    border: none;
                }
            }

            .left_middle {
                margin-top: 57px;
                position: absolute;
                width: 100%;

                p {
                    display: inline-block;
                    font-weight: 400;
                    font-size: 14px;
                    color: #84899A;
                    line-height: 22px;
                    text-align: left;
                    margin-right: 25px;
                }

                .info {
                    font-weight: 400;
                    font-size: 16px;
                    color: #2E3852;
                    line-height: 24px;
                    text-align: left;
                    margin-bottom: 12px;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                    overflow: hidden;
                    text-overflow: ellipsis;

                }

                .info_bottom {
                    // width: 80%;
                    // display: flex;
                    // justify-content: space-between;
                    font-weight: 300;
                    overflow: hidden;
                    text-overflow: ellipsis;

                    p {
                        color: #84899A;
                        // max-width: 160px;
                        overflow: hidden;
                        text-overflow: ellipsis;
                        white-space: nowrap;
                    }

                    .tips {
                        p {
                            display: flex;
                            justify-content: center;
                            align-items: center;
                        }
                    }

                    img {
                        width: 16px;
                        height: 16px;
                        margin-right: 3px;
                    }
                }
            }

            .left_bottom {
                position: absolute;
                bottom: 0;
                font-weight: 300;

                .bottom_tip {
                    p {
                        margin-left: 16px;

                    }
                }

                div {
                    display: inline-block;

                    p {
                        display: inline-block;
                        color: #84899A;
                    }

                    img {
                        display: inline-block;
                        margin-bottom: 3px;
                        margin-right: 4px;
                        width: 16px;
                        height: 16px;
                    }
                }

                .btn {
                    background-color: #1A66FB;
                    color: #E2ECFF;
                    width: 144px;
                    height: 40px;
                    border-radius: 4px 4px 4px 4px;
                    font-weight: bold;
                    font-size: 16px;
                    color: #FFFFFF;
                    line-height: 16px;
                }
            }
        }

        .right {
            display: inline-block;
            margin-left: 20px;
            width: 405px;
            height: 236px;

            img {
                width: 405px;
                height: 236px;
            }

        }
    }
}

.proList {
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    /* 控制显示的行数 */
    overflow: hidden;
    text-overflow: ellipsis;
    display: -webkit-box;
    flex: 1;
}

.ant-checkbox-group {
    display: flex !important;
    justify-content: center !important;
    margin-right: 12px !important;
    align-items: center !important;
}

.cards_c {
    .card_list {
        padding: 16px 22px;
        margin-bottom: 20px;
        background: #FFFFFF;
        box-shadow: 0px 4px 4px 0px rgba(176, 204, 230, 0.15);
        border-radius: 8px 8px 8px 8px;

        .card_title {
            font-weight: bold;
            font-size: 20px;
            color: #236CFF;
            line-height: 23px;
            padding-bottom: 16px;
            margin-bottom: 16px;
        }

        .card_desc {
            font-weight: 400;
            font-size: 16px;
            color: #2E3852;
            line-height: 28px;
            text-indent: 2em;
        }

        .line {
            height: 1px;
            background-color: #DAE2F5;
            margin-bottom: 24px;
        }

        .label_box {
            margin-top: 10px;
            margin-bottom: 23px;

            span {
                background: #D3DFF9;
                border-radius: 2px 2px 2px 2px;
                border: 1px solid #236CFF;
                font-weight: 400;
                font-size: 14px;
                color: #236CFF;
                line-height: 25px;
                padding: 2px 10px;
                margin-right: 8px;
            }
        }

        .list {
            display: flex;
            justify-content: start;
            flex-wrap: wrap;
            margin-right: 0;
            margin-left: 0;
            margin-top: 16px;
        }

        .tuijian {
            font-weight: bold;
            font-size: 18px;
            color: #2E3852;
            line-height: 21px;
        }

        .con_card {
            width: 558px;
            margin-right: 34px;
            background: linear-gradient(163deg, #F1F3F6 0%, #F6F7F9 38%, #FFFFFF 100%);
            box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
            border-radius: 0px 0px 0px 0px;
            border: 2px solid #FFFFFF;
            display: flex;
            padding: 16px 24px;
            font-weight: 400;
            font-size: 16px;
            color: #2E3852;
            line-height: 28px;
            margin-bottom: 16px;

            img {
                width: 48px;
                height: 48px;
                margin-right: 16px;
            }

            .con_right {
                width: 444px;
                display: flex;
                align-items: center;

                .con_top {
                    display: flex;
                    justify-content: space-between;

                    .price {
                        color: #F5222D;
                        font-size: 28px;
                    }

                    .name {
                        font-weight: bold;
                        font-size: 20px;
                        color: #2E3852;
                        line-height: 32px;
                        width: 102px;
                    }
                }
            }
        }

        .list .con_card:nth-child(2n) {
            margin-right: 0;
        }

        .con_con {
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
            /* 控制显示的行数 */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            line-height: 34px;
            height: 30px;
            width: 316px;
        }
    }
}

:deep(.el-select__popper) {
    width: 200px !important;
}

.add {
    background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
    border-radius: 4px 4px 4px 4px;
    font-weight: 500;
    font-size: 14px;
    color: #FFFFFF;
    line-height: 20px;
    text-align: center;
    padding: 6px 16px;
    width: 88px;
    margin-top: 24px;
}

.active {
    color: red;
}

.addCar {
    margin-top: 16px;

    button {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        border-radius: 4px 4px 4px 4px;
        padding: 6px 16px;
        font-weight: 500;
        font-size: 14px;
        color: #FFFFFF;
        line-height: 20px;
        border: none;
        cursor: pointer;
    }

    .disabled {
        background: #A6A6A6 !important;
        cursor: not-allowed;

    }
}

.white_bac {
    background: rgba(243, 247, 255, 0.6);
    box-shadow: 0px 2px 4px 0px rgba(77, 120, 170, 0.1);
    border-radius: 26px 26px 26px 26px;
    width: 240px;
    display: flex;
    justify-content: space-between;
    padding: 5px 16px;
    font-weight: 400;
    font-size: 12px;
    color: #0C213A;
    line-height: 22px;
}

.anchors {
    display: flex;
    box-shadow: 0px 4px 4px 0px rgba(77, 120, 170, 0.09);
    margin-bottom: 60px;
    justify-content: center;

    :deep(.ant-anchor-ink::before) {
        display: none;
    }

    :deep(.currentActive) {
        a {
            padding-bottom: 8px;
            border-bottom: 2px solid #236CFF;
        }
    }
}


:deep(.ant-anchor-ink) {
    height: 3px !important;
}

:deep(.ant-tabs-tab) {
    font-weight: bold;
    font-size: 20px;
    color: rgba(0, 0, 0, 0.85);
    line-height: 23px;
}

:deep(.ant-tabs-tab-active .ant-tabs-tab) {
    font-size: 20px;
    color: #2E7FFF;
    line-height: 23px;

    .ant-tabs-tab {
        font-weight: bold !important;
    }
}



:deep(.ant-anchor-link) {
    a {
        color: #262626 !important;
    }
}

:deep(.currentActive) {
    a {
        font-weight: bold !important;
        color: #236CFF !important;
    }
}

ul {
    list-style-type: disc;

    li {
        line-height: 20px;
    }
}

.tab_content {
    text-align: center;
    position: relative;
    display: flex;
    justify-content: center;
    align-items: center;

    .tit {
        font-weight: bold;
        font-size: 24px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 28px;
        display: inline-block;
        margin-left: 6px;
        margin-right: 6px;
    }

    img {
        width: 33px;
        height: 22px;
    }

    .left {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: contain;
        position: relative;
        top: 3px;
    }

    .right {
        display: inline-block;
        width: 24px;
        height: 24px;
        background-size: contain;
        position: relative;
        top: 3px;
    }
}

.content {
    width: 1200px;
    margin: 0 auto;
}

.card {
    box-shadow: none !important;
    padding-bottom: 56px;
    //display: flex;

    img {
        height: 280px;
        width: 420px;
        display: inline-block;
    }

    .card_content {

        display: flex;
        // flex: 1;
        width: 100%;

        //   卡片式
        .cards {
            display: flex;
            justify-content: start;
            flex-wrap: wrap;
            margin-top: 24px;

            & :nth-child(3n) {
                margin-right: 0 !important;
            }

            .item_card {
                width: 374px;
                margin-right: 39px;
                margin-bottom: 12px;
                height: 368px;
                background: linear-gradient(163deg, #F1F3F6 0%, #F6F7F9 38%, #FFFFFF 100%);
                box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                border-radius: 0px 0px 0px 0px;
                border: 2px solid #FFFFFF;

                p {
                    margin-bottom: 6px;
                    padding: 0 10px;
                }

                img {
                    width: 100%;
                    height: 200px;
                }

                .title {
                    margin-top: 12px;
                    font-weight: bold;
                    font-size: 20px;
                    color: #2E3852;
                }

                .desc {
                    font-weight: 400;
                    font-size: 16px;
                    color: rgba(46, 56, 82, 0.85);
                    -webkit-box-orient: vertical;
                    -webkit-line-clamp: 3;
                    /* 控制显示的行数 */
                    overflow: hidden;
                    text-overflow: ellipsis;
                    display: -webkit-box;
                }

                .productBody {
                    padding: 10px 20px;

                    .titleName {
                        font-size: 18px;
                        font-weight: 600;
                        line-height: 32px;
                        // text-decoration: underline;
                    }

                    .name {
                        font-size: 18px;
                        font-weight: 500;
                        display: flex;
                        line-height: 24px;

                        .nameTitle {
                            width: 90px;
                        }

                        .nameContent {
                            width: calc(100% - 90px);
                            font-size: 16px;
                            font-weight: 400;
                            color: rgba(46, 56, 82, 0.85);
                            overflow: hidden;
                            display: -webkit-box;
                            -webkit-line-clamp: 2;
                            -webkit-box-orient: vertical;
                            height: 48px;
                        }
                    }
                }
            }
        }

        .oneCards {
            display: flex;
            justify-content: start;
            margin-top: 24px;
        }

        // 文字描述
        .function {
            margin-top: 24px;
            flex-wrap: wrap;
            justify-content: space-between;

            .card_title {
                font-weight: bold;
                font-size: 18px;
                color: rgba(0, 0, 0, 0.85);
            }

            img {
                margin-right: 12px;
                width: 31px;
                height: 35px;
            }

            .card_list {
                background-image: url("@/assets/images/solution/detail/cardbg.png");
                background-size: cover;
                background-repeat: no-repeat;
                padding: 16px 32px;
                width: 49%;

                margin-bottom: 24px;
                overflow: hidden;

                .desc {
                    font-weight: 400;
                    font-size: 16px;
                    color: #2E3852;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                }
            }

            .cardAll_list {
                background-image: url("@/assets/images/scenario/bac.png");
                background-size: cover;
                background-repeat: no-repeat;
                padding: 40px 80px;
                width: 100%;
                overflow: hidden;
                .desc {
                    font-weight: 400;
                    font-size: 16px;
                    color: #2E3852;
                    overflow: hidden;
                    display: -webkit-box;
                    -webkit-line-clamp: 3;
                    -webkit-box-orient: vertical;
                }

                .top_key {
                    display: flex;
                    font-weight: bold;
                    font-size: 20px;
                    color: #236CFF;
                    line-height: 23px;
                    margin-bottom: 24px;
                    align-items: center;
                    img{
                        margin-right: 16px;
                        width: 40px;
                        height: 40px;
                    }
                }
            }
        }

        // 表格
        // 文字描述
        .table {
            margin-top: 24px;
            background: #FBFCFC;
            box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
            border-radius: 0px 0px 0px 0px;
            border: 1px solid #FFFFFF;
            padding: 16px 32px;

            .td {
                width: 85%;
                margin: 0 auto;
                margin-bottom: 16px;
                font-weight: 500;
                font-size: 20px;
                text-align: center;
                color: rgba(46, 56, 82, 0.65);
            }

            .tr {
                width: 85%;
                margin: 0 auto;
                margin-bottom: 12px;
                height: 50px;
                background: #F8FBFF;
                border-radius: 32px 32px 32px 32px;

                .tariff_index {
                    background: linear-gradient(180deg, #8DB7FF 0%, #65AEFF 100%);
                    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
                    border-radius: 32px 32px 32px 32px;
                    border: 1px solid #FFFFFF;
                    color: #fff;
                    font-weight: bold;
                    font-size: 22px;

                }

                .ant-col {
                    line-height: 50px;
                    text-align: center;
                }

                .name {
                    font-weight: 500;
                    font-size: 20px;
                    color: #2E3852;
                }

                .price {
                    font-weight: bold;
                    font-size: 20px;
                    color: #236CFF;
                }


            }

        }
    }

    .right {
        background: linear-gradient(163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
        box-shadow: -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
        font-weight: 400;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 32px;
        padding: 24px 48px;
        max-height: 280px;
        flex: 1;

        >div {
            // height: 100%;
            overflow: hidden;
            display: -webkit-box;
            -webkit-line-clamp: 7;
            -webkit-box-orient: vertical;
        }
    }
}

:deep(.ant-tabs-tab-active) {
    background: #E6EFFF !important;
    border-radius: 2px 2px 2px 2px;
    font-weight: 400 !important;
    color: #216AFB !important;
}

.lang {
    // padding: 40px 80px;
}

.tab_card {
    .right {
        background: linear-gradient(163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
        box-shadow: -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
        font-weight: 400;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.65);
        line-height: 32px;
        padding: 40px 54px;
        max-height: 280px;
        overflow-y: auto;
    }
}

:deep(.ant-tabs-bar) {
    margin-bottom: 40px;
}

.bottom {
    height: 80px;
    width: 100%;
    // background: linear-gradient(90deg, #0142FD 0%, #2475F9 100%);
}

*::-webkit-scrollbar-vertical {
    width: 101px;
}

/* 纵向滚动条轨道样式 */
:deep(::-webkit-scrollbar-track) {
    // background: red !important;
}


.top {
    position: fixed;
    bottom: 2%;
    left: 92%;
    cursor: pointer;
    width: 91px;
    height: 91px;
}

.info_card {
    background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
    box-shadow: 0px 4px 24px 0px #EAEDF3;
    border-radius: 10px 10px 10px 10px;
    border: 2px solid #FFFFFF;
    margin-left: 6.5%;
    margin-right: 6.5%;
    margin-top: 32px;
    margin-bottom: 56px;

    .content {
        background: linear-gradient(196deg, #EAEFF7 0%, rgba(234, 239, 247, 0.41) 100%);
        box-shadow: 0px -8px 32px 0px #FFFFFF, inset 0px 8px 24px 0px #DFE4ED;
        border-radius: 4px 4px 4px 4px;
        opacity: 0.8;
        margin: 32px 40px;
        padding-left: 32px;
        padding-top: 16px;
        padding-bottom: 1px;

        .line {
            width: 100%;
            // margin-bottom: 16px;

            p {
                display: inline-block;
                font-weight: 500;
                font-size: 14px;
                color: #2E3852;
                line-height: 28px;
                text-align: left;
                // margin-left: 25%;
            }

            span {
                font-weight: bold;
            }
        }

        .desc {
            font-weight: 400;
        }
    }
}

:deep(.fixed) {
    position: static !important;
}

:deep(.ant-anchor) {
    padding-left: 48px;

    .ant-anchor-link {
        font-weight: 500;
        font-size: 20px;
        color: rgba(0, 0, 0, 0.85);
        line-height: 23px;
        margin-right: 48px;
        padding-bottom: 20px;
    }
}

.top_nav {
    padding-left: 120px;
    height: 60px;
    background-color: #f5f7fc;
    width: 100%;
    margin-top: 8px;
    padding-right: 70px;
    display: flex;
    justify-content: space-between;
    position: fixed;
    top: 50px;
    z-index: 21;
    padding-top: 20px;

    div {
        display: inline-block;
    }

    .left_nav {
        padding-bottom: 8px;

        .title {
            font-weight: 400;
            font-size: 12px;
            color: #84899A;
            line-height: 20px;
            margin-right: 8px;
            cursor: pointer;
        }

        .current {
            font-weight: 400;
            font-size: 12px;
            color: #2E3852;
            line-height: 20px;
        }
    }

    .right_nav {
        color: #2E7FFF;
        cursor: pointer;
    }
}

.list {
    list-style-type: none;
    margin-top: 32px;
    // margin-bottom: 32px;
    margin-left: 82px;
    margin-right: 120px;

    li {
        width: 49%;
        display: inline-block;
        background: linear-gradient(180deg, #EDF0F9 0%, #FEFEFF 100%);
        box-shadow: 0px 4px 24px 0px #EAEDF3;
        border-radius: 10px 10px 10px 10px;
        border: 2px solid #FFFFFF;
        padding: 4px 24px 4px 24px;
        margin-bottom: 24px;

        img {
            display: inline-block;
            width: 72px;
            height: 72px;
        }

        p {
            display: inline-block;
            font-weight: 500;
            font-size: 16px;
            color: #2E3852;
            line-height: 28px;
        }

        .left_box {
            display: flex;
            padding-top: 19px;
        }
    }

    .li_box {
        display: flex;
        justify-content: space-between;
        align-items: center;
    }

    li:nth-of-type(odd) {
        margin-right: 24px;
    }

    .fileText {
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
}

.applyCard {
    :deep(.ant-tabs-tab) {
        font-weight: 500;
        font-size: 20px;
        line-height: 23px;
        // width: 100%;
    }

    :deep(.ant-tabs-nav-list) {
        display: flex;
        justify-content: space-between;
    }

    :deep(.ant-tabs-nav-container-scrolling) {
        padding: 0;
    }

    :deep(.ant-tabs-tab-prev) {
        display: none;
    }

    :deep(.ant-tabs-tab-next) {
        display: none;
    }

    :deep(.ant-tabs-nav) {
        width: 100%;
    }

    :deep(.ant-tabs-nav>div:first-child) {
        // display: flex;
        // justify-content: space-between;
    }

    :deep(.ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab) {
        font-weight: bold;
        font-size: 20px;
        color: #2E7FFF;
        line-height: 23px;
    }

    :deep(.ant-tabs-tab-active) {
        font-weight: bold;
        font-size: 20px;
        color: #2E7FFF;
        line-height: 23px;
    }

    :deep(.ant-tabs-content-nav-operations) {
        display: none;
    }

    :deep(.ant-tabs-nav-operations) {
        display: none !important;

    }

    :deep(.ant-tabs-tab) {
        // flex-grow: 1;
        text-align: center;
        color: #4D6886 !important;
        // border: 1px solid #C5D2E4 !important;
        margin-right: 16px !important;
        // padding: 0 16px !important;
    }

    :deep(.ant-tabs-tab:last-child) {
        margin-right: 0 !important;
    }

    :deep(.ant-tabs-tab-active) {
        color: #3A7AFC !important;
    }
}

:deep(.ant-breadcrumb) {
    li {
        cursor: pointer;

    }
}

.reset_btn {
    background-color: #FFFFFF;
    color: #2E3852;
    margin-right: 24px;
    border: 1px solid #C1CCE5;
    box-shadow: none;
}

.submit_btn {
    background-color: #1A66FB;
    color: #FFFFFF;
}

.btn_box {
    margin-top: 60px;
}

:deep(.ant-tabs-nav-list) {
    width: 100%;
}



:deep(.ant-tabs-tabpane) {
    // background-color: #F5F7FC;
    // margin-top: 32px;
    // height: 220px;
    display: flex;
}

:deep(.ant-tabs-ink-bar) {
    margin-top: 16px;
    height: 3px !important;
}

:deep(.ant-tabs-nav) {
    height: 60px;
    margin: 0;
}

.basic {
    padding: 24px 36px;
    background: linear-gradient(163deg, #F2F5F8 0%, #F6F7F9 38%, #FFFFFF 100%);
    box-shadow: 8px 8px 12px 0px rgba(0, 0, 0, 0.04), -8px -8px 12px 0px rgba(255, 255, 255, 0.5);
    border-radius: 0px 0px 0px 0px;
    border: 1px solid #FFFFFF;

    .basicInside {
        padding: 24px 36px;
        background: rgba(234, 239, 247, 0.1);
        box-shadow: 0px -8px 32px 0px #FFFFFF, inset 0px 8px 24px 0px rgba(223, 228, 237, 0.5);
        border-radius: 4px 4px 4px 4px;
    }

    .card_title {
        font-weight: bold;
        font-size: 18px;
        color: rgba(0, 0, 0, 0.85);
    }

    .server_time {
        font-weight: bold;
        font-size: 18px;
        color: #236CFF;
    }
}