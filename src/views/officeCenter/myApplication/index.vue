<template>
    <div class="qilin-myApplication" v-if="currentPage === 'table' ">
        <div class="qilin-myApplication-header">
            <QilinSearch v-model:searchConfig="searchConfig" ref="searchConfigRef"></QilinSearch>
        </div>
        <div class="qilin-myApplication-content">
            <QilinTable v-model:tableConfig="tableConfig" @changeCurrentPage="changeCurrentPage"
                @changeCurrentSize="changeCurrentSize" @rowClickData="clickRowData"
            >
                <template v-slot:procDefName="slotData">
                    <div v-html="getProcDefName(slotData.data.scope.row.procDefName)"></div>
                </template>
                <template v-slot:customDeptId="slotData">
                    <span>{{ getDeptName(slotData.data.scope.row.customDeptId) }}</span>
                </template>
                <template v-slot:process="slotData">
                    <span class="status-dot" :class="
                        getProcess(slotData.data.scope.row) === '已完成' ? 'success' :
                        getProcess(slotData.data.scope.row) === '驳回' ? 'reject' : 'normal'
                    ">{{getProcess(slotData.data.scope.row)}}</span>
                </template>
            </QilinTable>
        </div>
    </div>
    <ProcessApproval v-else :processInfo="processInfo" @showTable="showTable"></ProcessApproval>
</template>

<script setup>
import {
    getMyApplicationData
} from "@/api/officeCenter/myApplication/index.js";
import { getDeptInfo } from "@/utils/common.js";
import {useRoute,useRouter} from "vue-router";
import ProcessApproval from "../processApproval/index.vue";
const route=useRoute();
const router=useRouter();

/*
    数据变量定义区域
*/
// 获取搜索表单元素DOM
const searchConfigRef=ref(null);
// 当前显示页面类型
let currentPage=ref("table"); //默认为表格类型
// 行数据流程信息
let processInfo=reactive({});


/*
    计算属性等代码区域
*/
// 获取申请类型方法
const getProcDefName=computed(()=>{
    return (name)=>{
        // return `<b>【${name.slice(0,2)}】</b>申请`;
        return `<b>【${name}】</b>申请`;
    };
});
// 获取部门名称方法
const getDeptName = computed(() => {
    return (id) => {
        return getDeptInfo(id) || "--";
    };
});
// 获取申请进度方法
const getProcess=computed(()=>{
    return (row)=>{
        let str="";
        if(row.assigneeName === null){
            str="已完成";
        }else if(row.assigneeName === null && !row.formDataEdit){
            str="驳回";
        }else{
            str=row.taskName+`【${row.assigneeName}】`;
        };
        return str;
    };
});


/*
    逻辑脚本代码区域
*/
// 点击查询按钮事件监听
const searchData=()=>{
    getTableData();
};
// 点击重置按钮事件监听
const resetData=(formRef)=>{
    formRef.resetSearchData();
    tableConfig.paginationsObj.pageSize=10;
    tableConfig.paginationsObj.currentPage=1;
    getTableData();
};
// 点击表格行事件监听
const clickRowData=(row)=>{
    currentPage.value="detail";
    processInfo={...row,routeName:"myApplication"};
};
// 子组件返回按钮触发父组件事件监听
const showTable=()=>{
    router.push({ //去除地址栏由首页携带来的参数
        name:"myApplication"
    });
    currentPage.value="table";
    getTableData();
};
// 切换页码事件监听
const changeCurrentPage=(page)=>{
    tableConfig.paginationsObj.currentPage=page;
    getTableData();
};
// 切换页码事件监听
const changeCurrentSize=(size)=>{
    tableConfig.paginationsObj.pageSize=size;
    getTableData();
};
// 初始化获取表格数据
const getTableData=()=>{
    let params={
        pageNum:tableConfig.paginationsObj.currentPage,
        pageSize:tableConfig.paginationsObj.pageSize,
        // category:109
    };
    Object.keys(searchConfig.submitData).forEach((item)=>{
        if(searchConfig.submitData[item]){
            params[item]=searchConfig.submitData[item];
        };
    });
    if(params.date && params.date.length !== 0){
        params["params[beginTime]"]=params.date[0]+" 00:00:00";
        params["params[endTime]"]=params.date[1]+" 23:59:59";
        Reflect.deleteProperty(params,"date");
    };
    getMyApplicationData(params).then((res)=>{
        if(res.code === 200){
            tableConfig.tableData=res.rows;
            tableConfig.paginationsObj.total=res.total;
        }else if(res.code !== 401){
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};

// 搜索表单配置项
const searchConfig=reactive({
    itemConfig:[
        {
            labelName:"申请类型",
            dataName:"processName",
            type:"select",
            dataListName:"processNameList",
        },
        {
            labelName:"审批状态",
            dataName:"state",
            type:"select",
            dataListName:"stateList"
        },
        {
            labelName:"时间",
            dataName:"date",
            type:"date",
            dateType:"daterange",
            format:"YYYY-MM-DD",
            valueFormat:"YYYY-MM-DD"
        }
    ],
    selectLists:{
        processNameList:[
            {
                label:"请假申请",
                value:"请假申请"
            },
            {
                label:"加班申请",
                value:"加班申请"
            },
            {
                label:"出差申请",
                value:"出差申请"
            },
            {
                label:"报销申请",
                value:"报销申请"
            },
            {
                label:"费用申请",
                value:"费用申请"
            },
            {
                label:"销假申请",
                value:"销假申请"
            }
        ],
        stateList:[
            {
                label:"已完成",
                value:"finished"
            },
            {
                label:"进行中",
                value:"unfinished"
            }
        ]
    },
    buttonConfig:[
        {
            btnName:"查询",
            type:"primary",
            size:"default",
            formRefName:searchConfigRef,
            click:searchData
        },
        {
            btnName:"重置",
            type:"default",
            size:"default",
            formRefName:searchConfigRef,
            click:resetData
        }
    ],
    submitData:{
        processName:"",
        state:"",
        date:""
    }
});
// 表格配置项
const tableConfig=reactive({
    elTableConfig:{
        border:true
    },
    headerConfig:[
        {
            label:"申请类型",
            prop:"procDefName",
            type:"slot",
            slotName:"procDefName",
            align:"center",
            // width:120
        },
        {
            label:"申请人",
            prop:"startUserName",
            type:"text",
            align:"center",
            width:100
        },
        {
            label:"部门",
            prop:"customDeptId",
            type:"slot",
            slotName:"customDeptId",
            align:"center",
            width:150
        },
        {
            label:"申请进度",
            prop:"process",
            type:"slot",
            slotName:"process",
            align:"center",
            ellipsis:true
        },
        {
            label:"申请时间",
            prop:"createTime",
            type:"text",
            align:"center",
            width:180
        }
    ],
    tableData:[],
    paginationsObj:{
        currentPage:1, //当前页
        pageSize:10, //当前每页数量
        pageSizes:[10,20,30], //每页显示多少条选项集
        total:0, //表格总数量
        layout:"total,sizes,prev,pager,next,jumper"
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    getTableData();
    if(route.query.data){ //当从首页待办审批跳转进入时
        currentPage.value="detail";
        processInfo={...JSON.parse(route.query.data)};
    };
});

</script>

<style lang="scss" scoped>
.qilin-myApplication{
    width:100%;
    height:100%;
    display:flex;
    flex-flow:column nowrap;
    >.qilin-myApplication-header{
        background-color:#fff;
        padding:24px 20px 6px;
        border-radius:4px;
    }
    >.qilin-myApplication-content{
        margin-top:16px;
        background-color:#fff;
        padding:16px 20px;
        border-radius:4px;
        flex:1;
        display:flex;
        flex-flow:column nowrap;
        overflow:hidden;
        >.qilin-myApplication-content-header{
            display:flex;
            align-items:center;
            justify-content:flex-end;
            margin-bottom:14px;
            >span{
                font-size:16px;
                color:rgba(0,0,0,0.85);
            }
            >.qilin-myApplication-content-header-btn{
                >.el-button:nth-child(1){
                    background: rgba(86, 206, 163, 0.14);
                    color: #56cea3;
                    border: 1px solid #56cea3;
                }
                >.el-button:nth-child(2){
                    background: rgba(250, 173, 20, 0.15);
                    border: 1px solid #faad14;
                    color: #faad14;
                }
                >.el-button:nth-child(3){
                    background: rgba(20,140,250,0.15);
                    border: 1px solid #148CFA;
                    color:#148CFA;
                }
            }
        }
        .slot-box>span.status-dot{
            position:relative;
            &::before{
                content:"";
                position:absolute;
                left:-15px;
                top:50%;
                transform:translateY(-50%);
                width:8px;
                height:8px;
                border-radius:50%;
                margin-right:5px;
            }
            &.success::before{
                background-color:$color-text-success;
            }
            &.reject::before{
                background-color:$color-text-error;
            }
            &.normal::before{
                background-color:$color-text-info;
            }
        }
    }
}
</style>
