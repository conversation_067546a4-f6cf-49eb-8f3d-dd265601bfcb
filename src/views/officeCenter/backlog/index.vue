<template>
    <div class="qilin-backlog" v-if="currentPage === 'table' ">
        <div class="qilin-backlog-header">
            <QilinSearch v-model:searchConfig="searchConfig" ref="searchConfigRef"></QilinSearch>
        </div>
        <div class="qilin-backlog-content">
            <div class="qilin-backlog-content-header">
                <div class="qilin-backlog-content-header-btn">
                    <el-button icon="Stamp" @click="approvalMoreData()">审批</el-button>
                </div>
            </div>
            <QilinTable v-model:tableConfig="tableConfig" @changeCurrentPage="changeCurrentPage"
                @changeCurrentSize="changeCurrentSize" @rowClickData="clickRowData"
                @selectionChangeData="selectionChangeData"
            >
                <template v-slot:procDefName="slotData">
                    <div v-html="getProcDefName(slotData.data.scope.row.procDefName)"></div>
                </template>
                <template v-slot:customDeptId="slotData">
                    <span>{{ getDeptName(slotData.data.scope.row.customDeptId) }}</span>
                </template>
            </QilinTable>
        </div>
    </div>
    <ProcessApproval v-else :processInfo="processInfo" @showTable="showTable"></ProcessApproval>
    <QilinDialog v-model:dialogConfig="approvalDialogConfig" @handleClose="handleApprovalDialogConfig"
        @submitConfirm="submitApprovalDialogConfig"
    >
        <QilinForm v-model:formConfig="approvalFormConfig" ref="approvalFormConfigRef"></QilinForm>
    </QilinDialog>
</template>

<script setup>
import {
    getBacklogData,
    approvalMoreBacklogData
} from "@/api/officeCenter/backlog/index.js";
import {
    getApprovalRejectId,
    approvalReject
} from "@/api/officeCenter/processApproval/index.js";
import { getDeptInfo } from "@/utils/common.js";
import {useRoute,useRouter} from "vue-router";
import ProcessApproval from "../processApproval/index.vue";
const route=useRoute();
const router=useRouter();
import { login } from "@/store/login.js";
const loginStore=login();

/*
    数据变量定义区域
*/
// 获取搜索表单元素DOM
const searchConfigRef=ref(null);
// 当前显示页面类型
let currentPage=ref("table"); //默认为表格类型
// 行数据流程信息
let processInfo=reactive({});
// 审批选中的数据集
let selectedData=ref([]);
// 获取审批意见弹窗表单元素DOM
const approvalFormConfigRef=ref(null);


/*
    计算属性等代码区域
*/
// 获取申请类型方法
const getProcDefName=computed(()=>{
    return (name)=>{
        // return `<b>【${name.slice(0,2)}】</b>申请`;
        return `<b>【${name}】</b>申请`;
    };
});
// 获取部门名称方法
const getDeptName = computed(() => {
    return (id) => {
        return id;
    };
});


/*
    逻辑脚本代码区域
*/
// 点击查询按钮事件监听
const searchData=()=>{
    getTableData();
};
// 点击重置按钮事件监听
const resetData=(formRef)=>{
    formRef.resetSearchData();
    tableConfig.paginationsObj.pageSize=10;
    tableConfig.paginationsObj.currentPage=1;
    getTableData();
};
// 点击审批按钮事件监听
const approvalMoreData=()=>{
    if(selectedData.value.length === 0){
        ElMessage({
            message:"请先选择需要审批的数据",
            type:"warning"
        });
    }else{
        // 不可审批当前登录人自身的待办
        if(selectedData.value.some((item)=>{
            return item.startUserId == loginStore.userInfo.user.userId;
        })){
            ElMessage({
                message:"不可审批当前登录人自身的待办申请",
                type:"warning"
            });
        }else{
            if(selectedData.value.length > 1){
                approvalFormConfig.selectLists.chooseList[1].isHide=true;
            }else{
                approvalFormConfig.selectLists.chooseList[1].isHide=false;
            };
            approvalFormConfig.submitData.choose = "同意";
            approvalDialogConfig.isShow=true;
        };
    };
};
// 关闭审批意见弹窗事件监听
const handleApprovalDialogConfig=()=>{
    approvalFormConfigRef.value.formResetFields();
    approvalDialogConfig.isShow=false;
};
// 点击审批意见弹窗确认按钮事件监听
const submitApprovalDialogConfig=()=>{
    approvalFormConfigRef.value.formValidate((valid)=>{
        if(valid){
            // console.log(approvalFormConfig.submitData);
            if(approvalFormConfig.submitData.choose === "同意"){
                // console.log(selectedData.value);return;
                approvalMoreBacklogData(selectedData.value.map((item)=>{
                    return {
                        comment:approvalFormConfig.submitData.comment ? approvalFormConfig.submitData.comment : "同意",
                        procInsId:item.procInsId,
                        taskId:item.taskId
                    }
                })).then((res)=>{
                    if(res.code === 200){
                        handleApprovalDialogConfig();
                        getTableData();
                        ElMessage({
                            message:"审批成功",
                            type:"success"
                        });
                    }else{
                        ElMessage({
                            message:res.msg || "系统错误",
                            type:"error"
                        });
                    };
                })
            }else if(approvalFormConfig.submitData.choose === "不同意"){
                // console.log(selectedData.value);return;
                getApprovalRejectId({
                    procInsId:selectedData.value[0].procInsId,
                    taskId:selectedData.value[0].taskId
                }).then((res)=>{
                    if(res.code === 200){
                        res.data.forEach((item)=>{
                            if(item.name === "提交申请"){
                                approvalReject({
                                    comment:approvalFormConfig.submitData.comment ? approvalFormConfig.submitData.comment : "不同意",
                                    procInsId:selectedData.value[0].procInsId,
                                    taskId:selectedData.value[0].taskId,
                                    targetKey:item.id
                                }).then((res1)=>{
                                    if(res1.code === 200){
                                        handleApprovalDialogConfig();
                                        getTableData();
                                        ElMessage({
                                            message:"驳回成功",
                                            type:"success"
                                        });
                                    }else{
                                        ElMessage({
                                            message:res1.msg || "系统错误",
                                            type:"error"
                                        });
                                    };
                                });
                            };
                        });
                    }else{
                        ElMessage({
                            message:res.msg || "系统错误",
                            type:"error"
                        });
                    };
                });
            };
        };
    });
};
// 选中行数据复选框事件监听
const selectionChangeData=(selection)=>{
    // console.log(selection);
    selectedData.value=selection;
};
// 点击表格行事件监听
const clickRowData=(row)=>{
    currentPage.value="detail";
    processInfo={...row,routeName:"backlog"};
};
// 子组件返回按钮触发父组件事件监听
const showTable=()=>{
    router.push({ //去除地址栏由首页携带来的参数
        name:"backlog"
    });
    currentPage.value="table";
    getTableData();
};
// 切换页码事件监听
const changeCurrentPage=(page)=>{
    tableConfig.paginationsObj.currentPage=page;
    getTableData();
};
// 切换页码事件监听
const changeCurrentSize=(size)=>{
    tableConfig.paginationsObj.pageSize=size;
    getTableData();
};
// 初始化获取表格数据
const getTableData=()=>{
    let params={
        pageNum:tableConfig.paginationsObj.currentPage,
        pageSize:tableConfig.paginationsObj.pageSize,
        // category:109
    };
    Object.keys(searchConfig.submitData).forEach((item)=>{
        if(searchConfig.submitData[item]){
            params[item]=searchConfig.submitData[item];
        };
    });
    if(params.createUser){
        params["params[createUser]"]=params.createUser;
        Reflect.deleteProperty(params,"createUser");
    };
    if(params.date && params.date.length !== 0){
        params["params[beginTime]"]=params.date[0]+" 00:00:00";
        params["params[endTime]"]=params.date[1]+" 23:59:59";
        Reflect.deleteProperty(params,"date");
    };
    getBacklogData(params).then((res)=>{
        if(res.code === 200){
            tableConfig.tableData=res.rows;
            tableConfig.paginationsObj.total=res.total;
        }else if(res.code !== 401){
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};

// 搜索表单配置项
const searchConfig=reactive({
    itemConfig:[
        {
            labelName:"姓名",
            dataName:"createUser",
            type:"input",
            inputType:"text"
        },
        {
            labelName:"申请类型",
            dataName:"processName",
            type:"select",
            dataListName:"processNameList",
        },
        {
            labelName:"时间",
            dataName:"date",
            type:"date",
            dateType:"daterange",
            format:"YYYY-MM-DD",
            valueFormat:"YYYY-MM-DD"
        }
    ],
    selectLists:{
        processNameList:[
            {
                label:"请假申请",
                value:"请假申请"
            },
            {
                label:"加班申请",
                value:"加班申请"
            },
            {
                label:"出差申请",
                value:"出差申请"
            },
            {
                label:"报销申请",
                value:"报销申请"
            },
            {
                label:"费用申请",
                value:"费用申请"
            },
            {
                label:"销假申请",
                value:"销假申请"
            }
        ]
    },
    buttonConfig:[
        {
            btnName:"查询",
            type:"primary",
            size:"default",
            formRefName:searchConfigRef,
            click:searchData
        },
        {
            btnName:"重置",
            type:"default",
            size:"default",
            formRefName:searchConfigRef,
            click:resetData
        }
    ],
    submitData:{
        createUser:"",
        processName:"",
        date:""
    }
});
// 表格配置项
const tableConfig=reactive({
    elTableConfig:{
        border:true
    },
    headerConfig:[
        {
            type:"checkbox",
            align:"center",
            width:60
        },
        {
            label:"申请类型",
            prop:"procDefName",
            type:"slot",
            slotName:"procDefName",
            align:"center"
        },
        {
            label:"申请人",
            prop:"startUserName",
            type:"text",
            align:"center"
        },
        {
            label:"部门",
            prop:"customDeptId",
            type:"slot",
            slotName:"customDeptId",
            align:"center"
        },
        {
            label:"申请时间",
            prop:"processStartTime",
            type:"text",
            align:"center",
            width:180
        }
    ],
    tableData:[],
    paginationsObj:{
        currentPage:1, //当前页
        pageSize:10, //当前每页数量
        pageSizes:[10,20,30], //每页显示多少条选项集
        total:0, //表格总数量
        layout:"total,sizes,prev,pager,next,jumper"
    }
});
// 审批意见弹窗配置项
const approvalDialogConfig=reactive({
    isShow:false,
    title:"审批意见"
});
// 审批意见弹窗表单配置项
const approvalFormConfig=reactive({
    elFormConfig:{
        labelWidth:"10px"
    },
    itemConfig:[
        {
            hideLabel:true,
            dataName:"choose",
            type:"radio",
            dataListName:"chooseList",
            width:24
        },
        {
            hideLabel:true,
            dataName:"comment",
            type:"input",
            inputType:"textarea",
            placeholder:"请输入审批建议",
            rows:5,
            width:24
        }
    ],
    selectLists:{
        chooseList:[
            {
                label:"同意",
                value:"同意"
            },
            {
                label:"不同意",
                value:"不同意",
                isHide:true
            }
        ]
    },
    submitData:{
        choose:"同意",
        comment:""
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    getTableData();
    if(route.query.data){ //当从首页待办审批跳转进入时
        currentPage.value="detail";
        processInfo={...JSON.parse(route.query.data)};
    };
});

</script>

<style lang="scss" scoped>
.qilin-backlog{
    width:100%;
    height:100%;
    display:flex;
    flex-flow:column nowrap;
    >.qilin-backlog-header{
        background-color:#fff;
        padding:24px 20px 6px;
        border-radius:4px;
    }
    >.qilin-backlog-content{
        margin-top:16px;
        background-color:#fff;
        padding:16px 20px;
        border-radius:4px;
        flex:1;
        display:flex;
        flex-flow:column nowrap;
        overflow:hidden;
        >.qilin-backlog-content-header{
            display:flex;
            align-items:center;
            justify-content:flex-end;
            margin-bottom:14px;
            >span{
                font-size:16px;
                color:rgba(0,0,0,0.85);
            }
            >.qilin-backlog-content-header-btn{
                >.el-button:nth-child(1){
                    background: rgba(20, 142, 255, 0.14);
                    color: #046CE7;
                    border: 1px solid rgba(4, 108, 231, 0.6);
                }
                >.el-button:nth-child(2){
                    background: rgba(250, 173, 20, 0.15);
                    border: 1px solid #faad14;
                    color: #faad14;
                }
                >.el-button:nth-child(3){
                    background: rgba(20,140,250,0.15);
                    border: 1px solid #148CFA;
                    color:#148CFA;
                }
            }
        }
        .slot-box>span.status-dot{
            position:relative;
            &::before{
                content:"";
                position:absolute;
                left:-15px;
                top:50%;
                transform:translateY(-50%);
                width:8px;
                height:8px;
                border-radius:50%;
                margin-right:5px;
            }
            &.success::before{
                background-color:$color-text-success;
            }
            &.reject::before{
                background-color:$color-text-error;
            }
            &.normal::before{
                background-color:$color-text-info;
            }
        }
    }
}
</style>
