import Qilin from "qilin-utils";

const renderFormItem=(h,elementList)=>{
    // console.log(h,elementList);
    return elementList.map((item)=>{
        let labelWidth = item.__config__.labelWidth ? item.__config__.labelWidth + "px" : null;
        if(item.__config__.showLabel === false){
            labelWidth = 0;
        };
        return (
            <el-col span={item.__config__.span}>
                <el-form-item
                    label-width={labelWidth}
                    prop={item.__vModel__}
                    label={item.__config__.showLabel ? item.__config__.label : ""}
                >
                    <item.__config__.tag>

                    </item.__config__.tag>
                </el-form-item>
            </el-col>
        )
    });
};

const renderFormBtns=(h)=>{
    return (
        <el-col>
            <el-form-item size="large">
                <el-button type="primary" size="default">提交</el-button>
                <el-button size="default">重置</el-button>
            </el-form-item>
        </el-col>
    );
};

export default {
    props:{
        formDataObj:{
            type:Object,
            default(){
                return {}
            }
        }
    },
    data(){
        return {
            formData:{},
            rules:{},
        }
    },
    render(){
        console.log(this.formDataObj);
        // return h("div","我是你爹1");
        if(Qilin.Common.isEmptyObject(this.formDataObj)) return false;
        return (
            <el-row
                gutter={this.formDataObj.gutter}
                style={{
                    flex:1,
                    padding:"20px"
                }}
            >
                <el-form
                    style={{
                        width:"100%"
                    }}
                    ref={this.formDataObj.formRef}
                    label-position={this.formDataObj.labelPosition}
                    label-width={this.formDataObj.labelWidth}
                    label-suffix="："
                    size={"default"}
                    props={{model:this[this.formDataObj.formModel]}}
                    rules={this[this.formDataObj.formRules]}
                    disabled={this.formDataObj.disabled}
                >
                    {renderFormItem(h,this.formDataObj.fields)}
                    {this.formDataObj.formBtns && renderFormBtns(h)}
                </el-form>
            </el-row>
        )
    }
}
