<template>
	<div class="qilin-customApplication hide-scrollbar">
		<div class="qilin-customApplication-header">
			<span>自定义申请</span>
		</div>
		<div class="qilin-customApplication-content">
			<Form :config="config" @submitData="submitData" />
		</div>
	</div>
</template>
<script setup>
import {
    submitCostData
} from "@/api/officeCenter/costApplication/index.js";
import { initiateCustomProcess } from '@/api/officeCenter/customApplication/index.js'
import Qilin from 'qilin-utils'
import { getCustomProcessData } from '@/utils/common.js'
import { ref, watchEffect } from 'vue'
import Form from './form.vue'
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
// const name = decodeURI(Qilin.Common.getUrlSearchParamsValue('name'))
const currentProcessData = getCustomProcessData(route.query.name)
console.log(currentProcessData);

//自定义申请表单数据
const config = ref(null)

// 获取自定义申请表单数据
const getCustomFormDetailData = () => {
	initiateCustomProcess({
		definitionId: getCustomProcessData(route.query.name).definitionId,
		deployId: getCustomProcessData(route.query.name).deploymentId
	}).then(res => {
		config.value = res.data;
        console.log(res.data)
	})
}

getCustomFormDetailData();

// 点击提交按钮事件监听
const submitData=(formData,formRef)=>{
    // console.log(formData,"========42");
    submitCostData(getCustomProcessData(route.query.name).definitionId,{
        customDataForm:JSON.stringify(formData)
    }).then((res)=>{
        if(res.code === 200){
            formRef.resetFields();
            router.push("/officeCenter/completed");
            ElMessage({
                message:`提交${route.query.name}成功`,
                type:"success"
            });
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};

// //创建一个Row组件
// const Row = (gutter = 0, children = []) => {
// 	return h(
// 		resolveComponent('el-row'),
// 		{
// 			gutter: gutter
// 		},
// 		{
// 			default: () => children
// 		}
// 	)
// }
// //创建一个Col组件
// const Col = (span = 24, children = []) => {
// 	return h(
// 		resolveComponent('el-col'),
// 		{
// 			span: span
// 		},
// 		{
// 			default: () => children
// 		}
// 	)
// }
// //创建一个Form组件
// const Form = (ref = 'formRef', labelPosition = 'right', labelWidth = '', children = []) => {
// 	return h(
// 		resolveComponent('el-form'),
// 		{
// 			ref,
// 			labelPosition,
// 			labelWidth
// 		},
// 		{
// 			default: () => children
// 		}
// 	)
// }
// //创建一个FormItem组件
// const FormItem = (label = '', labelWidth = null, required = false, children = []) => {
// 	return h(
// 		resolveComponent('el-form-item'),
// 		{
// 			label,
// 			labelWidth,
// 			required
// 		},
// 		{
// 			default: () => children
// 		}
// 	)
// }
// //获取组件属性
// const getComponentProps = item => {
// 	const result = {}
// 	for (let key in item) {
// 		if (!key.startsWith('__')) {
// 			result[key] = item[key]
// 		}
// 	}
// 	return result
// }

// //定义异步组件，获取数据后渲染
// const Result = defineAsyncComponent(async () => {
// 	const data = reactive(await getCustomFormDetailData())
// 	console.log(data)
// 	return defineComponent(() => {
// 		return () => {
// 			const FormItems = data.fields.map(item => {
// 				console.log(item)
// 				return Row(0, [
// 					Col(item.__config__.span, [
// 						FormItem(item.__config__.showLabel ? item.__config__.label : '', item.__config__.labelWidth, item.__config__.required, [
// 							h(resolveComponent(item.__config__.tag), {
// 								modelValue: item[item.__vModel__],
// 								'onUpdate:modelValue': val => {
// 									item[item.__vModel__] = val
// 								},
// 								...getComponentProps(item)
// 							})
// 						])
// 					])
// 				])
// 			})
// 			return Row(data.gutter, [Col(data.span, [Form(data.formRef, data.labelPosition, data.labelWidth, FormItems)])])
// 		}
// 	})
// })

watch(route,(newValue)=>{
    // console.log(newValue,route.query.name,"=============");
    getCustomFormDetailData();
})
</script>
<style lang="scss" scoped>
.qilin-customApplication {
	width: 100%;
	height: 100%;
	background-color: #fff;
	padding: 30px 50px;
	border-radius: 2px;
	> .qilin-customApplication-header {
		padding-bottom: 20px;
		border-bottom: 1px solid $border-color;
		> span {
			color: $text-color-85;
			font-size: $medium-text-size;
		}
	}
	> .qilin-customApplication-content {
		width: 100%;
		padding: 20px;
		:deep(> .el-form) {
			width: 70%;
			margin: 50px auto;
			.el-date-editor.el-input {
				width: 100%;
				.el-input__wrapper {
					width: 100%;
					max-width: 500px;
				}
			}
			.el-input {
				width: 300px;
			}
			.el-textarea {
				width: 100%;
				max-width: 500px;
			}
		}
	}
}
</style>
