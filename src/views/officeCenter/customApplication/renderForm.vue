<template>
    <renderForm></renderForm>

</template>
<script setup lang="jsx">
import Qilin from "qilin-utils";
import render from "./render.js";

const propsValue = defineProps({
    formDataObj:{
        type:Object,
        default(){
            return {

            };
        }
    }
});
const emits=defineEmits(["submit"]);
// const [propsValue.formDataObj.formModel]=reactive({});
// const [propsValue.formDataObj.formRules]=reactive({});
const that=reactive({
    [propsValue.formDataObj.formModel]:{},
    [propsValue.formDataObj.formRules]:{},
    [propsValue.formDataObj.formRef]:ref(null)
});

const layouts={
    colFormItem(h,scheme){
        const config = scheme.__config__;
        const listeners = buildListeners(scheme);
        let labelWidth = config.labelWidth ? config.labelWidth + "px" : null;
        if(config.showLabel === false){
            labelWidth = 0;
        };
        return (
            <el-col span={config.span}>
                <el-form-item
                    label-width={labelWidth}
                    prop={scheme.__vModel__}
                    label={config.showLabel ? config.label : ""}
                >
                    <render conf={scheme} on={listeners}></render>
                </el-form-item>
            </el-col>
        );
    },
    rowFormItem(h,scheme){
        let child = renderChildren(arguments);
        if(scheme.type === "flex"){
            child = (
                <el-row
                    type={scheme.type}
                    justify={scheme.justify}
                    align={scheme.align}
                >
                    {child}
                </el-row>
            );
        };
        return (
            <el-col span={scheme.span}>
                <el-row gutter={scheme.gutter}>
                    {child}
                </el-row>
            </el-col>
        );
    }
};

const renderFormItem=(h,elementList)=>{
    // console.log(h,elementList);
    return elementList.map((scheme)=>{
        const config = scheme.__config__;
        const layout = layouts[config.layout];
        if(layout){
            return layout(h,scheme);
        };
        throw new Error(`没有与${config.layout}匹配的layout`);
    });
};

const renderFormBtns=(h)=>{
    return (
        <el-col>
            <el-form-item size="large">
                <el-button type="primary" onClick={submitForm}>提交</el-button>
                <el-button onClick={resetForm}>重置</el-button>
            </el-form-item>
        </el-col>
    );
};

const buildListeners=(scheme)=>{
    const config = scheme.__config__;
    const methods = propsValue.formDataObj.__methods__ || {};
    const listeners = {};
    // 给__methods__中的方法绑定event
    Object.keys(methods).forEach((item)=>{
        listeners[item] = (event)=>{
            methods[item](event);
        };
    });
    // 响应render.js中的vModel
    listeners.input = (event)=>{
        config.defaultValue = event;
        that[propsValue.formDataObj.formModel][scheme.__vModel__] = event;
    };
    return listeners;
};
const renderChildren=(h,scheme)=>{
    const config = scheme.__config__;
    if(!Array.isArray(config.children)){
        return null;
    };
    return renderFormItem(h,config.children);
};
const submitForm=()=>{
    console.log(that[propsValue.formDataObj.formRef]);return;
    that[propsValue.formDataObj.formRef].value.validate((valid)=>{
        if(!valid) return false;
        const param = {
            formData:propsValue.formDataObj,
            valData:that(propsValue.formDataObj.formModel)
        };
        emits("submit",params);
        return true;
    });
};
const resetForm=()=>{
    console.log(propsValue.formDataObj.formRef)
    console.log(that[propsValue.formDataObj.formRef]);return;
    that[propsValue.formDataObj.formRef].value.resetFields();
};


const renderForm={
    render:()=>{
        // console.log(propsValue.formDataObj);
        // console.log(Qilin.Common.isEmptyObject(propsValue.formDataObj));
        // return h("div","我是你爹");
        if(Qilin.Common.isEmptyObject(propsValue.formDataObj)) return false;
        return (
            <el-row gutter={propsValue.formDataObj.gutter}>
                <el-form
                    ref={propsValue.formDataObj.formRef}
                    label-position={propsValue.formDataObj.labelPosition}
                    label-width={propsValue.formDataObj.labelWidth}
                    size={"default"}
                    props={{model:that[propsValue.formDataObj.formModel]}}
                    rules={that[propsValue.formDataObj.formRules]}
                    disabled={propsValue.formDataObj.disabled}
                >
                    {renderFormItem(h,propsValue.formDataObj.fields)}
                    {propsValue.formDataObj.formBtns && renderFormBtns(h)}
                </el-form>
            </el-row>
        )
    }
}
</script>
