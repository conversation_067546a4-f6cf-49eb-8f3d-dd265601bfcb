<template>
	<CustomElement></CustomElement>
</template>
<script setup>
import { computed, defineComponent, ref, resolveComponent } from 'vue';

const propsValue = defineProps({
	//双向绑定
	modelValue: {
		type: [String, Number, Boolean, Object, Array],
		default: ''
	},
	//配置数据
	config: {
		type: Object,
		default: null
	}
})
const emits = defineEmits(['update:modelValue'])

//获取自定义组件的props属性
const customElementProps = computed(() => {
	const data = {}
	for (let key in propsValue.config) {
		if (!key.startsWith('__')) {
			data[key] = propsValue.config[key]
		}
	}
	return data
})

const CustomElement = defineComponent(() => {
	return () => {
		// console.log(propsValue.config);
		return h(
            resolveComponent(propsValue.config.__config__.tag),
            {
                //设置props
                ...customElementProps.value,
                autocomplete:"new-password",
                //双向绑定
                modelValue: propsValue.modelValue,
                'onUpdate:modelValue': val => {
                    emits('update:modelValue', val)
                }
		    },
            {
                default:()=>{
                    return propsValue.config.__slot__.options ? propsValue.config.__slot__.options.map((item)=>{
                        return h(
                            resolveComponent("el-option"),
                            {
                                key:item.value,
                                label:item.label,
                                value:item.value
                            },
                            {
                                default:()=>{ return item.label}
                            }
                        )
                    }) : []
                }
            }
        )
	}
})
</script>
