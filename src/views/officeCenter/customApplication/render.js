import Qilin from "qilin-utils";

const componentChild={};

const mountSlotFiles=(h,confClone,children)=>{
    const childObjs = componentChild[confClone.__config__.tag];
    if(childObjs){
        Object.keys(childObjs).forEach((item)=>{
            const childFunc = childObjs[key];
            if(confClone.__slot__ && confClone.__slot__[item]){
                children.push(childFunc(h,confClone,key));
            };
        });
    };
};
const emitEvents=(confClone)=>{
    ["on","nativeOn"].forEach((attr)=>{
        const eventKeyList = Object.keys(confClone[attr] || {});
        eventKeyList.forEach((key)=>{
            const val = confClone[attr][key];
            if(typeof val === "string"){
                confClone[attr][key] = (event)=>{
                    this.$emit(val,event);
                };
            };
        });
    });
};
const buildDataObject=(confClone,dataObject)=>{
    Object.keys(confClone).forEach((key)=>{
        const val = confClone[key];
        if(key === "__vModel__"){
            vModel.call(this,dataObject,confClone.__config__.defaultValue);
        }else if(dataObject[key] !== undefined){
            if(dataObject[key] === null ||
                dataObject[key] instanceof RegExp ||
                ["boolean","string","number","function"].includes(typeof dataObject[key])
            ){
                dataObject[key] = val;
            }else if(Array.isArray(dataObject[key])){
                dataObject[key] = [...dataObject[key],...val];
            }else{
                dataObject[key]= {...dataObject[key],...val};
            };
        }else{
            dataObject.attrs[key] = val;
        };
    });
    clearAttrs(dataObject); //清理属性
};
const vModel=(dataObject,defaultValue)=>{
    dataObject.props.value = defaultValue;
    dataObject.on.input = (val)=>{
        this.$emits("input",val);
    };
};
const clearAttrs=(dataObject)=>{
    delete dataObject.attrs.__config__;
    delete dataObject.attrs.__slot__;
    delete dataObject.attrs.__methods__;
};
const makeDataObject=()=>{
    return {
        class:{},
        attrs:{},
        props:{},
        domProps:{},
        nativeOn:{},
        on:{},
        style:{},
        directives:[],
        scopedSlots:{},
        slot:null,
        key:null,
        ref:null,
        refInFor:true
    };
};

export default {
    props:{
        conf:{
            type:Object,
            required:true
        }
    },
    render(){
        // console.log(this);
        const dataObject = makeDataObject();
        const confClone = Qilin.Common.deepCopy(this.conf,{});
        const children = this.$slots.default || [];
        // 如果slots文件夹存在与当前tag同名的文件，则执行文件中的代码
        mountSlotFiles.call(this,h,confClone,children);
        // 将字符串类型的事件，发送为消息
        emitEvents.call(this,confClone);
        // 将json表单配置转化为vue render可以识别的数据对象（dataObject）
        buildDataObject.call(this,confClone,dataObject);

        return h(this.conf.__config__.tag,dataObject,children);
    }
};
