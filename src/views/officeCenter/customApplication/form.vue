<template>
	<el-row v-if="propsValue.config" :gutter="propsValue.config.gutter">
		<el-col :span="propsValue.config.span">
			<el-form ref="formRef" :disabled="propsValue.config.disabled" :model="formData"
                :label-position="propsValue.config.labelPosition" :label-width="propsValue.config.labelWidth"
            >
				<el-row v-for="item in propsValue.config.fields">
					<el-col :span="item.__config__.span">
						<el-form-item
                            :required="item.__config__.required"
                            :label="item.__config__.showLabel ? item.__config__.label : ''"
                            :label-width="item.__config__.labelWidth"
                            :prop="item.__vModel__"
                        >
							<FormItem :config="item" v-model="formData[item.__vModel__]" />
						</el-form-item>
					</el-col>
				</el-row>
				<el-row>
					<el-col>
						<el-button type="primary" @click="onSubmit">提交</el-button>
						<el-button @click="onReset">重置</el-button>
					</el-col>
				</el-row>
			</el-form>
		</el-col>
	</el-row>
</template>
<script setup>
import { reactive, ref, watch } from 'vue'
import FormItem from './formItem.vue'
//接收的props
const propsValue = defineProps({
	config: {
		type: Object,
		default: null
	}
})

const emits=defineEmits(["submitData"]);
//表单组件实例
const formRef = ref(null)
//表单数据
let formData = ref({})

//监听config有值
watch(
	() => propsValue.config,
	newVal => {
        formData.value={};
		if (newVal) {
			newVal.fields.forEach(item => {
				formData.value[item.__vModel__] = ''
			})
		}
	}
)

const onSubmit = () => {
	formRef.value.validate((valid) => {
        if(valid){
            emits("submitData",formData.value,formRef.value);
        };
	})
}

const onReset = () => {
	propsValue.config.fields.forEach(item => {
		formData.value[item.__vModel__] = ''
	});
}
</script>
