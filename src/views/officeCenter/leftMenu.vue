<template>
    <div class="qilin-leftMenu">
        <el-menu
            class="qilin-leftMenu-menu hide-scrollbar"
            :default-openeds="['/officeCenter1','/officeCenter2']"
            :default-active="currentRoute"
            router
        >
            <template
                v-for="item in menuList"
                :key="item.path"
            >
                <el-sub-menu
                    v-if="(item.children && item.children.length > 0) && !item.isHide"
                    :index="item.path"
                    :class="route.path.includes(item.path) ? 'is-active' : '' "
                >
                    <template v-slot:title>
                        <i :class="item.iconfont"></i>
                        <span>{{item.title}}</span>
                    </template>
                    <template
                        v-for="ite in item.children"
                        :key="ite.path"
                    >
                        <el-menu-item
                            :index="ite.path"
                            v-show="!ite.isHide"
                        >
                            {{ite.title}}
                        </el-menu-item>
                    </template>
                </el-sub-menu>
            </template>
        </el-menu>
    </div>
</template>

<script setup>
import { getProcessData } from "@/api/officeCenter/index.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();

/*
    数据变量定义区域
*/
// 菜单数据集
const menuList=reactive([
    {
        title:"流程中心",
        path:"/officeCenter1",
        iconfont:"iconfont icon-bangong",
        children:[
            {
                title:"待办",
                path:"/officeCenter/backlog"
            },
            {
                title:"已办",
                path:"/officeCenter/completed"
            },
            {
                title:'我的申请',
                path:"/officeCenter/myApplication"
            },
            // {
            //     title:"草稿",
            //     path:"/officeCenter/draft"
            // }
        ]
    },
    {
        title:"新建流程",
        path:"/officeCenter2",
        iconfont:"iconfont icon-liucheng",
        children:[
        
        ]
    }
]);

// 获取流程申请信息数据事件监听
const getProcessInfoData=()=>{
    getProcessData({
        pageNum:1,
        pageSize:50
    }).then((res)=>{
        if(res.code === 200){
        	console.log(res);
            menuList[1].children=res.rows?.map((item)=>{
                return {
                    title:item.processName,
                    path:item.processName === "请假申请" ? "/officeCenter/leaveApplication" :
                        item.processName === "加班申请" ? "/officeCenter/overtimeApplication" :
                        item.processName === "出差申请" ? "/officeCenter/businessApplication" :
                        item.processName === "报销申请" ? "/officeCenter/expenseApplication" :
                        item.processName === "费用申请" ? "/officeCenter/costApplication" :
                        item.processName === "用车申请" ? "/officeCenter/usingVehicle" : "/officeCenter/customApply?"+`name=${item.processName}`  ,
                    isHide:item.processName === "销假申请" || item.processName === "补卡申请" ? true : false ,
                    ...item
                };
            });
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};


/*
    生命周期等代码区域
*/
onActivated(()=>{
    getProcessInfoData();
});

/*
    计算属性等代码区域
*/
// 当前路由
const currentRoute=computed(()=>{
    return route.path;
});


/*
    逻辑脚本代码区域
*/


</script>

<style lang="scss" scoped>
.qilin-leftMenu{
    background-color:#fff;
    width:200px;
    height:100%;
    >.qilin-leftMenu-menu{
        height:100%;
        :deep(.el-sub-menu.is-active > .el-sub-menu__title) {
            color: $primary-text-color;
        }
        .el-sub-menu>.el-sub-menu__title{
            >i{
                color:$text-color-45;
            }
        }
        .el-sub-menu.is-active>.el-sub-menu__title{
            >i{
                color:$primary-text-color;
            }
        }
        .el-menu-item.is-active {
            color: $primary-text-color;
            background-color: rgba(20, 142, 255, .2);
            >i {
                color: $primary-text-color;
            }
            &:focus {
                background-color: rgba(20, 142, 255, .2);
            }
            &::after {
                content: "";
                width: 4px;
                height: 100%;
                position: absolute;
                right: 0;
                background-color: $primary-text-color;
            }
        }
    }
}
</style>
