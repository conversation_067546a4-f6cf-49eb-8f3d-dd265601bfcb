<template>
    <div class="qilin-officeCenter">
        <LeftMenu></LeftMenu>
        <div class="qilin-officeCenter-content">
            <router-view></router-view>
        </div>
    </div>
</template>

<script setup>
import LeftMenu from "./leftMenu.vue";

/*
    数据变量定义区域
*/


/*
    计算属性等代码区域
*/


/*
    逻辑脚本代码区域
*/


/*
    生命周期等代码区域
*/

</script>

<style lang="scss" scoped>
.qilin-officeCenter{
    width:100%;
    height:100%;
    display:flex;
    >.qilin-officeCenter-content{
        flex:1;
        margin-left:20px;
        display:flex;
        flex-direction:column;
        overflow:hidden;
        position: relative;
    }
}
</style>
