<template>
    <div class="qilin-draft">
        <div class="qilin-draft-header">
            <QilinSearch v-model:searchConfig="searchConfig" ref="searchConfigRef"></QilinSearch>
        </div>
        <div class="qilin-draft-content">
            <QilinTable v-model:tableConfig="tableConfig" @changeCurrentPage="changeCurrentPage"
                @changeCurrentSize="changeCurrentSize" @rowClickData="clickRowData"
            >
                <template v-slot:processName="slotData">
                    <div v-html="getProcDefName(slotData.data.scope.row.processName)"></div>
                </template>
            </QilinTable>
        </div>
    </div>
</template>

<script setup>
import { getDraftData,deleteDraftData } from "@/api/officeCenter/draft/index.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();

/*
    数据变量定义区域
*/
// 获取搜索表单元素DOM
const searchConfigRef=ref(null);


/*
    计算属性等代码区域
*/
// 获取申请类型方法
const getProcDefName=computed(()=>{
    return (name)=>{
        return `<b>【${name.slice(0,2)}】</b>申请`
    };
});


/*
    逻辑脚本代码区域
*/
// 点击查询按钮事件监听
const searchData=()=>{
    getTableData();
};
// 点击重置按钮事件监听
const resetData=(formRef)=>{
    formRef.resetSearchData();
    tableConfig.paginationsObj.pageSize=10;
    tableConfig.paginationsObj.currentPage=1;
    getTableData();
};
// 点击表格行事件监听
const clickRowData=(row)=>{
    // console.log(row);
    switch(row.processName){
        case "加班申请":
            router.push({
                path:"/officeCenter/overtimeApplication",
                query:{
                    id:row.id
                }
            });
            break;
        case "请假申请":
            router.push({
                path:"/officeCenter/leaveApplication",
                query:{
                    id:row.id
                }
            });
            break;
        case "出差申请":
            router.push({
                path:"/officeCenter/businessApplication",
                query:{
                    id:row.id
                }
            });
            break;
        case "报销申请":
            router.push({
                path:"/officeCenter/expenseApplication",
                query:{
                    id:row.id
                }
            });
            break;
        case "费用申请":
            router.push({
                path:"/officeCenter/costApplication",
                query:{
                    id:row.id
                }
            });
            break;
    };
};
// 点击表格删除按钮事件监听
const deleteData=(row)=>{
    ElMessageBox.confirm("是否删除该条数据？","删除",{
        cancelButtonText:"取消",
        confirmButtonText:"确定",
        type:"error",
        icon:"WarningFilled"
    }).then(()=>{
        // console.log("点击确定");
        deleteDraftData(row.id).then((res)=>{
            if(res.code === 200){
                getTableData();
                ElMessage({
                    message:"删除成功",
                    type:"success"
                });
            }else{
                ElMessage({
                    message:res.msg || "系统错误",
                    type:"error"
                });
            };
        });
    }).catch(()=>{
        console.log("点击取消");
    });
};
// 切换页码事件监听
const changeCurrentPage=(page)=>{
    tableConfig.paginationsObj.currentPage=page;
    getTableData();
};
// 切换页码事件监听
const changeCurrentSize=(size)=>{
    tableConfig.paginationsObj.pageSize=size;
    getTableData();
};
// 初始化获取表格数据
const getTableData=()=>{
    let params={
        pageNum:tableConfig.paginationsObj.currentPage,
        pageSize:tableConfig.paginationsObj.pageSize,
        category:109
    };
    Object.keys(searchConfig.submitData).forEach((item)=>{
        if(searchConfig.submitData[item]){
            params[item]=searchConfig.submitData[item];
        };
    });
    getDraftData(params).then((res)=>{
        if(res.code === 200){
            tableConfig.tableData=res.rows;
            tableConfig.paginationsObj.total=res.total;
        }else if(res.code !== 401){
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};

// 搜索表单配置项
const searchConfig=reactive({
    itemConfig:[
        {
            labelName:"申请类型",
            dataName:"processName",
            type:"select",
            dataListName:"processNameList",
        }
    ],
    selectLists:{
        processNameList:[
            {
                label:"请假申请",
                value:"请假申请"
            },
            {
                label:"加班申请",
                value:"加班申请"
            },
            {
                label:"出差申请",
                value:"出差申请"
            },
            {
                label:"报销申请",
                value:"报销申请"
            },
            {
                label:"费用申请",
                value:"费用申请"
            },
            {
                label:"销假申请",
                value:"销假申请"
            }
        ]
    },
    buttonConfig:[
        {
            btnName:"查询",
            type:"primary",
            size:"default",
            formRefName:searchConfigRef,
            click:searchData
        },
        {
            btnName:"重置",
            type:"default",
            size:"default",
            formRefName:searchConfigRef,
            click:resetData
        }
    ],
    submitData:{
        processName:""
    }
});
// 表格配置项
const tableConfig=reactive({
    elTableConfig:{
        border:true
    },
    headerConfig:[
        // {
        //     type:"checkbox",
        //     align:"center",
        //     width:60
        // },
        {
            label:"申请类型",
            prop:"processName",
            type:"slot",
            slotName:"processName",
            align:"center"
        },
        {
            label:"申请人",
            prop:"nickName",
            type:"text",
            align:"center"
        },
        {
            label:"部门",
            prop:"deptName",
            type:"text",
            align:"center"
        },
        {
            label:"创建时间",
            prop:"createTime",
            type:"text",
            align:"center"
        },
        {
            label:"操作",
            type:"operate",
            align:"center",
            width:180,
            operateOptions:[
                {
                    buttonName:"编辑",
                    buttonType:"primary",
                    buttonSize:"default",
                    text:true,
                    buttonEvent:clickRowData
                },
                {
                    buttonName:"删除",
                    buttonType:"primary",
                    buttonSize:"default",
                    text:true,
                    buttonEvent:deleteData
                }
            ]
        }
    ],
    tableData:[],
    paginationsObj:{
        currentPage:1, //当前页
        pageSize:10, //当前每页数量
        pageSizes:[10,20,30], //每页显示多少条选项集
        total:0, //表格总数量
        layout:"total,sizes,prev,pager,next,jumper"
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    getTableData();
});

</script>

<style lang="scss" scoped>
.qilin-draft{
    width:100%;
    height:100%;
    display:flex;
    flex-flow:column nowrap;
    >.qilin-draft-header{
        background-color:#fff;
        padding:24px 20px 6px;
        border-radius:4px;
    }
    >.qilin-draft-content{
        margin-top:16px;
        background-color:#fff;
        padding:16px 20px;
        border-radius:4px;
        flex:1;
        display:flex;
        flex-flow:column nowrap;
        overflow:hidden;
        >.qilin-draft-content-header{
            display:flex;
            align-items:center;
            justify-content:space-between;
            margin-bottom:14px;
            >span{
                font-size:16px;
                color:rgba(0,0,0,0.85);
            }
            >.qilin-draft-content-header-btn{
                >.el-button:nth-child(1){
                    background: rgba(86, 206, 163, 0.14);
                    color: #56cea3;
                    border: 1px solid #56cea3;
                }
                >.el-button:nth-child(2){
                    background: rgba(250, 173, 20, 0.15);
                    border: 1px solid #faad14;
                    color: #faad14;
                }
                >.el-button:nth-child(3){
                    background: rgba(20,140,250,0.15);
                    border: 1px solid #148CFA;
                    color:#148CFA;
                }
            }
        }
        .slot-box>span.status-dot{
            position:relative;
            &::before{
                content:"";
                position:absolute;
                left:-15px;
                top:50%;
                transform:translateY(-50%);
                width:8px;
                height:8px;
                border-radius:50%;
                margin-right:5px;
            }
            &.success::before{
                background-color:$color-text-success;
            }
            &.reject::before{
                background-color:$color-text-error;
            }
            &.normal::before{
                background-color:$color-text-info;
            }
        }
    }
}
</style>
