<template>
    <div class="qilin-expenseApplication hide-scrollbar">
        <template v-if="!propsValue.processInfo.formDataEdit">
            <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
            <div class="qilin-expenseApplication-content-cost">
                <div class="qilin-expenseApplication-content-cost-header">
                    <span>费用明细</span>
                </div>
                <QilinTable v-model:tableConfig="costTableConfig">
                    <template v-slot:fileUrls="slotData">
                        <!-- <el-image
                            v-if="slotData.data.scope.row.fileUrls && slotData.data.scope.row.fileUrls.length > 0"
                            style="width: 100px; height: 100px"
                            :preview-teleported="true"
                            :src="getImageUrl(slotData.data.scope.row.fileUrls[0])"
                            :preview-src-list="getImageUrlList(slotData.data.scope.row.fileUrls)"
                            :initial-index="0"
                            fit="cover"
                        ></el-image>
                        <span v-else>无图片~</span> -->
                        <span v-if="slotData.data.scope.row.fileUrls && slotData.data.scope.row.fileUrls.length > 0"
                            style="color:#046CE7;cursor:pointer;" @click="previewFiles(slotData.data.scope.row.fileUrls)"
                        >
                            费用附件*{{slotData.data.scope.row.fileUrls.length}}
                        </span>
                        <span v-else>无附件~</span>
                    </template>
                </QilinTable>
            </div>
        </template>
        <template v-else>
            <QilinForm v-model:formConfig="formConfig" ref="formConfigRef">
                <template v-slot:businessLink="slotData">
                    <el-input v-model="formConfig.submitData.businessLink" placeholder="请选择出差申请链接"
                        disabled style="max-width:300px;"
                    ></el-input>
                    <el-button type="default" class="busniessLink" @click="selectBusinessData()">选择出差申请链接</el-button>
                </template>
                <template v-slot:costApplicationLink="slotData">
                <el-input v-model="formConfig.submitData.costApplicationLink" placeholder="请选择费用申请链接"
                        disabled style="max-width:300px;"
                    ></el-input>
                    <el-button type="default" class="busniessLink" @click="selectCostApplicationData()">选择费用申请链接</el-button>
                </template>
                <template v-slot:extraItem>
                    <div class="qilin-expenseApplication-content-cost">
                        <div class="qilin-expenseApplication-content-cost-header">
                            <span>费用明细</span>
                            <el-button type="default" class="addCost" size="small" icon="Plus" @click="addSpecification()">添加</el-button>
                        </div>
                        <QilinTable v-model:tableConfig="costTableConfig">
                            <template v-slot:fileUrls="slotData">
                                <!-- <el-image
                                    v-if="slotData.data.scope.row.fileUrls && slotData.data.scope.row.fileUrls.length > 0"
                                    style="width: 100px; height: 100px"
                                    :preview-teleported="true"
                                    :src="getImageUrl(slotData.data.scope.row.fileUrls[0])"
                                    :preview-src-list="getImageUrlList(slotData.data.scope.row.fileUrls)"
                                    :initial-index="0"
                                    fit="cover"
                                ></el-image>
                                <span v-else>无图片~</span> -->
                                <span v-if="slotData.data.scope.row.fileUrls && slotData.data.scope.row.fileUrls.length > 0"
                                    style="color:#046CE7;cursor:pointer;" @click="previewFiles(slotData.data.scope.row.fileUrls)"
                                >
                                    费用附件*{{slotData.data.scope.row.fileUrls.length}}
                                </span>
                                <span v-else>无附件~</span>
                            </template>
                        </QilinTable>
                        <div class="qilin-expenseApplication-content-cost-footer">
                            <div class="qilin-expenseApplication-content-cost-footer-total">
                                费用合计：<span>￥{{totalCost}}</span>
                            </div>
                        </div>
                    </div>
                </template>
            </QilinForm>
        </template>
    </div>
    <QilinDialog v-model:dialogConfig="dialogConfig" @handleClose="handleCloseDialog" @submitConfirm="submitConfirm">
        <QilinSearch v-model:searchConfig="searchConfig" ref="searchConfigRef"></QilinSearch>
        <QilinTable v-model:tableConfig="tableConfig" ref="tableConfigRef"
            @changeCurrentPage="changeCurrentPage" @changeCurrentSize="changeCurrentSize"
            @currentChangeData="currentChangeData"
        >
            <template v-slot:trafficType="slotData">
                <span class="ellipsis">{{getType(slotData.data.scope.row.trafficType)}}</span>
            </template>
            <template v-slot:finishFlag="slotData">
                <span>{{slotData.data.scope.row.finishFlag == "0" ? "进行中" : "结束"}}</span>
            </template>
        </QilinTable>
    </QilinDialog>
    <QilinDialog v-model:dialogConfig="specificationDialogConfig" @handleClose="handleCloseSpecificationDialog"
        @submitConfirm="submitConfirmSpecificationDialog"
    >
        <QilinForm v-model:formConfig="specificationFormConfig" ref="specificationFormConfigRef"></QilinForm>
    </QilinDialog>
    <QilinDialog v-model:dialogConfig="viewDialogConfig" @handleClose="handleCloseViewDialogConfig">
        <QilinView v-model:viewConfig="viewBusinessConfig" ref="viewBusinessConfigRef"></QilinView>
    </QilinDialog>
    <QilinDialog v-model:dialogConfig="costDialogConfig" @handleClose="handleCloseCostDialog" @submitConfirm="submitConfirmCost">
        <QilinSearch v-model:searchConfig="searchCostConfig" ref="searchCostConfigRef"></QilinSearch>
        <QilinTable v-model:tableConfig="tableCostConfig" ref="tableCostConfigRef"
            @changeCurrentPage="changeCostCurrentPage" @changeCurrentSize="changeCostCurrentSize"
            @currentChangeData="currentCostChangeData"
        >
            <template v-slot:finishFlag="slotData">
                <span>{{slotData.data.scope.row.finishFlag == "0" ? "进行中" : "结束"}}</span>
            </template>
        </QilinTable>
    </QilinDialog>
    <QilinDialog v-model:dialogConfig="viewCostDialogConfig" @handleClose="handleCloseViewCostDialogConfig">
        <QilinView v-model:viewConfig="viewCostConfig" ref="viewCostConfigRef"></QilinView>
    </QilinDialog>
    <previewFile ref="previewWordRef" :fileList="previewFileList"></previewFile>
</template>

<script setup>
import Qilin from "qilin-utils";
import {imageIp,imageOptions} from "@/setting.js";
import {
    calculateExpenseConfilct,
    getBusinessInExpenseData,
    getBusinessInfoData,
    getCostApplicationData
} from "@/api/officeCenter/expenseApplication/index.js";
import {
    submitWithdrawProcess
} from "@/api/officeCenter/processApproval/index.js";
import {getCountryAddressData} from "@/utils/common.js";
import { login } from "@/store/login.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
const loginStore=login();
import previewFile from "@/components/previewFiles/index.vue";

/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);
// 获取表单组件元素DOM
const formConfigRef=ref(null);
// 是否重复提交报销时间
let flag=ref(false);
// 获取出差申请链接弹窗内搜索表单元素DOM
const searchConfigRef=ref(null);
// 获取出差申请链接弹窗内表格元素DOM
const tableConfigRef=ref(null);
// 选中的出差申请链接行数据
let selectedBusinessData=ref(null);
// 费用总计金额
let totalCost=ref(0);
// 获取新增修改费用明细弹窗表单元素DOM
const specificationFormConfigRef=ref(null);
// 获取查看出差申请数据表单元素DOM
const viewBusinessConfigRef=ref(null);
// 获取预览组件元素DOM
const previewWordRef=ref(null);
// 预览附件数据集
let previewFileList=ref([]);
// 出差申请链接提交id
let busniessLinkId=ref("");
// 获取费用申请链接弹窗内搜索表单元素DOM
const searchCostConfigRef=ref(null);
// 获取费用申请链接弹窗内表格元素DOM
const tableCostConfigRef=ref(null);
// 选中的费用申请链接行数据
const selectedCostApplicationData=ref(null);
// 费用申请链接提交id
const costApplicationLinkId=ref("");
// 获取查看费用申请数据表单元素DOM
const viewCostConfigRef=ref(null);


/*
    计算属性等代码区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);

// 获取出差工具方法
const getType=computed(()=>{
    return function(type){
        type=type ? type.includes("[") ? JSON.parse(type) : type : [];
        return Array.isArray(type) ? type.join() : type;
    };
});
// 获取图片的url方法
const getImageUrl=computed(()=>{
    return (item)=>{
        if(!item[imageOptions.serverUrl || 'fileName']){
            return item.url.replace("noa","oa");
        }else{
            return imageIp + item[imageOptions.serverUrl || 'fileName'];
        };
    };
});
// 获取图片的url-list方法
const getImageUrlList=computed(()=>{
    return (list)=>{
        return list.map((item)=>{
            if(!item[imageOptions.serverUrl || 'fileName']){
                return item.url.replace("noa","oa");
            }else{
                return imageIp + item[imageOptions.serverUrl || 'fileName'];
            };
        });
    };
});


/*
    逻辑脚本代码区域
*/
// 校验开始时间事件监听
const checkStartTime=(rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.endTime && new Date(formConfig.submitData.endTime).getTime() < new Date(value).getTime()){
            callback("开始时间不得大于结束时间");
        }else{
            callback();
        };
    };
};
// 校验结束时间事件监听
const checkEndTime=(rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.startTime && new Date(formConfig.submitData.startTime).getTime() > new Date(value).getTime()){
            callback("结束时间不得小于出开始时间");
        }else{
            callback();
        };
    };
};
// 选择报销类型事件监听
const changeExpenseType=async (val,item,type)=>{
    if(val === "差旅费"){
        formConfig.itemConfig.forEach((item1)=>{
            if(["businessLink","address","subsidy","trafficAmount","foodAmount"].includes(item1.dataName)){
                item1.isHide=false;
            };
            if(item1.dataName === "costApplicationLink"){
                item1.isHide=true;
            };
        });
        formConfig.submitData.subsidy="是";
        await blurSelectStartTime(null,formConfig.submitData.startTime);
    }else{
        formConfig.itemConfig.forEach((item1)=>{
            if(["businessLink","address","subsidy","trafficAmount","foodAmount","reason"].includes(item1.dataName)){
                item1.isHide=true;
            };
            if(item1.dataName === "costApplicationLink"){
                if(["办公费","工会费"].includes(val)){
                    item1.isHide=false;
                    if(type !== "draft"){ // 防止回显进入时数据被清除
                        formConfig.submitData.costApplicationLink="";
                        costApplicationLinkId.value=null;
                    };
                }else{
                    item1.isHide=true;
                };
            };
        });
        flag.value=false;
    };
    calculateTotalCost();
};
// 选择开始时间失去焦点事件监听
const blurSelectStartTime=async (item,e)=>{
    if(formConfig.submitData.expenseType === "差旅费"){
        if(new Date().getTime() >= new Date(e).getTime() + 24 * 60 * 60 * 1000 * 7){ // 若申请开始时间超过距离当前时间七日，则需要填写情况说明
            formConfig.itemConfig.forEach((ite)=>{
                if(ite.dataName === "reason"){
                    ite.isHide=false;
                };
            });
        }else{
            formConfig.itemConfig.forEach((ite)=>{
                if(ite.dataName === "reason"){
                    ite.isHide=true;
                };
            });
        };
    }else{
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "reason"){
                ite.isHide=true;
            };
        });
    };
    if(e && formConfig.submitData.endTime && formConfig.submitData.expenseType === "差旅费"){
        await calculateExpenseConfilctData(e,formConfig.submitData.endTime);
    };
};
// 选择结束时间失去焦点事件监听
const blurSelectEndTime=async (item,e)=>{
    if(e && formConfig.submitData.startTime && formConfig.submitData.expenseType === "差旅费"){
        await calculateExpenseConfilctData(formConfig.submitData.startTime,e);
    };
};
// 初始化判断报销时间有无冲突事件监听
const calculateExpenseConfilctData=async (startTime,endTime)=>{
    await calculateExpenseConfilct({
        procInsId:propsValue.processInfo.procInsId,
        processKey:4,
        params:{
            startTime,
            endTime
        }
    }).then((res)=>{
        if(res.code === 200){
            if(!res.data){
                ElMessage({
                    message:"该时间段已提交报销申请，请勿重复提交",
                    type:"error"
                });
            };
            flag.value=!res.data;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 选择是否申请出差补助事件监听
const changeSubsidy=(val,item)=>{
    // console.log(val,item);
    if(val === "是"){
        formConfig.itemConfig.forEach((item1)=>{
            if(["trafficAmount","foodAmount"].includes(item1.dataName)){
                item1.isHide=false;
            };
        });
    }else if(val === "否"){
        formConfig.itemConfig.forEach((item1)=>{
            if(["trafficAmount","foodAmount"].includes(item1.dataName)){
                item1.isHide=true;
            };
        });
    };
};
// 计算费用合计事件监听
const calculateTotalCost=()=>{
    let tableCost=costTableConfig.tableData.reduce((prev,next)=>{
        // console.log(prev,next);
        return prev+next.cost*1;
    },0);
    if(formConfig.submitData.expenseType === "差旅费"){
        totalCost.value=+(tableCost+(formConfig.submitData.trafficAmount || 0)*1+(formConfig.submitData.foodAmount || 0)*1).toFixed(2);
    }else{
        totalCost.value=+tableCost.toFixed(2);
    };
};

/** 出差申请链接逻辑代码区域 */
// 查看基本信息时点击出差申请链接事件监听
const viewBusinessData=(item,val)=>{
    // console.log(item,val);
    getBusinessInfoData({
        procInsId:val
    }).then((res)=>{
        if(res.code === 200){
            // console.log(res);
            const data=JSON.parse(JSON.parse(res.data.customDataForm));
            viewBusinessConfig.viewData={...data};
            viewBusinessConfig.viewData.trafficType=typeof viewBusinessConfig.viewData.trafficType === "string" ?
                viewBusinessConfig.viewData.trafficType : viewBusinessConfig.viewData.trafficType.join(",");
            viewDialogConfig.isShow=true;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 关闭查看出差申请数据详情事件监听
const handleCloseViewDialogConfig=()=>{
    viewDialogConfig.isShow=false;
};
// 点击选择出差申请链接按钮事件监听
const selectBusinessData=()=>{
    getTableData();
    dialogConfig.isShow=true;
};
// 点击查询按钮事件监听
const searchData=()=>{
    tableConfigRef.value.setCurrentRow();
    selectedBusinessData.value=null;
    getTableData();
};
// 点击重置按钮事件监听
const resetData=(formRef)=>{
    tableConfigRef.value.setCurrentRow();
    selectedBusinessData.value=null;
    formRef.resetSearchData();
    tableConfig.paginationsObj.pageSize=10;
    tableConfig.paginationsObj.currentPage=1;
    getTableData();
};
// 关闭出差申请链接弹窗事件监听
const handleCloseDialog=()=>{
    tableConfigRef.value.setCurrentRow();
    selectedBusinessData.value=null;
    searchConfigRef.value.resetSearchData();
    tableConfig.paginationsObj.pageSize=10;
    tableConfig.paginationsObj.currentPage=1;
    dialogConfig.isShow=false;
};
// 出差申请链接弹窗确认按钮事件监听
const submitConfirm=async ()=>{
    if(selectedBusinessData.value){ // 当选中行数据时才可确认
        formConfig.submitData.businessLink=selectedBusinessData.value.nickName+Qilin.Date.formatDate(selectedBusinessData.value.startTime,"chinese")+"出差申请";
        busniessLinkId.value=selectedBusinessData.value.processInstanceId;
        formConfig.submitData.address=selectedBusinessData.value.address.split("-");
        formConfig.submitData.startTime=selectedBusinessData.value.startTime;
        formConfig.submitData.endTime=selectedBusinessData.value.endTime;
        formConfigRef.value.formValidateField("businessLink");
        formConfigRef.value.formValidateField("address");
        formConfigRef.value.formValidateField("startTime");
        formConfigRef.value.formValidateField("endTime");
        await blurSelectStartTime(null,formConfig.submitData.startTime);
        handleCloseDialog();
    }else{ //无选中行数据
        ElMessage({
            message:"请先点击选择行数据",
            type:"warning"
        });
    };
};
// 出差申请链接弹窗内表格行数据单选事件监听
const currentChangeData=(row,oldRow)=>{
    // console.log(row,oldRow);
    selectedBusinessData.value={...row};
};
// 切换页码事件监听
const changeCurrentPage=(page)=>{
    tableConfig.paginationsObj.currentPage=page;
    getTableData();
};
// 切换页码事件监听
const changeCurrentSize=(size)=>{
    tableConfig.paginationsObj.pageSize=size;
    getTableData();
};
// 初始化出差申请获取表格数据事件监听
const getTableData=()=>{
    let params={
        pageNum:tableConfig.paginationsObj.currentPage,
        pageSize:tableConfig.paginationsObj.pageSize
    };
    Object.keys(searchConfig.submitData).forEach((item)=>{
        if(searchConfig.submitData[item]){
            params[item]=searchConfig.submitData[item];
        };
    });
    if(params.date && params.date.length !== 0){
        params.startTime=params.date[0];
        params.endTime=params.date[1];
        Reflect.deleteProperty(params,"date");
    };
    getBusinessInExpenseData(params).then((res)=>{
        if(res.code === 200){
            tableConfig.tableData=res.data.rows;
            tableConfig.paginationsObj.total=res.data.total;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};

/** 费用申请链接逻辑代码区域 */
// 查看基本信息时点击费用申请链接事件监听
const viewCostApplicationData=(item,val)=>{
    getBusinessInfoData({
        procInsId:val
    }).then((res)=>{
        if(res.code === 200){
            // console.log(res);
            const data=JSON.parse(JSON.parse(res.data.customDataForm));
            viewCostConfig.viewData={...data};
            viewCostDialogConfig.isShow=true;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 关闭查看费用申请数据详情事件监听
const handleCloseViewCostDialogConfig=()=>{
    viewCostDialogConfig.isShow=false;
};
// 点击选择费用申请链接按钮事件监听
const selectCostApplicationData=()=>{
    getCostTableData();
    costDialogConfig.isShow=true;
};
// 点击查询按钮事件监听
const searchCostData=()=>{
    tableCostConfigRef.value.setCurrentRow();
    selectedCostApplicationData.value=null;
    getCostTableData();
};
// 点击重置按钮事件监听
const resetCostData=(formRef)=>{
    tableCostConfigRef.value.setCurrentRow();
    selectedCostApplicationData.value=null;
    formRef.resetSearchData();
    tableCostConfig.paginationsObj.pageSize=10;
    tableCostConfig.paginationsObj.currentPage=1;
    getCostTableData();
};
// 关闭费用申请链接弹窗事件监听
const handleCloseCostDialog=()=>{
    tableCostConfigRef.value.setCurrentRow();
    selectedCostApplicationData.value=null;
    searchCostConfigRef.value.resetSearchData();
    tableCostConfig.paginationsObj.pageSize=10;
    tableCostConfig.paginationsObj.currentPage=1;
    costDialogConfig.isShow=false;
};
// 费用申请链接弹窗确认按钮事件监听
const submitConfirmCost=()=>{
    if(selectedCostApplicationData.value){ // 当选中行数据时才可确认
        formConfig.submitData.costApplicationLink=selectedCostApplicationData.value.nickName+Qilin.Date.formatDate(selectedCostApplicationData.value.startTime,"chinese")+formConfig.submitData.expenseType+"用申请";
        costApplicationLinkId.value=selectedCostApplicationData.value.processInstanceId;
        formConfig.submitData.startTime=selectedCostApplicationData.value.startTime;
        formConfig.submitData.endTime=selectedCostApplicationData.value.endTime;
        formConfigRef.value.formValidateField("costApplicationLink");
        formConfigRef.value.formValidateField("startTime");
        formConfigRef.value.formValidateField("endTime");
        handleCloseCostDialog();
    }else{ //无选中行数据
        ElMessage({
            message:"请先点击选择行数据",
            type:"warning"
        });
    };
};
// 出差申请链接弹窗内表格行数据单选事件监听
const currentCostChangeData=(row,oldRow)=>{
    // console.log(row,oldRow);
    selectedCostApplicationData.value={...row};
};
// 切换页码事件监听
const changeCostCurrentPage=(page)=>{
    tableCostConfig.paginationsObj.currentPage=page;
    getCostTableData();
};
// 切换页码事件监听
const changeCostCurrentSize=(size)=>{
    tableCostConfig.paginationsObj.pageSize=size;
    getCostTableData();
};
// 初始化获取费用申请表格数据事件监听
const getCostTableData=()=>{
    let params={
        pageNum:tableCostConfig.paginationsObj.currentPage,
        pageSize:tableCostConfig.paginationsObj.pageSize,
        costType:formConfig.submitData.expenseType
    };
    Object.keys(searchCostConfig.submitData).forEach((item)=>{
        if(searchCostConfig.submitData[item]){
            params[item]=searchCostConfig.submitData[item];
        };
    });
    if(params.date && params.date.length !== 0){
        params.startTime=params.date[0];
        params.endTime=params.date[1];
        Reflect.deleteProperty(params,"date");
    };
    getCostApplicationData(params).then((res)=>{
        if(res.code === 200){
            tableCostConfig.tableData=res.data.rows;
            tableCostConfig.paginationsObj.total=res.data.total;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};

/** 费用明细逻辑代码区域 */
// 点击添加按钮事件监听
const addSpecification=()=>{
    renderCostType(formConfig.submitData.expenseType);
    specificationFormConfig.submitData.id="";
    specificationDialogConfig.title="新增费用";
    specificationDialogConfig.isShow=true;
};
// 点击附件预览事件监听
const previewFiles=(fileList)=>{
    previewFileList.value=fileList.map((item)=>{
        if(!item.fileName){ //若为之前服务器旧数据，则其fileName为空字符串，否则fileName有值
            const index=item.url.indexOf("/profile");
            return{
                fileName:item.url.slice(index),
                originalFilename:item.url.slice(index),
                newFileName:"",
                url:item.url
            };
        }else{ //新服务器新数据
            return {
                fileName:item.fileName,
                originalFilename:item.originalFilename,
                newFileName:item.newFileName,
                url:item.url
            };
        };
    });
    const index=previewFileList.value[0][imageOptions.serverOriginalName || "originalFilename" ].lastIndexOf(".");
    const fileType=previewFileList.value[0][imageOptions.serverOriginalName || "originalFilename" ].slice(index+1);
    if(["png","jpg","jpeg","gif"].includes(fileType)){
        previewWordRef.value.initPreview({
            isOnline:true,
            type:"image",
            url:previewFileList.value[0][imageOptions.serverUrl || "fileName"]
        });
    }else if(fileType === "docx" || fileType === "doc"){
        if(previewFileList.value[0][imageOptions.serverUrl || "fileName" ]){
            previewWordRef.value.initPreview({
                isOnline:false,
                type:"word",
                url:previewFileList.value[0][imageOptions.serverUrl || "fileName"]
            });
        }else{
            previewWordRef.value.initPreview({
                isOnline:true,
                type:"word",
                url:previewFileList.value[0][imageOptions.serverUrl || "fileName"]
            });
        };
    }else if(fileType === "xlsx" || fileType === "xls"){
        if(previewFileList.value[0][imageOptions.serverUrl || "fileName" ]){
            previewWordRef.value.initPreview({
                isOnline:false,
                type:"excel",
                url:previewFileList.value[0][imageOptions.serverUrl || "fileName"]
            });
        }else{
            previewWordRef.value.initPreview({
                isOnline:true,
                type:"excel",
                url:previewFileList.value[0][imageOptions.serverUrl || "fileName"]
            });
        };
    }else if(fileType === "pdf"){
        previewWordRef.value.initPreview({
            isOnline:true,
            type:"pdf",
            url:previewFileList.value[0][imageOptions.serverUrl || "fileName"]
        });
    };
};
// 关闭新增修改费用明细弹窗事件监听
const handleCloseSpecificationDialog=()=>{
    specificationFormConfigRef.value.formResetFields();
    specificationDialogConfig.isShow=false;
};
// 点击新增修改费用明细弹窗确认按钮事件监听
const submitConfirmSpecificationDialog=()=>{
    specificationFormConfigRef.value.formValidate((valid)=>{
        if(valid){
            if(specificationFormConfig.submitData.id !== ""){ //编辑费用明细时进入
                costTableConfig.tableData[specificationFormConfig.submitData.id]={...specificationFormConfig.submitData};
            }else{ //新增费用明细时进入
                costTableConfig.tableData.push({...specificationFormConfig.submitData});
            };
            handleCloseSpecificationDialog();
            calculateTotalCost();
        };
    });
};
// 点击费用明细表格中编辑按钮事件监听
const updateData=(row,item,scope)=>{
    // console.log(row,item,scope);
    renderCostType(formConfig.submitData.expenseType);
    specificationFormConfig.submitData={...row,id:scope.$index};
    specificationDialogConfig.title="修改明细";
    specificationDialogConfig.isShow=true;
};
// 点击费用明细表格中删除按钮事件监听
const deleteData=(row,item,scope)=>{
    ElMessageBox.confirm("是否删除该条费用数据？","删除",{
        cancelButtonText:"取消",
        confirmButtonText:"确定",
        type:"error",
        icon:"WarningFilled"
    }).then(()=>{
        costTableConfig.tableData.splice(scope.$index,1);
        calculateTotalCost();
    }).catch(()=>{
        console.log("点击取消");
    });
};
// 初始化渲染报销类型及其下拉数据集
const renderCostType=(type)=>{
    if(type === "差旅费"){
        specificationFormConfig.selectLists.costTypeList=[
            {
                label:"飞机票",
                value:"飞机票"
            },
            {
                label:"火车票",
                value:"火车票"
            },
            {
                label:"汽车票",
                value:"汽车票"
            },
            {
                label:"住宿票",
                value:"住宿票"
            }
        ];
        specificationFormConfig.submitData.costType="飞机票";
    }else if(type === "培训费"){
        specificationFormConfig.selectLists.costTypeList=[
            {
                label:"培训费",
                value:"培训费"
            }
        ];
        specificationFormConfig.submitData.costType="培训费";
    }else if(type === "办公费"){
        specificationFormConfig.selectLists.costTypeList=[
            {
                label:"办公费",
                value:"办公费"
            }
        ];
        specificationFormConfig.submitData.costType="办公费";
    }else if(type === "工会费"){
        specificationFormConfig.selectLists.costTypeList=[
            {
                label:"工会费",
                value:"工会费"
            }
        ];
        specificationFormConfig.submitData.costType="工会费";
    }else if(type === "体检费"){
        specificationFormConfig.selectLists.costTypeList=[
            {
                label:"入职体检",
                value:"入职体检"
            },
            {
                label:"年度体检",
                value:"年度体检"
            }
        ];
        specificationFormConfig.submitData.costType="入职体检";
    }else if(type === "其他"){
        specificationFormConfig.selectLists.costTypeList=[
            {
                label:"工会活动费",
                value:"工会活动费"
            },
            {
                label:"下午茶活动费",
                value:"下午茶活动费"
            },
            {
                label:"办公用品费",
                value:"办公用品费"
            },
            {
                label:"其他费用",
                value:"其他费用"
            }
        ];
        specificationFormConfig.submitData.costType="工会活动费";
    };
};

// 点击提交按钮事件监听
const submitData=()=>{
    formConfigRef.value.formValidate((valid)=>{
        // console.log(formConfig.submitData);
        if(flag.value){
            ElMessage({
                message:"该时间段已提交报销申请，请勿重复提交",
                type:"error"
            });
            return;
        };
        let formData={...formConfig.submitData,totalAmount:totalCost.value};
        formData.businessLink=busniessLinkId.value;
        let formObj={};
        let params={
            startTime:formData.startTime,
            endTime:formData.endTime,
            expenseType:formData.expenseType,
            workContent:formData.workContent,
            totalAmount:formData.totalAmount
        };
        if(formData.expenseType !== "差旅费"){ //若选的不是差旅费，则相关字段不需要传
            formObj.expenseType=formData.expenseType;
            formObj.startTime=formData.startTime;
            formObj.endTime=formData.endTime;
            formObj.workContent=formData.workContent;
            formObj.totalAmount=formData.totalAmount;
            formObj.otherFiles=formData.otherFiles;
            if(["办公费","工会费"].includes(formData.expenseType)){
                formObj.costApplicationLink=costApplicationLinkId.value;
            };
        }else{
            formData.address=formData.address.join("-");
            params.address=formData.address;
            formObj={...formData};
        };
        submitWithdrawProcess({
            procInsId:propsValue.processInfo.procInsId,
            taskId:propsValue.processInfo.taskId,
            comment:"",
            variables:{
                ...params,
                customDataForm:JSON.stringify({
                    formItem:formObj,
                    tableItem:costTableConfig.tableData,
                    employee:propsValue.formData.employee,
                    deptName:propsValue.formData.deptName,
                    nickName:propsValue.formData.nickName
                })
            }
        }).then((res)=>{
            if(res.code === 200){
                formConfigRef.value.formResetFields();
                router.push("/officeCenter/completed");
                ElMessage({
                    message:"提交报销申请成功",
                    type:"success"
                });
            }else{
                ElMessage({
                    message:res.msg || "系统错误",
                    type:"error"
                });
            };
        });
    });
};

// 查看组件配置项
const viewConfig=reactive({
    // isTable:true,
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"报销类型",
            value:"expenseType",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"出差申请链接",
            value:"businessLink",
            type:"link",
            labelSuffix:"：",
            isHide:false,
            click:viewBusinessData,
            col:12
        },
        {
            label:"费用申请链接",
            value:"costApplicationLink",
            type:"link",
            labelSuffix:"：",
            isHide:false,
            click:viewCostApplicationData,
            col:12
        },
        {
            label:"出差地点",
            value:"address",
            type:"text",
            labelSuffix:"：",
            isHide:false,
            col:12
        },
        {
            label:"开始时间",
            value:"startTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"结束时间",
            value:"endTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"是否申请出差补助",
            value:"subsidy",
            type:"text",
            labelSuffix:"：",
            isHide:false,
            col:12
        },
        {
            label:"交通补贴（￥）",
            value:"trafficAmount",
            type:"text",
            labelSuffix:"：",
            isHide:false,
            col:12
        },
        {
            label:"伙食补贴（￥）",
            value:"foodAmount",
            type:"text",
            labelSuffix:"：",
            isHide:false,
            col:12
        },
        {
            label:"报销事由",
            value:"workContent",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"情况说明",
            value:"reason",
            type:"text",
            labelSuffix:"：",
            isHide:true,
            col:24
        },
        {
            label:"报销金额（￥）",
            value:"totalAmount",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"其他附件",
            value:"otherFiles",
            type:"file",
            labelSuffix:"：",
            isHide:true,
            col:24
        }
    ],
    viewData:{
        expenseType:"",
        businessLink:"",
        costApplicationLink:"",
        address:"",
        startTime:"",
        endTime:"",
        subsidy:"",
        trafficAmount:"",
        foodAmount:"",
        workContent:"",
        reason:"",
        totalAmount:"",
        otherFiles:""
    }
});
// 表单配置项
const formConfig=reactive({
    elFormConfig:{
        labelWidth:"150px",
        style:"padding:20px 0 40px;"
    },
    itemConfig:[
        {
            labelName:"报销类型",
            dataName:"expenseType",
            type:"radio",
            dataListName:"expenseTypeList",
            width:24,
            change:changeExpenseType,
            validate:[
                {
                    required:true,
                    message:"请选择报销类型",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"出差申请链接",
            dataName:"businessLink",
            type:"slot",
            slotName:"businessLink",
            isHide:false,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请选择出差申请链接",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"费用申请链接",
            dataName:"costApplicationLink",
            type:"slot",
            slotName:"costApplicationLink",
            isHide:true,
            validate:[
                {
                    required:true,
                    message:"请选择费用申请链接",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"出差省市区",
            dataName:"address",
            type:"cascader",
            dataListName:"addressList",
            isHide:false,
            width:24,
            style:"width:300px;",
            validate:[
                {
                    required:true,
                    message:"请选择省市区",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"开始时间",
            dataName:"startTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,8,30,0),
            domWidth:300,
            width:24,
            change:blurSelectStartTime,
            validate:[
                {
                    required:true,
                    message:"请选择开始时间",
                    trigger:"blur"
                },
                {
                    validator:checkStartTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"结束时间",
            dataName:"endTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,17,30,0),
            domWidth:300,
            width:24,
            change:blurSelectEndTime,
            validate:[
                {
                    required:true,
                    message:"请选择结束时间",
                    trigger:"blur"
                },
                {
                    validator:checkEndTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"是否申请出差补助",
            dataName:"subsidy",
            type:"radio",
            dataListName:"subsidyList",
            isHide:false,
            width:24,
            change:changeSubsidy,
            validate:[
                {
                    required:true,
                    message:"请选择是否申请出差补助",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"交通补贴（￥）",
            dataName:"trafficAmount",
            type:"input",
            inputType:"number",
            isHide:false,
            decimal:2,
            width:24,
            style:"width:300px;",
            blur:calculateTotalCost,
            validate:[
                {
                    required:true,
                    message:"请输入交通补贴",
                    trigger:"blur"
                },
                {
                    pattern: /^[1-9]\d*\.?\d{0,2}|0\.\d*[1-9]\d*$/,
                    message:"数额不可为0或负值",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"伙食补贴（￥）",
            dataName:"foodAmount",
            type:"input",
            inputType:"number",
            isHide:false,
            decimal:2,
            width:24,
            style:"width:300px;",
            blur:calculateTotalCost,
            validate:[
                {
                    required:true,
                    message:"请输入伙食补贴",
                    trigger:"blur"
                },
                {
                    pattern: /^[1-9]\d*\.?\d{0,2}|0\.\d*[1-9]\d*$/,
                    message:"数额不可为0或负值",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"报销事由",
            dataName:"workContent",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请输入报销事由",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"情况说明",
            dataName:"reason",
            type:"input",
            inputType:"textarea",
            placeholder:"报销申请需在出差完成后7天内完成提交，您本次流程超出规定提交时间，请详细补充情况说明",
            isHide:true,
            rows:3,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请输入情况说明",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"其他附件",
            dataName:"otherFiles",
            type:"upload",
            fileList:[],
            fileSize:20 * 1000 * 1000,
            fileType:["pdf","xls","xlsx","doc","docx","png","jpg","jpeg","gif"],
            fileTypeText:"支持pdf、excel、word和png等图片文件格式",
            multiple:true,
            isHide:true,
            limit:20
        }
    ],
    selectLists:{
        expenseTypeList:[ //报销类型数据集
            {
                label:"差旅费",
                value:"差旅费"
            },
            {
                label:"培训费",
                value:"培训费"
            },
            {
                label:"办公费",
                value:"办公费"
            },
            {
                label:"工会费",
                value:"工会费"
            },
            {
                label:"体检费",
                value:"体检费"
            },
            {
                label:"其他",
                value:"其他"
            },
        ],
        addressList:getCountryAddressData(),
        subsidyList:[ //是否申请出差补助数据集
            {
                label:"是",
                value:"是"
            },
            {
                label:"否",
                value:"否"
            }
        ]
    },
    submitData:{
        expenseType:"差旅费",
        businessLink:"",
        costApplicationLink:"",
        address:"",
        startTime:"",
        endTime:"",
        subsidy:"是",
        trafficAmount:0,
        foodAmount:0,
        workContent:"",
        reason:"",
        otherFiles:""
    }
});
// 出差申请链接弹窗配置项
const dialogConfig=reactive({
    isShow:false,
    title:"出差申请链接",
    width:800
});
// 出差申请链接弹窗内搜索表单配置项
const searchConfig=reactive({
    itemConfig:[
        {
            labelName:"申请时间范围",
            dataName:"date",
            type:"date",
            dateType:"datetimerange",
            width:380
        }
    ],
    buttonConfig:[
        {
            btnName:"查询",
            type:"primary",
            size:"default",
            formRefName:searchConfigRef,
            click:searchData
        },
        {
            btnName:"重置",
            type:"default",
            size:"default",
            formRefName:searchConfigRef,
            click:resetData
        }
    ],
    submitData:{
        date:""
    }
});
// 出差申请链接弹窗内表格配置项
const tableConfig=reactive({
    elTableConfig:{
        border:true,
        highlightCurrentRow:true
    },
    headerConfig:[
        {
            label:"序号",
            type:"sortIndex",
            align:"center",
            width:55
        },
        {
            label:"工号",
            prop:"employee",
            type:"text",
            align:"center",
            ellipsis:true,
            width:80
        },
        {
            label:"姓名",
            prop:"nickName",
            type:"text",
            align:"center",
            ellipsis:true,
            width:80
        },
        {
            label:"部门",
            prop:"deptName",
            type:"text",
            align:"center",
            ellipsis:true,
            width:100
        },
        {
            label:"申请时间",
            prop:"applyTime",
            type:"text",
            align:"center",
            width:160
        },
        {
            label:"开始时间",
            prop:"startTime",
            type:"text",
            align:"center",
            width:160
        },
        {
            label:"结束时间",
            prop:"endTime",
            type:"text",
            align:"center",
            width:160
        },
        {
            label:"交通工具",
            prop:"trafficType",
            type:"slot",
            slotName:"trafficType",
            align:"center",
            ellipsis:true,
            width:100
        },
        {
            label:"出差事由",
            prop:"workContent",
            type:"text",
            align:"center",
            ellipsis:true,
            width:100
        },
        {
            label:"状态",
            prop:"finishFlag",
            type:"slot",
            slotName:"finishFlag",
            align:"center",
            ellipsis:true,
            width:80
        }
    ],
    tableData:[],
    paginationsObj:{
        currentPage:1, //当前页
        pageSize:10, //当前每页数量
        pageSizes:[10,20,30], //每页显示多少条选项集
        total:0, //表格总数量
        layout:"total,sizes,prev,pager,next,jumper"
    }
});
// 费用申请表格配置项
const costTableConfig=reactive({
    elTableConfig:{
        border:true
    },
    headerConfig:[
        {
            label:"序号",
            type:"sortIndex",
            align:"center",
            width:55
        },
        {
            label:"费用类型",
            prop:"costType",
            type:"text",
            align:"center",
            width:90
        },
        {
            label:"消费金额（￥）",
            prop:"cost",
            type:"text",
            align:"center",
            width:130
        },
        {
            label:"附件",
            prop:"fileUrls",
            type:"slot",
            slotName:"fileUrls",
            align:"center"
        },
        {
            label:"操作",
            type:"operate",
            align:"center",
            width:160,
            operateOptions:[
                {
                    buttonName:"编辑",
                    buttonType:"primary",
                    buttonSize:"default",
                    text:true,
                    buttonEvent:updateData
                },
                {
                    buttonName:"删除",
                    buttonType:"primary",
                    buttonSize:"default",
                    text:true,
                    buttonEvent:deleteData
                }
            ]
        }
    ],
    tableData:[],
    paginationsObj:{
        isHide:true,
        currentPage:1, //当前页
        pageSize:10, //当前每页数量
        pageSizes:[10,20,30], //每页显示多少条选项集
        total:0, //表格总数量
        layout:"total,sizes,prev,pager,next,jumper"
    }
});
// 新增修改费用弹窗配置项
const specificationDialogConfig=reactive({
    isShow:false,
    title:"新增费用",
    width:700
});
// 新增修改费用表单配置项
const specificationFormConfig=reactive({
    elFormConfig:{
        labelWidth:"140px"
    },
    itemConfig:[
        {
            labelName:"报销类型",
            dataName:"costType",
            type:"radio",
            dataListName:"costTypeList",
            width:24,
            validate:[
                {
                    required:true,
                    message:"请选择报销类型",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"消费金额（￥）",
            dataName:"cost",
            type:"input",
            inputType:"number",
            decimal:2,
            width:24,
            style:"width:200px;",
            validate:[
                {
                    required:true,
                    message:"请输入消费金额",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"附件",
            dataName:"fileUrls",
            type:"upload",
            fileList:[],
            fileSize:20 * 1000 * 1000,
            fileType:["pdf","xls","xlsx","doc","docx","png","jpg","jpeg","gif"],
            fileTypeText:"支持pdf、excel、word和png等图片文件格式",
            multiple:true,
            limit:20
        }
    ],
    selectLists:{
        costTypeList:[]
    },
    submitData:{
        costType:"",
        cost:"",
        fileUrls:[]
    }
});
// 查看出差申请数据弹窗配置项
const viewDialogConfig=reactive({
    isShow:false,
    title:"出差申请数据",
    isHideFooter:true
});
// 查看出差申请数据组件配置项
const viewBusinessConfig=reactive({
    isTable:true,
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"工号",
            value:"employee",
            type:"text",
            col:12
        },
        {
            label:"姓名",
            value:"nickName",
            type:"text",
            col:12
        },
        {
            label:"部门",
            value:"deptName",
            type:"text",
            col:12
        },
        {
            label:"出差开始时间",
            value:"startTime",
            type:"text",
            labelEllipsis:true,
            col:12
        },
        {
            label:"出差结束时间",
            value:"endTime",
            type:"text",
            labelEllipsis:true,
            col:12
        },
        {
            label:"交通工具",
            value:"trafficType",
            type:"text",
            col:12
        },
        {
            label:"出差地点",
            value:"address",
            type:"text",
            col:24
        },
        {
            label:"出差事由",
            value:"workContent",
            type:"text",
            col:24
        }
    ],
    viewData:{
        employee:"",
        nickName:"",
        deptName:"",
        startTime:"",
        endTime:"",
        trafficType:"",
        address:"",
        workContent:""
    }
});
// 费用申请链接弹窗配置项
const costDialogConfig=reactive({
    isShow:false,
    title:"费用申请链接",
    width:800
});
// 费用申请链接弹窗内搜索表单配置项
const searchCostConfig=reactive({
    itemConfig:[
        {
            labelName:"申请时间范围",
            dataName:"date",
            type:"date",
            dateType:"daterange",
            format:"YYYY-MM-DD",
            width:300
        }
    ],
    buttonConfig:[
        {
            btnName:"查询",
            type:"primary",
            size:"default",
            formRefName:searchCostConfigRef,
            click:searchCostData
        },
        {
            btnName:"重置",
            type:"default",
            size:"default",
            formRefName:searchCostConfigRef,
            click:resetCostData
        }
    ],
    submitData:{
        date:""
    }
});
// 费用申请链接弹窗内表格配置项
const tableCostConfig=reactive({
    elTableConfig:{
        border:true,
        highlightCurrentRow:true
    },
    headerConfig:[
        {
            label:"序号",
            type:"sortIndex",
            align:"center",
            width:55
        },
        {
            label:"工号",
            prop:"employee",
            type:"text",
            align:"center",
            ellipsis:true,
            width:80
        },
        {
            label:"姓名",
            prop:"nickName",
            type:"text",
            align:"center",
            ellipsis:true,
            width:80
        },
        {
            label:"部门",
            prop:"deptName",
            type:"text",
            align:"center",
            ellipsis:true,
            width:100
        },
        {
            label:"申请时间",
            prop:"applyTime",
            type:"text",
            align:"center",
            width:160
        },
        {
            label:"开始时间",
            prop:"startTime",
            type:"text",
            align:"center",
            width:160
        },
        {
            label:"结束时间",
            prop:"endTime",
            type:"text",
            align:"center",
            width:160
        },
        {
            label:"费用类型",
            prop:"costType",
            type:"text",
            align:"center",
            width:90
        },
        {
            label:"费用金额（元）",
            prop:"amount",
            type:"text",
            align:"center",
            width:130
        },
        {
            label:"费用事由",
            prop:"workContent",
            type:"text",
            align:"center",
            ellipsis:true,
            width:100
        },
        {
            label:"状态",
            prop:"finishFlag",
            type:"slot",
            slotName:"finishFlag",
            align:"center",
            ellipsis:true,
            width:80
        }
    ],
    tableData:[],
    paginationsObj:{
        currentPage:1, //当前页
        pageSize:10, //当前每页数量
        pageSizes:[10,20,30], //每页显示多少条选项集
        total:0, //表格总数量
        layout:"total,sizes,prev,pager,next,jumper"
    }
});
// 查看费用申请数据弹窗配置项
const viewCostDialogConfig=reactive({
    isShow:false,
    title:"费用申请数据",
    isHideFooter:true
});
// 查看费用申请数据组件配置项
const viewCostConfig=reactive({
    isTable:true,
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"工号",
            value:"employee",
            type:"text",
            col:12
        },
        {
            label:"姓名",
            value:"nickName",
            type:"text",
            col:12
        },
        {
            label:"部门",
            value:"deptName",
            type:"text",
            col:12
        },
        {
            label:"开始时间",
            value:"startTime",
            type:"text",
            labelEllipsis:true,
            col:12
        },
        {
            label:"结束时间",
            value:"endTime",
            type:"text",
            labelEllipsis:true,
            col:12
        },
        {
            label:"费用类型",
            value:"costType",
            type:"text",
            col:12
        },
        {
            label:"费用金额（元）",
            value:"totalAmount",
            type:"text",
            labelEllipsis:true,
            col:24
        },
        {
            label:"费用事由",
            value:"workContent",
            type:"text",
            col:24
        }
    ],
    viewData:{
        employee:"",
        nickName:"",
        deptName:"",
        startTime:"",
        endTime:"",
        costType:"",
        amount:"",
        workContent:""
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    if(propsValue.processInfo.formDataEdit){
        const draftData={...propsValue.formData};
        formConfig.submitData=draftData.formItem;
        // console.log(formConfig.submitData);return;
        costTableConfig.tableData=draftData.tableItem;
        changeExpenseType(formConfig.submitData.expenseType,null,"draft");
        calculateTotalCost();
        if(formConfig.submitData.expenseType === "差旅费"){
            formConfig.submitData.address=typeof formConfig.submitData.address === "string" ?
                formConfig.submitData.address.split("-") : formConfig.submitData.address;
            busniessLinkId.value=formConfig.submitData.businessLink; //防止出差链接提交丢失问题
        }else if(["办公费","工会费"].includes(formConfig.submitData.expenseType)){
            costApplicationLinkId.value=formConfig.submitData.costApplicationLink;
        };
        if(formConfig.submitData.subsidy === "否"){
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "trafficAmount" || item.dataName === "foodAmount"){
                    item.isHide=true;
                };
            });
        };
        if(formConfig.submitData.reason){ //若有情况说明，则表示该数据为申请开始时间超过距离当前时间七日
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "reason"){
                    item.isHide=false;
                };
            });
            formConfigRef.value.createFormRules();
        };
        if(formConfig.submitData.otherFiles && formConfig.submitData.otherFiles.length > 0){
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "otherFiles"){
                    item.isHide=false;
                };
            });
        };
    }else{
        const draftData={...propsValue.formData};
        viewConfig.viewData=draftData.formItem;
        costTableConfig.tableData=draftData.tableItem;
        costTableConfig.headerConfig[costTableConfig.headerConfig.length-1].isHide=true;
        if(viewConfig.viewData.expenseType === "差旅费"){
            viewConfig.itemConfig.forEach((item)=>{
                if(["businessLink","address","subsidy","trafficAmount","foodAmount"].includes(item.value)){
                    item.isHide=false;
                };
                if(item.value === "reason"){
                    item.isHide= viewConfig.viewData["reason"] ? false : true;
                };
                if(item.value === "costApplicationLink"){
                    item.isHide=true;
                };
            });
        }else{
            viewConfig.itemConfig.forEach((item)=>{
                if(["businessLink","address","subsidy","trafficAmount","foodAmount","reason"].includes(item.value)){
                    item.isHide=true;
                };
                if(item.value === "costApplicationLink"){
                    if(["办公费","工会费"].includes(viewConfig.viewData.expenseType)){
                        item.isHide=false;
                    }else{
                        item.isHide=true;
                    };
                };
            });
        };
        if(viewConfig.viewData.subsidy === "否"){
            viewConfig.itemConfig.forEach((item)=>{
                if(["trafficAmount","foodAmount"].includes(item.value)){
                    item.isHide=true;
                };
            });
        };
        if(viewConfig.viewData.otherFiles && viewConfig.viewData.otherFiles.length>0){
            viewConfig.itemConfig.forEach((item)=>{
                if(item.value === "otherFiles"){
                    item.isHide=false;
                };
            });
        };
    };
});
// 暴露子组件方法
defineExpose({
    submitData
});

</script>

<style lang="scss" scoped>
.qilin-expenseApplication{
    width:100%;
    height:100%;
    .qilin-expenseApplication-content-cost{
        width:100%;
        margin:auto;
        border-top:1px solid $border-color;
        padding:20px;
        >.qilin-expenseApplication-content-cost-header{
            display:flex;
            justify-content:space-between;
            align-items:center;
            margin-bottom:15px;
            >span{
                color:$text-color-85;
            }
            >.addCost{
                &:hover{
                    background-color:$primary-text-color;
                    color:#fff;
                }
            }
        }
        >.qilin-expenseApplication-content-cost-footer{
            display:flex;
            justify-content:flex-end;
            align-items:center;
            margin-top:15px;
            >.qilin-expenseApplication-content-cost-footer-total{
                >span{
                    color:$delete-color;
                    font-size:$medium-text-size;
                }
            }
        }
    }
    .busniessLink{
        margin-left:10px;
        &:hover{
            background-color:$primary-text-color;
            color:#fff;
        }
    }
}
</style>
