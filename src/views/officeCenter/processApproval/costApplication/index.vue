<template>
    <div class="qilin-costApplication hide-scrollbar">
        <template v-if="!propsValue.processInfo.formDataEdit">
            <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
        </template>
        <template v-else>
            <QilinForm v-model:formConfig="formConfig" ref="formConfigRef"></QilinForm>
        </template>
    </div>
</template>

<script setup>
import {
    submitWithdrawProcess
} from "@/api/officeCenter/processApproval/index.js";
import {getCountryAddressData} from "@/utils/common.js";
import { login } from "@/store/login.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
const loginStore=login();

/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);
// 获取表单组件元素DOM
const formConfigRef=ref(null);


/*
    计算属性等代码区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);


/*
    逻辑脚本代码区域
*/
// 校验费用开始时间事件监听
const checkStartTime=(rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.endTime && new Date(formConfig.submitData.endTime).getTime() < new Date(value).getTime()){
            callback("开始时间不得大于结束时间");
        }else{
            callback();
        };
    };
};
// 校验费用结束时间事件监听
const checkEndTime=(rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.startTime && new Date(formConfig.submitData.startTime).getTime() > new Date(value).getTime()){
            callback("结束时间不得小于开始时间");
        }else{
            callback();
        };
    };
};
// 初始化渲染由草稿进入时数据事件监听
const renderCostData=(id)=>{
    getCostDataByDraft(id).then((res)=>{
        if(res.code === 200){
            formConfig.submitData=JSON.parse(res.data.draftData);
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 点击提交按钮事件监听
const submitData=()=>{
    formConfigRef.value.formValidate((valid)=>{
        if(valid){
            submitWithdrawProcess({
                procInsId:propsValue.processInfo.procInsId,
                taskId:propsValue.processInfo.taskId,
                comment:"",
                variables:{
                    startTime:formConfig.submitData.startTime,
                    endTime:formConfig.submitData.endTime,
                    customDataForm:JSON.stringify({
                        ...formConfig.submitData,
                        employee:propsValue.formData.employee,
                        deptName:propsValue.formData.deptName,
                        nickName:propsValue.formData.nickName
                    })
                }
            }).then((res)=>{
                if(res.code === 200){
                    formConfigRef.value.formResetFields();
                    emits("backTable");
                    ElMessage({
                        message:"提交待办费用申请成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

// 查看组件配置项
const viewConfig=reactive({
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"费用类型",
            value:"costType",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"开始时间",
            value:"startTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"结束时间",
            value:"endTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"费用金额（元）",
            value:"totalAmount",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"费用事由",
            value:"workContent",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"附件",
            value:"fileUrls",
            type:"file",
            labelSuffix:"：",
            col:24
        }
    ],
    viewData:{
        costType:"",
        startTime:"",
        endTime:"",
        totalAmount:"",
        workContent:""
    }
});
// 表单配置项
const formConfig=reactive({
    elFormConfig:{
        labelWidth:"150px",
        style:"padding:20px 0 40px;"
    },
    itemConfig:[
        {
            labelName:"费用类型",
            dataName:"costType",
            type:"radio",
            dataListName:"costTypeList",
            width:24,
            validate:[
                {
                    required:true,
                    message:"请选择费用类型",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"开始时间",
            dataName:"startTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,8,30,0),
            width:24,
            domWidth:300,
            validate:[
                {
                    required:true,
                    message:"请选择请假开始时间",
                    trigger:"blur"
                },
                {
                    validator:checkStartTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"结束时间",
            dataName:"endTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,17,30,0),
            width:24,
            domWidth:300,
            validate:[
                {
                    required:true,
                    message:"请选择请假结束时间",
                    trigger:"blur"
                },
                {
                    validator:checkEndTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"费用金额（元）",
            dataName:"totalAmount",
            type:"input",
            inputType:"number",
            decimal:2,
            width:24,
            style:"width:300px;",
            validate:[
                {
                    required:true,
                    message:"请输入费用金额",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"费用事由",
            dataName:"workContent",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请输入费用事由",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"附件",
            dataName:"fileUrls",
            type:"upload",
            fileList:[],
            fileSize:20 * 1000 * 1000,
            fileType:["pdf","xls","xlsx","doc","docx","png","jpg","jpeg","gif"],
            fileTypeText:"支持pdf、excel、word和png等图片文件格式",
            multiple:true,
            limit:20
        }
    ],
    selectLists:{
        costTypeList:[ //费用类型下拉数据集
            {
                label:"节日福利",
                value:"节日福利"
            },
            {
                label:"测评费",
                value:"测评费"
            },
            {
                label:"工会费",
                value:"工会费"
            },
            {
                label:"背调费",
                value:"背调费"
            },
            {
                label:"办公费",
                value:"办公费"
            },
            {
                label:"其他",
                value:"其他"
            }
        ]
    },
    submitData:{
        costType:"节日福利",
        startTime:"",
        endTime:"",
        totalAmount:0,
        workContent:"",
        fileUrls:""
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    if(propsValue.processInfo.formDataEdit){
        formConfig.submitData={...propsValue.formData};
    }else{
        viewConfig.viewData={...propsValue.formData};
    };
});
// 暴露子组件方法
defineExpose({
    submitData
});

</script>

<style lang="scss" scoped>
.qilin-costApplication{
    width:100%;
    height:100%;
}
</style>
