<template>
    <div class="qilin-overtimeApplication hide-scrollbar">
        <template v-if="!propsValue.processInfo.formDataEdit">
            <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
        </template>
        <template v-else>
            <QilinForm v-model:formConfig="formConfig" ref="formConfigRef"></QilinForm>
        </template>
    </div>
</template>

<script setup>
import Qilin from "qilin-utils";
import {
    getOvertimeTotalCount,
    calculateOvertimeConfilct,
    judgeOvertimeTypeData
} from "@/api/officeCenter/overtimeApplication/index.js";
import {
    submitWithdrawProcess
} from "@/api/officeCenter/processApproval/index.js";
import { login } from "@/store/login.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
const loginStore=login();

/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);
// 获取表单组件元素DOM
const formConfigRef=ref(null);
//当前登录人岗级
const customRank=loginStore.userInfo.user.customRank ? Number(loginStore.userInfo.user.customRank.slice(1)) : 1 ;
// 本月加班总时长
let overtimeAllCount=ref(0);
// 本次流程初始加班时长
let currentOvertimeCount=ref(0);
// 是否重复提交加班时间
let flag=ref(false);


/*
    计算属性等代码区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);


/*
    逻辑脚本代码区域
*/
// 自定义加班开始时间校验方法监听
const checkOvertimeStartTime=(rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.endTime && new Date(formConfig.submitData.endTime).getMonth() !== new Date(value).getMonth()){
            callback("不允许提交隔月加班申请");
        }else if(formConfig.submitData.endTime && new Date(formConfig.submitData.endTime).getTime() < new Date(value).getTime()){
            callback("加班开始时间不得大于加班结束时间");
        }else{
            callback();
        };
    };
};
// 自定义加班结束时间校验方法监听
const checkOvertimeEndTime=(rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.startTime && new Date(formConfig.submitData.startTime).getMonth() !== new Date(value).getMonth()){
            callback("不允许提交隔月加班申请");
        }else if(formConfig.submitData.startTime && new Date(formConfig.submitData.startTime).getTime() > new Date(value).getTime()){
            callback("加班结束时间不得小于加班开始时间");
        }else{
            callback();
        };
    };
};
// 选择加班开始时间失去焦点事件监听
const blurSelectStartTime=async (item,e)=>{
    await getOvertimeAllCount(e);
    // formConfig.submitData.monthOverTime = overtimeAllCount.value;
    if(e && formConfig.submitData.endTime){
        getOvertimeTypeData(e,formConfig.submitData.endTime);
        calculateOvertimeConfilctData();
        let minutes=(new Date(formConfig.submitData.endTime).getTime()-new Date(e).getTime())/1000/60;
        let m=Math.floor(minutes/60);
        let s=minutes%60 < 30 ? 0 : 0.5;
        formConfig.submitData.overTime=m+s;
        formConfig.submitData.monthOverTime = formConfig.submitData.monthOverTime+m+s-currentOvertimeCount.value;
    };
};
// 选择加班结束时间失去焦点事件监听
const blurSelectEndTime= (item,e)=>{
    if((new Date(e).getTime() + 2*24*60*60*1000) < new Date().getTime()){
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "reason"){
                ite.isHide=false;
                ite.validate=[
                    {
                        required:true,
                        message:"请输入情况说明",
                        trigger:"blur"
                    }
                ];
            };
        });
    }else{
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "reason"){
                ite.isHide=true;
                ite.validate=[];
            };
        });
    };
    formConfigRef.value.createFormRules();
    formConfig.submitData.monthOverTime = overtimeAllCount.value;
    if(e && formConfig.submitData.startTime){
        getOvertimeTypeData(formConfig.submitData.startTime,e);
        calculateOvertimeConfilctData();
        let minutes=(new Date(e).getTime() - new Date(formConfig.submitData.startTime).getTime())/1000/60;
        let m=Math.floor(minutes/60);
        let s=minutes%60 < 30 ? 0 : 0.5;
        formConfig.submitData.overTime=m+s;
        formConfig.submitData.monthOverTime = formConfig.submitData.monthOverTime+m+s-currentOvertimeCount.value;
    };
};
// 加班时长失去焦点事件监听
const blurOvertimeCount=async (item,e)=>{
    if(e.target.value){
        await getOvertimeAllCount(formConfig.submitData.startTime);
        formConfig.submitData.monthOverTime = formConfig.submitData.monthOverTime+Number(e.target.value)-currentOvertimeCount.value;
    }else{
        await getOvertimeAllCount(formConfig.submitData.startTime);
    };
};
// 获取本月加班总时长事件监听
const getOvertimeAllCount=async (startTime=formConfig.submitData.startTime)=>{
    await getOvertimeTotalCount({
        startTime
    }).then((res)=>{
        if(res.code === 200){
            formConfig.submitData.monthOverTime=overtimeAllCount.value=res.data || 0 ;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 判断获取加班类型事件监听
const getOvertimeTypeData=(startTime,endTime)=>{
    judgeOvertimeTypeData({
        startTime,
        endTime
    }).then((res)=>{
        if(res.code === 200){
            formConfig.submitData.overType=res.data;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 计算加班时间有无冲突事件监听
const calculateOvertimeConfilctData=()=>{
    calculateOvertimeConfilct({
        procInsId:propsValue.processInfo.procInsId,
        processKey:1,
        params:{
            startTime:formConfig.submitData.startTime,
            endTime:formConfig.submitData.endTime
        }
    }).then((res)=>{
        if(res.code === 200){
            // console.log(res);
            if(!res.data){
                ElMessage({
                    message:"该时间段已提交加班申请，请勿重复提交",
                    type:"error"
                });
            };
            flag.value=!res.data;
        };
    });
};
// 提交申请事件监听
const submitData=()=>{
    formConfigRef.value.formValidate((valid)=>{
        // console.log(formConfig.submitData);return;
        if(valid){
            if(formConfig.submitData.overTime === 0){
                ElMessage({
                    message:"加班时长须大于0",
                    type:"warning"
                });
                return;
            }else if((formConfig.submitData.monthOverTime || 0) > 36){
                ElMessage({
                    message:"当月加班时长不可超过36小时",
                    type:"warning"
                });
                return;
            }else if(flag.value){
                ElMessage({
                    message:"该时间段已提交加班申请，请勿重复提交",
                    type:"error"
                });
                return;
            };
            submitWithdrawProcess({
                procInsId:propsValue.processInfo.procInsId,
                taskId:propsValue.processInfo.taskId,
                comment:"",
                variables:{
                    startTime:formConfig.submitData.startTime,
                    endTime:formConfig.submitData.endTime,
                    overTime:formConfig.submitData.overTime,
                    customDataForm:JSON.stringify(formConfig.submitData)
                }
            }).then((res)=>{
                if(res.code === 200){
                    formConfigRef.value.formResetFields();
                    emits("backTable");
                    ElMessage({
                        message:"提交待办加班申请成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

// 查看组件配置项
const viewConfig=reactive({
    // isTable:true,
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"加班类型",
            value:"overType",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"加班补偿方式",
            value:"changeType",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"加班开始时间",
            value:"startTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"加班结束时间",
            value:"endTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"加班时长（小时）",
            value:"overTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"加班总时长（小时）",
            value:"monthOverTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"加班事由",
            value:"workContent",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"情况说明",
            value:"reason",
            type:"text",
            isHide:true,
            labelSuffix:"：",
            col:24
        },
        {
            label:"附件",
            value:"fileUrls",
            type:"image",
            labelSuffix:"：",
            col:24
        }
    ],
    viewData:{
        overType:"",
        changeType:"",
        startTime:"",
        endTime:"",
        overTime:"",
        monthOverTime:"",
        workContent:"",
        reason:"",
        fileUrls:""
    }
});
// 表单配置项
const formConfig=reactive({
    elFormConfig:{
        labelWidth:"180px",
        style:"padding:20px 0 40px;"
    },
    itemConfig:[
        {
            dataName:"changeType",
            labelName:"加班补偿方式",
            dataListName:"overtimeCompensateWayList",
            type:"radio",
            width:24
        },
        {
            dataName:"startTime",
            labelName:"加班开始时间",
            type:"date",
            dateType:"datetime",
            width:24,
            change:blurSelectStartTime,
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,17,30,0),
            domWidth:300,
            validate:[
                {
                    required:true,
                    message:"请选择加班开始时间",
                    trigger:"blur"
                },
                {
                    validator:checkOvertimeStartTime,
                    trigger:"blur"
                }
            ]
        },
        {
            dataName:"endTime",
            labelName:"加班结束时间",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,19,30,0),
            domWidth:300,
            width:24,
            change:blurSelectEndTime,
            validate:[
                {
                    required:true,
                    message:"请选择加班结束时间",
                    trigger:"blur"
                },
                {
                    validator:checkOvertimeEndTime,
                    trigger:"blur"
                }
            ]
        },
        {
            dataName:"overType",
            labelName:"加班类型",
            dataListName:"overtimeTypeList",
            type:"radio",
            disabled:true,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请选择加班类型",
                    trigger:blur
                }
            ]
        },
        {
            dataName:"overTime",
            labelName:"加班时长（小时）",
            type:"input",
            inputType:"number",
            decimal:1,
            disabled:false,
            blur:blurOvertimeCount,
            width:24,
            style:"width:300px;"
        },
        {
            dataName:"monthOverTime",
            labelName:"本月加班总时长（小时）",
            type:"input",
            inputType:"number",
            disabled:true,
            width:24,
            style:"width:300px;"
        },
        {
            dataName:"workContent",
            labelName:"加班事由",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请填写加班事由",
                    trigger:"blur"
                }
            ]
        },
        {
            dataName:"reason",
            labelName:"情况说明",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            isHide:true,
            placeholder:"加班申请需在实际加班完成后2天内完成提交，您本次流程超出规定提交时间，请详细补充情况说明。",
            validate:[]
        },
        {
            dataName:"fileUrls",
            labelName:"上传图片",
            type:"image",
            fileList:[],
            width:24,
            isHide:false,
            fileType:["png","jpg","jpeg","gif"],
            fileSize:20 * 1000 * 1000,
            multiple:true,
            validate:[]
        }
    ],
    selectLists:{
        overtimeTypeList:[
            {
                label:"工作日加班",
                value:"工作日加班"
            },
            {
                label:"双休日加班",
                value:"双休日加班"
            },
            {
                label:"法定节假日加班",
                value:"法定节假日加班"
            }
        ],
        overtimeCompensateWayList:[
            {
                label:"加班费",
                value:"加班费",
                isHide:customRank >= 5.5
            },
            {
                label:"调休",
                value:"调休"
            }
        ]
    },
    // buttonConfig:[
    //     {
    //         btnName:"提交",
    //         type:"primary",
    //         size:"default",
    //         style:"margin-left:160px;",
    //         formRefName:formConfigRef,
    //         // click:submitData
    //     },
    //     {
    //         btnName:"保存",
    //         type:"default",
    //         size:"default",
    //         formRefName:formConfigRef,
    //         // click:saveData
    //     }
    // ],
    submitData:{
        overType:"", //加班类型
        changeType:customRank >= 5.5 ? "调休" : "加班费", //加班补偿方式
        startTime:"", //加班开始时间
        endTime:"", //加班结束时间
        overTime:"", //实际加班时长
        monthOverTime:"", //加班总时长
        workContent:"", //加班事由
        fileUrls:[], //上传附件
        reason:"",
   }
});


/*
    生命周期等代码区域
*/
onMounted(async ()=>{
    if(propsValue.processInfo.formDataEdit){
        formConfig.submitData={...propsValue.formData};
        await getOvertimeAllCount();
        currentOvertimeCount.value=formConfig.submitData.overTime;
        if(formConfig.submitData.reason){ //若有情况说明，则表示该数据超出两天时间限制提出的申请
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "reason"){
                    item.isHide=false;
                    item.validate=[
                        {
                            required:true,
                            message:"请输入情况说明",
                            trigger:"blur"
                        }
                    ];
                };
            });
            formConfigRef.value.createFormRules();
        };
    }else{
        viewConfig.viewData={...propsValue.formData};
        viewConfig.itemConfig.forEach((item)=>{
            if(item.value === "reason"){
                item.isHide= viewConfig.viewData["reason"] ? false : true;
            };
        });
    };
});
// 暴露子组件方法
defineExpose({
    submitData
});

</script>

<style lang="scss" scoped>
.qilin-overtimeApplication{
    width:100%;
    height:100%;
}
</style>
