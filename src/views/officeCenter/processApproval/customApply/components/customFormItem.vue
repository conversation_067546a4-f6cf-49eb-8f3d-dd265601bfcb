<script>
import MobileLayerAutoForm from "./mobileLayerAutoForm.js";
import Qilin from "qilin-utils";

const formItem=new MobileLayerAutoForm();

const layouts = {
    colFormItem(h,itemConfig,props,attrs,emit){
        let labelWidth = itemConfig.labelWidth;
        if(!itemConfig.showLabel){
            labelWidth = 0;
        };
        let config = Qilin.Common.deepCopy(itemConfig,{});
        Reflect.deleteProperty(config,"label");
        return h(
            resolveComponent("el-col"),
            {
                span:itemConfig.span
            },
            {
                default:()=>{
                    return h(
                        resolveComponent("el-form-item"),
                        {
                            labelWidth:(itemConfig.tag !== "h1" && itemConfig.tag !== "h2") ?
                                (labelWidth ? labelWidth : null) :
                                (itemConfig.tag === "h1" ? 1 : 10),
                            label:(itemConfig.tag !== "h1" && itemConfig.tag !== "h2" && itemConfig.showLabel ? itemConfig.label : "") || "",
                            prop:itemConfig.vModel,
                            // required:itemConfig.required
                        },
                        {
                            default:()=>{
                                return [
                                    h(
                                        resolveComponent(itemConfig.tag),
                                        {
                                            ...config,
                                            modelValue:props.modelValue,
                                            "onUpdate:modelValue":(val)=>{
                                                emit("update:modelValue",val);
                                            }
                                        },
                                        {
                                            default:()=>{
                                                for(let key in formItem.componentChild[itemConfig.tag]){
                                                    if(itemConfig[key]){
                                                        return h(
                                                            "div",
                                                            null,
                                                            [
                                                                formItem.componentChild[itemConfig.tag][key](h,itemConfig,key)
                                                            ]
                                                        );
                                                    };
                                                };
                                            }
                                        }
                                    )
                                ]
                            }
                        }
                    )
                }
            }
        )
    },
    rowFormItem(h,itemConfig,attrs){
        return h(
            resolveComponent("el-col"),
            {
                span:itemConfig.span
            },
            {
                default:()=>{
                    return h(
                        resolveComponent("el-row"),
                        {
                            gutter:itemConfig.gutter
                        },
                        {
                            default:()=>{
                                return [
                                    h(
                                        "span",
                                        {
                                            class:"component-name"
                                        },
                                        [
                                            itemConfig.componentName
                                        ]
                                    )
                                ]
                            }
                        }
                    )
                }
            }
        )
    }
};

const layoutIsNotFound=(self)=>{
    throw new Error(`没有与${self.itemConfig.layout}匹配的layout`);
};

export default defineComponent({
    props:{
        itemConfig:Object,
        modelValue:{
            type: [String, Number, Boolean, Object, Array],
            default:""
        }
    },
    emits:["update:modelValue"],
    setup(props,{attrs,emit}){
        // console.log(emit,"=====93");
        const layout = layouts[props.itemConfig.layout];
        if(layout){
            return ()=>{
                return layout(h,props.itemConfig,props,attrs,emit);
            };
        };
        return ()=>{
            layoutIsNotFound(props);
        }
    }
});

</script>
