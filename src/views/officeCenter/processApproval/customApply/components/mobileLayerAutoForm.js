import { resolveComponent } from "vue";

class MobileLayerAutoForm {
    constructor(options = {}, showMessage = false, successMsg = '操作成功', errorMsg = '操作失败') {

    }

    componentChild = {
        'el-button': {
            default(h, conf, key) {
                return conf[key]
            },
        },
        'el-input': {
            prepend(h, conf, key) {
                return h('div', null, { prepend: () => conf[key] })
            },
            append(h, conf, key) {
                return h('div', null, { append: () => conf[key] })
            }
        },
        'el-select': {
            options(h, conf, key) {
                const list = []
                conf.options.forEach(item => {
                    list.push(h(resolveComponent('el-option'), { label: item.label, value: item.value, disabled: item.disabled }))
                })
                return list
            }
        },
        'el-radio-group': {
            options(h, conf, key) {
                const list = []
                conf.options.forEach(item => {
                    if (conf.optionType === 'button') {
                        list.push(h(resolveComponent("el-radio-button"), { value: item.value }, { default: () => item.label }))
                    } else {
                        list.push(h(resolveComponent('el-radio'), { value: item.value, border: conf.border }, { default: () => item.label }))
                    }
                })
                return list
            }
        },
        'el-checkbox-group': {
            options(h, conf, key) {
                const list = []
                conf.options.forEach(item => {
                    if (conf.optionType === 'button') {
                        list.push(h(resolveComponent("el-checkbox-button"), { value: item.value }, { default: () => item.label }))
                    } else {
                        list.push(h(resolveComponent("el-checkbox"), { value: item.value }, { default: () => item.label }))
                    }
                })
                return list
            }
        },
        'h1': {
            label(h, conf, key) {
                return conf[key]
            }
        },
        'h2': {
            label(h, conf, key) {
                return conf[key]
            }
        },
        'el-upload': {
            'list-type': (h, conf, key) => {
                const list = []
                if (conf['list-type'] === 'picture-card') {
                    list.push(h(
                        resolveComponent("Plus")
                    ));
                } else {
                    list.push(h(
                        resolveComponent("el-button"),
                        {
                            size:"small",
                            type:"primary",
                            icon:"el-icon-upload"
                        },
                        {
                            default:()=>{
                                return conf.buttonText;
                            }
                        }
                    ));
                };
                if (conf.showTip) {
                    list.push(h(
                        "div",
                        {
                            class:"el-upload__tip"
                        },
                        {
                            tip:()=>{
                                return `只能上传不超过${conf.fileSize}${conf.sizeUnit}的${conf.accept}文件`
                            }
                        }
                    ))
                }
                return list
            }
        }
    }

}


export default MobileLayerAutoForm
