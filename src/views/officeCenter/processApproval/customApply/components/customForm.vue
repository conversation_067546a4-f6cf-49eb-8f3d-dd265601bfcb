<template>
    <div class="qilin-customForm">
        <el-row
            v-if="propsValue.formConfig"
            :key="propsValue.formConfig"
            class="qilin-customForm-row"
            :gutter="propsValue.formConfig.gutter"
        >
            <el-form
                class="qilin-customForm-row-form flex-row"
                ref="formRef"
                :model="formData"
                :rules="formRules"
                :size="propsValue.formConfig.size"
                :label-position="propsValue.formConfig.labelPosition"
                :label-width="propsValue.formConfig.labelWidth"
            >
                <template
                    v-for="item in propsValue.formConfig.fields"
                    :key="item.renderKey"
                >
                    <CustomFormItem
                        v-model="formData[item.vModel]"
                        :itemConfig="item"
                    ></CustomFormItem>
                </template>
                <el-row style="width:100%;text-align:center;margin-top:50px;" class="is-hide">
                    <el-col>
                        <el-button type="primary" @click="submitData()">提交</el-button>
                    </el-col>
                </el-row>
            </el-form>
        </el-row>
        <QilinDialog v-model:dialogConfig="dialogConfig" @handleClose="closeDialog">
            <el-image
                style="width:100%;height:500px;"
                :src="currentImage"
                fit="scale-down"
            ></el-image>
        </QilinDialog>
    </div>
</template>

<script setup>
import CustomFormItem from "./customFormItem.vue";
import {uploadFiles} from "@/api/index.js";
import {imageIp} from "@/setting.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
import {
    submitWithdrawProcess
} from "@/api/officeCenter/processApproval/index.js";


/*
    响应式选项区域
*/
const propsValue = defineProps({
    formConfig:{
        type:Object,
        default:null
    },
    formData:{
        type:Object,
        default:null
    }
});
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);


/*
    数据变量定义区域
*/
let formData = ref({}); //表单提交数据集
const formRef = ref(null); //表单元素DOM
let formRules = ref({}); //表单校验规则
// 当前预览图片url
let currentImage=ref("");
// 当前预览图片索引位置
let initialIndex=ref(0);


/*
    计算属性等代码区域
*/


/*
    逻辑脚本代码区域
*/
// 添加正则校验事件监听
const addRules=(item)=>{
    if(item.required){ //若为必填
        formRules.value[item.vModel] = [
            {
                required:true,
                message:item.placeholder || "请选择" + item.label,
                trigger:["blur","change"]
            }
        ];
        if(item.regList && item.regList.length > 0){
            formRules.value[item.vModel].push(...item.regList);
        };
    }else{
        formRules.value[item.vModel] = item.regList;
    };
};
// 文件上传时添加上传配置项事件监听
const addUploadConfig=(config)=>{
    if(config.defaultValue){
        config["file-list"] = config.defaultValue;
    };
    config["on-change"] = (file,fileList)=>{
        const index=file.name.lastIndexOf(".");
        const fileType=file.name.slice(index+1).toLowerCase();
        if(file.size > (config.sizeUnit === "KB" ? config.fileSize * 1000 : config.sizeUnit === "MB" ? config.fileSize * 1000 * 1000 :
            config.sizeUnit === "GB" ? config.fileSize * 1000 * 1000 * 1000 : 20 * 1000 * 1000
        )){
            ElMessage({
                message:"文件大小超过限制",
                type:"warning"
            });
        }else{
            let formdata=new FormData();
            formdata.append(config.name || "file",file.raw);
            uploadFiles(formdata).then((res)=>{
                if(res.code === 200){
                    // console.log(res.data);
                    formRef.value.clearValidate(config.vModel);
                    formData.value[config.vModel].push(res.data);
                }else{
                    ElMessage({
                        message:"文件上传失败",
                        type:"error"
                    });
                };
            });
        };
    };
    config["on-preview"] = (file)=>{
        currentImage.value = file.url;
        dialogConfig.isShow = true;
    };
    config["on-remove"] = (file,fileList)=>{
        // console.log(file,fileList);
        const index = formData.value[config.vModel].findIndex((item)=>{
            return item.originalFilename === file.name;
        });
        if(index !== -1){
            formData.value[config.vModel].splice(index,1);
        };
    };
};
// 关闭预览弹窗事件监听
const closeDialog=()=>{
    dialogConfig.isShow=false;
};
// 点击提交按钮事件监听
const submitData=(processInfo)=>{
    // console.log(formData.value,propsValue.formConfig.fields);
    formRef.value.validate((valid)=>{
        if(valid){
            // console.log(formData.value);return;
            submitWithdrawProcess({
                procInsId:processInfo.procInsId,
                taskId:processInfo.taskId,
                comment:"",
                variables:{
                    customDataForm:JSON.stringify({
                        ...formData.value,
                        employee:propsValue.formData.employee,
                        deptName:propsValue.formData.deptName,
                        nickName:propsValue.formData.nickName
                    })
                }
            }).then((res)=>{
                if(res.code === 200){
                    formRef.value.resetFields();
                    emits("backTable");
                    ElMessage({
                        message:"提交成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

// 图片预览弹窗配置项
let dialogConfig=reactive({
    isShow:false,
    title:"图片预览",
    isHideFooter:true
});


/*
    生命周期等代码区域
*/
watch(()=>propsValue.formConfig,(newValue)=>{
    if(newValue){
        newValue.fields.forEach((item)=>{
            formData.value[item.vModel] = item.defaultValue;
            addRules(item);
            if(item.tag === "el-upload"){
                formData.value[item.vModel] = [];
                addUploadConfig(item);
            };
        });
    };
},{immediate:true});

// 回显自定义表单数据
nextTick(()=>{
    propsValue.formConfig.fields.forEach((item)=>{
        if(item.tag === "el-upload" && propsValue.formData[item.vModel].length > 0 ){ //图片回显
            formData.value[item.vModel] = item["file-list"] = propsValue.formData[item.vModel];
        }else{
            formData.value[item.vModel] = propsValue.formData[item.vModel];
        };
    });
});

defineExpose({
    submitData
});



</script>

<style lang="scss" scoped>
.qilin-customForm{
    width:100%;
    >.qilin-customForm-row{
        width:100%;
        height:100%;
        position:relative;
        >.qilin-customForm-row-form{
            width:100%;
            flex-wrap:wrap;
            .el-col-1{
                width:4.1666666667%;
            }
            .el-col-2{
                width:8.3333333333%;
            }
            .el-col-3{
                width:12.5%;
            }
            .el-col-4{
                width:16.6666666667%;
            }
            .el-col-5{
                width:20.8333333333%;
            }
            .el-col-6{
                width:25%
            }
            .el-col-7{
                width:29.1666666667%;
            }
            .el-col-8{
                width:33.3333333333%;
            }
            .el-col-9{
                width:37.5%;
            }
            .el-col-10{
                width:41.6666666667%;
            }
            .el-col-11{
                width:45.8333333333%;
            }
            .el-col-12{
                width:50%;
            }
            .el-col-13{
                width:54.1666666667%;
            }
            .el-col-14{
                width:58.3333333333%;
            }
            .el-col-15{
                width:62.5%;
            }
            .el-col-16{
                width:66.6666666667%;
            }
            .el-col-17{
                width:70.8333333333%;
            }
            .el-col-18{
                width:75%;
            }
            .el-col-19{
                width:79.1666666667%;
            }
            .el-col-20{
                width:83.3333333333%;
            }
            .el-col-21{
                width:87.5%;
            }
            .el-col-22{
                width:91.6666666667%;
            }
            .el-col-23{
                width:95.8333333333%;
            }
            .el-col-24{
                width:100%;
            }
        }
    }
}
</style>
