<template>
    <div class="qilin-customApply hide-scrollbar">
        <template v-if="!propsValue.processInfo.formDataEdit">
            <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
        </template>
        <template v-else>
            <CustomForm :formData="propsValue.formData" :formConfig="propsValue.formConfig" ref="customFormRef"
                @backTable="emits('backTable')"
            ></CustomForm>
        </template>
    </div>
</template>

<script setup>
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
import CustomForm from "./components/customForm.vue";


/*
    响应式选项区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    },
    formConfig:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData,propsValue.formConfig);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);


/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);
// 获取自定义表单元素DOM
const customFormRef=ref(null);
let formConfig = ref(null);


/*
    计算属性等代码区域
*/


/*
    逻辑脚本代码区域
*/
const submitData=()=>{
    customFormRef.value.submitData(propsValue.processInfo);
};

// 查看组件配置项
const viewConfig=reactive({
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[],
    viewData:{}
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    // console.log(propsValue.processInfo,propsValue.formData,propsValue.formConfig);return;
    if(propsValue.processInfo.formDataEdit){
        // console.log(propsValue.formData,"============");
        // formConfig.submitData={...propsValue.formData};
    }else{
        // viewConfig.viewData={...propsValue.formData};
        viewConfig.itemConfig=propsValue.formConfig.fields.map((item)=>{
            return {
                type:item.tag === "el-upload" ? "file" : "text",
                label:item.label,
                value:item.vModel,
                labelSuffix:"：",
                col:item.span
            };
        });
        propsValue.formConfig.fields.forEach((item)=>{
            viewConfig.viewData[item.vModel] = propsValue.formData[item.vModel];
            if(item.tag === "el-select"){
                viewConfig.viewData[item.vModel] = item.options.filter((item1)=>{
                    return item1.value === propsValue.formData[item.vModel];
                })[0].label;
            };
        });
    };
});
defineExpose({
    submitData
});


</script>

<style lang="scss" scoped>
.qilin-customApply{
    width:100%;
    height:100%;
}
</style>
