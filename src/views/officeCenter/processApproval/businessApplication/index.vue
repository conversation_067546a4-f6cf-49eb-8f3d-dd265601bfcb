<template>
    <div class="qilin-businessApplication hide-scrollbar">
        <template v-if="!propsValue.processInfo.formDataEdit">
            <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
        </template>
        <template v-else>
            <QilinForm v-model:formConfig="formConfig" ref="formConfigRef"></QilinForm>
        </template>
    </div>
</template>

<script setup>
import {
    calculateBusinessConfilct
} from "@/api/officeCenter/businessApplication/index.js";
import {
    submitWithdrawProcess
} from "@/api/officeCenter/processApproval/index.js";
import {getCountryAddressData} from "@/utils/common.js";
import { login } from "@/store/login.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
const loginStore=login();

/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);
// 获取表单组件元素DOM
const formConfigRef=ref(null);
// 是否重复提交出差时间
let flag=ref(false);


/*
    计算属性等代码区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);


/*
    逻辑脚本代码区域
*/
// 校验出差开始时间事件监听
const checkStartTime=(rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.endTime && new Date(formConfig.submitData.endTime).getTime() < new Date(value).getTime()){
            callback("出差开始时间不得大于出差结束时间");
        }else{
            callback();
        };
    };
};
// 校验出差结束时间事件监听
const checkEndTime=(rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.startTime && new Date(formConfig.submitData.startTime).getTime() > new Date(value).getTime()){
            callback("出差结束时间不得小于出差开始时间");
        }else{
            callback();
        };
    };
};
// 选择出差开始时间失去焦点事件监听
const blurSelectStartTime=async (item,e)=>{
    if(new Date().getTime() >= new Date(e).getTime()){ // 若出差申请开始时间小于当前时间，则需要填写情况说明
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "reason"){
                ite.isHide=false;
            };
        });
    }else{
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "reason"){
                ite.isHide=true;
            };
        });
    };
    if(e && formConfig.submitData.endTime){
        await calculateBusinessConfilctData(e,formConfig.submitData.endTime);
    };
};
// 选择出差结束时间失去焦点事件监听
const blurSelectEndTime=async (item,e)=>{
    if(e && formConfig.submitData.startTime){
        await calculateBusinessConfilctData(formConfig.submitData.startTime,e);
    };
};
// 计算出差时间有无冲突事件监听
const calculateBusinessConfilctData=async (startTime,endTime)=>{
    await calculateBusinessConfilct({
        procInsId:propsValue.processInfo.procInsId,
        processKey:3,
        params:{
            startTime,
            endTime
        }
    }).then((res)=>{
        if(res.code === 200){
            if(!res.data){
                ElMessage({
                    message:"该时间段已提交出差申请，请勿重复提交",
                    type:"error"
                });
            };
            flag.value=!res.data;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 点击提交按钮事件监听
const submitData=()=>{
    formConfigRef.value.formValidate((valid)=>{
        if(valid){
            if(flag.value){
                ElMessage({
                    message:"该时间段已提交出差申请，请勿重复提交",
                    type:"error"
                });
                return;
            };
            let formData={...formConfig.submitData};
            formData.address=formData.address.join("-");
            submitWithdrawProcess({
                procInsId:propsValue.processInfo.procInsId,
                taskId:propsValue.processInfo.taskId,
                comment:"",
                variables:{
                    startTime:formConfig.submitData.startTime,
                    endTime:formConfig.submitData.endTime,
                    customDataForm:JSON.stringify(formData)
                }
            }).then((res)=>{
                if(res.code === 200){
                    formConfigRef.value.formResetFields();
                    emits("backTable");
                    ElMessage({
                        message:"提交待办出差申请成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

// 查看组件配置项
const viewConfig=reactive({
    // isTable:true,
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"出差地点",
            value:"address",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"交通工具",
            value:"trafficType",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"出差开始时间",
            value:"startTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"出差结束时间",
            value:"endTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"出差事由",
            value:"workContent",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"情况说明",
            value:"reason",
            type:"text",
            isHide:true,
            labelSuffix:"：",
            col:24
        }
    ],
    viewData:{
        address:"",
        trafficType:"",
        startTime:"",
        endTime:"",
        workContent:"",
        reason:""
    }
});
// 表单配置项
const formConfig=reactive({
    elFormConfig:{
        labelWidth:"150px",
        style:"padding:20px 0 40px;"
    },
    itemConfig:[
        {
            labelName:"出差省市区",
            dataName:"address",
            type:"cascader",
            dataListName:"addressList",
            width:24,
            style:"width:300px;",
            validate:[
                {
                    required:true,
                    message:"请选择出差省市区",
                    trigger:["change","blur"]
                }
            ]
        },
        {
            labelName:"交通工具",
            dataName:"trafficType",
            type:"checkbox",
            dataListName:"trafficTypeList",
            width:24,
            validate:[
                {
                    required:true,
                    message:"请选择交通工具",
                    trigger:["change","blur"]
                }
            ]
        },
        {
            labelName:"出差开始时间",
            dataName:"startTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,8,30,0),
            domWidth:300,
            change:blurSelectStartTime,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请选择出差开始时间",
                    trigger:"blur"
                },
                {
                    validator:checkStartTime,
                    trigger:"change"
                }
            ]
        },
        {
            labelName:"出差结束时间",
            dataName:"endTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,17,30,0),
            domWidth:300,
            change:blurSelectEndTime,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请选择出差结束时间",
                    trigger:"blur"
                },
                {
                    validator:checkEndTime,
                    trigger:"change"
                }
            ]
        },
        {
            labelName:"出差事由",
            dataName:"workContent",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请填写加班事由",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"情况说明",
            dataName:"reason",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            isHide:true,
            placeholder:"出差申请需提前提交申请单，您本次流程超出规定提交事件，请详细补充情况说明。",
            validate:[
                {
                    required:true,
                    message:"请输入情况说明",
                    trigger:"blur"
                }
            ]
        }
    ],
    selectLists:{
        addressList:getCountryAddressData(),
        trafficTypeList:[ // 交通工具数据集
            {
                label:"飞机"
            },
            {
                label:"高铁"
            },
            {
                label:"火车"
            },
            {
                label:"出租"
            },
            {
                label:"客车"
            },
            {
                label:"自驾"
            },
            {
                label:"公务车"
            },
            {
                label:"其他"
            }
        ]
    },
    submitData:{
        address:"",
        trafficType:[],
        startTime:"",
        endTime:"",
        workContent:"",
        reason:""
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    if(propsValue.processInfo.formDataEdit){
        formConfig.submitData={...propsValue.formData};
        formConfig.submitData.address=formConfig.submitData.address.split("-");
        if(formConfig.submitData.reason){ //若有情况说明，则表示该数据超出两天时间限制提出的申请
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "reason"){
                    item.isHide=false;
                };
            });
        };
    }else{
        viewConfig.viewData={...propsValue.formData};
        viewConfig.viewData.trafficType=viewConfig.viewData.trafficType.join(",");
        viewConfig.itemConfig.forEach((item)=>{
            if(item.value === "reason"){
                item.isHide= viewConfig.viewData["reason"] ? false : true;
            };
        });
    };
});
// 暴露子组件方法
defineExpose({
    submitData
});

</script>

<style lang="scss" scoped>
.qilin-businessApplication{
    width:100%;
    height:100%;
}
</style>
