<template>
    <div class="qilin-processApproval" id="processPage">
        <div class="qilin-processApproval-content">
            <div class="qilin-processApproval-content-headerTitle">{{propsValue.processInfo.procDefName}}</div>
            <div class="qilin-processApproval-content-title">基本信息</div>
            <OvertimeApplication v-if="flag && propsValue.processInfo.procDefName === '加班申请' "
                :processInfo="propsValue.processInfo" :formData="formData"
                @backTable="emits('showTable')" ref="OvertimeApplicationRef"
            ></OvertimeApplication>
            <LeaveApplication v-if="flag && propsValue.processInfo.procDefName === '请假申请' "
                :processInfo="propsValue.processInfo" :formData="formData"
                @backTable="emits('showTable')" ref="LeaveApplicationRef"
            ></LeaveApplication>
            <BusinessApplication v-if="flag && propsValue.processInfo.procDefName === '出差申请' "
                :processInfo="propsValue.processInfo" :formData="formData"
                @backTable="emits('showTable')" ref="BusinessApplicationRef"
            ></BusinessApplication>
            <ExpenseApplication v-if="flag && propsValue.processInfo.procDefName === '报销申请' "
                :processInfo="propsValue.processInfo" :formData="formData"
                @backTable="emits('showTable')" ref="ExpenseApplicationRef"
            ></ExpenseApplication>
            <CostApplication v-if="flag && propsValue.processInfo.procDefName === '费用申请' "
                :processInfo="propsValue.processInfo" :formData="formData"
                @backTable="emits('showTable')" ref="CostApplicationRef"
            ></CostApplication>
            <CancelLeaveApplication v-if="flag && propsValue.processInfo.procDefName === '销假申请' "
                :processInfo="propsValue.processInfo" :formData="formData"
                @backTable="emits('showTable')" ref="CancelLeaveApplicationRef"
            ></CancelLeaveApplication>
            <ClockApplication v-if="flag && propsValue.processInfo.procDefName === '补卡申请' "
                :processInfo="propsValue.processInfo" :formData="formData"
                @backTable="emits('showTable')" ref="ClockApplicationRef"
            ></ClockApplication>
            <usingVehicle v-if="flag && propsValue.processInfo.procDefName === '用车申请' "
                :processInfo="propsValue.processInfo" :formData="formData"
                @backTable="emits('showTable')" ref="usingVehicleRef"
            ></usingVehicle>
            <customApply v-if="flag && propsValue.processInfo.category != '109'"
                :processInfo="propsValue.processInfo" :formData="formData" :formConfig="formConfigs"
                @backTable="emits('showTable')" ref="customApplyRef"
            ></customApply>
        </div>
        <div class="qilin-processApproval-list">
            <div class="qilin-processApproval-list-title">流程审批</div>
            <div class="qilin-processApproval-list-container hide-scrollbar">
                <div class="qilin-processApproval-list-container-item" v-for="(item,index) in processApprovalData" :key="item.activityId">
                    <div class="item-header">
                        <div class="item-header-subtext">
                            {{getProcessText(item,"surname")}}
                            <img :src="getProcessImage(item)" alt="">
                        </div>
                        <div class="item-header-text">
                            <div class="item-header-text-info">
                                <div class="item-header-text-info-name single-ellipsis">{{getProcessText(item,"name")}}</div>
                                <div class="item-header-text-info-deptName">{{getDeptName(item.deptId)}}</div>
                            </div>
                            <div class="item-header-text-date">{{item.endTime}}</div>
                        </div>
                    </div>
                    <div class="item-content">
                        <div class="item-content-text" v-if="item.commentList && index != 0 && item.commentList[0]?.message ">
                            {{item.commentList[0]?.message}}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="qilin-processApproval-footer">
        <el-button type="default" @click="emits('showTable')">返回</el-button>
        <el-button type="danger" v-if="propsValue.processInfo.formDataEdit || propsValue.processInfo.allowDelete" @click="deleteProcessData()">删除</el-button>
        <el-button type="danger"
            v-else-if="!propsValue.processInfo.allowDelete &&
                (!!propsValue.processInfo.assigneeId || propsValue.processInfo.procDefName === '销假申请') &&
                propsValue.processInfo.routeName !== 'myApplication'"
            @click="withdraw()"
        >撤回</el-button>
        <el-button type="primary" v-if="propsValue.processInfo.formDataEdit" @click="submitData()">提交</el-button>
        <el-button type="primary" v-else-if="!propsValue.processInfo.formDataEdit && route.name === 'backlog' " @click="approval()">审批</el-button>
        <el-button type="primary"
            v-if="propsValue.processInfo.procDefName === '请假申请' && !propsValue.processInfo.assigneeName &&
                !propsValue.processInfo.formDataEdit && propsValue.processInfo.routeName !== 'backlog' &&
                propsValue.processInfo.startUserName === loginStore.userInfo.user.nickName"
            @click="cancelLeave()"
        >销假</el-button>
        <el-button type="primary" v-if="propsValue.processInfo.routeName !== 'backlog'" @click="exportCurrentPage()">一键导出</el-button>
        <el-button type="primary"
            v-if="propsValue.processInfo.routeName === 'myApplication' &&
                ['报销申请','请假申请'].includes(propsValue.processInfo.procDefName) "
            @click="suppleFiles()"
        >补交附件</el-button>
    </div>
    <QilinDialog v-model:dialogConfig="approvalDialogConfig" @handleClose="handleApprovalDialogConfig"
        @submitConfirm="submitApprovalDialogConfig"
    >
        <QilinForm v-model:formConfig="approvalFormConfig" ref="approvalFormConfigRef"></QilinForm>
    </QilinDialog>
    <QilinDialog v-model:dialogConfig="dialogConfig" @handleClose="handleCloseDialog"
        @submitConfirm="submitConfirm"
    >
        <QilinView v-model:viewConfig="viewConfig"></QilinView>
        <QilinForm v-model:formConfig="formConfig" ref="formConfigRef"></QilinForm>
    </QilinDialog>
    <QilinDialog v-model:dialogConfig="suppleFilesDialogConfig" @handleClose="handleSuppleFilesDialogConfig"
        @submitConfirm="submitSuppleFilesDialogConfig"
    >
        <QilinForm v-model:formConfig="suppleFilesFormConfig" ref="suppleFilesFormConfigRef"></QilinForm>
    </QilinDialog>
</template>

<script setup>
import html2canvas from "html2canvas";
import JSPDF from "jspdf";
import {saveAs} from "file-saver";
import html2pdf from "html2pdf.js";
import Qilin from "qilin-utils";
import {
    getProcessInfo,
    withdrawProcess,
    submitWithdrawProcess,
    approvalReject,
    getApprovalRejectId,
    deleteProcess,
    submitCancelLeaveData,
    suppleFilesData
} from "@/api/officeCenter/processApproval/index.js";
import {getLeaveCount} from "@/api/officeCenter/leaveApplication/index.js";
import { getDeptInfo } from "@/utils/common.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
import OvertimeApplication from "./overtimeApplication/index.vue";
import LeaveApplication from "./leaveApplication/index.vue";
import BusinessApplication from "./businessApplication/index.vue";
import ExpenseApplication from "./expenseApplication/index.vue";
import CostApplication from "./costApplication/index.vue";
import CancelLeaveApplication from "./cancelLeaveApplication/index.vue";
import ClockApplication from "./clockApplication/index.vue";
import usingVehicle from "./usingVehicle/index.vue";
import customApply from "./customApply/index.vue";
import {getProcessData} from "@/utils/common.js";
const currentProcessData=getProcessData("销假申请");
// console.log(currentProcessData);
import { login } from "@/store/login.js";
const loginStore=login();

/*
    数据变量定义区域
*/
// 流程审批信息数据
let processApprovalData=ref([]);
// 传递给子组件的详情数据
let formData=reactive({});
// 控制子组件的显示与否
let flag=ref(false);
// 获取加班申请表单元素DOM
const OvertimeApplicationRef=ref(null);
// 获取审批意见弹窗表单元素DOM
const approvalFormConfigRef=ref(null);
// 获取请假申请表单元素DOM
const LeaveApplicationRef=ref(null);
// 获取出差申请表单元素DOM
const BusinessApplicationRef=ref(null);
// 获取报销申请表单元素DOM
const ExpenseApplicationRef=ref(null);
// 获取费用申请表单元素DOM
const CostApplicationRef=ref(null);
// 获取补卡申请表单元素DOM
const ClockApplicationRef=ref(null);
// 获取销假弹窗表单元素DOM
const formConfigRef=ref(null);
// 获取销假申请表单元素DOM
const CancelLeaveApplicationRef=ref(null);
// 获取补交附件表单元素DOM
const suppleFilesFormConfigRef=ref(null);
// 获取用车申请表单元素DOM
const usingVehicleRef=ref(null);
// 获取自定义申请表单元素DOM
const customApplyRef=ref(null);
// 传递给子组件的表单自定义配置
let formConfigs=reactive({});


/*
    计算属性等代码区域
*/
// 获取来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    }
});
// console.log(propsValue.processInfo);
// 暴露父组件自定义方法
const emits=defineEmits(["showTable"]);
// 获取部门名称方法
const getDeptName = computed(() => {
    return (id) => {
        return getDeptInfo(id) || "";
    };
});
// 获取流程信息相关展示字段方法
const getProcessText=computed(()=>{
    return (item,type)=>{
        if(type === "surname"){ //获取姓
            if(!item.commentList && item.activityType === "endEvent"){ //结束时
                return "End";
            }else{
                return item.assigneeName?.slice(0,1);
            };
        }else if(type === "name"){ //获取姓名
            if(!item.commentList && item.activityType === "endEvent"){ //结束时
                return "流程结束";
            }else{
                return item.assigneeName;
            };
        };
    };
});
// 获取流程信息相关状态图标方法
const getProcessImage=computed(()=>{
    return (item)=>{
        if(item.commentList && item.commentList.length ===0){
            return new URL("@/assets/images/viewdata-wait.png",import.meta.url).href;
        }else if(item.commentList && item.commentList[0].type === "2"){
            return new URL("@/assets/images/viewdata-fail.png",import.meta.url).href;
        }else if((item.commentList && item.commentList[0].type === "1") || (!item.commentList && item.activityType === "endEvent")){
            return new URL("@/assets/images/viewdata-success.png",import.meta.url).href;
        };
    };
});


/*
    逻辑脚本代码区域
*/
// 点击删除按钮事件监听
const deleteProcessData=()=>{
    ElMessageBox.confirm("是否删除该流程？","删除",{
        cancelButtonText:"取消",
        confirmButtonText:"确定",
        type:"error",
        icon:"WarningFilled"
    }).then(()=>{
        deleteProcess({
            instanceId:propsValue.processInfo.procInsId
        }).then((res)=>{
            if(res.code === 200){
                emits("showTable");
                ElMessage({
                    message:"删除成功",
                    type:"success"
                });
            }else{
                ElMessage({
                    message:res.msg || "系统错误",
                    type:"error"
                });
            };
        });
    }).catch(()=>{
        console.log("点击取消");
    });
};
// 点击撤回按钮事件监听
const withdraw=()=>{
    withdrawProcess({
        procInsId:propsValue.processInfo.procInsId,
        taskId:propsValue.processInfo.taskId
    }).then((res)=>{
        if(res.code === 200){
            router.push({
                path:"/officeCenter/backlog"
            });
            ElMessage({
                message:"撤回成功",
                type:"success"
            });
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 点击提交按钮事件监听
const submitData=()=>{
    if(propsValue.processInfo.procDefName === "加班申请"){
        OvertimeApplicationRef.value.submitData();
    }else if(propsValue.processInfo.procDefName === "请假申请"){
        LeaveApplicationRef.value.submitData();
    }else if(propsValue.processInfo.procDefName === "出差申请"){
        BusinessApplicationRef.value.submitData();
    }else if(propsValue.processInfo.procDefName === "报销申请"){
        ExpenseApplicationRef.value.submitData();
    }else if(propsValue.processInfo.procDefName === "费用申请"){
        CostApplicationRef.value.submitData();
    }else if(propsValue.processInfo.procDefName === "销假申请"){
        CancelLeaveApplicationRef.value.submitData();
    }else if(propsValue.processInfo.procDefName === "用车申请"){
        usingVehicleRef.value.submitData();
    }else{ //自定义渲染申请
        customApplyRef.value.submitData();
    };
};
// 点击审批按钮事件监听
const approval=()=>{
    getApprovalRejectData();
    approvalFormConfig.submitData.choose="同意";
    approvalDialogConfig.isShow=true;
};
// 点击销假按钮事件监听
const cancelLeave=()=>{
    dialogConfig.isShow=true;
};
// 点击一键导出按钮事件监听
const exportCurrentPage=()=>{
    const pageElement=document.getElementById("processPage");
    /** 第一种方式-截图指定页面元素,类似于截图，不覆盖滚动页面*/
    // html2canvas(pageElement,{
    //     useCORS:true
    // }).then((canvas)=>{
    //     canvas.toBlob((blob)=>{
    //         saveAs(blob,"流程数据.png");
    //     });
    // });
    /** 第二种方式-导出成pdf，覆盖滚动页面*/
    // html2pdf().set({
    //     margin:0.15,
    //     html2canvas:{
    //         scale:5,
    //         width:document.getElementById("processPage").clientWidth, //解决pdf图片不完整问题
    //         useCORS:true, //解决图片不显示问题（解决图片跨域问题）
    //         widnowWidth:document.getElementById("processPage").clientWidth
    //     },
    //     filename:propsValue.processInfo.procDefName,
    //     image:{
    //         type:"jpeg",
    //         quality:0.98
    //     },
    //     jsPDF:{
    //         unit:"in",
    //         format:"a4",
    //         orientation:"portrait"
    //     },
    //     pagebreak:{
    //         mode:"avoid-all" //处理pdf分页问题
    //     }
    // }).from(pageElement).save();
    /** 第三种方式-导出成pdf */
    // console.log(Qilin.Element.getNodeHeight(".qilin-processApproval-list-container"));
    // console.log(Qilin.Element.getNodeHeight(".qilin-processApproval-content"));
    // console.log(Qilin.Element.getNodeHeight(".qilin-processApproval-list"));
    pageElement.style.height=Qilin.Element.getNodeHeight(".qilin-processApproval-list-container")+200+"px"; //解决滚动内容不显示问题
    // document.body.querySelector(".qilin-processApproval-content-headerTitle").style.display="block";
    html2canvas(pageElement,{
        logging:false,
        useCORS:true, //解决图片不显示问题（解决图片跨域问题）
        scale:2
    }).then((canvas)=>{
        let pdf=new JSPDF("p","mm","a4"); //a4纸，纵向
        let ctx=canvas.getContext("2d");
        let a4w=190;
        let a4h=257; //A4大小，210mm x 297mm，四边各保留20mm的边距
        let imgHeight=Math.floor(a4h * canvas.width / a4w); //按A4显示比例换算一页图像的像素高度
        let renderedHeight=0;
        while(renderedHeight < canvas.height){
            let page=document.createElement("canvas");
            page.width=canvas.width;
            page.height=Math.min(imgHeight,canvas.height-renderedHeight); //可能内容不足一页
            page.getContext("2d").putImageData(ctx.getImageData(0,renderedHeight,canvas.width,Math.min(imgHeight,canvas.height-renderedHeight)),0,0);
            pdf.addImage(page.toDataURL("image/jpeg",1.0),"JPEG",10,10,a4w,Math.min(a4h,a4w * page.height / page.width)); //添加图像到页面，保留10mm边距
            renderedHeight+=imgHeight;
            if(renderedHeight < canvas.height){ //如果后面还有内容，添加一个空页
                pdf.addPage();
            };
        };
        pdf.save(propsValue.processInfo.procDefName);
        pageElement.style.height="100%"; //解决滚动内容不显示问题
        document.body.querySelector(".qilin-processApproval-content-headerTitle").style.display="none";
    });
};
// 关闭审批意见弹窗事件监听
const handleApprovalDialogConfig=()=>{
    approvalFormConfigRef.value.formResetFields();
    approvalDialogConfig.isShow=false;
};
// 获取驳回各个节点事件监听
const getApprovalRejectData=()=>{
    getApprovalRejectId({
        procInsId:propsValue.processInfo.procInsId,
        taskId:propsValue.processInfo.taskId
    }).then((res)=>{
        if(res.code === 200){
            // console.log(res.data);
            approvalFormConfig.selectLists.rejectToList = res.data.map((item)=>{
                return {
                    label:item.name,
                    value:item.id
                };
            });
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 改变审批状态事件监听
const changeChoose=(val)=>{
    // console.log(val);
    if(val === "不同意"){
        approvalFormConfig.itemConfig[1].isHide = false;
        approvalFormConfig.submitData.rejectTo = approvalFormConfig.selectLists.rejectToList[approvalFormConfig.selectLists.rejectToList.length - 1].value;
    }else{
        approvalFormConfig.itemConfig[1].isHide = true;
    };
};
// 点击审批意见弹窗确认按钮事件监听
const submitApprovalDialogConfig=()=>{
    approvalFormConfigRef.value.formValidate((valid)=>{
        if(valid){
            // console.log(approvalFormConfig.submitData);return;
            if(approvalFormConfig.submitData.choose === "同意"){
                submitWithdrawProcess({
                    comment:approvalFormConfig.submitData.comment ? approvalFormConfig.submitData.comment : "同意",
                    procInsId:propsValue.processInfo.procInsId,
                    taskId:propsValue.processInfo.taskId
                }).then((res)=>{
                    if(res.code === 200){
                        emits("showTable");
                        ElMessage({
                            message:"审批成功",
                            type:"success"
                        });
                    }else{
                        ElMessage({
                            message:res.msg || "系统错误",
                            type:"error"
                        });
                    };
                })
            }else{
                approvalReject({
                    comment:approvalFormConfig.submitData.comment ? approvalFormConfig.submitData.comment : "不同意",
                    procInsId:propsValue.processInfo.procInsId,
                    taskId:propsValue.processInfo.taskId,
                    targetKey:approvalFormConfig.submitData.rejectTo
                }).then((res)=>{
                    if(res.code === 200){
                        emits("showTable");
                        ElMessage({
                            message:"驳回成功",
                            type:"success"
                        });
                    }else{
                        ElMessage({
                            message:res.msg || "系统错误",
                            type:"error"
                        });
                    };
                });
            };
        };
    });
};
// 初始化获取流程信息事件监听
const getProcessInfoData=()=>{
    getProcessInfo(propsValue.processInfo.procInsId).then((res)=>{
        if(res.code === 200){
            processApprovalData.value=res.data.historyProcNodeList.reverse().slice(1);
            // console.log(processApprovalData.value);
            formData=JSON.parse(JSON.parse(res.data.customDataForm));
            formConfigs=res.data.processFormList[0];
            // console.log(formData);
            if(propsValue.processInfo.procDefName === '请假申请' && !propsValue.processInfo.assigneeName){
                viewConfig.viewData={...formData};
                if(viewConfig.viewData.leaveType === "调休假"){
                    viewConfig.viewData.overTime=viewConfig.viewData.overTime+"小时";
                    formConfig.itemConfig[2].labelName="销假时长/小时";
                }else{
                    viewConfig.viewData.overTime=viewConfig.viewData.overTime+"天";
                    formConfig.itemConfig[2].labelName="销假时长/天";
                };
                if(viewConfig.viewData.leaveType === "年假1"){ //若用今年年假请，则请假日期必然是今年的1月-12月
                    viewConfig.viewData.leaveType="年假"+new Date(viewConfig.viewData.startTime).getFullYear();
                }else if(viewConfig.viewData.leaveType === "年假0"){ //若用去年年假请，则请假日期则为去年的12月-今年的3月
                    if(new Date(viewConfig.viewData.startTime).getMonth()+1 === 12){ //若月份为12月
                        viewConfig.viewData.leaveType="年假"+new Date(viewConfig.viewData.startTime).getFullYear();
                    }else{
                        viewConfig.viewData.leaveType="年假"+(new Date(viewConfig.viewData.startTime).getFullYear()-1);
                    };
                };
            };
            flag.value=true;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};

/** 销假申请逻辑代码区域 */
// 校验销假开始时间事件监听
const checkStartTime=(rule,value,callback)=>{
    if(value){
        // console.log(Qilin.Date.compareDate(value,formData.startTime))
        if(Qilin.Date.compareDate(value,formData.startTime)){
            callback("销假开始时间不得早于请假开始时间");
        }else if(formConfig.submitData.counteractEndTime && !Qilin.Date.compareDate(value,formConfig.submitData.counteractEndTime)){
            callback("销假开始时间不得早于销假结束时间");
        }else{
            callback();
        };
    };
};
// 校验销假结束时间事件监听
const checkEndTime=(rule,value,callback)=>{
    if(value){
        if(Qilin.Date.compareDate(formData.endTime,value)){
            callback("销假结束时间不得晚于请假结束时间");
        }else if(formConfig.submitData.counteractStartTime && Qilin.Date.compareDate(value,formConfig.submitData.counteractStartTime)){
            callback("销假结束时间不得晚于销假结束时间");
        }else{
            callback();
        };
    };
};
// 选择销假开始时间事件监听
const blurCounteractStartTime=(item,e)=>{
    // console.log(item,e)
    if(e && formConfig.submitData.counteractEndTime){
        getOvertime(e,formConfig.submitData.counteractEndTime);
    };
};
// 选择销假结束时间事件监听
const blurCounteractEndTime=(item,e)=>{
    if(e && formConfig.submitData.counteractStartTime){
        getOvertime(formConfig.submitData.counteractStartTime,e);
    };
};
// 获取销假时长事件监听
const getOvertime=(startTime,endTime)=>{
    getLeaveCount({
        startTime,
        endTime,
        leaveType:formData.leaveType === "调休假" ? 0 : 1
    }).then((res)=>{
        if(res.code === 200){
            formConfig.submitData.counteractOverTime=res.data;
        };
    });
};
// 关闭销假申请弹窗事件监听
const handleCloseDialog=()=>{
    formConfigRef.value.formResetFields();
    dialogConfig.isShow=false;
};
// 点击销假弹窗确定按钮事件监听
const submitConfirm=()=>{
    formConfigRef.value.formValidate((valid)=>{
        if(valid){
            submitCancelLeaveData(currentProcessData.definitionId,{
                customDataForm:JSON.stringify({
                    ...formConfig.submitData,
                    leaveType:formData.leaveType,
                    startTime:formData.startTime,
                    endTime:formData.endTime,
                    overTime:formData.overTime,
                    leaveInstanceId:propsValue.processInfo.procInsId
                })
            }).then((res)=>{
                // console.log(res);
                if(res.code === 200){
                    handleCloseDialog();
                    router.push("/officeCenter/myApplication");
                    emits('showTable');
                    ElMessage({
                        message:"提交销假申请成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

/** 补交附件逻辑代码区域 */
// 点击补交附件按钮事件监听
const suppleFiles=()=>{
    suppleFilesDialogConfig.isShow=true;
};
// 关闭补交附件弹窗事件监听
const handleSuppleFilesDialogConfig=()=>{
    suppleFilesFormConfigRef.value.formResetFields();
    suppleFilesDialogConfig.isShow=false;
};
// 点击补交附件弹窗确认按钮事件监听
const submitSuppleFilesDialogConfig=()=>{
    suppleFilesFormConfigRef.value.formValidate((valid)=>{
        if(valid){
            // console.log(suppleFilesFormConfig.submitData,formData);
            let customDataForm={...formData};
            if(propsValue.processInfo.procDefName === "报销申请"){
                if(!customDataForm.formItem.otherFiles){
                    customDataForm.formItem.otherFiles=suppleFilesFormConfig.submitData.otherFiles;
                }else{
                    customDataForm.formItem.otherFiles.push(...suppleFilesFormConfig.submitData.otherFiles);
                };
            }else if(propsValue.processInfo.procDefName === "请假申请"){
                if(!customDataForm.otherFiles){
                    customDataForm.otherFiles=suppleFilesFormConfig.submitData.otherFiles;
                }else{
                    customDataForm.otherFiles.push(...suppleFilesFormConfig.submitData.otherFiles);
                };
            };
            suppleFilesData({
                procInsId:propsValue.processInfo.procInsId,
                customDataForm:JSON.stringify(customDataForm)
            }).then((res)=>{
                if(res.code === 200){
                    flag.value=false;
                    handleSuppleFilesDialogConfig();
                    getProcessInfoData();
                    ElMessage({
                        message:"补交附件成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

// 审批意见弹窗配置项
const approvalDialogConfig=reactive({
    isShow:false,
    title:"审批意见"
});
// 审批意见弹窗表单配置项
const approvalFormConfig=reactive({
    elFormConfig:{
        labelWidth:"90px"
    },
    itemConfig:[
        {
            // hideLabel:true,
            labelName:"审批结果",
            dataName:"choose",
            type:"radio",
            dataListName:"chooseList",
            change:changeChoose,
            width:24
        },
        {
            labelName:"驳回至",
            dataName:"rejectTo",
            type:"select",
            dataListName:"rejectToList",
            placeholder:"请选择某驳回节点",
            isHide:true,
            width:24
        },
        {
            // hideLabel:true,
            labelName:"审批建议",
            dataName:"comment",
            type:"input",
            inputType:"textarea",
            placeholder:"请输入审批建议",
            rows:5,
            width:24
        }
    ],
    selectLists:{
        chooseList:[
            {
                label:"同意",
                value:"同意"
            },
            {
                label:"不同意",
                value:"不同意"
            }
        ],
        rejectToList:[]
    },
    submitData:{
        choose:"同意",
        rejectTo:"",
        comment:""
    }
});
// 销假弹窗配置项
const dialogConfig=reactive({
    isShow:false,
    title:"销假申请",
    width:800
});
// 销假弹窗查看表单配置项
const viewConfig=reactive({
    isTable:true,
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"请假类型",
            value:"leaveType",
            type:"text",
            col:12
        },
        {
            label:"请假开始时间",
            value:"startTime",
            type:"text",
            col:12
        },
        {
            label:"请假时长",
            value:"overTime",
            type:"text",
            col:12
        },
        {
            label:"请假结束时间",
            value:"endTime",
            type:"text",
            col:12
        }
    ],
    viewData:{
        leaveType: "",
        startTime: "",
        endTime: "",
        overTime: ""
    }
});
// 销假弹窗表单申请配置项
const formConfig=reactive({
    elFormConfig:{
        labelWidth:"120px"
    },
    itemConfig:[
        {
            labelName:"销假开始时间",
            dataName:"counteractStartTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            valueFormat:"YYYY-MM-DD HH:mm:ss",
            defaultTime:new Date(2000,1,1,8,30,0),
            width:12,
            change:blurCounteractStartTime,
            validate:[
                {
                    required:true,
                    message:"请选择销假开始时间",
                    trigger:"blur"
                },
                {
                    validator:checkStartTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"销假结束时间",
            dataName:"counteractEndTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            valueFormat:"YYYY-MM-DD HH:mm:ss",
            defaultTime:new Date(2000,1,1,17,30,0),
            width:12,
            change:blurCounteractEndTime,
            validate:[
                {
                    required:true,
                    message:"请选择销假结束时间",
                    trigger:"blur"
                },
                {
                    validator:checkEndTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"销假时长/小时",
            dataName:"counteractOverTime",
            type:"input",
            inputType:"number",
            disabled:true,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请输入销假时长",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"销假事由",
            dataName:"workContent",
            type:"input",
            inputType:"textarea",
            width:24,
            validate:[
                {
                    required:true,
                    message:"请输入销假事由",
                    trigger:"blur"
                }
            ]
        }
    ],
    submitData:{
        counteractStartTime:"",
        counteractEndTime:"",
        counteractOverTime:"",
        workContent:""
    }
});
// 补交附件弹窗配置项
const suppleFilesDialogConfig=reactive({
    isShow:false,
    title:"补交附件"
});
// 补交附件表单配置项
const suppleFilesFormConfig=reactive({
    elFormConfig:{
        labelWidth:"100px"
    },
    itemConfig:[
        {
            labelName:"其他附件",
            dataName:"otherFiles",
            type:"upload",
            fileList:[],
            fileSize:20 * 1000 * 1000,
            fileType:["pdf","xls","xlsx","doc","docx","png","jpg","jpeg","gif"],
            fileTypeText:"支持pdf、excel、word和png等图片文件格式",
            multiple:true,
            limit:20,
            validate:[
                {
                    required:true,
                    message:"请选择附件",
                    trigger:"blur"
                }
            ]
        }
    ],
    submitData:{
        otherFiles:[]
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    getProcessInfoData();
});

</script>

<style lang="scss" scoped>
.qilin-processApproval{
    width:100%;
    height:100%;
    background-color:#fff;
    display:flex;
    flex-flow:row nowrap;
    padding-bottom: 60px;
    >.qilin-processApproval-list{
        width:300px;
        padding:5px 15px 15px;
        display:flex;
        flex-direction:column;
        border-left:1px solid $border-color;
        >.qilin-processApproval-list-title{
            font-size:$medium-text-size;
            color:$text-color-85;
            position:relative;
            margin-left:12px;
            padding:10px 0;
            &::before{
                content:"";
                position:absolute;
                width:4px;
                height:50%;
                left:-12px;
                top:50%;
                transform:translateY(-50%);
                background-color:$primary-text-color;
                border-radius:2px;
            }
        }
        >.qilin-processApproval-list-container{
            padding:30px 0;
            flex:1;
            >.qilin-processApproval-list-container-item{
                >.item-header{
                    display:flex;
                    >.item-header-subtext{
                        width:36px;
                        height:36px;
                        background-color:rgba(24,144,255,.2);
                        text-align:center;
                        line-height:36px;
                        color:$primary-text-color;
                        font-size:$large-text-size;
                        border-radius:4px;
                        position:relative;
                        font-weight: bolder;
                        >img{
                            position:absolute;
                            right:-10%;
                            bottom:-10%;
                        }
                    }
                    >.item-header-text{
                        flex:1;
                        padding-left:16px;
                        // display:flex;
                        // justify-content:space-between;
                        // align-items:center;
                        >.item-header-text-info{
                            flex:1;
                            // padding-left:16px;
                            >.item-header-text-info-name{
                                color:$text-color-65;
                                width:100%;
                            }
                            >.item-header-text-info-deptName{
                                color:$text-color-45;
                                font-size:$small-text-size;
                                margin-top:4px;
                            }
                        }
                        >.item-header-text-date{
                            color:$text-color-45;
                            font-size:$small-text-size;
                            white-space:nowrap;
                            margin-top: 6px;
                        }
                    }
                }
                >.item-content{
                    min-height: 36px;
                    margin: -10px 18px 12px;
                    border-left:1px dashed $border-color;
                    display:flex;
                    align-items:center;
                    >.item-content-text{
                        background-color:#F7F7F7;
                        padding:10px 15px;
                        margin:15px 0 0 25px;
                        flex:1;
                    }
                }
                &:last-child{
                    >.item-content{
                        display:none;
                    }
                }
            }
        }
    }
    >.qilin-processApproval-content{
        flex:1;
        padding:15px;
        >.qilin-processApproval-content-headerTitle{
            text-align:center;
            font-size:$large-text-size;
            color:$text-color-85;
            display:none;
        }
        >.qilin-processApproval-content-title{
            font-size:$medium-text-size;
            color:$text-color-85;
            position:relative;
            padding-bottom:15px;
            border-bottom:1px solid $border-color;
        }
    }
}
.qilin-processApproval-footer{
    background-color:#fff;
    text-align:right;
    padding:15px;
    box-shadow: -5px -3px 8px 0px rgba(73, 127, 176, 0.15);
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    z-index:99;
}
</style>
