<template>
    <div class="qilin-leaveApplication hide-scrollbar">
        <template v-if="!propsValue.processInfo.formDataEdit">
            <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
        </template>
        <template v-else>
            <span>基本信息-注：婚假（13天）、产假（158天）、病假、陪产假（15天）、丧假（3天）、工伤假、育儿假、独生子女陪护假（5天）</span>
            <QilinForm v-model:formConfig="formConfig" ref="formConfigRef"></QilinForm>
        </template>
    </div>
</template>

<script setup>
import {
    getLeaveTypeList,
    getLeaveCount,
    calculateLeaveConfilct
} from "@/api/officeCenter/leaveApplication/index.js";
import {
    submitWithdrawProcess
} from "@/api/officeCenter/processApproval/index.js";
import { login } from "@/store/login.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
const loginStore=login();

/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);
// 获取表单组件元素DOM
const formConfigRef=ref(null);
// 各类假期类型剩余时间
let leaveType=reactive({
    compensatoryLeave:0, //调休假
    annualLeaveCurrent:0, //当年年假
    annualLeaveLast:0, //上年年假
    parentalLeave:0 //育儿假
});
// 是否重复提交请假时间
let flag=ref(false);
// 初始请假时长（被撤回进入重新提交时显示的请假时长）
// let initialOvertime=ref(0);


/*
    计算属性等代码区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);


/*
    逻辑脚本代码区域
*/
// 校验请假开始时间事件监听
const checkStartTime=(rule,value,callback)=>{
    if(value){
        const startTime1=new Date(new Date().getFullYear(),0,1,0,0,0).getTime(); //当年的1月1日0点0分0秒
        const startTime2=new Date(new Date().getFullYear()-1,11,1,0,0,0).getTime(); //去年的12月1日0点0分0秒
        const endTime1=new Date(new Date().getFullYear(),2,31,23,59,59).getTime(); //当年的3月31日23点59分59秒
        const endTime2=new Date(new Date().getFullYear(),11,31,23,59,59).getTime(); //当年的12月31日23点59分59秒
        // console.log(formConfig.submitData.leaveType,formConfig.submitData.leaveType === "年假"+(new Date().getFullYear()-1))
        if(formConfig.submitData.leaveType === "年假1" && (new Date(value).getTime() < startTime1 || new Date(value).getTime() > endTime2 )){
            callback("本年度的年假只能在本年度使用");
        }else if(formConfig.submitData.leaveType === "年假0" && (new Date(value).getTime() < startTime2 || new Date(value).getTime() > endTime1 )){
            callback("上年度的年假须在规定期限内使用");
        }else if((["调休假"].includes(formConfig.submitData.leaveType)) && new Date().getFullYear() !== new Date(value).getFullYear()){
            callback("本年度调休不允许跨年使用");
        }else if(formConfig.submitData.endTime && new Date(formConfig.submitData.endTime).getTime() < new Date(value).getTime()){
            callback("请假开始时间不得大于请假结束时间");
        }else{
            callback();
        };
    };
};
// 校验请假结束时间事件监听
const checkEndTime=(rule,value,callback)=>{
    if(value){
        const startTime1=new Date(new Date().getFullYear(),0,1,0,0,0).getTime(); //当年的1月1日0点0分0秒
        const startTime2=new Date(new Date().getFullYear()-1,11,1,0,0,0).getTime(); //去年的12月1日0点0分0秒
        const endTime1=new Date(new Date().getFullYear(),2,31,23,59,59).getTime(); //当年的3月31日23点59分59秒
        const endTime2=new Date(new Date().getFullYear(),11,31,23,59,59).getTime(); //当年的12月31日23点59分59秒
        if(formConfig.submitData.leaveType === "年假1" && (new Date(value).getTime() < startTime1 || new Date(value).getTime() > endTime2 )){
            callback("本年度的年假只能在本年度使用");
        }else if(formConfig.submitData.leaveType === "年假0" && (new Date(value).getTime() < startTime2 || new Date(value).getTime() > endTime1 )){
            callback("上年度的年假须在规定期限内使用");
        }else if((["调休假"].includes(formConfig.submitData.leaveType)) && new Date().getFullYear() !== new Date(value).getFullYear()){
            callback("本年度调休不允许跨年使用");
        }else if(formConfig.submitData.startTime && new Date(formConfig.submitData.startTime).getTime() > new Date(value).getTime()){
            callback("请假结束时间不得小于请假开始时间");
        }else{
            callback();
        };
    };
};
// 校验请假时长事件监听
const checkOvertime=async (rule,value,callback)=>{
    if(value){
        if(formConfig.submitData.startTime && formConfig.submitData.endTime){
            if(formConfig.submitData.leaveType === "年假1" && formConfig.submitData.overTime > leaveType.annualLeaveCurrent){
                callback("本年度年假余额不足");
            }else if(formConfig.submitData.leaveType === "年假0" && formConfig.submitData.overTime > leaveType.annualLeaveLast){
                callback("上年度年假余额不足");
            }else if(formConfig.submitData.leaveType === "调休假" && formConfig.submitData.overTime > leaveType.compensatoryLeave){
                callback("调休假剩余时长不足");
            }else if(formConfig.submitData.leaveType === "育儿假" && formConfig.submitData.overTime > leaveType.parentalLeave){
                callback("育儿假剩余时长不足");
            }else{
                callback();
            };
        }else{
            callback();
        };
    };
};
// 选择请假类型事件监听
const changeLeaveType=(item,e)=>{
    formConfig.submitData.startTime="";
    formConfig.submitData.endTime="";
    formConfig.submitData.reason="";
    formConfig.submitData.overTime="";
    if(e === "调休假"){
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "overTime"){
                ite.labelName = "请假时长（小时）";
            }else if(ite.dataName === "fileUrls"){
                ite.validate=[];
            };
        });
    }else{
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "overTime"){
                ite.labelName = "请假时长（天）";
            }else if(ite.dataName === "fileUrls"){
                ite.validate=(e.includes("年假") || ["事假","产假","产检假","丧假","育儿假"].includes(e) ) ? [] : [
                    {
                        required:true,
                        message:"请上传附件",
                        trigger:"blur"
                    }
                ];
            };
        });
    };
    formConfig.itemConfig.forEach((ite)=>{
        if(ite.dataName === "reason"){
            ite.isHide=true;
        };
    });
    formConfigRef.value.createFormRules();
};
// 选择请假开始时间失去焦点事件监听
const blurSelectStartTime=async (item,e)=>{
    if(new Date().getTime() >= new Date(e).getTime()){ // 若请假申请开始时间小于当前时间，则需要填写情况说明
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "reason"){
                ite.isHide=false;
            };
        });
    }else{
        formConfig.itemConfig.forEach((ite)=>{
            if(ite.dataName === "reason"){
                ite.isHide=true;
            };
        });
    };
    formConfigRef.value.createFormRules();
    if(e && formConfig.submitData.endTime){
        await getLeaveCountData(e,formConfig.submitData.endTime,formConfig.submitData.leaveType);
        await calculateLeaveConfilctData(e,formConfig.submitData.endTime);
    };
};
// 选择请假结束时间失去焦点事件监听
const blurSelectEndTime=async (item,e)=>{
    if(e && formConfig.submitData.startTime){
        await getLeaveCountData(formConfig.submitData.startTime,e,formConfig.submitData.leaveType);
        await calculateLeaveConfilctData(formConfig.submitData.startTime,e);
    };
};
// 获取请假时长事件监听
const getLeaveCountData=async (startTime,endTime,leaveName)=>{
    console.log(111)
    await getLeaveCount({
        startTime,
        endTime,
        leaveName,
        leaveType:leaveName === "调休假" ? 0 : 1 // 只有调休假时为0，其余假为1
    }).then((res)=>{
        if(res.code === 200){
            console.log(res);
            formConfig.submitData.overTime=res.data;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 计算请假时间有无冲突事件监听
const calculateLeaveConfilctData=async (startTime,endTime)=>{
    await calculateLeaveConfilct({
        procInsId:propsValue.processInfo.procInsId,
        processKey:2,
        params:{
            startTime,
            endTime
        }
    }).then((res)=>{
        if(res.code === 200){
            if(!res.data){
                ElMessage({
                    message:"该时间段已提交请假申请，请勿重复提交",
                    type:"error"
                });
            };
            flag.value=!res.data;
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 初始化获取请假类型下拉数据事件监听
const getLeaveTypeData=async (formData)=>{
    // console.log(formData);
    await getLeaveTypeList().then((res)=>{
        if(res.code === 200){
            // console.log(res);
            formConfig.selectLists.leaveTypeList=res.data.map((item)=>{
                if(item.name === "调休假"){
                    leaveType.compensatoryLeave=formData.leaveType === "调休假" ? item.value+formData.overTime : item.value ;
                    item.label=formData.leaveType === "调休假" ? item.name+`（剩余${item.value+formData.overTime+item.unit}）`
                        : item.name+`（剩余${item.value+item.unit}）`;
                }else if(item.name === "年假"+new Date().getFullYear()){
                    leaveType.annualLeaveCurrent=formData.leaveType === "年假1" ? item.value+formData.overTime : item.value;
                    item.label=formData.leaveType === "年假1" ? item.name+`（剩余${item.value+formData.overTime+item.unit}）`
                        : item.name+`（剩余${item.value+item.unit}）`;
                }else if(item.name === "年假"+(new Date().getFullYear() - 1)){
                    leaveType.annualLeaveLast=formData.leaveType === "年假0" ? item.value+formData.overTime : item.value;
                    item.label=formData.leaveType === "年假0" ? item.name+`（剩余${item.value+formData.overTime+item.unit}）`
                        : item.name+`（剩余${item.value+item.unit}）`;
                }else if(item.name === "育儿假"){
                    leaveType.parentalLeave=formData.leaveType === "育儿假" ? item.value+formData.overTime : item.value;
                    item.label=formData.leaveType === "育儿假" ? item.name+`（剩余${item.value+formData.overTime+item.unit}）`
                        : item.name+`（剩余${item.value+item.unit}）`;
                };
                return {
                    label:item.label,
                    value:item.name === "年假"+new Date().getFullYear() ? "年假1" : item.name === "年假"+(new Date().getFullYear()-1) ? "年假0" : item.name
                };
            });
        }else{
            ElMessage({
                message:res.msg || "系统错误",
                type:"error"
            });
        };
    });
};
// 点击提交按钮事件监听
const submitData=()=>{
    formConfigRef.value.formValidate((valid)=>{
        if(valid){
            if(flag.value){
                ElMessage({
                    message:"该时间段已提交请假申请，请勿重复提交",
                    type:"error"
                });
                return;
            };
            submitWithdrawProcess({
                procInsId:propsValue.processInfo.procInsId,
                taskId:propsValue.processInfo.taskId,
                comment:"",
                variables:{
                    startTime:formConfig.submitData.startTime,
                    endTime:formConfig.submitData.endTime,
                    customDataForm:JSON.stringify(formConfig.submitData),
                    leaveType:formConfig.submitData.leaveType,
                    overTime:formConfig.submitData.overTime
                }
            }).then((res)=>{
                if(res.code === 200){
                    formConfigRef.value.formResetFields();
                    emits("backTable");
                    ElMessage({
                        message:"提交待办请假申请成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

// 查看组件配置项
const viewConfig=reactive({
    // isTable:true,
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"请假类型",
            value:"leaveType",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假开始时间",
            value:"startTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假结束时间",
            value:"endTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假时长（小时）",
            value:"overTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假事由",
            value:"workContent",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"情况说明",
            value:"reason",
            type:"text",
            isHide:true,
            labelSuffix:"：",
            col:24
        },
        {
            label:"图片",
            value:"fileUrls",
            type:"image",
            labelSuffix:"：",
            col:24
        },
        {
            label:"其他附件",
            value:"otherFiles",
            type:"file",
            labelSuffix:"：",
            isHide:true,
            col:24
        }
    ],
    viewData:{
        leaveType:"",
        startTime:"",
        endTime:"",
        overTime:"",
        workContent:"",
        reason:"",
        fileUrls:"",
        otherFiles:""
    }
});
// 表单配置项
const formConfig=reactive({
    elFormConfig:{
        labelWidth:"150px",
        style:"padding:20px 0 40px;"
    },
    itemConfig:[
        {
            labelName:"请假类型",
            dataName:"leaveType",
            type:"select",
            dataListName:"leaveTypeList",
            style:"width:300px;",
            width:24,
            change:changeLeaveType,
            validate:[
                {
                    required:true,
                    message:"请选择请假类型",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"请假开始时间",
            dataName:"startTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,8,30,0),
            domWidth:300,
            width:24,
            change:blurSelectStartTime,
            validate:[
                {
                    required:true,
                    message:"请选择请假开始时间",
                    trigger:"blur"
                },
                {
                    validator:checkStartTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"请假结束时间",
            dataName:"endTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,17,30,0),
            domWidth:300,
            width:24,
            change:blurSelectEndTime,
            validate:[
                {
                    required:true,
                    message:"请选择请假结束时间",
                    trigger:"blur"
                },
                {
                    validator:checkEndTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"请假事由",
            dataName:"workContent",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请输入请假事由",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"情况说明",
            dataName:"reason",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            isHide:true,
            placeholder:"请假申请需提前申请，您此次流程超出规定提交时间，请详细补充情况说明。",
            validate:[
                {
                    required:true,
                    message:"请填写情况说明",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"请假时长（小时）",
            dataName:"overTime",
            type:"input",
            inputType:"number",
            placeholder:"请输入",
            disabled:true,
            width:24,
            style:"width:300px;",
            validate:[
                {
                    validator:checkOvertime,
                    trigger:"change"
                }
            ]
        },
        {
            labelName:"上传图片",
            dataName:"fileUrls",
            type:"image",
            fileList:[],
            fileType:["png","jpg","jpeg","gif"],
            fileSize:20 * 1000 * 1000,
            multiple:true,
            limit:5,
            width:24,
            validate:[]
        },
        {
            labelName:"其他附件",
            dataName:"otherFiles",
            type:"upload",
            fileList:[],
            fileSize:20 * 1000 * 1000,
            fileType:["pdf","xls","xlsx","doc","docx","png","jpg","jpeg","gif"],
            fileTypeText:"支持pdf、excel、word和png等图片文件格式",
            multiple:true,
            isHide:true,
            limit:20
        }
    ],
    selectLists:{
        leaveTypeList:[]
    },
    // buttonConfig:[
    //     {
    //         btnName:"提交",
    //         type:"primary",
    //         size:"default",
    //         style:"margin-left:160px;",
    //         formRefName:formConfigRef,
    //         click:submitData
    //     },
    //     {
    //         btnName:"保存",
    //         type:"default",
    //         size:"default",
    //         formRefName:formConfigRef,
    //         click:saveData
    //     }
    // ],
    submitData:{
        leaveType:"调休假",
        startTime:"",
        endTime:"",
        workContent:"",
        reason:"",
        overTime:"",
        fileUrls:"",
        otherFiles:""
    }
});


/*
    生命周期等代码区域
*/
onMounted(async ()=>{
    if(propsValue.processInfo.formDataEdit){
        await getLeaveTypeData({...propsValue.formData});
        formConfig.submitData={...propsValue.formData};
        // console.log(formConfig.submitData);
        if(formConfig.submitData.leaveType === "年假1"){
            if(new Date(formConfig.submitData.startTime).getFullYear() !== new Date().getFullYear()){
                formConfig.submitData.leaveType="年假0";
            };
        };
        if(formConfig.submitData.reason){ //若有情况说明，则表示该数据为请假开始时间小于当时申请时间提出的申请
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "reason"){
                    item.isHide=false;
                };
            });
        };
        if(formConfig.submitData.leaveType === "调休假"){
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "overTime"){
                    item.labelName = "请假时长（小时）";
                }else if(item.dataName === "fileUrls"){
                    item.validate=[];
                };
            });
        }else{
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "overTime"){
                    item.labelName = "请假时长（天）";
                }else if(item.dataName === "fileUrls"){
                    item.validate=(formConfig.submitData.leaveType.includes("年假") || ["事假","产假","产检假","丧假","育儿假"].includes(formConfig.submitData.leaveType) ) ? [] : [
                        {
                            required:true,
                            message:"请上传附件",
                            trigger:"blur"
                        }
                    ];
                };
            });
        };
        if(formConfig.submitData.otherFiles && formConfig.submitData.otherFiles.length > 0){
            formConfig.itemConfig.forEach((item)=>{
                if(item.dataName === "otherFiles"){
                    item.isHide=false;
                };
            });
        };
        formConfigRef.value.createFormRules();
    }else{
        viewConfig.viewData={...propsValue.formData};
        // console.log(viewConfig.viewData);
        viewConfig.itemConfig.forEach((item)=>{
            if(item.value === "reason"){
                item.isHide= viewConfig.viewData["reason"] ? false : true;
            }else if(item.value === "overTime"){
                if(viewConfig.viewData.leaveType === "调休假"){
                    item.label="请假时长（小时）";
                }else{
                    item.label="请假时长（天）";
                };
            };
        });
        if(viewConfig.viewData.leaveType === "年假1"){ //若用今年年假请，则请假日期必然是今年的1月-12月
            viewConfig.viewData.leaveType="年假"+new Date(viewConfig.viewData.startTime).getFullYear();
        }else if(viewConfig.viewData.leaveType === "年假0"){ //若用去年年假请，则请假日期则为去年的12月-今年的3月
            if(new Date(viewConfig.viewData.startTime).getMonth()+1 === 12){ //若月份为12月
                viewConfig.viewData.leaveType="年假"+new Date(viewConfig.viewData.startTime).getFullYear();
            }else{
                viewConfig.viewData.leaveType="年假"+(new Date(viewConfig.viewData.startTime).getFullYear()-1);
            };
        };
        if(viewConfig.viewData.otherFiles && viewConfig.viewData.otherFiles.length>0){
            viewConfig.itemConfig.forEach((item)=>{
                if(item.value === "otherFiles"){
                    item.isHide=false;
                };
            });
        };
    };
});
// 暴露子组件方法
defineExpose({
    submitData
});

</script>

<style lang="scss" scoped>
.qilin-leaveApplication{
    width:100%;
    height:100%;
    >span{
        color:$text-color-45;
        margin-top:20px;
        display:inline-block;
        padding-left:20px;
    }
}
</style>
