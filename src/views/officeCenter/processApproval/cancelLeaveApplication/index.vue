<template>
    <div class="qilin-cancelLeaveApplication hide-scrollbar">
        <template v-if="!propsValue.processInfo.formDataEdit">
            <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
        </template>
        <template v-else>
            <QilinView v-model:viewConfig="approvalViewConfig"></QilinView>
            <QilinForm v-model:formConfig="formConfig" ref="formConfigRef"></QilinForm>
        </template>
    </div>
</template>

<script setup>
import Qilin from "qilin-utils";
import {
    submitWithdrawProcess
} from "@/api/officeCenter/processApproval/index.js";
import {getLeaveCount} from "@/api/officeCenter/leaveApplication/index.js";
import { login } from "@/store/login.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
const loginStore=login();

/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);
// 获取表单组件元素DOM
const formConfigRef=ref(null);


/*
    计算属性等代码区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);


/*
    逻辑脚本代码区域
*/
// 校验销假开始时间事件监听
const checkStartTime=(rule,value,callback)=>{
    if(value){
        // console.log(Qilin.Date.compareDate(value,propsValue.formData.startTime))
        if(Qilin.Date.compareDate(value,propsValue.formData.startTime)){
            callback("销假开始时间不得早于请假开始时间");
        }else if(formConfig.submitData.counteractEndTime && !Qilin.Date.compareDate(value,formConfig.submitData.counteractEndTime)){
            callback("销假开始时间不得早于销假结束时间");
        }else{
            callback();
        };
    };
};
// 校验销假结束时间事件监听
const checkEndTime=(rule,value,callback)=>{
    if(value){
        if(Qilin.Date.compareDate(propsValue.formData.endTime,value)){
            callback("销假结束时间不得晚于请假结束时间");
        }else if(formConfig.submitData.counteractStartTime && Qilin.Date.compareDate(value,formConfig.submitData.counteractStartTime)){
            callback("销假结束时间不得晚于销假开始时间");
        }else{
            callback();
        };
    };
};
// 选择销假开始时间事件监听
const blurCounteractStartTime=(item,e)=>{
    // console.log(item,e)
    if(e && formConfig.submitData.counteractEndTime){
        getOvertime(e,formConfig.submitData.counteractEndTime);
    };
};
// 选择销假结束时间事件监听
const blurCounteractEndTime=(item,e)=>{
    if(e && formConfig.submitData.counteractStartTime){
        getOvertime(formConfig.submitData.counteractStartTime,e);
    };
};
// 获取销假时长事件监听
const getOvertime=(startTime,endTime)=>{
    getLeaveCount({
        startTime,
        endTime,
        leaveType:propsValue.formData.leaveType === "调休假" ? 0 : 1
    }).then((res)=>{
        if(res.code === 200){
            formConfig.submitData.counteractOverTime=res.data;
        };
    });
};
// 点击提交按钮事件监听
const submitData=()=>{
    formConfigRef.value.formValidate((valid)=>{
        if(valid){
            submitWithdrawProcess({
                procInsId:propsValue.processInfo.procInsId,
                taskId:propsValue.processInfo.taskId,
                comment:"",
                variables:{
                    startTime:propsValue.formData.startTime,
                    endTime:propsValue.formData.endTime,
                    customDataForm:JSON.stringify({
                        ...formConfig.submitData,
                        leaveType:propsValue.formData.leaveType,
                        startTime:propsValue.formData.startTime,
                        endTime:propsValue.formData.endTime,
                        overTime:propsValue.formData.overTime
                    })
                }
            }).then((res)=>{
                if(res.code === 200){
                    formConfigRef.value.formResetFields();
                    emits("backTable");
                    ElMessage({
                        message:"提交待办销假申请成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

// 查看表单配置项
const viewConfig=reactive({
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"请假类型",
            value:"leaveType",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假开始时间",
            value:"startTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假时长",
            value:"overTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假结束时间",
            value:"endTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"销假开始时间",
            value:"counteractStartTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"销假结束时间",
            value:"counteractEndTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"销假时长",
            value:"counteractOverTime",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"销假事由",
            value:"workContent",
            type:"text",
            labelSuffix:"：",
            col:24
        }
    ],
    viewData:{
        leaveType:"",
        startTime:"",
        overTime:"",
        endTime:"",
        counteractStartTime:"",
        counteractEndTime:"",
        counteractOverTime:"",
        workContent:""
    }
});
// 审批时查看表单配置项
const approvalViewConfig=reactive({
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"请假类型",
            value:"leaveType",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假开始时间",
            value:"startTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假时长",
            value:"overTime",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"请假结束时间",
            value:"endTime",
            type:"text",
            labelSuffix:"：",
            col:12
        }
    ],
    viewData:{
        leaveType:"",
        startTime:"",
        overTime:"",
        endTime:""
    }
});
// 审批时申请表单配置项
const formConfig=reactive({
    elFormConfig:{
        labelWidth:"130px",
    },
    itemConfig:[
        {
            labelName:"销假开始时间",
            dataName:"counteractStartTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            valueFormat:"YYYY-MM-DD HH:mm:ss",
            defaultTime:new Date(2000,1,1,8,30,0),
            width:24,
            domWidth:300,
            change:blurCounteractStartTime,
            validate:[
                {
                    required:true,
                    message:"请选择销假开始时间",
                    trigger:"blur"
                },
                {
                    validator:checkStartTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"销假结束时间",
            dataName:"counteractEndTime",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            valueFormat:"YYYY-MM-DD HH:mm:ss",
            defaultTime:new Date(2000,1,1,17,30,0),
            width:24,
            domWidth:300,
            change:blurCounteractEndTime,
            validate:[
                {
                    required:true,
                    message:"请选择销假结束时间",
                    trigger:"blur"
                },
                {
                    validator:checkEndTime,
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"销假时长/h",
            dataName:"counteractOverTime",
            type:"input",
            inputType:"number",
            width:24,
            disabled:true,
            style:"width:300px;",
            validate:[
                {
                    required:true,
                    message:"请输入销假时长",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"销假事由",
            dataName:"workContent",
            type:"input",
            inputType:"textarea",
            width:24,
            rows:3,
            validate:[
                {
                    required:true,
                    message:"请输入销假事由",
                    trigger:"blur"
                }
            ]
        }
    ],
    submitData:{
        counteractStartTime:"",
        counteractEndTime:"",
        counteractOverTime:"",
        workContent:""
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    if(propsValue.processInfo.formDataEdit){
        formConfig.submitData=approvalViewConfig.viewData={...propsValue.formData};
        if(approvalViewConfig.viewData.leaveType === "调休假"){
            approvalViewConfig.viewData.overTime=approvalViewConfig.viewData.overTime+"小时";
            approvalViewConfig.itemConfig[2].label="请假时长（小时）";
            formConfig.itemConfig[2].labelName="销假时长（小时）";
        }else{
            approvalViewConfig.viewData.overTime=approvalViewConfig.viewData.overTime+"天";
            approvalViewConfig.itemConfig[2].label="请假时长（天）";
            formConfig.itemConfig[2].labelName="销假时长（天）";
        };
    }else{
        viewConfig.viewData={...propsValue.formData};
        if(viewConfig.viewData.leaveType === "调休假"){
            viewConfig.viewData.overTime=viewConfig.viewData.overTime+"小时";
            viewConfig.viewData.counteractOverTime=viewConfig.viewData.counteractOverTime+"小时";
            viewConfig.itemConfig[2].label="请假时长（小时）";
            viewConfig.itemConfig[6].label="销假时长（小时）";
        }else{
            viewConfig.viewData.overTime=viewConfig.viewData.overTime+"天";
            viewConfig.viewData.counteractOverTime=viewConfig.viewData.counteractOverTime+"天";
            viewConfig.itemConfig[2].label="请假时长（天）";
            viewConfig.itemConfig[6].label="销假时长（天）";
        };
        if(viewConfig.viewData.leaveType === "年假1"){ //若用今年年假请，则请假日期必然是今年的1月-12月
            viewConfig.viewData.leaveType="年假"+new Date(viewConfig.viewData.startTime).getFullYear();
        }else if(viewConfig.viewData.leaveType === "年假0"){ //若用去年年假请，则请假日期则为去年的12月-今年的3月
            if(new Date(viewConfig.viewData.startTime).getMonth()+1 === 12){ //若月份为12月
                viewConfig.viewData.leaveType="年假"+new Date(viewConfig.viewData.startTime).getFullYear();
            }else{
                viewConfig.viewData.leaveType="年假"+(new Date(viewConfig.viewData.startTime).getFullYear()-1);
            };
        };
    };
});
defineExpose({
    submitData
});

</script>

<style lang="scss" scoped>

</style>
