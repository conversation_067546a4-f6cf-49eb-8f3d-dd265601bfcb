<template>
    <div class="qilin-usingVehicle hide-scrollbar">
        <template v-if="!propsValue.processInfo.formDataEdit">
            <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
        </template>
        <template v-else>
            <QilinForm v-model:formConfig="formConfig" ref="formConfigRef"></QilinForm>
        </template>
    </div>
</template>

<script setup>
import {
    submitWithdrawProcess
} from "@/api/officeCenter/processApproval/index.js";
import {getCountryAddressData} from "@/utils/common.js";
import { login } from "@/store/login.js";
import {useRoute,useRouter} from "vue-router";
const route=useRoute();
const router=useRouter();
const loginStore=login();

/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);
// 获取表单组件元素DOM
const formConfigRef=ref(null);


/*
    计算属性等代码区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);



/*
    逻辑脚本代码区域
*/
// 点击提交按钮事件监听
const submitData=()=>{
    formConfigRef.value.formValidate((valid)=>{
        if(valid){
            submitWithdrawProcess({
                procInsId:propsValue.processInfo.procInsId,
                taskId:propsValue.processInfo.taskId,
                comment:"",
                variables:{
                    customDataForm:JSON.stringify(formConfig.submitData)
                }
            }).then((res)=>{
                if(res.code === 200){
                    formConfigRef.value.formResetFields();
                    emits("backTable");
                    ElMessage({
                        message:"提交用车申请成功",
                        type:"success"
                    });
                }else{
                    ElMessage({
                        message:res.msg || "系统错误",
                        type:"error"
                    });
                };
            });
        };
    });
};

// 查看组件配置项
const viewConfig=reactive({
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"用车去向",
            value:"destination",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"车辆型号",
            value:"model",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"用车时间",
            value:"time",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"联系人号码",
            value:"phonenumber",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"用车事由",
            value:"workContent",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"附件",
            value:"fileUrls",
            type:"file",
            labelSuffix:"：",
            col:24
        }
    ],
    viewData:{
        destination:"",
        model:"",
        time:"",
        phonenumber:"",
        workContent:"",
        fileUrls:""
    }
});
// 表单配置项
const formConfig=reactive({
    elFormConfig:{
        labelWidth:"150px",
        style:"padding:20px 0 40px;"
    },
    itemConfig:[
        {
            labelName:"用车去向",
            dataName:"destination",
            type:"input",
            width:24,
            style:"width:300px;",
            validate:[
                {
                    required:true,
                    message:"请输入用车去向",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"车辆型号",
            dataName:"model",
            type:"input",
            inputType:"text",
            width:24,
            style:"width:300px;",
            validate:[
                {
                    required:true,
                    message:"请输入车辆型号",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"用车时间",
            dataName:"time",
            type:"date",
            dateType:"datetime",
            format:"YYYY-MM-DD HH:mm",
            defaultTime:new Date(2000,1,1,8,30,0),
            width:24,
            domWidth:300,
            validate:[
                {
                    required:true,
                    message:"请选择用车时间",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"联系人号码",
            dataName:"phonenumber",
            type:"input",
            inputType:"text",
            width:24,
            style:"width:300px;",
            validate:[
                {
                    required:true,
                    message:"请输入联系人号码",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"用车事由",
            dataName:"workContent",
            type:"input",
            inputType:"textarea",
            rows:3,
            width:24,
            validate:[
                {
                    required:true,
                    message:"请输入用车事由",
                    trigger:"blur"
                }
            ]
        },
        {
            labelName:"附件",
            dataName:"fileUrls",
            type:"upload",
            fileList:[],
            fileSize:20 * 1000 * 1000,
            fileType:["pdf","xls","xlsx","doc","docx","png","jpg","jpeg","gif"],
            fileTypeText:"支持pdf、excel、word和png等图片文件格式",
            multiple:true,
            limit:20
        }
    ],
    submitData:{
        destination:"",
        model:"",
        time:"",
        phonenumber:"",
        workContent:"",
        fileUrls:""
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    // console.log(propsValue.processInfo)
    if(propsValue.processInfo.formDataEdit){
        formConfig.submitData={...propsValue.formData};
    }else{
        viewConfig.viewData={...propsValue.formData};
    };
});
// 暴露子组件方法
defineExpose({
    submitData
});

</script>

<style lang="scss" scoped>
.qilin-usingVehicle{

}
</style>
