<template>
    <div class="qilin-clockApplication hide-scrollbar">
        <QilinView v-model:viewConfig="viewConfig" ref="viewConfigRef"></QilinView>
    </div>
</template>

<script setup>

/*
    数据变量定义区域
*/
// 获取查看组件元素DOM
const viewConfigRef=ref(null);


/*
    计算属性等代码区域
*/
// 接收来自父组件的传值
const propsValue=defineProps({
    processInfo:{
        type:Object,
        default(){
            return {}
        }
    },
    formData:{
        type:Object,
        default(){
            return {};
        }
    }
});
// console.log(propsValue.processInfo,propsValue.formData);
// 暴露父组件自定义方法
const emits=defineEmits(["backTable"]);


/*
    逻辑脚本代码区域
*/
// 查看组件配置项
const viewConfig=reactive({
    headerConfig:{
        hideHeader:true
    },
    itemConfig:[
        {
            label:"补卡理由",
            value:"reason",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"补卡时间",
            value:"recordDate",
            type:"text",
            labelSuffix:"：",
            col:12
        },
        {
            label:"补卡事由",
            value:"workContent",
            type:"text",
            labelSuffix:"：",
            col:24
        },
        {
            label:"附件",
            value:"fileUrls",
            type:"file",
            labelSuffix:"：",
            col:24
        }
    ],
    viewData:{
        reason:"",
        recordDate:"",
        workContent:"",
        fileUrls:""
    }
});


/*
    生命周期等代码区域
*/
onMounted(()=>{
    viewConfig.viewData={...propsValue.formData};
});

</script>

<style lang="scss" scoped>

</style>
