<template>
  <div class="ability">
    <div class="rightContent">
      <div class="solution flex">
        <div class="right_title">能力汇聚</div>
        <div class="checkMore pointer" @click="checkMore">
          <span>全部能力 </span><span> &nbsp;></span>
        </div>
        <div class="arrow-right">
          <span class="arrow-line"></span> <span class="arrow"> </span>
        </div>
      </div>

      <div class="right_text">
        <p>
          为满足不同行业客户的特定需求，将网络、云、平台、应用和终端等多种技术融合在一起，贯穿整个销售、交付和服务等全流程。
        </p>
        <p>
          通过能力汇聚生产、属地化运营，汇聚全网能力、属地能力、生态能力，支撑集中调用、客户侧部署等多类能力的使用和计量，同步建立属地运营管理流程。
        </p>
        <p>
          可独立提供全流程服务，并支持快速组合调用，为客户提供更加个性化、高效的能力支持服务。
        </p>
      </div>
    </div>
    <div class="cardPlay flex">
      <div
        class="card_item flex"
        v-for="(item, index) in abilityList"
        :key="index"
        @click="toAbilityDetail(item.id)"
      >
        <img
          v-if="item.abilityPictureUrl"
          style="width: 180px; height: 112px"
          :src="`${item.abilityPictureUrl}`"
          alt=""
        />
        <img
          v-else
          src="@/assets/images/ability/empty.jpg"
          style="width: 180px; height: 112px"
          alt=""
        />
        <div class="left">
          <a-tag class="tag">{{ item.abilityAreas }}</a-tag>
          <span class="city">{{ item.city }}</span>
          <span class="left_title">{{ item.abilityName }}</span>
          <p class="desc">{{ item.abilityDesc }}</p>
          <div class="footer flex">
            <div class="footer_left">
              <a-tag
                :bordered="false"
                :class="{
                  cityStyle: true,
                  common: item.abilityType == '通用能力',
                  industry: item.abilityType == '行业能力',
                }"
                >{{ item.abilityType }}</a-tag
              >
            </div>
            <div class="footer_right">
              <img
                src="@/assets/images/home/<USER>"
                style="width: 16px; height: 16px"
                alt=""
              />
              <span style="font-size: 12px; color: rgba(0, 0, 0, 0.45)">
                {{ item.viewsNum }}</span
              >
              <!-- 
              <img src="@/assets/images/ability/position.svg" style="width:12px;height:11px;margin-left: 16px;" />
              <span style="font-size: 12px;color: rgba(0,0,0,0.45)">{{ item.city }}</span> -->
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import { useRouter } from "vue-router";
import { getHotAbility } from "@/api/ability/home.js";
import { getMakeUrl } from "@/utils/getUrl";

export default defineComponent({
  setup() {
    const baseURL = getMakeUrl();
    const Router = useRouter();
    const data = reactive({
      abilityList: [],
    });
    const getList = () => {
      let params = {
        pageNo: 1,
        pageSize: 4,
        sortType: 3,
      };
      getHotAbility(params).then((res) => {
        data.abilityList = res.data.rows;
      });
    };
    const toAbilityDetail = (val) => {
      Router.push({
        query: {
          id: val,
        },
        name: "abilityDetail",
      });
    };
    getList();
    const checkMore = () => {
      Router.push({
        name: "abilityHome",
      });
    };
    return {
      ...toRefs(data),
      Router,
      baseURL,
      checkMore,
      toAbilityDetail,
    };
  },
});
</script>

<style lang="scss" scoped>
.ability {
  position: relative;
  padding: 0 120px;
  height: 750px;
  background-image: url("@/assets/images/home/<USER>");
  margin-top: 100px auto;
  background-repeat: no-repeat;
  background-size: cover;
}

.rightContent {
  margin-top: 100px;

  .solution {
    position: relative;
    justify-content: space-between;

    .arrow-right {
      position: absolute;
      opacity: 0.85;
      left: 150px;
      top: 7px;

      .arrow-line {
        display: inline-block;
        width: 112px;
        height: 2px;
        background-color: #325a88;
        margin-left: -12px;
      }

      .arrow {
        width: 10px;
        height: 10px;
        border-top: 2px solid #325a88;
        border-right: 2px solid #325a88;
        transform: rotate(45deg);
        position: absolute;
        left: 91px;
        top: 10px;
      }
    }
  }

  .right_title {
    font-weight: 500;
    font-size: 28px;
    color: #24456a;
    float: right;
    margin-right: 115px;
  }

  .right_text {
    font-size: 15px;
    color: rgba(0, 0, 0, 0.85);
    width: 92%;
    font-weight: 400;
    line-height: 34px;
    margin-top: 30px;
  }

  .checkMore {
    width: 90px;
    height: 28px;
    background: rgba(30, 99, 255, 0.06);
    border-radius: 4px;
    font-size: 14px;
    color: #1e63ff;
    text-align: center;
    line-height: 28px;
    float: right;
    margin-top: 10px;
  }
}

.cardPlay {
  justify-content: space-between;
  flex-wrap: wrap;
  margin: 80px auto;
  height: 400px;

  .card_item {
    width: 48%;
    padding: 24px;
    height: 160px;
    background: #f5f7fc;
    box-shadow: 0px 0px 24px 0px rgba(116, 157, 219, 0.3);
    cursor: pointer;
    .left {
      width: 100%;
      padding-left: 24px;

      .tag {
        border: none;
        background: #d7e6ff;
        border-radius: 2px 2px 2px 2px;
        font-weight: 500;
        font-size: 12px;
        color: #2e7fff;
      }

      .city {
        font-size: 12px;
        font-weight: 400;
        color: rgba(0, 0, 0, 0.45);
        display: inline-block;
        float: right;
      }

      .desc {
        margin-top: 12px;
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
      }

      .cityStyle {
        font-size: 12px;
        text-align: center;
        background: #f5f7fc;
        width: 64px;
        height: 20px;
        line-height: 18px;
        font-weight: 400;
        border-radius: 100px;
      }

      .common {
        border: 1px solid #236cff;
        color: #236cff;
      }

      .industry {
        border: 1px solid #fa8c16;
        color: #fa8c16;
      }

      .footer {
        justify-content: space-between;

        div {
          width: 50%;
        }

        .footer_right {
          text-align: right;
        }
      }
    }
  }
}
</style>