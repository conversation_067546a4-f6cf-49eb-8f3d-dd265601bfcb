<template>
  <!-- 方案数据 -->
  <info />
  <!-- 解决方案 -->
  <!-- <ability /> -->
  <!-- 热门产品 -->
  <!-- <cases /> -->
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import info from "./components/info.vue";
// import ability from "./components/ability.vue";
// import cases from "./components/cases.vue";
import { useRouter } from "vue-router";

export default defineComponent({
  components: {
    info,
    // ability,
    // cases,
  },
  setup() {
    const Router = useRouter();
    const data = reactive({
      desList: ["快速引入", "沉淀复用", "组织协调", "机制保障"],
      newsList: [
        "江苏移动通信能力建设公告发布",
        "南京能力最新版本发布",
        "燃气自动化能力的全面提升与系统集成方",
      ],
      current: null,
    });
    const moveChange = (val) => {
      data.current = val;
    };
    const leaveChange = () => {
      data.current = null;
    };
    const centerPage = () => {
      Router.push({
        name: "center",
      });
    };
    return {
      ...toRefs(data),
      moveChange,
      leaveChange,
      centerPage,
    };
  },
});
</script>
<style lang="scss" scoped>
.topContent {
  width: 100%;
  background-image: url("@/assets/images/home/<USER>");
  height: 709px;
  background-repeat: no-repeat;
  background-size: cover;

  .des {
    font-weight: 600;
    font-size: 40px;
    color: #24456a;
  }

  .introduce {
    width: 100px;
    height: 36px;
    border-radius: 2px;
    border: 1px solid #24456a;
    font-size: 16px;
    color: #24456a;
    text-align: center;
    line-height: 36px;
    margin-right: 14px;
  }

  .rightClass {
    right: 80px;
    position: absolute;
    bottom: -10px;
  }

  .leftClass {
    position: absolute;
    bottom: -6px;
    left: -46px;
  }
}

.dynamic {
  background: #ffffff;
  box-shadow: 0px 8px 6px 0px rgba(74, 86, 111, 0.06);
  padding: 18px 0;
  .news {
    margin-right: 80px;
  }
  .newColor {
    width: 30px;
    height: 15px;
    margin-top: -6px;
  }
  .newsTitle {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 1;
    overflow: hidden;
    text-overflow: ellipsis;
    font-size: 16px;
    color: #24456a;
    border-bottom: 1px solid #24456a;
  }
  .checkMore {
    width: 48px;
    height: 24px;
    background: rgba(30, 99, 255, 0.06);
    border-radius: 2px 2px 2px 2px;
    font-size: 12px;
    color: #1e63ff;
    text-align: center;
    margin-right: 119px;
  }

  .activeTitle {
    color: #236cff;
    border-bottom: 1px solid #236cff;
  }
}
</style>