<template>
  <div id="make">
    <div
      class="link pointer"
      @click="newPage(detailObj.linkUrl, detailObj.name)"
    >
      <span class="text">获取</span><span class="arrow"> </span>
    </div>
    <div class="horizontal">
      <span class="active"> 简介</span>
    </div>

    <p class="first content">
      {{ detailObj.description }}
    </p>
    <div class="horizontal">
      <span class="active">工具概述</span>
    </div>
    <!-- <p class="title">{{ detailObj.toolTitle }} </p> -->
    <p class="content">
      {{ detailObj.toolDescription }}
    </p>
    <img class="img" :src="`${detailObj.toolImgUrl}`" />
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import { getDetail } from "@/api/makeUp/make.js";
import { getMakeUrl } from "@/utils/getUrl.js";
import { AESlx } from "@/utils/aes.js";

export default defineComponent({
  props: {
    id: {
      type: Number,
      default: "",
    },
  },
  setup(props) {
    const baseURL = getMakeUrl();
    const lxToken = encodeURIComponent(AESlx());
    const data = reactive({
      detailObj: {},
    });
    const getDetailById = () => {
      getDetail({ id: props.id }).then((res) => {
        if (res.code === 200) {
          data.detailObj = res.data;
        }
      });
    };
    getDetailById();

    const newPage = (url, name) => {
      if (name == "灵犀低代码平台") {
        window.open(`${url}?token=${lxToken}`, "_blank");
        return;
      }
      window.open(url, "_blank");
    };
    return {
      ...toRefs(data),
      baseURL,
      lxToken,
      newPage,
    };
  },
});
</script>

<style lang="scss" scoped>
.ant-modal-header {
  box-shadow: 0px 2px 4px 0px rgba(201, 218, 244, 0.3);
}

:deep(.ant-modal-body) {
  padding: 24px 36px !important;
}

#make {
  color: #24456a;
  font-size: 14px;
  max-height: 600px;
  overflow-y: auto;

  .link {
    height: 28px;
    line-height: 28px;
    position: absolute;
    right: 60px;
    top: 15px;
    color: #fff;
    background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
    border-radius: 4px;

    .arrow {
      width: 8px;
      height: 8px;
      border-top: 1px solid #fff;
      border-right: 1px solid #fff;
      transform: rotate(45deg);
      position: absolute;
      right: 14px;
      top: 10px;
    }

    .text {
      padding-left: 12px;
      padding-right: 32px;
    }
  }

  .horizontal {
    width: 100%;
    border-bottom: 1px solid #e9e9e9;
    font-weight: 500;
    font-size: 16px;
    color: #24456a;
  }

  .active {
    display: inline-block;
    padding: 8px 0;
    margin-bottom: -1px;
    border-bottom: 2px solid #236cff;
  }

  .title {
    margin-top: 16px;
    font-weight: 500;
  }

  .first {
    margin: 16px 0 36px 0;
  }

  .content {
    font-weight: 400;
    line-height: 34px;
    text-indent: 2rem;
  }

  .img {
    display: block;
    height: 450px;
    width: 720px;
    margin: 0 auto;
  }
}
</style>