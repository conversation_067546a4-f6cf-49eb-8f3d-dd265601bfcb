.searchInfo {
    background-image: url("@/assets/images/home/<USER>");
    background-repeat: no-repeat;
    background-size: 100% 100%;
    margin: 0 120px;
    padding: 24px 0;

    .vocationPull {
        // background: #FFFFFF;
        margin: 0 40px;
        display: flex;
        align-items: center;
        //     border-top-left-radius: 5px;
        //     border-bottom-left-radius: 5px;
        //     border-top-right-radius: 5px;
        //     border-bottom-right-radius: 5px;
    }

    .inputClass {
        border: none;
        width: 60%;
        height: 56px;
        box-shadow: none !important;
        border-radius: 5px;
        margin-right: 16px;
    }

    .line {
        width: 1px;
        height: 26px;
        background: #EFF0F4;
    }

    .lines {
        width: 1px;
        height: 56px;
        height: 56px;
        background: #EFF0F4;
    }

    :deep(.ant-select) {
        height: 56px;
        background-color: #fff;
        border-radius: 5px;
    }

    :deep(.ant-select-selector) {
        margin-top: 12px;
        font-size: 16px;
        font-weight: 500;
        color: rgba(0, 0, 0, 0.85);
    }

    .seekInfo {
        background: linear-gradient(270deg, #0142FD 0%, #2475F9 100%);
        font-weight: bold;
        font-size: 20px;
        color: #FFFFFF;
        display: flex;
        align-items: center;
        width: 120px;
        height: 56px;
        cursor: pointer;
        border-top-left-radius: 5px;
        border-bottom-left-radius: 5px;


        img {
            width: 24px;
            height: 24px;
            margin-left: 20px;
            margin-right: 8px;
        }
    }

    .reset {
        margin-left: 8px;
        border-top-left-radius: 0 !important;
        border-bottom-left-radius: 0 !important;
        border-top-right-radius: 5px !important;
        border-bottom-right-radius: 5px !important;
    }

    .stage {
        margin-left: 44px;
        margin-top: 16px;
    }
}

.tabContent {
    margin: 40px 120px 40px 120px;
    background: #F5F7FC;
    border-radius: 10px;
    display: flex;
    align-items: flex-start;
    position: relative;

    .loading {
        position: absolute;
        left: 50%;
        top: 50%;
        transform: translate(-50%, -50%);
    }

    .tabModel {
        border-right: 1px solid #DAE2F5;
        height: 850px;
        overflow: hidden auto;
        flex: none;

        .isChildren {
            font-weight: 500;
            font-size: 16px;
        }

        .draggable-tree {
            margin-left: 30px;

            :deep(.ant-tree-title) {
                font-weight: 500;
                font-size: 20px;
                color: #262626;
            }

            :deep(li) {}

            :deep(.ant-tree-switcher-icon) {
                display: none !important
            }

            :deep(.ant-tree-node-content-wrapper) {
                width: 80%;
                height: 36px !important;
                line-height: 36px !important;

            }

            :deep(.ant-tree-node-selected .selectedTree) {
                color: #236cff !important;
            }

            :deep(.ant-tree-node-selected) {
                background: none !important;

                :hover {
                    background: none !important;
                }
            }

            :deep(.ant-tree-switcher_close) {
                display: inline-block;
                margin-top: 10px;
                background: url("@/assets/images/make/expand.svg") no-repeat !important;

                i {
                    display: none;
                }
            }

            :deep(.ant-tree-switcher_open) {
                display: inline-block;
                margin-top: 10px;
                background: url("@/assets/images/make/close.svg") no-repeat !important;

                i {
                    display: none;
                }
            }


        }
    }

    .tabModel::-webkit-scrollbar {
        display: none;
    }

    .cardContent {
        height: 780px;
        overflow: hidden auto;
    }

    .cardContent::-webkit-scrollbar {
        display: none;
    }

    .tab_text {
        font-size: 18px;
        color: #262626;
        cursor: pointer;
        height: 40px;
    }

    .activeTab {
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-image: url("@/assets/images/home/<USER>");
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 40px;
        padding-top: 32px;
    }

    .topTab {
        margin-top: 0;
        background-image: url("@/assets/images/home/<USER>");
        font-weight: bold;
        font-size: 18px;
        color: #FFFFFF;
        background-repeat: no-repeat;
        background-size: contain;
        width: 226px;
        height: 104px;
        line-height: 104px;
    }

    :deep(.ant-tag) {
        font-weight: 500;
        font-size: 12px;
        color: #2E7FFF;
        height: 20px;
        display: flex;
        align-items: center;
    }

    .card_total {
        display: flex;
        flex-wrap: wrap;
        align-items: flex-start;
        border-bottom: 1px solid #DAE2F5;
    }

    .emptyPhoto {
        margin: auto;

        img {
            width: 240px;
            height: 248px;
        }
    }

    .card_content {
        width: 50%;
        height: 163px;
        border-right: 1px solid #DAE2F5;
        border-bottom: 1px solid #DAE2F5;
        cursor: pointer;
    }

    .rightActive {
        border-right: none;
    }

    .cardActive {
        background-color: #FFFFFF;
        transition: all 0.2s;
        box-shadow: 0 0 10px #DAE2F5;
    }

    .bottomLine {
        border-bottom: none;
    }

    .cardObvious {
        border-bottom: none;
    }

    .card_center {
        display: flex;
        flex-direction: column;
        justify-content: space-between;
        height: 105px;
        margin-left: 12px;
    }

    .card_text {
        display: flex;
        align-items: center;
        justify-content: space-between;
        flex: 1;

        .card_tag {
            display: flex;
            align-items: center;
        }

        .card_title {
            font-weight: bold;
            font-size: 16px;
            color: rgba(0, 0, 0, 0.85);
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-box-orient: vertical;
            -webkit-line-clamp: 1;
        }

        :deep(.cityStyle) {
            font-size: 12px;
            color: rgba(0, 0, 0, 0.45);
            background-color: transparent;
            margin-right: -6px;
        }
    }

    .card_des {
        font-size: 14px;
        color: rgba(0, 0, 0, 0.65);
        display: -webkit-box;
        -webkit-box-orient: vertical;
        -webkit-line-clamp: 2;
        /* 控制显示的行数 */
        overflow: hidden;
        text-overflow: ellipsis;
        word-break: break-all;
    }

    .layPage {
        width: 100%;
        text-align: center;
        margin: 15px 0;
    }
}