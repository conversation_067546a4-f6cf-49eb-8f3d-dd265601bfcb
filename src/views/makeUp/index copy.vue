<template>
  <div class="contentCenter">
    <div class="flex align-center just-sb">
      <!-- <img src="@/assets/images/make/make.png" class="tool" /> -->
      <!-- <div class="vocationPull">
        <a-input
          v-model:value="toolName"
          allow-clear
          :bordered="false"
          placeholder="输入工具名称搜索"
          @keyup.enter="seekContent"
          class="inputClass"
        />

        <div class="seekInfo" @click="seekContent">
          <img src="@/assets/images/make/search.png" />
          <div style="margin-right: 20px">搜索</div>
        </div>
      </div> -->
    </div>
    <div class="titleTool">研发协同</div>
    <!-- <p class="textWork">{{ makeData.totalRows }}个编排工具盒</p> -->
    <div v-if="makeData.rows && makeData.rows.length > 0">
      <div class="flex align-center imageCard">
        <template v-for="(item, index) in makeData.rows" :key="index">
          <div class="imageContent pointer" @click="makeDetail(item)">
            <div class="imageDetails">
              <div
                class="imgBg"
                :style="{
                  backgroundImage: `url('${item.coverUrl}')`,
                }"
              ></div>
              <p class="textWord">{{ item.name }}</p>
              <p class="introduce">
                {{ item.description }}
              </p>
            </div>
          </div>
        </template>
      </div>

      <!-- <div class="layPage">
        <pagination :totalItemCount="totalItemCount" :currentPage="pageNo" :pageItemSize="pageSize"
          @size-change="sizeChange" @page-change="pageChange" />
      </div> -->
    </div>
    <div v-else style="margin-top: 12%">
      <empty />
    </div>
    <div class="loading" v-show="loadingShow">
      <a-spin />
    </div>

    <div class="flex align-center just-sb">
      <div class="titleTool" style="margin-top: 12px">研发工具</div>
    </div>
    <div v-if="toolDate.rows && toolDate.rows.length > 0">
      <div class="flex align-center imageCard" style="margin-bottom: 20px">
        <template v-for="(item, index) in toolDate.rows" :key="index">
          <div class="imageContent pointer" @click="makeDetail(item)">
            <div class="imageDetails">
              <div
                class="imgBg"
                :style="{
                  backgroundImage: `url('${item.coverUrl}')`,
                }"
              ></div>
              <p class="textWord">{{ item.name }}</p>
              <p class="introduce">
                {{ item.description }}
              </p>
            </div>
          </div>
        </template>
      </div>
    </div>
    <div v-else style="margin-top: 12%">
      <empty />
    </div>

    <!-- 编排弹框 -->
    <a-modal
      :visible="previewVisible"
      :footer="null"
      :id="id"
      @cancel="closeModal"
      :width="800"
      :destroyOnClose="true"
      :maskClosable="false"
    >
      <template v-slot:title="title">
        <img src="@/assets/images/make/titleIcon.svg" />
        {{ modalTitle }}
      </template>
      <detail-modal :id="id"></detail-modal>
    </a-modal>
  </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from "vue";
import { getMakeList } from "@/api/makeUp/make.js";
import { getMakeUrl } from "@/utils/getUrl.js";
import pagination from "@/components/pagination/index.vue";
import detailModal from "./components/detailModal.vue";
import empty from "@/components/empty/empty.vue";

export default defineComponent({
  components: {
    pagination,
    empty,
    detailModal,
  },
  setup() {
    const baseURL = getMakeUrl();
    const data = reactive({
      id: "",
      toolName: "",
      totalItemCount: 0,
      pageNo: 1,
      pageSize: 10,
      modalTitle: "",
      makeData: {},
      toolDate: {},
      loadingShow: true,
      previewVisible: false,
    });
    const getList = () => {
      let params = {
        pageNo: data.pageNo,
        pageSize: data.pageSize,
        name: data.toolName,
      };
      data.loadingShow = true;
      getMakeList({ ...params, type: "研发协同" })
        .then((res) => {
          if (res.code === 200) {
            data.loadingShow = false;
            data.makeData = res.data;
            // data.totalItemCount = res.data.totalRows;
          }
        })
        .catch((error) => {
          data.loadingShow = false;
        });
      getMakeList({ ...params, type: "研发工具" })
        .then((res) => {
          if (res.code === 200) {
            data.loadingShow = false;
            data.toolDate = res.data;
          }
        })
        .catch((error) => {
          data.loadingShow = false;
        });
    };
    getList();
    const pageChange = (page) => {
      data.pageNo = page;
      getList();
    };
    const sizeChange = (size) => {
      data.pageSize = size;
    };
    const closeModal = () => {
      data.previewVisible = false;
    };

    const makeDetail = (item) => {
      data.id = item.id;
      data.modalTitle = item.name;
      data.previewVisible = true;
    };
    const seekContent = () => {
      getList();
    };
    return {
      ...toRefs(data),
      pageChange,
      sizeChange,
      baseURL,
      closeModal,
      seekContent,
      makeDetail,
    };
  },
});
</script>

<style lang="scss" scoped>
.contentCenter {
  margin: 20px 120px 0 120px;

  .titleTool {
    font-weight: bold;
    font-size: 24px;
    color: #24456a;
  }

  .inputClass {
    border: none;
    width: 100%;
    height: 100%;
    box-shadow: none !important;
  }

  .tool {
    width: 216px;
    height: 59px;
  }

  .vocationPull {
    :deep(.ant-input-borderless) {
      margin-left: 12px;
    }

    flex: auto;
    margin-left: 30%;
    background: #ffffff;
    display: flex;
    align-items: center;
    border-top-left-radius: 5px;
    border-bottom-left-radius: 5px;
    border-top-right-radius: 5px;
    border-bottom-right-radius: 5px;
    height: 56px;

    .seekInfo {
      background: linear-gradient(270deg, #0142fd 0%, #2475f9 100%);
      font-size: 16px;
      color: #ffffff;
      display: flex;
      align-items: center;
      height: 56px;
      cursor: pointer;
      border-top-right-radius: 5px;
      border-bottom-right-radius: 5px;
      white-space: nowrap;
      padding: 0 20px;

      img {
        width: 18px;
        height: 18px;
        margin-right: 8px;
      }
    }
  }
  .imgBg {
    width: 234px;
    height: 120px;
    background-size: 100% 100%;
    background-repeat: no-repeat;
  }

  .textWork {
    font-size: 14px;
    color: rgba(36, 69, 106, 0.45);
    margin-bottom: 13px;
  }

  .loading {
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
  }

  .imageCard {
    flex-wrap: wrap;
    gap: 20px 24px;
    margin-top: 20px;

    .imageContent {
      background: #ffffff;
      border-radius: 2px;
      border: 1px solid #e9e9e9;
      width: max-content;

      .imageDetails {
        margin: 16px;
      }

      .textWord {
        font-weight: 500;
        font-size: 16px;
        color: rgba(0, 0, 0, 0.85);
        margin: 20px 0 10px 0;
      }

      .introduce {
        font-size: 12px;
        color: rgba(0, 0, 0, 0.45);
        margin-top: 10px;
        width: 234px;
        overflow: hidden;
        display: -webkit-box;
        -webkit-line-clamp: 2;
        -webkit-box-orient: vertical;
      }
    }
  }

  .layPage {
    margin-top: 66px;
    margin-bottom: 50px;

    :deep(.ant-pagination-item) {
      background-color: #ffffff;
    }

    :deep(.ant-pagination-item-active) {
      background-color: #007eff;
    }
  }
}
</style> 
