<template>
  <div class="topImage">
    <div class="topImage_content">
      <div class="topImage_title">集成封装图谱</div>
      <div class="topImage_details">
        实现完整的生命周期管理，包括研发项目管理、需求管理、任务管理、缺陷管理、测试用例管理、计划发布及部署等功能，覆盖了从需求定义到最终交付的各个环节。提供页面编排和大屏编排工具，通过可视化配置简化复杂任务，优化资源分配，实现模块间的高效协作与集成，提升项目研发整体研发质量和交付速度。
      </div>
    </div>

    <div
      class="flex just-sb align-center"
      style="margin: 24px auto; width: 70%"
    >
      <template v-for="(item, index) in projectList" :key="index">
        <div
          @click="update()"
          class="cardBac"
          :style="{ backgroundImage: `url('${item.backgroundImageUrl}')` }"
        >
          <div class="card_dec">
            <div class="card_num">
              <p class="dec_num">{{ item.number }}</p>
              <p class="dec_box" style="margin-top: 8px; margin-left: -5px">
                {{ item.text }}
              </p>
            </div>
            <p
              style="
                font-weight: bold;
                font-size: 16px;
                color: rgba(0, 0, 0, 0.85);
                margin-top: 4px;
              "
            >
              {{ item.title }}
            </p>
          </div>
          <div style="margin-right: 24px" class="margin_t_24">
            <img :src="item.image" style="width: 88px; height: 88px" />
            <!-- <div v-show="item.rate"
              style="font-weight: 400; font-size: 14px; color: rgba(0, 0, 0, 0.45);display: flex;align-items: center;margin-top:8px">
              <p>{{ item.rateText }}</p>
              <p style="font-family: DIN, DIN;font-weight: bold;font-size: 20px;color: rgba(0,0,0,0.65);margin: 0 6px 12px 8px">{{ item.rate }}</p>
              <p style="font-weight: 400;font-size: 14px;color: rgba(0,0,0,0.45)">%</p>
            </div> -->
            <div v-show="!item.rate" style="margin-top: 8px">&nbsp</div>
          </div>
        </div>
      </template>
    </div>
  </div>

  <div class="totaoText">集成封装</div>

  <table-list />

  <a-modal
    :visible="previewVisible"
    @cancel="closeModal"
    :width="600"
    @ok="handleOk"
    :destroyOnClose="true"
    :maskClosable="false"
  >
    <div ref="add">
      支撑项目数：<a-input v-model:value="formData.projectCount" />
      大屏编排数：<a-input v-model:value="formData.screenCount" />
      页面编排数：<a-input v-model:value="formData.pageCount" />
    </div>
  </a-modal>
</template>
<script>
import { defineComponent, reactive, toRefs } from "vue";
import tableList from "./components/tableList.vue";
import { addView, countUpdate } from "@/api/makeUp/make.js";
import image1 from "@/assets/images/make/project.png";
import image2 from "@/assets/images/make/screen.png";
import image3 from "@/assets/images/make/page.png";
import backgroundImageUrl1 from "@/assets/images/home/<USER>";

export default defineComponent({
  name: "topContent",
  components: {
    tableList,
  },
  setup() {
    const data = reactive({
      projectList: [
        {
          number: "",
          text: "个",
          title: "支撑项目数",
          image: image1,
          label: "projectCount",
          backgroundImageUrl: backgroundImageUrl1,
        },
        {
          number: "",
          text: "个",
          title: "大屏编排数",
          image: image2,
          label: "screenCount",
          backgroundImageUrl: backgroundImageUrl1,
        },
        {
          number: "",
          text: "个",
          title: "页面编排数",
          image: image3,
          label: "pageCount",
          backgroundImageUrl: backgroundImageUrl1,
        },
      ],
      previewVisible: false,
      formData: {},
      clickCount: 0,
    });
    const getCountDate = () => {
      addView().then((res) => {
        let result = (data.formData = res.data);
        for (let key in result) {
          for (let i = 0; i < data.projectList.length; i++) {
            if (key == data.projectList[i].label) {
              data.projectList[i].number = result[key];
            }
          }
        }
      });
    };
    getCountDate();

    const closeModal = () => {
      data.previewVisible = false;
    };

    const handleOk = () => {
      countUpdate(data.formData).then((res) => {
        data.previewVisible = false;
        getCountDate();
      });
    };

    const update = () => {
      data.clickCount++;
      if (data.clickCount == 5) {
        data.previewVisible = true;
        data.clickCount = 0;
      }
    };
    return {
      ...toRefs(data),
      update,
      closeModal,
      handleOk,
    };
  },
});
</script>

<style lang="scss" scoped>
.topImage {
  width: 100%;
  height: 400px;
  background-image: url("@/assets/images/make/banner.png");
  background-repeat: no-repeat;
  background-size: cover;

  .topImage_content {
    padding: 63px 0 16px 97px;

    .topImage_title {
      font-weight: bold;
      font-size: 42px;
      color: #122c6c;
    }

    .topImage_details {
      width: 603px;
      font-size: 16px;
      color: #2b3f66;
      line-height: 32px;
      margin-top: 16px;
    }
  }

  .cardBac {
    height: 170px;
    flex: 1;
    background-repeat: no-repeat;
    background-size: 100% 100%;
    display: flex;
    align-items: center;
    justify-content: space-around;

    .card_num {
      display: flex;
      align-items: flex-start;
      height: 49px;
    }

    .card_dec {
      margin: 24px 0 24px 24px;

      .dec_num {
        font-size: 40px;
        font-weight: bold;
        font-family: DIN, DIN;
        color: #2e7fff;
        text-shadow: 0px 4px 6px rgba(46, 127, 255, 0.25);
      }

      .dec_box {
        background-color: #ff5b00;
        border-radius: 50%;
        width: 18px;
        height: 18px;
        font-weight: 500;
        font-size: 12px;
        color: #ffffff;
        text-align: center;
        line-height: 18px;
      }
    }
  }
}

.totaoText {
  font-weight: bold;
  font-size: 28px;
  color: rgba(0, 0, 0, 0.85);
  text-align: center;
  margin-top: 122px;
  margin-bottom: 32px;
}
</style>