<template>
    <div class="qilin-loading" v-show="loadingObj.isShow">
        <div class="qilin-loading-box" :style="{backgroundColor:loadingObj.background}">
            <i class="iconfont icon-jiazai" v-if="!loadingObj.customIcon"></i>
            <i v-else :class="['iconfont',loadingObj.customIcon]"></i>
            <p class="qilin-loading-box-text">{{loadingObj.text}}</p>
        </div>
    </div>
</template>
<script setup>
import { defineProps } from 'vue';
const propsValue=defineProps({
    loadingObj:{
        type:Object,
        default(){
            return {
                isShow:false,
                text:"请求加载中..."
            };
        }
    }
});
</script>
<style lang="scss" scoped>
.qilin-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color:rgba(255,255,255,.5);
    z-index:3000;
    > .qilin-loading-box {
        position: absolute;
        top: 50%;
        width: 100%;
        text-align: center;
        display: flex;
        flex-flow: column nowrap;
        align-items: center;
        justify-content: center;
        transform: translateY(-50%);
        > i {
            width:50px;
            height:50px;
            font-size: 40px;
            line-height:50px;
            animation: loading-rotate 2s linear infinite;
            color:#00060e;
        }
        > .qilin-loading-box-text {
            color: #00060e;
            font-size: 14px;
            margin-top:10px;
        }
    }
}
@keyframes loading-rotate {
    100% {
        transform: rotate(360deg);
    }
}
</style>
