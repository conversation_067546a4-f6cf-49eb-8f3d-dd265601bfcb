import Loading from "./loading.vue";
import { reactive, createApp } from "vue";

const loadingObj = reactive({
    isShow: false,
    text: "请求加载中..."
});
const $loading = createApp(Loading, { loadingObj }).mount(document.createElement("div"));
const loading = {
    show(options) {
        // if(options){
        //     for(let key in options){
        //         loadingObj[key]=options[key];
        //     };
        // };
        Object.assign(loadingObj, options);
        loadingObj.isShow = true;
        document.body.appendChild($loading.$el);
    },
    hide() {
        loadingObj.isShow = false;
    }
};

export {
    loading
};
