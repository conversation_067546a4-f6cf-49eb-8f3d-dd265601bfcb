<template>
  <div style="min-height: 441px">
    <div class="versionContent">
      <div style="padding: 0 40px" v-if="noticeShow">
        <div class="newNoctice">
          <div class="text margin_r_8">{{ noticeObj.title }}</div>
          <img src="@/assets/images/home/<USER>" alt="" />
        </div>
        <div class="versionInfo">
          <div class="text">版本号：{{ noticeObj.version }}</div>
          <div class="time text">发布时间：{{ createTime }}</div>
        </div>
      </div>
    </div>
    <div
      class="reviewDetail"
      v-html="noticeObj.content"
      v-if="noticeShow"
    ></div>
    <div style="height: 80px"></div>
    <div class="knowBtn">
      <a-button
        type="primary"
        style="
          background: #0c70eb;
          width: 88px;
          height: 32px;
          border-radius: 4px;
          font-weight: 500;
          font-size: 14px;
          color: #ffffff;
        "
        @click="knowClick"
      >
        我知道了
      </a-button>
    </div>
  </div>
</template>
<script>
import { defineComponent, reactive, toRefs } from "vue";
import { getVisionList, havedNotice } from "@/api/versionNotice/index.js";

export default defineComponent({
  emits: ["know-cancel", "read-notice"],
  props: {},
  setup(props, { emit }) {
    const data = reactive({
      noticeObj: {},
      createTime: "",
      noticeShow: false,
      loadShow: false,
    });

    const userInfo = JSON.parse(localStorage.getItem("userInfo"));

    const knowClick = () => {
      emit("know-cancel");
    };
    const getList = () => {
      data.loadShow = true;
      getVisionList()
        .then((res) => {
          data.loadShow = false;
          if (res.data) {
            data.noticeShow = true;
            data.createTime = res.data.createTime.slice(0, 10);
            data.noticeObj = res.data;
          } else {
            data.noticeShow = false;
          }
          let params = {
            userId: userInfo.id,
            noticeId: res.data.noticeId,
          };
          havedNotice(params).then(() => {
            emit("read-notice");
          });
        })
        .catch(() => {
          data.loadShow = false;
        });
    };
    getList();

    return {
      ...toRefs(data),
      knowClick,
    };
  },
});
</script>

<style lang="scss" scoped>
.versionContent {
  background-image: url("@/assets/images/home/<USER>");
  background-repeat: no-repeat;
  background-size: 100% 100%;
  height: 441px;

  .newNoctice {
    padding: 40px 0 16px 0;
    display: flex;
    align-items: flex-end;
    .text {
      font-weight: bold;
      font-size: 24px;
      color: #2d4f76;
      line-height: 32px;
    }
    img {
      width: 43px;
      height: 23px;
    }
  }

  .versionInfo {
    display: flex;
    .text {
      font-weight: 400;
      font-size: 16px;
      color: #325a88;
      line-height: 22px;
    }
    .time {
      margin-left: 114px;
    }
  }
}

.emptyNotice {
  font-weight: bold;
  font-size: 24px;
  color: #2d4f76;
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.viewLoad {
  position: absolute;
  left: 50%;
  top: 50%;
  transform: translate(-50%, -50%);
}

.reviewDetail {
  background: rgba(255, 255, 255, 0.5);
  border-radius: 8px;
  border: 2px solid;
  border-image: linear-gradient(
      180deg,
      rgba(255, 255, 255, 1),
      rgba(141, 169, 221, 0.19)
    )
    2 2;
  margin: 0 40px;
  margin-top: -305px;
  width: -webkit-fill-available;
  padding: 24px;
}
.knowBtn {
  text-align: center;
  position: absolute;
  bottom: 24px;
  left: 50%;
  transform: translate(-50%);
}
</style>