<template>
  <a-form :model="formState" id="form" :rules="rules" ref="formRef">
    <a-form-item label="提交人" name="contact">
      {{ formState.creator }}
    </a-form-item>
    <a-form-item label="联系电话" name="phone">
      {{ formState.phone }}
    </a-form-item>
    <a-form-item label="联系邮箱" name="mail">
      {{ formState.mail }}
    </a-form-item>
    <a-form-item
      label="问题类型"
      name="typeName"
      :rules="[{ required: true, message: '请选择问题类型' }]"
    >
      <a-select
        placeholder="请选择问题类型"
        v-model:value="formState.typeName"
        allowClear
      >
        <template v-for="(opt, index) in typeOptions" :key="index">
          <a-select-option :value="opt.label">
            {{ opt.label }}
          </a-select-option>
        </template>
      </a-select>
    </a-form-item>
    <a-form-item
      label="反馈描述"
      name="detail"
      :rules="[
        { required: true, message: '请输入反馈描述' },
        { validator: describeVal, trigger: 'blur' },
      ]"
    >
      <a-textarea
        v-model:value="formState.detail"
        placeholder="请输入反馈描述(限300字)"
      />
    </a-form-item>
    <a-form-item label="相关截图" name="articleCover">
      <a-upload
        v-model:file-list="imgList"
        list-type="picture-card"
        accept="image/png,image/jpeg,image/jpg"
        :customRequest="uploadImg"
        class="avatar-uploader"
        :before-upload="beforeUpload"
      >
        <div v-if="imgList.length < 4">
          <plus-outlined class="tip" />
          <div class="tip ant-upload-text font_A2ABB5 font_14">上传截图</div>
        </div>
      </a-upload>

      <span class="tip font_12"
        >支持jpg、jpeg、png等格式的图片上传，文件限制为10M,最多上传4张图片</span
      >
    </a-form-item>

    <a-form-item style="text-align: center">
      <a-button style="margin-right: 20px" @click="closeModal">取消</a-button>
      <a-button type="primary" @click="onSubmit">提交</a-button>
    </a-form-item>
  </a-form>
</template>
<script>
import { defineComponent, reactive, toRefs, ref } from "vue";
import { PlusOutlined, LoadingOutlined } from "@ant-design/icons-vue";
import { uploadFileList } from "@/api/fileUpload/uploadFile.js";
import { create } from "@/api/suggest/suggest.js";

import { message } from "ant-design-vue";
export default defineComponent({
  components: { PlusOutlined, LoadingOutlined },
  emits: ["submit-ok", "submit-cancel"],
  props: {},

  setup(props, { emit }) {
    const describeVal = (rule, value) => {
      return new Promise((resolve, reject) => {
        if (value && value.replace(/\s/g, "").length > 300) {
          reject("描述长度不得超过三百个字");
        } else {
          resolve();
        }
      });
    };
    const formRef = ref();
    const userInfo = JSON.parse(localStorage.getItem("userInfo"));
    const data = reactive({
      imgList: [],
      formState: {
        creator: userInfo.realName,
        mail: userInfo.mail,
        phone: userInfo.phone,
        fileList: [],
      },
      uploading: false,
      rules: {
        detail: [
          { required: true, message: "请输入反馈描述", trigger: "blur" },
          {
            pattern: /^.{1,300}$/,
            message: "反馈描述不得超过三百个字",
            trigger: "blur",
          },
        ],
      },
      typeOptions: [
        {
          label: "产品建议",
          value: "1",
        },
        {
          label: "功能建议",
          value: "2",
        },
        {
          label: "内容建议",
          value: "3",
        },
        {
          label: "安全建议",
          value: "4",
        },
        {
          label: "其他",
          value: "5",
        },
      ],
    });

    const closeModal = () => {
      data.formState = {
      	creator: userInfo.realName,
        mail: userInfo.mail,
        phone: userInfo.phone,
        fileList: [],
      };
      data.imgList = [];
      emit("submit-cancel");
    };

    const beforeUpload = (file, fileList) => {
      if (data.uploading) {
        message.warning("正在上传中，请稍后再试");
        return Promise.reject();
      }
      return new Promise((resolve, reject) => {
        if (data.totalSize + file.size > 10485760) {
          message.warning("文件大小不能超过10M");
          reject();
        }
        resolve();
        return;
      });
    };

    const uploadFile = async (info) => {
      data.uploading = true;
      let formData = new FormData();
      formData.append("file", info.file);
      const result = await uploadFileList(formData);
      if (result.code == 200) {
        message.success("上传成功");
        data.uploading = false;
        info.onSuccess();
        data.uploading = false;
        return result;
      }
      message.success("上传失败");
    };
    const uploadImg = async (info) => {
      let result = await uploadFile(info);
      data.formState.fileList.push({ uri: result.data.fileUrl });
    };

    const onSubmit = () => {
      formRef.value
        .validate()
        .then(() => {
          console.log(data.formState);
          create(data.formState)
            .then((res) => {
              message.success("反馈成功");
              closeModal();
            })
            .catch((err) => {
              console.log(err);
            });
        })
        .catch((errorInfo) => {
          console.log("Validate Failed:", errorInfo);
        });
    };

    return {
      ...toRefs(data),
      uploadImg,
      // handleChange,
      onSubmit,
      closeModal,
      beforeUpload,
      formRef,
      describeVal,
    };
  },
});
</script>

<style lang="scss" scoped>
:deep(.ant-form-item-label) {
  width: 100px;
}
.m-img {
  max-width: 78px;
  max-height: 78px;
}
.tip {
  color: #a2abb5;
}
</style>