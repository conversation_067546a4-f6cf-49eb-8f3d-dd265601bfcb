<template>
    <div class="show_toast">
        <div class="toast-content">
            <h1 class="toast-title">麒麟能力平台暂不支持通过移动端设备访问</h1>
            <h1 class="toast-link">请使用PC端浏览器访问 <a href="https://smart.jsisi.cn:8099"
                    target="_blank">https://smart.jsisi.cn:8099</a> 体验完整功能。</h1>
        </div>
    </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
    name: 'MobileLayout',
    setup() {
        const data = reactive({})

        return {
            ...toRefs(data),
        }
    }
})
</script>

<style lang="scss" scoped>
.show_toast {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100vh;
    background: linear-gradient(135deg, #f5f7fa, #c3cfe2); // 渐变背景
    padding: 20px;
    box-sizing: border-box;

    .toast-content {
        background: #fff; // 白色背景
        padding: 18px;
        border-radius: 12px; // 圆角
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1); // 阴影效果
        text-align: center;
        max-width: 500px; // 限制最大宽度
        width: 100%;
        animation: fadeIn 0.5s ease-in-out; // 淡入动画
    }

    .toast-title {
        font-size: 24px;
        color: #333;
        line-height: 1.5;
        margin-bottom: 20px;
        font-weight: bold;
        padding-bottom: 20px; // 增加下边距
    }

    .toast-link {
        font-size: 18px;
        color: #666;
        line-height: 1.5;

        a {
            color: #007bff;
            text-decoration: none;
            font-weight: bold;

            &:hover {
                text-decoration: underline;
            }
        }
    }
}

// 响应式设计：针对小屏幕调整样式
@media (max-width: 480px) {
    .show_toast {
        .toast-content {
            padding: 18px;
        }

        .toast-title {
            font-size: 20px;
            padding-bottom: 15px;

            &::after {
                width: 40px; // 小屏幕下装饰线缩短
            }
        }

        .toast-link {
            font-size: 16px;
        }
    }
}

// 淡入动画
@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }

    to {
        opacity: 1;
        transform: translateY(0);
    }
}
</style>