<!--
 * @Description: 
 * @Author: xiuji
 * @Date: 2024-03-27 10:01:26
 * @LastEditTime: 2024-03-27 10:07:15
 * @LastEditors: Do not edit
-->
<template>
    <div>
        首页
    </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
    setup() {
        const data = reactive({

        })

        return {
            ...toRefs(data),
        }
    }
})
</script>

<style lang="scss" scoped></style>