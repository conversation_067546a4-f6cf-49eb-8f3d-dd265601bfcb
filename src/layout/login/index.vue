<!--
 * @Description: 
 * @Author: xiuji
 * @Date: 2024-03-27 09:58:57
 * @LastEditTime: 2024-03-27 10:02:10
 * @LastEditors: Do not edit
-->
<template>
    <div>
        登录页面
    </div>
</template>

<script>
import { defineComponent, reactive, toRefs } from 'vue'

export default defineComponent({
    setup() {
        const data = reactive({
            count: 0,
        })

        return {
            ...toRefs(data),
        }
    }
})
</script>
<style lang="scss" scoped></style>