import BasicLayout from '@/layout/index.vue'

let ROUTES = [{
		path: '/office',
		component: BasicLayout,
		hidden: true,
		meta: {
			title: "办公"
		},
		children: [{
			path: "/officeCenter",
			name: "officeCenter",
			redirect: "/officeCenter/backlog",
			component: () =>
				import("@/views/officeCenter/index.vue"),
			meta: {
				title: "办公中心"
			},
			children: [{
					path: "/officeCenter/backlog",
					name: "backlog",
					component: () =>
						import("@/views/officeCenter/backlog/index.vue"),
					meta: {
						title: "待办"
					}
				},
				{
					path: "/officeCenter/completed",
					name: "completed",
					component: () =>
						import("@/views/officeCenter/completed/index.vue"),
					meta: {
						title: "已办"
					}
				},
				{
					path: "/officeCenter/myApplication",
					name: "myApplication",
					component: () =>
						import("@/views/officeCenter/myApplication/index.vue"),
					meta: {
						title: "我的申请"
					}
				}
			]
		}]
	},
	{
		path: '/viewFile',
		component: () =>
			import('@/views/moduleList/viewFile/index.vue'),
		name: 'viewFile'
	}
]

export default ROUTES;