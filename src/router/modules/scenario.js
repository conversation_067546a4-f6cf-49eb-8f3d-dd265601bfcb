import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/scenario',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "场景"
        },
        children: [
            {
                path: 'scenarioPlan',
                name: 'scenarioPlan',
                meta: {},
                component: () => import('@/views/scenario/homePage/topContent/index.vue')
            },
            {
                path: 'scenarioDetail',
                name: 'scenarioDetail',
                component: () => import('@/views/scenario/detail/index.vue')

            },
        ]
    },
    {
        path: '/scenarioFile',
        component: () => import('@/views/scenario/viewFile/index.vue'),
        name: 'scenarioFile'
    }
]

export default ROUTES;