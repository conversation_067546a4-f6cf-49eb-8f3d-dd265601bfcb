import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/cityArea',
        name: 'cityArea',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "地市专区",
            KeepAlive: true,
        },
        children: [
            {
                path: 'cityAreaNew',
                name: 'cityAreaNew',
                meta: {
                	KeepAlive: true,
                },
                component: () => import('@/views/cityArea/homePage/topContent/index.vue')
            },
            {
                path: 'cityAreaDetailNew',
                name: 'cityAreaDetailNew',
                component: () => import('@/views/cityArea/detail/index.vue')

            },
            {
                path: 'cityAreaApplyNew',
                name: 'cityAreaApplyNew',
                component: () => import('@/views/cityArea/detail/applyDetail.vue')

            },
        ]
    },
    {
        path: '/viewFile',
        component: () => import('@/views/solution/viewFile/index.vue'),
        name: 'viewFile'
    }
]

export default ROUTES;