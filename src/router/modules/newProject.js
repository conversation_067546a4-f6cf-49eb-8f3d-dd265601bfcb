import BasicLayout from '@/layout/index.vue'
import { KeepAlive } from 'vue';

let ROUTES = [
    {
        path: '/newProject',
        component: BasicLayout,
        hidden: true,
        name: 'newProject',
        meta: {
            title: "图谱",
            KeepAlive: true,
        },
        children: [
            {
                path: 'newProject',
                name: 'newAllProject',
                meta: {
                    KeepAlive: true,
                },
                component: () => import('@/views/NewProject/index.vue')
            }
        ]
    }
]

export default ROUTES;