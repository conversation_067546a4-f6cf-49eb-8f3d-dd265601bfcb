import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/ability',
        component: BasicLayout,
        hidden: true, 
        meta: {
            title: "能力汇聚"
        },
        children: [
            {
                path: 'abilityHome',
                name: 'abilityHome',
                meta: {},
                component: () => import('@/views/ability/topContent/index.vue')
            },
            {
                path: 'detail',
                name: 'abilityDetail',
                component: () => import('@/views/ability/detail/index.vue')

            },
        ]
    },
]

export default ROUTES;