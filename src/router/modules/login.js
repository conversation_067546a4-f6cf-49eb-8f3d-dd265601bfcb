let ROUTES = [
    {
        path: "/",
        redirect: "/login",
        name: 'redirector'
    },
    {
        path: '/login',
        name: 'login',
        component: () => import('@/views/login/index.vue'),
    },
    {
        path: '/freeLogin',
        name: 'freeLogin',
        component: () => import('@/views/login/freeIndex.vue'),
    },
    {
	    	path: '/lookPdf',
	    	name: 'lookPdf',
	    	component: () => import('@/views/404/lookPdf.vue'),
    },
    {
        path: '/sharePdf',
        name: 'sharePdf',
        component: () => import('@/views/404/sharePdf.vue'),
},
]

export default ROUTES;