/*
 * @Description: 
 * @Author: xiuji
 * @Date: 2024-03-27 10:31:25
 * @LastEditTime: 2025-01-26 10:56:38
 * @LastEditors: Do not edit
 */
import BasicLayout from '@/layout/index.vue'

let ROUTES = [{
    path: '/',
    component: BasicLayout,
    hidden: true,
    redirect: "/home",
    meta: {
        title: "首页",
        keepAlive: true
    },
    children: [{
        path: 'home',
        name: 'home',
        meta: {},
        component: () => import('@/views/NewProject/home/<USER>')
    },
    {
        path: 'center',
        name: 'center',
        meta: {},
        component: () => import('@/views/center/index.vue')
    },
    {
        path: 'boting',
        name: 'boting',
        component: () => import('@/views/404/index.vue'),
    },
    {
        path: 'botingNull',
        name: 'botingNull',
        component: () => import('@/views/Null/index.vue'),
    },
    {
        path: 'thirdView',
        component: () => import('@/views/thirdView/index.vue'),
        name: 'thirdView',
        meta: {
            title: "能力",
            keepAlive: true
        },
    },
    {
        path: 'second',
        component: () => import('@/views/second/index.vue'),
        name: 'second',
    },
    {
        path: 'AISearch',
        name: 'AISearch',
        meta: {},
        component: () => import('@/views/NewProject/home/<USER>')
    },
    ]
},
{
    path: '/viewFile',
    component: () => import('@/views/solution/viewFile/index.vue'),
    name: 'viewFile'
},
{
    path: '/mobile',
    name: 'mobile',
    component: () => import('@/layout/mobile/index.vue'),
}
]

export default ROUTES;