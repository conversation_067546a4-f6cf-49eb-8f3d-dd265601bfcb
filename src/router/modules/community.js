import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/community',
        component: BasicLayout,
        hidden: true, 
        meta: {
            title: "智慧社区"
        },
        children: [
            {
                path: 'communityHome',
                name: 'communityHome',
                meta: {},
                component: () => import('@/views/community/index.vue')
            },
            {
                path: 'communityDetail',
                name: 'communityDetail',
                meta: {},
                component: () => import('@/views/community/detail/index.vue')
            },
        ]
    },
]

export default ROUTES;