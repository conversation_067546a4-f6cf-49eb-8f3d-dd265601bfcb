import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/caseStore',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "案例库"
        },
        children: [
            {
                path: 'topContentCase',
                name: 'topContentCase',
                meta: {},
                component: () => import('@/views/caseStore/homePage/topContent/index.vue')
            },
            {
                path: 'detailNew',
                name: 'caseDetailNew',
                component: () => import('@/views/caseStore/detail/applyDetail.vue')

            },
        ]
    },
    {
        path: '/viewFile',
        component: () => import('@/views/caseStore/viewFile/index.vue'),
        name: 'viewFile'
    }
]

export default ROUTES;