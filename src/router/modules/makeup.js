import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/makeTool',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "集成封装"
        },
        children: [
            {
                path: 'makeUp',
                name: 'makeUp',
                meta: {},
                component: () => import('@/views/makeUp/index.vue')
            },
            {
                path: 'making',
                name: 'making',
                component: () => import('@/views/404/index.vue'),
            },
        ]
    },
]

export default ROUTES;