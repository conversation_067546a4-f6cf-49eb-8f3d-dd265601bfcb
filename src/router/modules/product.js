import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/product',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "产品",
            KeepAlive: true,
        },
        children: [
            {
                path: 'productList',
                name: 'productList',
                meta: {
                	KeepAlive: true,
                },
                component: () => import('@/views/product/homePage/topContent/index.vue')
            },
            {
                path: 'productDetail',
                name: 'productDetail',
                component: () => import('@/views/product/detail/index.vue')

            },
        ]
    },
    {
        path: '/viewFile',
        component: () => import('@/views/product/viewFile/index.vue'),
        name: 'viewFile'
    }
]

export default ROUTES;