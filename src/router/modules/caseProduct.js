import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/caseProductStore',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "产品案例库"
        },
        children: [
            {
                path: 'topContentProduct',
                name: 'topContentProduct',
                meta: {},
                component: () => import('@/views/caseProduct/homePage/topContent/index.vue')
            },
            {
                path: 'caseProductDetail',
                name: 'caseProductDetail',
                component: () => import('@/views/caseProduct/detail/applyDetail.vue')

            },
        ]
    },
    {
        path: '/viewCaseFile',
        component: () => import('@/views/caseProduct/viewFile/index.vue'),
        name: 'viewCaseFile'
    }
]

export default ROUTES;