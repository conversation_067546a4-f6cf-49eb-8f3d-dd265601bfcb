import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/system',
        component: BasicLayout,
        hidden: true,
        children: [
            {
                path: 'personal',
                name: 'personal',
                component: () => import('@/views/personal/index.vue'),
                meta: {
                    title: "个人信息"
                },
            },
            {
                path: 'revampPwd',
                name: 'revampPwd',
                component: () => import('@/views/revampPwd/index.vue'),
                meta: {
                    title: "修改密码"
                },
            },
            {
                path: 'viewPageMd',
                name: 'viewPageMd',
                component: () => import('@/views/home/<USER>/viewMD.vue'),
                meta: {
                    title: "预览"
                },
            }
        ]
    },
]

export default ROUTES;