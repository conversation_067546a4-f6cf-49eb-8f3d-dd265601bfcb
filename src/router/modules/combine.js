import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/customized',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "组合"
        },
        children: [
            // {
            //     path: 'customizedList',
            //     name: 'customizedList',
            //     meta: {},
            //     component: () => import('@/views/combine/index.vue')
            // },
            {
                path: 'customizedList',
                name: 'customizedList',
                meta: {},
                component: () => import('@/views/customized/index.vue')
            },

        ]
    }
]

export default ROUTES;