import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/visionPolicy',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "政策洞察"
        },
        children: [
            {
                path: 'policyList',
                name: 'policyList',
                meta: {},
                component: () => import('@/views/visionPolicy/index.vue')
            },
            {
                path: 'visionDetail',
                name: 'visionDetail',
                meta: {},
                component: () => import('@/views/visionPolicy/components/visionDetail.vue')
            },
        ]
    },
]

export default ROUTES;