import BasicLayout from '@/layout/index.vue'
import { KeepAlive } from 'vue';

let ROUTES = [
    {
        path: '/buyList',
        component: BasicLayout,
        hidden: true, 
        name:'buyList',
        meta: {
            title: "订购",
            KeepAlive:true,
        },
        children: [
            {
                path: 'buyList',
                name: 'buyListPage',
                meta: {},
                component: () => import('@/views/myBuy/index.vue')
            },
           
        ]
    },
]

export default ROUTES;