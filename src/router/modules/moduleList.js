import BasicLayout from '@/layout/index.vue'
import { KeepAlive } from 'vue';

let ROUTES = [
    {
        path: '/module',
        name: "module",
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "模块库",
            KeepAlive: true,
        },
        children: [
            {
                path: 'moduleList',
                name: 'moduleList',
                meta: {
                    KeepAlive: true,
                },
                component: () => import('@/views/moduleList/homePage/topContent/index.vue')
            },
            {
                path: 'modulelNew',
                name: 'modulelNew',
                component: () => import('@/views/moduleList/detail/index.vue')

            },
        ]
    },
    {
        path: '/viewFile',
        component: () => import('@/views/moduleList/viewFile/index.vue'),
        name: 'viewFile'
    }
]

export default ROUTES;