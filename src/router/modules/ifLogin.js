import BasicLayout from '@/layout/index.vue'

let ROUTES = [{
    path: '/login',
    component: BasicLayout,
    hidden: true,
    meta: {
        title: "信息中心"
    },
    children: [{
        path: 'unLogin',
        name: "unLogin",
        meta: {},
        component: () => import('../../views/unLogin/index.vue'),
    },{
        path:'isLogin',
        name:'isLogin',
        meta:{},
        component:()=>import('../../views/isLogin/index.vue')
    },{
        path:"detail",
        name:'detail',
        meta:{},
        component:()=>import('../../views/isLogin/detail.vue')
    }],
}, ]

export default ROUTES;