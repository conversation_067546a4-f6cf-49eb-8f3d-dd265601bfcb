import BasicLayout from '@/layout/index.vue'

let ROUTES = [
    {
        path: '/prefecture',
        component: BasicLayout,
        hidden: true,
        meta: {
            title: "专区"
        },
        children: [
            {
                path: 'regionHome',
                name: 'regionHome',
                meta: {},
                component: () => import('@/views/prefecture/index.vue')
            },
            {
                path: 'lowLevel',
                name: 'lowLevel',
                meta: {},
                component: () => import('@/views/lowLevel/index.vue')
            },
            {
                path: 'policyDetail',
                name: 'policyDetail',
                meta: {},
                component: () => import('@/views/lowLevel/policyDetail/index.vue')
            },
            {
                path: 'proLevel',
                name: 'proLevel',
                meta: {},
                component: () => import('@/views/proLevel/index.vue')
            },
            {
                path: 'precinctDetail',
                name: 'precinctDetail',
                meta: {},
                component: () => import('@/views/proLevel/detail/index.vue')
            },
        ]
    },
]

export default ROUTES;