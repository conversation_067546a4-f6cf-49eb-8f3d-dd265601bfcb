/*
 * @Description: 
 * @Author: xiuji
 * @Date: 2024-03-27 15:56:56
 * @LastEditTime: 2024-03-27 16:03:07
 * @LastEditors: Do not edit
 */
import { defineConfig, presetUno, transformerDirectives, transformerVariantGroup } from 'unocss';

export default defineConfig({
    presets: [
        presetUno(),
    ],
    rules: [
        ['btn', { padding: '8px 16px', borderRadius: '4px', fontWeight: '600' }],
    ],
    shortcuts: [
        ['btn-primary', 'btn bg-blue-500 text-white border-none hover:bg-blue-600'],
        ['card', 'p-16 rounded-box shadow-md bg-white'],
    ],
    transformers: [
        transformerDirectives(),
        transformerVariantGroup(),
    ],
});
