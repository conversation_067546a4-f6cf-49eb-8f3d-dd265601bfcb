import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

// 获取当前文件的目录路径
const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 更新版本号
function updateVersion() {
  const versionFile = path.join(__dirname, '../public/version.json');
  const currentTime = new Date().toISOString();
  const version = `1.0.${Date.now()}`;
  
  const versionData = {
    version: version,
    timestamp: currentTime
  };
  
  fs.writeFileSync(versionFile, JSON.stringify(versionData, null, 2));
  console.log(`版本已更新为: ${version}`);
}

updateVersion(); 